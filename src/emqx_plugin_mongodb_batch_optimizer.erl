%%%-------------------------------------------------------------------
%%% @doc MongoDB插件批处理优化和性能评估模块
%%% 这个模块提供智能批处理优化和全面的性能评估功能
%%%
%%% 核心功能：
%%% 1. 自适应批处理大小优化 - 根据系统负载和性能动态调整批处理大小
%%% 2. 性能指标收集和分析 - 收集详细的性能数据并进行智能分析
%%% 3. 吞吐量分析和调优 - 实时监控吞吐量并提供优化建议
%%% 4. 延迟优化 - 监控和优化操作延迟
%%% 5. 资源使用优化 - 优化CPU、内存、网络资源使用
%%% 6. 性能预测和容量规划 - 基于历史数据预测性能趋势
%%%
%%% 优化算法：
%%% - 动态批处理大小调整：基于延迟和吞吐量的双目标优化
%%% - 性能回归分析：使用线性回归预测性能趋势
%%% - 负载自适应：根据系统负载自动调整优化策略
%%% - 多维度评分：综合考虑延迟、吞吐量、资源使用等因素
%%%
%%% Java等价概念：
%%% 类似于Spring Batch的性能优化和Apache Kafka的批处理优化
%%% 以及JVM性能调优工具如JProfiler、VisualVM等
%%%
%%% @end
%%%-------------------------------------------------------------------
-module(emqx_plugin_mongodb_batch_optimizer).

-behaviour(gen_server).

-include("emqx_plugin_mongodb.hrl").

%% API导出
-export([
    start_link/0,                    % 启动批处理优化服务
    stop/0,                          % 停止服务
    
    % 批处理优化API
    optimize_batch_size/2,           % 优化批处理大小
    get_optimal_batch_size/1,        % 获取最优批处理大小
    update_batch_performance/3,      % 更新批处理性能数据
    
    % 性能评估API
    collect_performance_metrics/0,   % 收集性能指标
    get_performance_report/0,        % 获取性能报告
    analyze_throughput/1,            % 分析吞吐量
    analyze_latency/1,               % 分析延迟
    
    % 优化建议API
    get_optimization_suggestions/0,  % 获取优化建议
    apply_optimization/1,            % 应用优化策略
    
    % 集成API
    integrate/0                      % 集成到协调器
]).

%% gen_server回调
-export([
    init/1,
    handle_call/3,
    handle_cast/2,
    handle_info/2,
    terminate/2,
    code_change/3
]).

%% 内部状态记录
-record(state, {
    % 批处理优化状态
    batch_sizes = #{},               % 各操作类型的当前批处理大小
    optimal_sizes = #{},             % 各操作类型的最优批处理大小
    performance_history = [],        % 性能历史数据
    
    % 性能指标
    metrics = #{},                   % 当前性能指标
    throughput_data = [],            % 吞吐量数据
    latency_data = [],               % 延迟数据
    
    % 优化配置
    optimization_enabled = true,     % 是否启用优化
    analysis_interval = 60000,       % 分析间隔（毫秒）
    max_batch_size = 1000,          % 最大批处理大小
    min_batch_size = 10,             % 最小批处理大小
    
    % 统计信息
    total_optimizations = 0,         % 总优化次数
    successful_optimizations = 0,    % 成功优化次数
    last_optimization_time = 0       % 上次优化时间
}).

%% ============================================================================
%% API函数
%% ============================================================================

%% @doc 启动批处理优化服务
start_link() ->
    gen_server:start_link({local, ?MODULE}, ?MODULE, [], []).

%% @doc 停止服务
stop() ->
    gen_server:call(?MODULE, stop, 5000).

%% @doc 优化批处理大小
%% 根据操作类型和当前性能数据优化批处理大小
optimize_batch_size(OperationType, CurrentPerformance) ->
    gen_server:call(?MODULE, {optimize_batch_size, OperationType, CurrentPerformance}).

%% @doc 获取最优批处理大小
get_optimal_batch_size(OperationType) ->
    gen_server:call(?MODULE, {get_optimal_batch_size, OperationType}).

%% @doc 更新批处理性能数据
update_batch_performance(OperationType, BatchSize, PerformanceData) ->
    gen_server:cast(?MODULE, {update_batch_performance, OperationType, BatchSize, PerformanceData}).

%% @doc 收集性能指标
collect_performance_metrics() ->
    gen_server:call(?MODULE, collect_performance_metrics).

%% @doc 获取性能报告
get_performance_report() ->
    gen_server:call(?MODULE, get_performance_report).

%% @doc 分析吞吐量
analyze_throughput(TimeRange) ->
    gen_server:call(?MODULE, {analyze_throughput, TimeRange}).

%% @doc 分析延迟
analyze_latency(TimeRange) ->
    gen_server:call(?MODULE, {analyze_latency, TimeRange}).

%% @doc 获取优化建议
get_optimization_suggestions() ->
    gen_server:call(?MODULE, get_optimization_suggestions).

%% @doc 应用优化策略
apply_optimization(OptimizationStrategy) ->
    gen_server:call(?MODULE, {apply_optimization, OptimizationStrategy}).

%% @doc 集成到协调器
integrate() ->
    try
        % 注册到协调器（如果协调器存在）
        case erlang:whereis(emqx_plugin_mongodb_coordinator) of
            undefined ->
                ?SLOG(debug, #{msg => "coordinator_not_running_skipping_batch_optimizer_integration"});
            CoordinatorPid ->
                % 向协调器注册批处理优化服务
                CoordinatorPid ! {register_service, batch_optimizer, self()},
                ?SLOG(info, #{msg => "batch_optimizer_integrated_with_coordinator"})
        end,
        ok
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "failed_to_integrate_batch_optimizer",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {integration_failed, R}}
    end.

%% ============================================================================
%% gen_server回调实现
%% ============================================================================

%% @doc 初始化服务
init([]) ->
    ?SLOG(info, #{msg => "starting_batch_optimizer_service"}),
    
    % 创建性能数据ETS表
    ets:new(?BATCH_PERFORMANCE_TAB, [named_table, public, set, {read_concurrency, true}]),
    ets:new(?PERFORMANCE_METRICS_TAB, [named_table, public, set, {read_concurrency, true}]),
    
    % 初始化默认批处理大小
    DefaultBatchSizes = #{
        insert => 100,
        update => 50,
        delete => 30,
        query => 200
    },
    
    % 设置定期性能分析
    erlang:send_after(60000, self(), analyze_performance),
    
    State = #state{
        batch_sizes = DefaultBatchSizes,
        optimal_sizes = DefaultBatchSizes,
        metrics = #{},
        performance_history = [],
        throughput_data = [],
        latency_data = []
    },
    
    ?SLOG(info, #{msg => "batch_optimizer_service_started"}),
    {ok, State}.

%% @doc 处理同步调用
handle_call({optimize_batch_size, OperationType, CurrentPerformance}, _From, State) ->
    try
        {NewBatchSize, NewState} = do_optimize_batch_size(OperationType, CurrentPerformance, State),
        {reply, {ok, NewBatchSize}, NewState}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "batch_size_optimization_failed",
                operation_type => OperationType,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {reply, {error, R}, State}
    end;

handle_call({get_optimal_batch_size, OperationType}, _From, State) ->
    OptimalSize = maps:get(OperationType, State#state.optimal_sizes, 100),
    {reply, {ok, OptimalSize}, State};

handle_call(collect_performance_metrics, _From, State) ->
    try
        Metrics = do_collect_performance_metrics(State),
        NewState = State#state{metrics = Metrics},
        {reply, {ok, Metrics}, NewState}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "performance_metrics_collection_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {reply, {error, R}, State}
    end;

handle_call(get_performance_report, _From, State) ->
    try
        Report = generate_performance_report(State),
        {reply, {ok, Report}, State}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "performance_report_generation_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {reply, {error, R}, State}
    end;

handle_call({analyze_throughput, TimeRange}, _From, State) ->
    try
        Analysis = analyze_throughput_data(TimeRange, State),
        {reply, {ok, Analysis}, State}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "throughput_analysis_failed",
                time_range => TimeRange,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {reply, {error, R}, State}
    end;

handle_call({analyze_latency, TimeRange}, _From, State) ->
    try
        Analysis = analyze_latency_data(TimeRange, State),
        {reply, {ok, Analysis}, State}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "latency_analysis_failed",
                time_range => TimeRange,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {reply, {error, R}, State}
    end;

handle_call(get_optimization_suggestions, _From, State) ->
    try
        Suggestions = generate_optimization_suggestions(State),
        {reply, {ok, Suggestions}, State}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "optimization_suggestions_generation_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {reply, {error, R}, State}
    end;

handle_call({apply_optimization, OptimizationStrategy}, _From, State) ->
    try
        NewState = apply_optimization_strategy(OptimizationStrategy, State),
        {reply, ok, NewState}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "optimization_strategy_application_failed",
                strategy => OptimizationStrategy,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {reply, {error, R}, State}
    end;

handle_call(stop, _From, State) ->
    {stop, normal, ok, State};

handle_call(_Request, _From, State) ->
    {reply, {error, unknown_request}, State}.

%% @doc 处理异步消息
handle_cast({update_batch_performance, OperationType, BatchSize, PerformanceData}, State) ->
    try
        NewState = do_update_batch_performance(OperationType, BatchSize, PerformanceData, State),
        {noreply, NewState}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "batch_performance_update_failed",
                operation_type => OperationType,
                batch_size => BatchSize,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {noreply, State}
    end;

handle_cast(_Msg, State) ->
    {noreply, State}.

%% @doc 处理定时器和其他消息
handle_info(analyze_performance, State) ->
    try
        NewState = do_periodic_performance_analysis(State),
        % 设置下次分析
        erlang:send_after(State#state.analysis_interval, self(), analyze_performance),
        {noreply, NewState}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "periodic_performance_analysis_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            % 即使分析失败，也要设置下次分析
            erlang:send_after(State#state.analysis_interval, self(), analyze_performance),
            {noreply, State}
    end;

handle_info(_Info, State) ->
    {noreply, State}.

%% @doc 服务终止处理
terminate(_Reason, _State) ->
    ?SLOG(info, #{msg => "batch_optimizer_service_terminated"}),
    
    % 清理ETS表
    catch ets:delete(?BATCH_PERFORMANCE_TAB),
    catch ets:delete(?PERFORMANCE_METRICS_TAB),
    
    ok.

%% @doc 代码更新处理
code_change(_OldVsn, State, _Extra) ->
    {ok, State}.

%% ============================================================================
%% 内部实现函数
%% ============================================================================

%% @doc 执行批处理大小优化
%% 使用双目标优化算法：最小化延迟，最大化吞吐量
do_optimize_batch_size(OperationType, CurrentPerformance, State) ->
    CurrentBatchSize = maps:get(OperationType, State#state.batch_sizes, 100),

    % 获取历史性能数据
    HistoryKey = {OperationType, batch_performance},
    HistoryData = case ets:lookup(?BATCH_PERFORMANCE_TAB, HistoryKey) of
        [{_, History}] -> History;
        [] -> []
    end,

    % 分析当前性能
    #{
        latency := Latency,
        throughput := Throughput,
        success_rate := SuccessRate
    } = CurrentPerformance,

    % 计算性能评分（0-1，越高越好）
    LatencyScore = calculate_latency_score(Latency),
    ThroughputScore = calculate_throughput_score(Throughput, OperationType),
    SuccessScore = SuccessRate,

    % 综合评分（延迟权重40%，吞吐量权重40%，成功率权重20%）
    OverallScore = LatencyScore * 0.4 + ThroughputScore * 0.4 + SuccessScore * 0.2,

    % 基于历史数据和当前性能决定批处理大小调整
    NewBatchSize = case length(HistoryData) of
        N when N < 3 ->
            % 数据不足，使用保守策略
            adjust_batch_size_conservative(CurrentBatchSize, OverallScore, State);
        _ ->
            % 数据充足，使用智能优化算法
            optimize_batch_size_intelligent(CurrentBatchSize, OverallScore, HistoryData, State)
    end,

    % 更新状态
    NewBatchSizes = maps:put(OperationType, NewBatchSize, State#state.batch_sizes),
    NewOptimalSizes = maps:put(OperationType, NewBatchSize, State#state.optimal_sizes),

    % 记录优化历史
    OptimizationRecord = #{
        timestamp => erlang:system_time(millisecond),
        operation_type => OperationType,
        old_batch_size => CurrentBatchSize,
        new_batch_size => NewBatchSize,
        performance_score => OverallScore,
        latency => Latency,
        throughput => Throughput,
        success_rate => SuccessRate
    },

    NewHistory = [OptimizationRecord | lists:sublist(HistoryData, 99)], % 保留最近100条记录
    ets:insert(?BATCH_PERFORMANCE_TAB, {HistoryKey, NewHistory}),

    NewState = State#state{
        batch_sizes = NewBatchSizes,
        optimal_sizes = NewOptimalSizes,
        total_optimizations = State#state.total_optimizations + 1,
        successful_optimizations = State#state.successful_optimizations +
            if NewBatchSize =/= CurrentBatchSize -> 1; true -> 0 end,
        last_optimization_time = erlang:system_time(millisecond)
    },

    ?SLOG(info, #{
        msg => "batch_size_optimized",
        operation_type => OperationType,
        old_size => CurrentBatchSize,
        new_size => NewBatchSize,
        performance_score => OverallScore
    }),

    {NewBatchSize, NewState}.

%% @doc 保守的批处理大小调整策略
%% 当历史数据不足时使用
adjust_batch_size_conservative(CurrentSize, PerformanceScore, State) ->
    MinSize = State#state.min_batch_size,
    MaxSize = State#state.max_batch_size,

    if
        PerformanceScore > 0.8 ->
            % 性能很好，可以适度增加批处理大小
            min(round(CurrentSize * 1.2), MaxSize);
        PerformanceScore > 0.6 ->
            % 性能一般，保持当前大小
            CurrentSize;
        PerformanceScore > 0.4 ->
            % 性能较差，适度减少批处理大小
            max(round(CurrentSize * 0.8), MinSize);
        true ->
            % 性能很差，大幅减少批处理大小
            max(round(CurrentSize * 0.6), MinSize)
    end.

%% @doc 智能批处理大小优化算法 - 高级多策略实现
%% 基于历史数据使用多种优化策略和A/B测试
optimize_batch_size_intelligent(CurrentSize, CurrentScore, HistoryData, State) ->
    MinSize = State#state.min_batch_size,
    MaxSize = State#state.max_batch_size,

    try
        ?SLOG(debug, #{msg => "starting_intelligent_batch_optimization"}),

        % 1. 多策略优化
        Strategies = [
            {gradient_descent, apply_gradient_descent_strategy(CurrentSize, CurrentScore, HistoryData, State)},
            {genetic_algorithm, apply_genetic_algorithm_strategy(CurrentSize, HistoryData, State)},
            {reinforcement_learning, apply_reinforcement_learning_strategy(CurrentSize, CurrentScore, State)},
            {adaptive_hill_climbing, apply_adaptive_hill_climbing_strategy(CurrentSize, CurrentScore, HistoryData, State)},
            {bayesian_optimization, apply_bayesian_optimization_strategy(CurrentSize, HistoryData, State)}
        ],

        % 2. 策略评估和选择
        BestStrategy = select_best_optimization_strategy(Strategies, HistoryData),

        % 3. A/B测试决策
        ABTestResult = should_run_ab_test(CurrentSize, CurrentScore, State),

        % 4. 根据A/B测试结果决定最终策略
        FinalSize = case ABTestResult of
            {run_ab_test, TestSizes} ->
                % 运行A/B测试
                run_batch_size_ab_test(TestSizes, CurrentScore, State);
            no_ab_test ->
                % 使用最佳策略
                apply_selected_strategy(BestStrategy, CurrentSize, MinSize, MaxSize)
        end,

        % 5. 自适应参数调优
        OptimizedSize = apply_adaptive_parameter_tuning(FinalSize, CurrentScore, HistoryData, State),

        % 6. 安全性检查和边界约束
        SafeSize = apply_safety_constraints(OptimizedSize, CurrentSize, MinSize, MaxSize, CurrentScore),

        ?SLOG(debug, #{
            msg => "intelligent_batch_optimization_completed",
            current_size => CurrentSize,
            optimized_size => SafeSize,
            strategy => element(1, BestStrategy),
            ab_test => ABTestResult =/= no_ab_test
        }),

        SafeSize
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "intelligent_batch_optimization_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            % 失败时使用保守策略
            apply_conservative_optimization(CurrentSize, CurrentScore, MinSize, MaxSize)
    end.

%% ============================================================================
%% 高级优化策略实现
%% ============================================================================

%% @doc 梯度下降策略
%% 基于性能梯度调整批处理大小
apply_gradient_descent_strategy(CurrentSize, CurrentScore, HistoryData, State) ->
    try
        % 计算性能梯度
        Gradient = calculate_performance_gradient(HistoryData),

        % 学习率（自适应）
        LearningRate = calculate_adaptive_learning_rate(CurrentScore, State),

        % 梯度下降更新
        Delta = round(Gradient * LearningRate * CurrentSize),
        NewSize = CurrentSize + Delta,

        #{
            strategy => gradient_descent,
            size => NewSize,
            confidence => abs(Gradient) * 0.8,  % 梯度越大置信度越高
            gradient => Gradient,
            learning_rate => LearningRate
        }
    catch
        _:_ ->
            #{strategy => gradient_descent, size => CurrentSize, confidence => 0.0}
    end.

%% @doc 遗传算法策略
%% 使用遗传算法搜索最优批处理大小
apply_genetic_algorithm_strategy(CurrentSize, HistoryData, State) ->
    try
        MinSize = State#state.min_batch_size,
        MaxSize = State#state.max_batch_size,

        % 生成候选解（种群）
        Population = generate_batch_size_population(CurrentSize, MinSize, MaxSize, 8),

        % 评估每个候选解的适应度
        FitnessScores = [
            evaluate_batch_size_fitness(Size, HistoryData) || Size <- Population
        ],

        % 选择最佳候选解
        BestIndex = find_max_index(FitnessScores),
        BestSize = lists:nth(BestIndex, Population),
        BestFitness = lists:nth(BestIndex, FitnessScores),

        #{
            strategy => genetic_algorithm,
            size => BestSize,
            confidence => BestFitness,
            population_size => length(Population)
        }
    catch
        _:_ ->
            #{strategy => genetic_algorithm, size => CurrentSize, confidence => 0.0}
    end.

%% @doc 强化学习策略
%% 基于Q-learning调整批处理大小
apply_reinforcement_learning_strategy(CurrentSize, CurrentScore, State) ->
    try
        % 获取Q表（存储在状态中）
        QTable = maps:get(q_table, State#state.metrics, #{}),

        % 当前状态（离散化的性能分数）
        CurrentState = discretize_performance_score(CurrentScore),

        % 可能的动作（批处理大小调整）
        Actions = [-20, -10, -5, 0, 5, 10, 20],  % 批处理大小变化量

        % 选择动作（ε-贪心策略）
        Epsilon = 0.1,  % 探索率
        Action = case rand:uniform() < Epsilon of
            true ->
                % 探索：随机选择动作
                lists:nth(rand:uniform(length(Actions)), Actions);
            false ->
                % 利用：选择Q值最高的动作
                select_best_action(CurrentState, Actions, QTable)
        end,

        NewSize = CurrentSize + Action,

        #{
            strategy => reinforcement_learning,
            size => NewSize,
            confidence => 0.7,  % 强化学习的置信度
            action => Action,
            state => CurrentState
        }
    catch
        _:_ ->
            #{strategy => reinforcement_learning, size => CurrentSize, confidence => 0.0}
    end.

%% @doc 自适应爬山策略
%% 基于局部搜索的自适应优化
apply_adaptive_hill_climbing_strategy(CurrentSize, CurrentScore, HistoryData, _State) ->
    try
        % 计算搜索步长（自适应）
        StepSize = calculate_adaptive_step_size(CurrentScore, HistoryData),

        % 生成邻域解
        Neighbors = [
            CurrentSize - StepSize,
            CurrentSize - round(StepSize / 2),
            CurrentSize,
            CurrentSize + round(StepSize / 2),
            CurrentSize + StepSize
        ],

        % 评估邻域解
        NeighborScores = [
            predict_batch_size_performance(Size, HistoryData) || Size <- Neighbors
        ],

        % 选择最佳邻域解
        BestIndex = find_max_index(NeighborScores),
        BestSize = lists:nth(BestIndex, Neighbors),
        BestScore = lists:nth(BestIndex, NeighborScores),

        #{
            strategy => adaptive_hill_climbing,
            size => BestSize,
            confidence => BestScore,
            step_size => StepSize
        }
    catch
        _:_ ->
            #{strategy => adaptive_hill_climbing, size => CurrentSize, confidence => 0.0}
    end.

%% @doc 贝叶斯优化策略
%% 使用贝叶斯优化搜索最优批处理大小
apply_bayesian_optimization_strategy(CurrentSize, HistoryData, State) ->
    try
        MinSize = State#state.min_batch_size,
        MaxSize = State#state.max_batch_size,

        % 构建高斯过程模型
        GPModel = build_gaussian_process_model(HistoryData),

        % 计算采集函数（Expected Improvement）
        CandidateSizes = lists:seq(MinSize, MaxSize, max(1, (MaxSize - MinSize) div 20)),

        AcquisitionScores = [
            calculate_expected_improvement(Size, GPModel) || Size <- CandidateSizes
        ],

        % 选择采集函数值最高的点
        BestIndex = find_max_index(AcquisitionScores),
        BestSize = lists:nth(BestIndex, CandidateSizes),
        BestAcquisition = lists:nth(BestIndex, AcquisitionScores),

        #{
            strategy => bayesian_optimization,
            size => BestSize,
            confidence => min(1.0, BestAcquisition),
            model_quality => evaluate_gp_model_quality(GPModel)
        }
    catch
        _:_ ->
            #{strategy => bayesian_optimization, size => CurrentSize, confidence => 0.0}
    end.

%% ============================================================================
%% A/B测试和策略选择
%% ============================================================================

%% @doc 选择最佳优化策略
select_best_optimization_strategy(Strategies, HistoryData) ->
    try
        % 根据置信度和历史表现选择策略
        ScoredStrategies = [
            {Strategy, score_optimization_strategy(StrategyResult, HistoryData)}
            || {Strategy, StrategyResult} <- Strategies
        ],

        % 选择得分最高的策略
        {BestStrategy, _BestScore} = lists:foldl(
            fun({Strategy, Score}, {CurrentBest, CurrentScore}) ->
                case Score > CurrentScore of
                    true -> {Strategy, Score};
                    false -> {CurrentBest, CurrentScore}
                end
            end,
            {gradient_descent, 0.0},
            ScoredStrategies
        ),

        % 返回最佳策略的结果
        {BestStrategy, proplists:get_value(BestStrategy, Strategies)}
    catch
        _:_ ->
            {gradient_descent, #{strategy => gradient_descent, size => 100, confidence => 0.5}}
    end.

%% @doc 评估优化策略得分
score_optimization_strategy(StrategyResult, HistoryData) ->
    try
        Confidence = maps:get(confidence, StrategyResult, 0.0),
        Strategy = maps:get(strategy, StrategyResult, unknown),

        % 基于历史表现的策略权重
        HistoricalWeight = case Strategy of
            gradient_descent -> 0.8;      % 梯度下降通常稳定
            genetic_algorithm -> 0.7;     % 遗传算法适合全局搜索
            reinforcement_learning -> 0.6; % 强化学习需要时间学习
            adaptive_hill_climbing -> 0.75; % 爬山算法适合局部优化
            bayesian_optimization -> 0.85;  % 贝叶斯优化通常效果好
            _ -> 0.5
        end,

        % 数据质量权重
        DataQualityWeight = case length(HistoryData) of
            N when N >= 20 -> 1.0;
            N when N >= 10 -> 0.8;
            N when N >= 5 -> 0.6;
            _ -> 0.4
        end,

        % 综合得分
        Confidence * HistoricalWeight * DataQualityWeight
    catch
        _:_ -> 0.0
    end.

%% @doc 判断是否应该运行A/B测试
should_run_ab_test(CurrentSize, CurrentScore, State) ->
    try
        % A/B测试条件
        Conditions = [
            % 1. 当前性能不够好
            CurrentScore < 0.7,

            % 2. 最近没有运行过A/B测试
            not recently_ran_ab_test(State),

            % 3. 有足够的历史数据
            has_sufficient_data_for_ab_test(State),

            % 4. 系统负载不高
            not is_system_under_high_load()
        ],

        case lists:all(fun(C) -> C end, Conditions) of
            true ->
                % 生成A/B测试的候选批处理大小
                TestSizes = generate_ab_test_sizes(CurrentSize, State),
                {run_ab_test, TestSizes};
            false ->
                no_ab_test
        end
    catch
        _:_ -> no_ab_test
    end.

%% @doc 运行批处理大小A/B测试
run_batch_size_ab_test(TestSizes, CurrentScore, State) ->
    try
        % 为每个测试大小计算预期性能
        TestResults = [
            {Size, predict_ab_test_performance(Size, CurrentScore, State)}
            || Size <- TestSizes
        ],

        % 选择预期性能最好的大小
        {BestSize, _BestPerformance} = lists:foldl(
            fun({Size, Performance}, {CurrentBest, CurrentPerf}) ->
                case Performance > CurrentPerf of
                    true -> {Size, Performance};
                    false -> {CurrentBest, CurrentPerf}
                end
            end,
            {hd(TestSizes), 0.0},
            TestResults
        ),

        % 记录A/B测试结果
        record_ab_test_result(TestSizes, TestResults, State),

        BestSize
    catch
        _:_ ->
            % A/B测试失败，返回第一个测试大小
            hd(TestSizes)
    end.

%% @doc 应用选定的策略
apply_selected_strategy({_StrategyName, StrategyResult}, CurrentSize, MinSize, MaxSize) ->
    ProposedSize = maps:get(size, StrategyResult, CurrentSize),
    max(MinSize, min(MaxSize, ProposedSize)).

%% @doc 自适应参数调优
apply_adaptive_parameter_tuning(Size, CurrentScore, HistoryData, _State) ->
    try
        % 基于当前性能调整参数
        AdjustmentFactor = case CurrentScore of
            Score when Score > 0.8 -> 1.0;    % 性能很好，不调整
            Score when Score > 0.6 -> 0.95;   % 性能一般，轻微调整
            Score when Score > 0.4 -> 0.9;    % 性能较差，适度调整
            _ -> 0.85                          % 性能很差，较大调整
        end,

        % 基于历史数据的稳定性调整
        StabilityFactor = calculate_performance_stability(HistoryData),

        % 综合调整
        FinalAdjustment = AdjustmentFactor * StabilityFactor,

        round(Size * FinalAdjustment)
    catch
        _:_ -> Size
    end.

%% @doc 应用安全性约束
apply_safety_constraints(ProposedSize, CurrentSize, MinSize, MaxSize, CurrentScore) ->
    % 基本边界检查
    BoundedSize = max(MinSize, min(MaxSize, ProposedSize)),

    % 变化幅度限制（避免剧烈变化）
    MaxChange = case CurrentScore of
        Score when Score > 0.7 -> 0.3;  % 性能好时允许较大变化
        Score when Score > 0.5 -> 0.2;  % 性能一般时限制变化
        _ -> 0.1                         % 性能差时保守变化
    end,

    MaxIncrease = round(CurrentSize * (1 + MaxChange)),
    MaxDecrease = round(CurrentSize * (1 - MaxChange)),

    max(MaxDecrease, min(MaxIncrease, BoundedSize)).

%% @doc 保守优化策略（失败时使用）
apply_conservative_optimization(CurrentSize, CurrentScore, MinSize, MaxSize) ->
    case CurrentScore of
        Score when Score > 0.7 ->
            % 性能好，小幅增加
            min(round(CurrentSize * 1.05), MaxSize);
        Score when Score > 0.5 ->
            % 性能一般，保持不变
            CurrentSize;
        _ ->
            % 性能差，小幅减少
            max(round(CurrentSize * 0.95), MinSize)
    end.

%% ============================================================================
%% 数学计算和辅助函数
%% ============================================================================

%% @doc 计算性能梯度
calculate_performance_gradient(HistoryData) ->
    case length(HistoryData) of
        N when N < 2 -> 0.0;
        _ ->
            % 使用最近的数据点计算梯度
            RecentData = lists:sublist(HistoryData, 5),
            Scores = [maps:get(performance_score, Record, 0.0) || Record <- RecentData],

            % 简单的线性回归计算梯度
            N = length(Scores),
            X = lists:seq(1, N),
            Y = Scores,

            SumX = lists:sum(X),
            SumY = lists:sum(Y),
            SumXY = lists:sum(lists:zipwith(fun(Xi, Yi) -> Xi * Yi end, X, Y)),
            SumX2 = lists:sum([Xi * Xi || Xi <- X]),

            case N * SumX2 - SumX * SumX of
                0 -> 0.0;
                Denominator -> (N * SumXY - SumX * SumY) / Denominator
            end
    end.

%% @doc 计算自适应学习率
calculate_adaptive_learning_rate(CurrentScore, State) ->
    BaseRate = 0.1,

    % 根据当前性能调整学习率
    ScoreAdjustment = if
        CurrentScore > 0.8 -> 0.5;  % 性能好时降低学习率
        CurrentScore > 0.6 -> 0.8;
        CurrentScore > 0.4 -> 1.0;
        true -> 1.5                 % 性能差时提高学习率
    end,

    % 根据优化历史调整
    OptimizationHistory = State#state.total_optimizations,
    HistoryAdjustment = if
        OptimizationHistory > 100 -> 0.8;  % 经验丰富时降低学习率
        OptimizationHistory > 50 -> 0.9;
        OptimizationHistory > 20 -> 1.0;
        true -> 1.2                         % 经验不足时提高学习率
    end,

    BaseRate * ScoreAdjustment * HistoryAdjustment.

%% @doc 生成批处理大小种群（遗传算法）
generate_batch_size_population(CurrentSize, MinSize, MaxSize, PopulationSize) ->
    % 生成围绕当前大小的候选解
    Range = MaxSize - MinSize,
    StdDev = Range / 6,  % 标准差为范围的1/6

    Population = [CurrentSize | [  % 包含当前大小
        begin
            % 正态分布随机数生成
            Gaussian = generate_gaussian_random(),
            Candidate = round(CurrentSize + Gaussian * StdDev),
            max(MinSize, min(MaxSize, Candidate))
        end || _ <- lists:seq(1, PopulationSize - 1)
    ]],

    % 去重并确保种群大小
    UniquePopulation = lists:usort(Population),
    case length(UniquePopulation) < PopulationSize of
        true ->
            % 如果去重后数量不足，添加随机候选解
            Additional = [
                MinSize + rand:uniform(Range) ||
                _ <- lists:seq(1, PopulationSize - length(UniquePopulation))
            ],
            UniquePopulation ++ Additional;
        false ->
            lists:sublist(UniquePopulation, PopulationSize)
    end.

%% @doc 评估批处理大小适应度
evaluate_batch_size_fitness(Size, HistoryData) ->
    try
        % 基于历史数据预测该大小的性能
        PredictedPerformance = predict_batch_size_performance(Size, HistoryData),

        % 考虑大小的合理性（避免极端值）
        SizeReasonableness = case Size of
            S when S < 10 -> 0.5;      % 太小
            S when S > 1000 -> 0.7;    % 太大
            S when S >= 50, S =< 200 -> 1.0;  % 理想范围
            _ -> 0.8                    % 一般
        end,

        PredictedPerformance * SizeReasonableness
    catch
        _:_ -> 0.0
    end.

%% @doc 预测批处理大小性能
predict_batch_size_performance(Size, HistoryData) ->
    case length(HistoryData) of
        0 -> 0.5;  % 无历史数据，返回中等性能
        _ ->
            % 找到历史数据中最相似的批处理大小
            SimilarRecords = lists:filter(
                fun(Record) ->
                    HistoricalSize = maps:get(batch_size, Record, 100),
                    abs(HistoricalSize - Size) =< 20  % 相似度阈值
                end,
                HistoryData
            ),

            case SimilarRecords of
                [] ->
                    % 没有相似记录，使用插值估计
                    interpolate_performance(Size, HistoryData);
                _ ->
                    % 有相似记录，计算平均性能
                    Scores = [maps:get(performance_score, R, 0.0) || R <- SimilarRecords],
                    lists:sum(Scores) / length(Scores)
            end
    end.

%% @doc 性能插值估计
interpolate_performance(TargetSize, HistoryData) ->
    try
        % 按批处理大小排序
        SortedData = lists:sort(
            fun(A, B) ->
                maps:get(batch_size, A, 100) =< maps:get(batch_size, B, 100)
            end,
            HistoryData
        ),

        % 找到目标大小的上下界
        {Lower, Upper} = find_interpolation_bounds(TargetSize, SortedData),

        case {Lower, Upper} of
            {undefined, undefined} -> 0.5;
            {undefined, UpperRecord} -> maps:get(performance_score, UpperRecord, 0.5);
            {LowerRecord, undefined} -> maps:get(performance_score, LowerRecord, 0.5);
            {LowerRecord, UpperRecord} ->
                % 线性插值
                LowerSize = maps:get(batch_size, LowerRecord, 100),
                UpperSize = maps:get(batch_size, UpperRecord, 100),
                LowerScore = maps:get(performance_score, LowerRecord, 0.5),
                UpperScore = maps:get(performance_score, UpperRecord, 0.5),

                case UpperSize - LowerSize of
                    0 -> LowerScore;
                    Diff ->
                        Weight = (TargetSize - LowerSize) / Diff,
                        LowerScore + Weight * (UpperScore - LowerScore)
                end
        end
    catch
        _:_ -> 0.5
    end.

%% @doc 找到插值边界
find_interpolation_bounds(TargetSize, SortedData) ->
    find_bounds_helper(TargetSize, SortedData, undefined, undefined).

find_bounds_helper(_TargetSize, [], Lower, Upper) ->
    {Lower, Upper};
find_bounds_helper(TargetSize, [Record | Rest], Lower, Upper) ->
    RecordSize = maps:get(batch_size, Record, 100),
    if
        RecordSize =< TargetSize ->
            find_bounds_helper(TargetSize, Rest, Record, Upper);
        RecordSize > TargetSize andalso Upper =:= undefined ->
            find_bounds_helper(TargetSize, Rest, Lower, Record);
        true ->
            find_bounds_helper(TargetSize, Rest, Lower, Upper)
    end.

%% @doc 生成高斯随机数（Box-Muller变换）
generate_gaussian_random() ->
    U1 = rand:uniform(),
    U2 = rand:uniform(),
    math:sqrt(-2 * math:log(U1)) * math:cos(2 * math:pi() * U2).

%% @doc 找到最大值索引
find_max_index(List) ->
    find_max_index_helper(List, 1, 1, hd(List)).

find_max_index_helper([], _CurrentIndex, MaxIndex, _MaxValue) ->
    MaxIndex;
find_max_index_helper([H | T], CurrentIndex, MaxIndex, MaxValue) ->
    case H > MaxValue of
        true ->
            find_max_index_helper(T, CurrentIndex + 1, CurrentIndex, H);
        false ->
            find_max_index_helper(T, CurrentIndex + 1, MaxIndex, MaxValue)
    end.

%% ============================================================================
%% 强化学习和高级算法辅助函数
%% ============================================================================

%% @doc 离散化性能分数
discretize_performance_score(Score) ->
    if
        Score >= 0.9 -> excellent;
        Score >= 0.7 -> good;
        Score >= 0.5 -> fair;
        Score >= 0.3 -> poor;
        true -> critical
    end.

%% @doc 选择最佳动作（Q-learning）
select_best_action(State, Actions, QTable) ->
    QValues = [maps:get({State, Action}, QTable, 0.0) || Action <- Actions],
    MaxQValue = lists:max(QValues),

    % 找到所有最大Q值对应的动作
    BestActions = [
        Action || {Action, QValue} <- lists:zip(Actions, QValues), QValue =:= MaxQValue
    ],

    % 随机选择一个最佳动作
    lists:nth(rand:uniform(length(BestActions)), BestActions).

%% @doc 计算自适应步长
calculate_adaptive_step_size(CurrentScore, HistoryData) ->
    BaseStepSize = 10,

    % 根据性能调整步长
    ScoreAdjustment = if
        CurrentScore > 0.8 -> 0.5;  % 性能好时小步长
        CurrentScore > 0.6 -> 0.8;
        CurrentScore > 0.4 -> 1.0;
        true -> 1.5                 % 性能差时大步长
    end,

    % 根据历史数据的变异性调整
    VariabilityAdjustment = case calculate_performance_variability(HistoryData) of
        Var when Var > 0.3 -> 0.7;  % 高变异性时小步长
        Var when Var > 0.2 -> 0.9;
        Var when Var > 0.1 -> 1.0;
        _ -> 1.2                     % 低变异性时大步长
    end,

    round(BaseStepSize * ScoreAdjustment * VariabilityAdjustment).

%% @doc 计算性能变异性
calculate_performance_variability(HistoryData) ->
    case length(HistoryData) of
        N when N < 2 -> 0.0;
        _ ->
            Scores = [maps:get(performance_score, Record, 0.0) || Record <- HistoryData],
            Mean = lists:sum(Scores) / length(Scores),
            Variance = lists:sum([(Score - Mean) * (Score - Mean) || Score <- Scores]) / length(Scores),
            math:sqrt(Variance)
    end.

%% @doc 构建高斯过程模型（简化版）
build_gaussian_process_model(HistoryData) ->
    try
        % 提取批处理大小和性能数据
        DataPoints = [
            {maps:get(batch_size, Record, 100), maps:get(performance_score, Record, 0.0)}
            || Record <- HistoryData
        ],

        case length(DataPoints) of
            N when N < 3 ->
                #{type => insufficient_data, mean => 0.5, variance => 0.1};
            _ ->
                % 计算简单的统计模型
                Sizes = [Size || {Size, _} <- DataPoints],
                Scores = [Score || {_, Score} <- DataPoints],

                MeanSize = lists:sum(Sizes) / length(Sizes),
                MeanScore = lists:sum(Scores) / length(Scores),

                % 计算协方差
                Covariance = calculate_covariance(Sizes, Scores, MeanSize, MeanScore),

                #{
                    type => gaussian_process,
                    mean_size => MeanSize,
                    mean_score => MeanScore,
                    covariance => Covariance,
                    data_points => DataPoints
                }
        end
    catch
        _:_ ->
            #{type => model_error, mean => 0.5, variance => 0.1}
    end.

%% @doc 计算协方差
calculate_covariance(Sizes, Scores, MeanSize, MeanScore) ->
    N = length(Sizes),
    SumProduct = lists:sum([
        (Size - MeanSize) * (Score - MeanScore) || {Size, Score} <- lists:zip(Sizes, Scores)
    ]),
    SumProduct / N.

%% @doc 计算期望改进（Expected Improvement）
calculate_expected_improvement(Size, GPModel) ->
    case maps:get(type, GPModel) of
        gaussian_process ->
            % 简化的期望改进计算
            MeanSize = maps:get(mean_size, GPModel),
            MeanScore = maps:get(mean_score, GPModel),
            Covariance = maps:get(covariance, GPModel),

            % 预测均值和方差
            PredictedMean = MeanScore + Covariance * (Size - MeanSize) / 100,
            PredictedVariance = max(0.01, abs(Covariance) * 0.1),

            % 当前最佳值
            DataPoints = maps:get(data_points, GPModel),
            BestScore = lists:max([Score || {_, Score} <- DataPoints]),

            % 期望改进
            Improvement = max(0, PredictedMean - BestScore),
            Uncertainty = math:sqrt(PredictedVariance),

            Improvement + 0.1 * Uncertainty;  % 平衡利用和探索
        _ ->
            0.1  % 默认期望改进
    end.

%% @doc 评估高斯过程模型质量
evaluate_gp_model_quality(GPModel) ->
    case maps:get(type, GPModel) of
        gaussian_process ->
            DataPoints = maps:get(data_points, GPModel, []),
            case length(DataPoints) of
                N when N >= 10 -> 0.8;
                N when N >= 5 -> 0.6;
                N when N >= 3 -> 0.4;
                _ -> 0.2
            end;
        _ -> 0.1
    end.

%% ============================================================================
%% A/B测试辅助函数
%% ============================================================================

%% @doc 检查最近是否运行过A/B测试
recently_ran_ab_test(State) ->
    LastABTest = maps:get(last_ab_test_time, State#state.metrics, 0),
    CurrentTime = erlang:system_time(millisecond),
    CurrentTime - LastABTest < 300000.  % 5分钟内

%% @doc 检查是否有足够数据进行A/B测试
has_sufficient_data_for_ab_test(State) ->
    length(State#state.performance_history) >= 10.

%% @doc 检查系统是否处于高负载
is_system_under_high_load() ->
    try
        % 简单的负载检查
        ProcessCount = erlang:system_info(process_count),
        ProcessLimit = erlang:system_info(process_limit),

        LoadRatio = ProcessCount / ProcessLimit,
        LoadRatio > 0.8
    catch
        _:_ -> false
    end.

%% @doc 生成A/B测试的候选大小
generate_ab_test_sizes(CurrentSize, State) ->
    MinSize = State#state.min_batch_size,
    MaxSize = State#state.max_batch_size,

    % 生成3个测试候选
    [
        max(MinSize, round(CurrentSize * 0.8)),  % 减少20%
        CurrentSize,                             % 当前大小
        min(MaxSize, round(CurrentSize * 1.2))   % 增加20%
    ].

%% @doc 预测A/B测试性能
predict_ab_test_performance(Size, CurrentScore, _State) ->
    % 简化的性能预测
    SizeScore = if
        Size >= 50, Size =< 200 -> 1.0;  % 理想范围
        Size >= 20, Size =< 500 -> 0.8;  % 可接受范围
        true -> 0.6                      % 其他范围
    end,

    % 结合当前性能和大小评分
    (CurrentScore + SizeScore) / 2.

%% @doc 记录A/B测试结果
record_ab_test_result(TestSizes, TestResults, _State) ->
    try
        % 记录到ETS表或日志
        ?SLOG(info, #{
            msg => "ab_test_completed",
            test_sizes => TestSizes,
            test_results => TestResults
        })
    catch
        _:_ -> ok
    end.

%% @doc 计算性能稳定性
calculate_performance_stability(HistoryData) ->
    case length(HistoryData) of
        N when N < 3 -> 0.5;
        _ ->
            Scores = [maps:get(performance_score, Record, 0.0) || Record <- HistoryData],
            Mean = lists:sum(Scores) / length(Scores),
            Variance = lists:sum([(Score - Mean) * (Score - Mean) || Score <- Scores]) / length(Scores),
            StdDev = math:sqrt(Variance),

            % 稳定性 = 1 - 标准差（标准差越小越稳定）
            max(0.1, 1.0 - StdDev)
    end.

%% @doc 计算延迟评分
%% 延迟越低评分越高
calculate_latency_score(Latency) when Latency =< 10 -> 1.0;
calculate_latency_score(Latency) when Latency =< 50 -> 0.9;
calculate_latency_score(Latency) when Latency =< 100 -> 0.8;
calculate_latency_score(Latency) when Latency =< 200 -> 0.6;
calculate_latency_score(Latency) when Latency =< 500 -> 0.4;
calculate_latency_score(Latency) when Latency =< 1000 -> 0.2;
calculate_latency_score(_) -> 0.1.

%% @doc 计算吞吐量评分
%% 根据操作类型的不同有不同的期望值
calculate_throughput_score(Throughput, OperationType) ->
    ExpectedThroughput = case OperationType of
        insert -> 1000;  % 期望每秒1000次插入
        update -> 800;   % 期望每秒800次更新
        delete -> 600;   % 期望每秒600次删除
        query -> 2000;  % 期望每秒2000次查询
        _ -> 1000
    end,

    Ratio = Throughput / ExpectedThroughput,
    if
        Ratio >= 1.0 -> 1.0;
        Ratio >= 0.8 -> 0.9;
        Ratio >= 0.6 -> 0.8;
        Ratio >= 0.4 -> 0.6;
        Ratio >= 0.2 -> 0.4;
        true -> 0.2
    end.

%% @doc 计算性能趋势
calculate_performance_trend(HistoryData) when length(HistoryData) < 3 ->
    stable;
calculate_performance_trend(HistoryData) ->
    % 提取最近的性能评分
    Scores = [maps:get(performance_score, Record, 0.5) || Record <- HistoryData],

    % 计算趋势（简单线性回归）
    N = length(Scores),
    X = lists:seq(1, N),
    Y = Scores,

    % 计算斜率
    SumX = lists:sum(X),
    SumY = lists:sum(Y),
    SumXY = lists:sum([Xi * Yi || {Xi, Yi} <- lists:zip(X, Y)]),
    SumX2 = lists:sum([Xi * Xi || Xi <- X]),

    Slope = (N * SumXY - SumX * SumY) / (N * SumX2 - SumX * SumX),

    if
        Slope > 0.02 -> improving;
        Slope < -0.02 -> declining;
        true -> stable
    end.

%% @doc 找到历史最佳批处理大小
find_best_historical_batch_size(HistoryData) ->
    case HistoryData of
        [] -> 100; % 默认值
        _ ->
            % 找到性能评分最高的记录
            BestRecord = lists:foldl(
                fun(Record, Best) ->
                    CurrentScore = maps:get(performance_score, Record, 0),
                    BestScore = maps:get(performance_score, Best, 0),
                    if CurrentScore > BestScore -> Record; true -> Best end
                end,
                hd(HistoryData),
                HistoryData
            ),
            maps:get(new_batch_size, BestRecord, 100)
    end.

%% @doc 收集性能指标
do_collect_performance_metrics(State) ->
    CurrentTime = erlang:system_time(millisecond),

    % 收集系统级性能指标
    SystemMetrics = collect_system_metrics(),

    % 收集MongoDB操作性能指标
    MongoMetrics = collect_mongodb_metrics(),

    % 收集批处理性能指标
    BatchMetrics = collect_batch_metrics(State),

    % 合并所有指标
    AllMetrics = maps:merge(maps:merge(SystemMetrics, MongoMetrics), BatchMetrics),

    % 存储到ETS表
    ets:insert(?PERFORMANCE_METRICS_TAB, {current_metrics, AllMetrics}),
    ets:insert(?PERFORMANCE_METRICS_TAB, {last_collection_time, CurrentTime}),

    AllMetrics.

%% @doc 收集系统级性能指标
collect_system_metrics() ->
    try
        % 获取内存使用情况
        MemoryInfo = erlang:memory(),
        TotalMemory = proplists:get_value(total, MemoryInfo, 0),
        ProcessMemory = proplists:get_value(processes, MemoryInfo, 0),

        % 获取进程数量
        ProcessCount = erlang:system_info(process_count),
        ProcessLimit = erlang:system_info(process_limit),

        % 获取调度器信息
        SchedulerCount = erlang:system_info(schedulers),

        #{
            memory_total => TotalMemory,
            memory_processes => ProcessMemory,
            memory_usage_ratio => ProcessMemory / TotalMemory,
            process_count => ProcessCount,
            process_limit => ProcessLimit,
            process_usage_ratio => ProcessCount / ProcessLimit,
            scheduler_count => SchedulerCount,
            timestamp => erlang:system_time(millisecond)
        }
    catch
        E:R:S ->
            ?SLOG(warning, #{
                msg => "failed_to_collect_system_metrics",
                error => E,
                reason => R,
                stacktrace => S
            }),
            #{error => {system_metrics_collection_failed, R}}
    end.

%% @doc 收集MongoDB操作性能指标
collect_mongodb_metrics() ->
    try
        % 从ETS表中获取MongoDB操作统计
        InsertStats = get_operation_stats(insert),
        UpdateStats = get_operation_stats(update),
        DeleteStats = get_operation_stats(delete),
        QueryStats = get_operation_stats(query),

        #{
            insert_metrics => InsertStats,
            update_metrics => UpdateStats,
            delete_metrics => DeleteStats,
            query_metrics => QueryStats,
            total_operations => maps:get(count, InsertStats, 0) +
                              maps:get(count, UpdateStats, 0) +
                              maps:get(count, DeleteStats, 0) +
                              maps:get(count, QueryStats, 0),
            timestamp => erlang:system_time(millisecond)
        }
    catch
        E:R:S ->
            ?SLOG(warning, #{
                msg => "failed_to_collect_mongodb_metrics",
                error => E,
                reason => R,
                stacktrace => S
            }),
            #{error => {mongodb_metrics_collection_failed, R}}
    end.

%% @doc 收集批处理性能指标
collect_batch_metrics(State) ->
    try
        #{
            current_batch_sizes => State#state.batch_sizes,
            optimal_batch_sizes => State#state.optimal_sizes,
            total_optimizations => State#state.total_optimizations,
            successful_optimizations => State#state.successful_optimizations,
            optimization_success_rate => case State#state.total_optimizations of
                0 -> 0.0;
                Total -> State#state.successful_optimizations / Total
            end,
            last_optimization_time => State#state.last_optimization_time,
            optimization_enabled => State#state.optimization_enabled,
            timestamp => erlang:system_time(millisecond)
        }
    catch
        E:R:S ->
            ?SLOG(warning, #{
                msg => "failed_to_collect_batch_metrics",
                error => E,
                reason => R,
                stacktrace => S
            }),
            #{error => {batch_metrics_collection_failed, R}}
    end.

%% @doc 获取操作统计信息
get_operation_stats(OperationType) ->
    StatsKey = {operation_stats, OperationType},
    case ets:lookup(?PERFORMANCE_METRICS_TAB, StatsKey) of
        [{_, Stats}] -> Stats;
        [] -> #{
            count => 0,
            total_latency => 0,
            average_latency => 0,
            max_latency => 0,
            min_latency => 0,
            success_count => 0,
            error_count => 0,
            success_rate => 0.0
        }
    end.

%% @doc 生成性能报告
generate_performance_report(State) ->
    CurrentTime = erlang:system_time(millisecond),

    % 获取当前性能指标
    CurrentMetrics = case ets:lookup(?PERFORMANCE_METRICS_TAB, current_metrics) of
        [{_, Metrics}] -> Metrics;
        [] -> #{}
    end,

    % 生成批处理优化报告
    BatchReport = generate_batch_optimization_report(State),

    % 生成性能趋势报告
    TrendReport = generate_performance_trend_report(),

    % 生成资源使用报告
    ResourceReport = generate_resource_usage_report(CurrentMetrics),

    % 生成优化建议
    Suggestions = generate_optimization_suggestions(State),

    #{
        report_timestamp => CurrentTime,
        report_type => comprehensive_performance_report,
        batch_optimization => BatchReport,
        performance_trends => TrendReport,
        resource_usage => ResourceReport,
        optimization_suggestions => Suggestions,
        summary => #{
            total_optimizations => State#state.total_optimizations,
            successful_optimizations => State#state.successful_optimizations,
            optimization_success_rate => case State#state.total_optimizations of
                0 -> 0.0;
                Total -> State#state.successful_optimizations / Total
            end,
            current_batch_sizes => State#state.batch_sizes,
            optimal_batch_sizes => State#state.optimal_sizes
        }
    }.

%% @doc 生成批处理优化报告
generate_batch_optimization_report(State) ->
    OperationTypes = [insert, update, delete, query],

    Reports = lists:map(
        fun(OpType) ->
            CurrentSize = maps:get(OpType, State#state.batch_sizes, 100),
            OptimalSize = maps:get(OpType, State#state.optimal_sizes, 100),

            % 获取历史性能数据
            HistoryKey = {OpType, batch_performance},
            HistoryData = case ets:lookup(?BATCH_PERFORMANCE_TAB, HistoryKey) of
                [{_, History}] -> lists:sublist(History, 10); % 最近10条记录
                [] -> []
            end,

            % 计算性能统计
            PerformanceStats = case HistoryData of
                [] -> #{};
                _ ->
                    Scores = [maps:get(performance_score, Record, 0.5) || Record <- HistoryData],
                    #{
                        average_performance => lists:sum(Scores) / length(Scores),
                        max_performance => lists:max(Scores),
                        min_performance => lists:min(Scores),
                        performance_variance => calculate_variance(Scores)
                    }
            end,

            {OpType, #{
                current_batch_size => CurrentSize,
                optimal_batch_size => OptimalSize,
                optimization_needed => CurrentSize =/= OptimalSize,
                recent_history => HistoryData,
                performance_statistics => PerformanceStats
            }}
        end,
        OperationTypes
    ),

    maps:from_list(Reports).

%% @doc 生成性能趋势报告
generate_performance_trend_report() ->
    try
        % 获取最近的性能数据
        RecentMetrics = get_recent_performance_data(24), % 最近24小时

        case RecentMetrics of
            [] ->
                #{trend => no_data, message => "insufficient_data_for_trend_analysis"};
            _ ->
                % 分析吞吐量趋势
                ThroughputTrend = analyze_metric_trend(RecentMetrics, throughput),

                % 分析延迟趋势
                LatencyTrend = analyze_metric_trend(RecentMetrics, latency),

                % 分析成功率趋势
                SuccessRateTrend = analyze_metric_trend(RecentMetrics, success_rate),

                #{
                    throughput_trend => ThroughputTrend,
                    latency_trend => LatencyTrend,
                    success_rate_trend => SuccessRateTrend,
                    data_points => length(RecentMetrics),
                    analysis_period => "24_hours"
                }
        end
    catch
        E:R:S ->
            ?SLOG(warning, #{
                msg => "performance_trend_analysis_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            #{error => {trend_analysis_failed, R}}
    end.

%% @doc 生成资源使用报告
generate_resource_usage_report(CurrentMetrics) ->
    try
        MemoryUsage = maps:get(memory_usage_ratio, CurrentMetrics, 0),
        ProcessUsage = maps:get(process_usage_ratio, CurrentMetrics, 0),

        % 评估资源使用状况
        MemoryStatus = evaluate_resource_status(MemoryUsage),
        ProcessStatus = evaluate_resource_status(ProcessUsage),

        #{
            memory => #{
                usage_ratio => MemoryUsage,
                status => MemoryStatus,
                total_memory => maps:get(memory_total, CurrentMetrics, 0),
                process_memory => maps:get(memory_processes, CurrentMetrics, 0)
            },
            processes => #{
                usage_ratio => ProcessUsage,
                status => ProcessStatus,
                process_count => maps:get(process_count, CurrentMetrics, 0),
                process_limit => maps:get(process_limit, CurrentMetrics, 0)
            },
            schedulers => #{
                count => maps:get(scheduler_count, CurrentMetrics, 0)
            },
            overall_status => determine_overall_resource_status(MemoryStatus, ProcessStatus)
        }
    catch
        E:R:S ->
            ?SLOG(warning, #{
                msg => "resource_usage_report_generation_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            #{error => {resource_report_failed, R}}
    end.

%% @doc 生成优化建议
generate_optimization_suggestions(State) ->
    try
        _Suggestions = [],

        % 检查批处理大小优化建议
        BatchSuggestions = generate_batch_size_suggestions(State),

        % 检查性能优化建议
        PerformanceSuggestions = generate_performance_suggestions(State),

        % 检查资源优化建议
        ResourceSuggestions = generate_resource_suggestions(),

        AllSuggestions = BatchSuggestions ++ PerformanceSuggestions ++ ResourceSuggestions,

        #{
            total_suggestions => length(AllSuggestions),
            suggestions => AllSuggestions,
            priority_suggestions => filter_high_priority_suggestions(AllSuggestions)
        }
    catch
        E:R:S ->
            ?SLOG(warning, #{
                msg => "optimization_suggestions_generation_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            #{error => {suggestions_generation_failed, R}}
    end.

%% @doc 分析吞吐量数据
analyze_throughput_data(TimeRange, _State) ->
    try
        % 获取指定时间范围的吞吐量数据
        ThroughputData = get_throughput_data_in_range(TimeRange),

        case ThroughputData of
            [] ->
                #{analysis => no_data, message => "no_throughput_data_in_specified_range"};
            _ ->
                % 计算统计指标
                Values = [Data || {_Time, Data} <- ThroughputData],

                #{
                    data_points => length(Values),
                    average_throughput => lists:sum(Values) / length(Values),
                    max_throughput => lists:max(Values),
                    min_throughput => lists:min(Values),
                    throughput_variance => calculate_variance(Values),
                    trend => calculate_throughput_trend(ThroughputData),
                    time_range => TimeRange
                }
        end
    catch
        E:R:S ->
            ?SLOG(warning, #{
                msg => "throughput_analysis_failed",
                time_range => TimeRange,
                error => E,
                reason => R,
                stacktrace => S
            }),
            #{error => {throughput_analysis_failed, R}}
    end.

%% @doc 分析延迟数据
analyze_latency_data(TimeRange, _State) ->
    try
        % 获取指定时间范围的延迟数据
        LatencyData = get_latency_data_in_range(TimeRange),

        case LatencyData of
            [] ->
                #{analysis => no_data, message => "no_latency_data_in_specified_range"};
            _ ->
                % 计算统计指标
                Values = [Data || {_Time, Data} <- LatencyData],

                % 计算百分位数
                SortedValues = lists:sort(Values),
                P50 = percentile(SortedValues, 50),
                P95 = percentile(SortedValues, 95),
                P99 = percentile(SortedValues, 99),

                #{
                    data_points => length(Values),
                    average_latency => lists:sum(Values) / length(Values),
                    max_latency => lists:max(Values),
                    min_latency => lists:min(Values),
                    p50_latency => P50,
                    p95_latency => P95,
                    p99_latency => P99,
                    latency_variance => calculate_variance(Values),
                    trend => calculate_latency_trend(LatencyData),
                    time_range => TimeRange
                }
        end
    catch
        E:R:S ->
            ?SLOG(warning, #{
                msg => "latency_analysis_failed",
                time_range => TimeRange,
                error => E,
                reason => R,
                stacktrace => S
            }),
            #{error => {latency_analysis_failed, R}}
    end.

%% @doc 应用优化策略
apply_optimization_strategy(OptimizationStrategy, State) ->
    try
        #{
            strategy_type := StrategyType,
            parameters := Parameters
        } = OptimizationStrategy,

        case StrategyType of
            adjust_batch_sizes ->
                apply_batch_size_adjustments(Parameters, State);
            tune_performance_thresholds ->
                apply_performance_threshold_tuning(Parameters, State);
            optimize_resource_usage ->
                apply_resource_optimization(Parameters, State);
            _ ->
                ?SLOG(warning, #{
                    msg => "unknown_optimization_strategy",
                    strategy_type => StrategyType
                }),
                State
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "optimization_strategy_application_failed",
                strategy => OptimizationStrategy,
                error => E,
                reason => R,
                stacktrace => S
            }),
            State
    end.

%% ============================================================================
%% 辅助函数实现
%% ============================================================================

%% @doc 计算方差
calculate_variance([]) -> 0;
calculate_variance([_]) -> 0;
calculate_variance(Values) ->
    Mean = lists:sum(Values) / length(Values),
    SumSquaredDiffs = lists:sum([(X - Mean) * (X - Mean) || X <- Values]),
    SumSquaredDiffs / length(Values).

%% @doc 计算百分位数
percentile([], _) -> 0;
percentile(SortedValues, Percentile) ->
    N = length(SortedValues),
    Index = round(N * Percentile / 100),
    AdjustedIndex = max(1, min(Index, N)),
    lists:nth(AdjustedIndex, SortedValues).

%% @doc 获取指定时间范围的吞吐量数据
get_throughput_data_in_range(TimeRange) ->
    % 这里应该从ETS表或其他存储中获取数据
    % 暂时返回模拟数据
    CurrentTime = erlang:system_time(millisecond),
    StartTime = CurrentTime - TimeRange * 1000,

    % 模拟数据生成
    generate_mock_throughput_data(StartTime, CurrentTime, []).

%% @doc 获取指定时间范围的延迟数据
get_latency_data_in_range(TimeRange) ->
    % 这里应该从ETS表或其他存储中获取数据
    % 暂时返回模拟数据
    CurrentTime = erlang:system_time(millisecond),
    StartTime = CurrentTime - TimeRange * 1000,

    % 模拟数据生成
    generate_mock_latency_data(StartTime, CurrentTime, []).

%% @doc 生成模拟吞吐量数据
generate_mock_throughput_data(StartTime, EndTime, Acc) when StartTime >= EndTime ->
    lists:reverse(Acc);
generate_mock_throughput_data(StartTime, EndTime, Acc) ->
    % 生成随机吞吐量数据
    Throughput = 800 + rand:uniform(400), % 800-1200 范围
    NewAcc = [{StartTime, Throughput} | Acc],
    generate_mock_throughput_data(StartTime + 60000, EndTime, NewAcc). % 每分钟一个数据点

%% @doc 生成模拟延迟数据
generate_mock_latency_data(StartTime, EndTime, Acc) when StartTime >= EndTime ->
    lists:reverse(Acc);
generate_mock_latency_data(StartTime, EndTime, Acc) ->
    % 生成随机延迟数据
    Latency = 50 + rand:uniform(100), % 50-150ms 范围
    NewAcc = [{StartTime, Latency} | Acc],
    generate_mock_latency_data(StartTime + 60000, EndTime, NewAcc). % 每分钟一个数据点

%% @doc 计算吞吐量趋势
calculate_throughput_trend(ThroughputData) when length(ThroughputData) < 3 ->
    stable;
calculate_throughput_trend(ThroughputData) ->
    Values = [Value || {_Time, Value} <- ThroughputData],
    calculate_trend_from_values(Values).

%% @doc 计算延迟趋势
calculate_latency_trend(LatencyData) when length(LatencyData) < 3 ->
    stable;
calculate_latency_trend(LatencyData) ->
    Values = [Value || {_Time, Value} <- LatencyData],
    % 对于延迟，趋势相反（延迟增加是坏事）
    case calculate_trend_from_values(Values) of
        improving -> declining;
        declining -> improving;
        stable -> stable
    end.

%% @doc 从数值列表计算趋势
calculate_trend_from_values(Values) ->
    N = length(Values),
    X = lists:seq(1, N),
    Y = Values,

    % 简单线性回归计算斜率
    SumX = lists:sum(X),
    SumY = lists:sum(Y),
    SumXY = lists:sum([Xi * Yi || {Xi, Yi} <- lists:zip(X, Y)]),
    SumX2 = lists:sum([Xi * Xi || Xi <- X]),

    Slope = (N * SumXY - SumX * SumY) / (N * SumX2 - SumX * SumX),

    if
        Slope > 5 -> improving;
        Slope < -5 -> declining;
        true -> stable
    end.

%% @doc 过滤高优先级建议
filter_high_priority_suggestions(Suggestions) ->
    lists:filter(
        fun(Suggestion) ->
            Priority = maps:get(priority, Suggestion, medium),
            Priority =:= high orelse Priority =:= critical
        end,
        Suggestions
    ).

%% @doc 生成批处理大小建议
generate_batch_size_suggestions(State) ->
    OperationTypes = [insert, update, delete, query],
    lists:foldl(
        fun(OpType, Acc) ->
            CurrentSize = maps:get(OpType, State#state.batch_sizes, ?DEFAULT_BATCH_SIZE),
            OptimalSize = maps:get(OpType, State#state.optimal_sizes, ?DEFAULT_BATCH_SIZE),

            if
                abs(CurrentSize - OptimalSize) > (?DEFAULT_BATCH_SIZE * 0.2) ->
                    Suggestion = #{
                        type => batch_size_adjustment,
                        operation_type => OpType,
                        current_size => CurrentSize,
                        suggested_size => OptimalSize,
                        priority => high,
                        description => io_lib:format("调整~p操作的批处理大小从~p到~p", [OpType, CurrentSize, OptimalSize])
                    },
                    [Suggestion | Acc];
                true ->
                    Acc
            end
        end,
        [],
        OperationTypes
    ).

%% @doc 生成性能建议
generate_performance_suggestions(_State) ->
    % 基于性能指标生成建议
    [
        #{
            type => performance_monitoring,
            priority => medium,
            description => "建议启用详细的性能监控以获得更准确的优化建议"
        }
    ].

%% @doc 生成资源建议
generate_resource_suggestions() ->
    % 基于资源使用情况生成建议
    [
        #{
            type => resource_optimization,
            priority => low,
            description => "建议定期监控系统资源使用情况"
        }
    ].

%% @doc 应用批处理大小调整
apply_batch_size_adjustments(Parameters, State) ->
    NewBatchSizes = maps:merge(State#state.batch_sizes, Parameters),
    State#state{batch_sizes = NewBatchSizes}.

%% @doc 应用性能阈值调优
apply_performance_threshold_tuning(_Parameters, State) ->
    % 这里可以调整性能阈值
    State.

%% @doc 应用资源优化
apply_resource_optimization(_Parameters, State) ->
    % 这里可以应用资源优化策略
    State.

%% @doc 更新批处理性能数据
do_update_batch_performance(OperationType, BatchSize, PerformanceData, State) ->
    % 更新性能历史记录
    HistoryKey = {OperationType, batch_performance},

    % 创建性能记录
    PerformanceRecord = #{
        timestamp => erlang:system_time(millisecond),
        batch_size => BatchSize,
        performance_data => PerformanceData
    },

    % 获取现有历史记录
    ExistingHistory = case ets:lookup(?BATCH_PERFORMANCE_TAB, HistoryKey) of
        [{_, History}] -> History;
        [] -> []
    end,

    % 添加新记录并限制历史记录数量
    NewHistory = [PerformanceRecord | lists:sublist(ExistingHistory, ?PERFORMANCE_HISTORY_SIZE - 1)],

    % 更新ETS表
    ets:insert(?BATCH_PERFORMANCE_TAB, {HistoryKey, NewHistory}),

    State.

%% @doc 执行定期性能分析
do_periodic_performance_analysis(State) ->
    try
        % 收集当前性能指标
        CurrentMetrics = do_collect_performance_metrics(State),

        % 分析性能趋势
        _TrendAnalysis = analyze_performance_trends(CurrentMetrics),

        % 生成优化建议
        _Suggestions = generate_optimization_suggestions(State),

        ?SLOG(debug, #{msg => "periodic_performance_analysis_completed"}),

        State#state{metrics = CurrentMetrics}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "periodic_performance_analysis_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            State
    end.

%% @doc 分析性能趋势
analyze_performance_trends(_CurrentMetrics) ->
    % 这里可以实现更复杂的趋势分析
    #{trend => stable}.

%% @doc 获取最近的性能数据
get_recent_performance_data(Hours) ->
    CurrentTime = erlang:system_time(millisecond),
    StartTime = CurrentTime - (Hours * 3600 * 1000),

    % 从ETS表中获取数据
    AllKeys = ets:tab2list(?PERFORMANCE_METRICS_TAB),

    % 过滤时间范围内的数据
    lists:filter(
        fun({_Key, Data}) ->
            case maps:get(timestamp, Data, 0) of
                Timestamp when Timestamp >= StartTime -> true;
                _ -> false
            end
        end,
        AllKeys
    ).

%% @doc 分析指标趋势
analyze_metric_trend(MetricsData, MetricType) ->
    Values = lists:filtermap(
        fun({_Key, Data}) ->
            case maps:get(MetricType, Data, undefined) of
                undefined -> false;
                Value -> {true, Value}
            end
        end,
        MetricsData
    ),

    case Values of
        [] -> #{trend => no_data};
        _ -> #{
            trend => calculate_trend_from_values(Values),
            average => lists:sum(Values) / length(Values),
            count => length(Values)
        }
    end.

%% @doc 评估资源状态
evaluate_resource_status(UsageRatio) when UsageRatio < 0.5 -> good;
evaluate_resource_status(UsageRatio) when UsageRatio < 0.7 -> normal;
evaluate_resource_status(UsageRatio) when UsageRatio < 0.9 -> warning;
evaluate_resource_status(_) -> critical.

%% @doc 确定整体资源状态
determine_overall_resource_status(MemoryStatus, ProcessStatus) ->
    case {MemoryStatus, ProcessStatus} of
        {critical, _} -> critical;
        {_, critical} -> critical;
        {warning, _} -> warning;
        {_, warning} -> warning;
        {normal, normal} -> normal;
        _ -> good
    end.

%% ============================================================================
%% 高级批处理优化功能
%% ============================================================================

%% @doc 动态批处理超时优化
%% 基于批处理大小、系统负载和历史性能数据动态调整超时时间
-spec optimize_batch_timeout_dynamic(integer(), float(), list()) -> integer().
optimize_batch_timeout_dynamic(BatchSize, CurrentScore, HistoryData) ->
    try
        % 基础超时时间（毫秒）
        BaseTimeout = ?DEFAULT_BATCH_TIMEOUT,

        % 1. 基于批处理大小的调整
        SizeAdjustment = calculate_timeout_size_adjustment(BatchSize),

        % 2. 基于性能评分的调整
        PerformanceAdjustment = calculate_timeout_performance_adjustment(CurrentScore),

        % 3. 基于历史数据的趋势调整
        TrendAdjustment = calculate_timeout_trend_adjustment(HistoryData),

        % 4. 系统负载感知调整
        LoadAdjustment = calculate_timeout_load_adjustment(),

        % 5. 计算最终超时时间
        OptimizedTimeout = round(BaseTimeout * SizeAdjustment * PerformanceAdjustment *
                                TrendAdjustment * LoadAdjustment),

        % 6. 边界检查
        FinalTimeout = max(?MIN_BATCH_TIMEOUT, min(?MAX_BATCH_TIMEOUT, OptimizedTimeout)),

        ?SLOG(debug, #{
            msg => "batch_timeout_optimized",
            base_timeout => BaseTimeout,
            size_adjustment => SizeAdjustment,
            performance_adjustment => PerformanceAdjustment,
            trend_adjustment => TrendAdjustment,
            load_adjustment => LoadAdjustment,
            final_timeout => FinalTimeout
        }),

        FinalTimeout
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "batch_timeout_optimization_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            ?DEFAULT_BATCH_TIMEOUT
    end.

%% @doc 计算基于批处理大小的超时调整因子
calculate_timeout_size_adjustment(BatchSize) ->
    % 批处理大小越大，需要更长的超时时间
    % 使用对数函数避免线性增长过快
    BaseFactor = 1.0,
    SizeFactor = math:log(BatchSize / ?DEFAULT_BATCH_SIZE + 1) * 0.3,
    max(0.5, min(2.0, BaseFactor + SizeFactor)).

%% @doc 计算基于性能评分的超时调整因子
calculate_timeout_performance_adjustment(CurrentScore) ->
    % 性能越差，给予更长的超时时间
    if
        CurrentScore > 0.8 -> 0.8;  % 性能很好，缩短超时
        CurrentScore > 0.6 -> 1.0;  % 性能一般，保持默认
        CurrentScore > 0.4 -> 1.3;  % 性能较差，延长超时
        true -> 1.6                 % 性能很差，大幅延长超时
    end.

%% @doc 计算基于历史趋势的超时调整因子
calculate_timeout_trend_adjustment(HistoryData) ->
    try
        case length(HistoryData) of
            N when N < 3 ->
                1.0; % 数据不足，使用默认值
            _ ->
                % 分析最近的性能趋势
                RecentData = lists:sublist(HistoryData, 5),
                AvgLatency = calculate_average_latency(RecentData),

                % 基于平均延迟调整超时
                if
                    AvgLatency < 50 -> 0.9;   % 延迟很低，可以缩短超时
                    AvgLatency < 100 -> 1.0;  % 延迟正常，保持默认
                    AvgLatency < 200 -> 1.2;  % 延迟较高，延长超时
                    true -> 1.5               % 延迟很高，大幅延长超时
                end
        end
    catch
        _:_ -> 1.0
    end.

%% @doc 计算基于系统负载的超时调整因子
calculate_timeout_load_adjustment() ->
    try
        % 获取系统CPU使用率
        CpuUsage = get_system_cpu_usage(),

        % 获取内存使用率
        MemoryUsage = get_system_memory_usage(),

        % 综合负载评估
        LoadScore = (CpuUsage + MemoryUsage) / 2,

        % 基于负载调整超时
        if
            LoadScore < 0.5 -> 0.9;   % 负载低，缩短超时
            LoadScore < 0.7 -> 1.0;   % 负载正常，保持默认
            LoadScore < 0.85 -> 1.3;  % 负载较高，延长超时
            true -> 1.6               % 负载很高，大幅延长超时
        end
    catch
        _:_ -> 1.0 % 获取失败时使用默认值
    end.

%% @doc 多维度性能预测
%% 基于批处理大小、超时时间和历史数据预测性能表现
-spec predict_batch_performance(integer(), integer(), list()) -> map().
predict_batch_performance(BatchSize, Timeout, HistoryData) ->
    try
        % 1. 吞吐量预测
        PredictedThroughput = predict_throughput(BatchSize, Timeout, HistoryData),

        % 2. 延迟预测
        PredictedLatency = predict_latency(BatchSize, Timeout, HistoryData),

        % 3. 成功率预测
        PredictedSuccessRate = predict_success_rate(BatchSize, Timeout, HistoryData),

        % 4. 资源使用预测
        PredictedResourceUsage = predict_resource_usage(BatchSize, Timeout),

        % 5. 综合性能评分预测
        PredictedScore = calculate_predicted_performance_score(
            PredictedThroughput, PredictedLatency, PredictedSuccessRate, PredictedResourceUsage),

        % 6. 置信度计算
        Confidence = calculate_prediction_confidence(HistoryData),

        #{
            throughput => PredictedThroughput,
            latency => PredictedLatency,
            success_rate => PredictedSuccessRate,
            resource_usage => PredictedResourceUsage,
            overall_score => PredictedScore,
            confidence => Confidence,
            prediction_timestamp => erlang:system_time(millisecond)
        }
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "batch_performance_prediction_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            #{
                throughput => 0,
                latency => 0,
                success_rate => 0.0,
                resource_usage => 0.0,
                overall_score => 0.0,
                confidence => 0.0,
                prediction_timestamp => erlang:system_time(millisecond)
            }
    end.

%% @doc 预测吞吐量
predict_throughput(BatchSize, Timeout, HistoryData) ->
    try
        % 基于历史数据的线性回归预测
        case extract_throughput_data(HistoryData) of
            [] ->
                % 没有历史数据，使用经验公式
                estimate_throughput_by_formula(BatchSize, Timeout);
            ThroughputData ->
                % 使用历史数据进行预测
                predict_throughput_by_regression(BatchSize, Timeout, ThroughputData)
        end
    catch
        _:_ -> 0
    end.

%% @doc 预测延迟
predict_latency(BatchSize, Timeout, HistoryData) ->
    try
        % 基于批处理大小和历史数据预测延迟
        BaseLatency = estimate_base_latency(BatchSize),

        % 历史延迟趋势调整
        HistoryAdjustment = case extract_latency_data(HistoryData) of
            [] -> 1.0;
            LatencyData -> calculate_latency_trend_factor(LatencyData)
        end,

        % 超时时间影响
        TimeoutAdjustment = min(1.0, Timeout / ?DEFAULT_BATCH_TIMEOUT),

        round(BaseLatency * HistoryAdjustment * TimeoutAdjustment)
    catch
        _:_ -> ?DEFAULT_BATCH_TIMEOUT
    end.

%% @doc 预测成功率
predict_success_rate(BatchSize, Timeout, HistoryData) ->
    try
        % 基础成功率（基于批处理大小）
        BaseSuccessRate = calculate_base_success_rate(BatchSize),

        % 历史成功率调整
        HistoryAdjustment = case extract_success_rate_data(HistoryData) of
            [] -> 1.0;
            SuccessRateData -> calculate_success_rate_trend(SuccessRateData)
        end,

        % 超时时间对成功率的影响
        TimeoutBonus = if
            Timeout > ?DEFAULT_BATCH_TIMEOUT -> 0.05; % 更长超时提高成功率
            true -> 0.0
        end,

        min(1.0, BaseSuccessRate * HistoryAdjustment + TimeoutBonus)
    catch
        _:_ -> 0.95 % 默认95%成功率
    end.

%% @doc 预测资源使用
predict_resource_usage(BatchSize, Timeout) ->
    try
        % 基于批处理大小预测内存使用
        MemoryUsage = estimate_memory_usage(BatchSize),

        % 基于超时时间预测CPU使用
        CpuUsage = estimate_cpu_usage(BatchSize, Timeout),

        % 综合资源使用评分
        (MemoryUsage + CpuUsage) / 2
    catch
        _:_ -> 0.5 % 默认50%资源使用
    end.

%% @doc 更新批处理配置
%% 将优化后的参数应用到实际的批处理配置中
-spec update_batch_configuration(integer(), integer(), map()) -> ok.
update_batch_configuration(OptimizedSize, OptimizedTimeout, PredictedPerformance) ->
    try
        % 1. 更新自适应批处理模块的配置
        update_adaptive_batch_config(OptimizedSize, OptimizedTimeout),

        % 2. 更新性能预测缓存
        update_performance_prediction_cache(PredictedPerformance),

        % 3. 通知相关模块配置变更
        notify_batch_config_change(OptimizedSize, OptimizedTimeout),

        % 4. 记录配置更新日志
        ?SLOG(info, #{
            msg => "batch_configuration_updated",
            optimized_size => OptimizedSize,
            optimized_timeout => OptimizedTimeout,
            predicted_performance => PredictedPerformance
        }),

        ok
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "batch_configuration_update_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            error
    end.

%% ============================================================================
%% 支持函数实现
%% ============================================================================

%% @doc 计算平均延迟
calculate_average_latency(HistoryData) ->
    try
        LatencyList = [maps:get(latency, Data, 0) || Data <- HistoryData],
        case LatencyList of
            [] -> 100; % 默认延迟
            _ -> lists:sum(LatencyList) / length(LatencyList)
        end
    catch
        _:_ -> 100
    end.

%% @doc 获取系统CPU使用率
get_system_cpu_usage() ->
    try
        % 使用recon获取CPU使用率
        case recon:node_stats(1, 1000) of
            [{_, Stats}] ->
                proplists:get_value(scheduler_usage, Stats, 0.5);
            _ ->
                0.5 % 默认50%
        end
    catch
        _:_ -> 0.5
    end.

%% @doc 获取系统内存使用率
get_system_memory_usage() ->
    try
        % 获取内存信息
        MemInfo = erlang:memory(),
        Total = proplists:get_value(total, MemInfo, 1),
        Used = proplists:get_value(processes_used, MemInfo, 0),
        Used / Total
    catch
        _:_ -> 0.5
    end.

%% @doc 提取吞吐量数据
extract_throughput_data(HistoryData) ->
    [maps:get(throughput, Data, 0) || Data <- HistoryData, maps:is_key(throughput, Data)].

%% @doc 提取延迟数据
extract_latency_data(HistoryData) ->
    [maps:get(latency, Data, 0) || Data <- HistoryData, maps:is_key(latency, Data)].

%% @doc 提取成功率数据
extract_success_rate_data(HistoryData) ->
    [maps:get(success_rate, Data, 0.95) || Data <- HistoryData, maps:is_key(success_rate, Data)].

%% @doc 基于公式估算吞吐量
estimate_throughput_by_formula(BatchSize, Timeout) ->
    % 简化的吞吐量估算公式
    % 吞吐量 = 批处理大小 / (超时时间/1000) * 效率因子
    EfficiencyFactor = 0.8, % 80%效率
    round(BatchSize / (Timeout / 1000) * EfficiencyFactor).

%% @doc 基于回归预测吞吐量
predict_throughput_by_regression(BatchSize, Timeout, ThroughputData) ->
    try
        % 简化的线性回归预测
        AvgThroughput = lists:sum(ThroughputData) / length(ThroughputData),

        % 基于批处理大小调整
        SizeAdjustment = BatchSize / ?DEFAULT_BATCH_SIZE,

        % 基于超时时间调整
        TimeoutAdjustment = ?DEFAULT_BATCH_TIMEOUT / Timeout,

        round(AvgThroughput * SizeAdjustment * TimeoutAdjustment)
    catch
        _:_ -> estimate_throughput_by_formula(BatchSize, Timeout)
    end.

%% @doc 估算基础延迟
estimate_base_latency(BatchSize) ->
    % 基于批处理大小的延迟估算
    % 使用对数函数模拟延迟增长
    BaseLatency = 50, % 基础延迟50ms
    SizeImpact = math:log(BatchSize / ?DEFAULT_BATCH_SIZE + 1) * 30,
    round(BaseLatency + SizeImpact).

%% @doc 计算延迟趋势因子
calculate_latency_trend_factor(LatencyData) ->
    try
        case length(LatencyData) of
            N when N < 2 -> 1.0;
            _ ->
                % 计算最近趋势
                Recent = lists:sublist(LatencyData, 3),
                Older = lists:sublist(lists:nthtail(min(3, length(LatencyData) - 3), LatencyData), 3),

                RecentAvg = lists:sum(Recent) / length(Recent),
                OlderAvg = case Older of
                    [] -> RecentAvg;
                    _ -> lists:sum(Older) / length(Older)
                end,

                % 趋势因子
                if
                    OlderAvg == 0 -> 1.0;
                    true -> RecentAvg / OlderAvg
                end
        end
    catch
        _:_ -> 1.0
    end.

%% @doc 计算基础成功率
calculate_base_success_rate(BatchSize) ->
    % 批处理大小越大，成功率可能略有下降
    if
        BatchSize =< ?DEFAULT_BATCH_SIZE -> 0.98;
        BatchSize =< ?DEFAULT_BATCH_SIZE * 2 -> 0.96;
        BatchSize =< ?DEFAULT_BATCH_SIZE * 3 -> 0.94;
        true -> 0.92
    end.

%% @doc 计算成功率趋势
calculate_success_rate_trend(SuccessRateData) ->
    try
        case SuccessRateData of
            [] -> 1.0;
            _ ->
                AvgSuccessRate = lists:sum(SuccessRateData) / length(SuccessRateData),
                % 基于平均成功率调整
                if
                    AvgSuccessRate > 0.95 -> 1.05; % 历史成功率高，提升预期
                    AvgSuccessRate > 0.90 -> 1.0;  % 历史成功率正常
                    true -> 0.95                   % 历史成功率低，降低预期
                end
        end
    catch
        _:_ -> 1.0
    end.

%% @doc 估算内存使用
estimate_memory_usage(BatchSize) ->
    % 基于批处理大小估算内存使用
    % 假设每个消息平均1KB
    MessageSize = 1024, % 1KB
    EstimatedMemory = BatchSize * MessageSize,

    % 转换为使用率（假设系统有1GB可用内存）
    SystemMemory = 1024 * 1024 * 1024, % 1GB
    min(1.0, EstimatedMemory / SystemMemory).

%% @doc 估算CPU使用
estimate_cpu_usage(BatchSize, Timeout) ->
    % 基于批处理大小和超时时间估算CPU使用
    % 批处理大小越大，CPU使用越高
    SizeImpact = min(0.8, BatchSize / ?MAX_BATCH_SIZE),

    % 超时时间越短，CPU压力越大
    TimeoutImpact = min(0.3, ?MAX_BATCH_TIMEOUT / Timeout * 0.1),

    min(1.0, SizeImpact + TimeoutImpact).

%% @doc 计算预测性能评分
calculate_predicted_performance_score(Throughput, Latency, SuccessRate, ResourceUsage) ->
    try
        % 标准化各项指标
        ThroughputScore = min(1.0, Throughput / 10000), % 假设10000为满分吞吐量
        LatencyScore = max(0.0, 1.0 - Latency / 1000),  % 延迟越低分数越高
        ResourceScore = max(0.0, 1.0 - ResourceUsage),  % 资源使用越低分数越高

        % 加权计算综合评分
        % 吞吐量40%，延迟30%，成功率20%，资源使用10%
        ThroughputScore * 0.4 + LatencyScore * 0.3 + SuccessRate * 0.2 + ResourceScore * 0.1
    catch
        _:_ -> 0.5
    end.

%% @doc 计算预测置信度
calculate_prediction_confidence(HistoryData) ->
    try
        DataCount = length(HistoryData),
        if
            DataCount >= 10 -> 0.9;  % 数据充足，高置信度
            DataCount >= 5 -> 0.7;   % 数据一般，中等置信度
            DataCount >= 2 -> 0.5;   % 数据较少，低置信度
            true -> 0.3              % 数据不足，很低置信度
        end
    catch
        _:_ -> 0.3
    end.

%% @doc 更新自适应批处理配置
update_adaptive_batch_config(OptimizedSize, OptimizedTimeout) ->
    try
        % 调用自适应批处理模块的配置更新接口
        case whereis(emqx_plugin_mongodb_adaptive_batch) of
            Pid when is_pid(Pid) ->
                gen_server:cast(Pid, {update_config, OptimizedSize, OptimizedTimeout});
            _ ->
                ?SLOG(warning, #{msg => "adaptive_batch_process_not_found"})
        end
    catch
        E:R ->
            ?SLOG(error, #{
                msg => "update_adaptive_batch_config_failed",
                error => E,
                reason => R
            })
    end.

%% @doc 更新性能预测缓存
update_performance_prediction_cache(PredictedPerformance) ->
    try
        % 将预测结果存储到ETS表中
        ets:insert(?PERFORMANCE_METRICS_TAB, {
            performance_prediction,
            PredictedPerformance,
            erlang:system_time(millisecond)
        })
    catch
        E:R ->
            ?SLOG(error, #{
                msg => "update_performance_prediction_cache_failed",
                error => E,
                reason => R
            })
    end.

%% @doc 通知批处理配置变更
notify_batch_config_change(OptimizedSize, OptimizedTimeout) ->
    try
        % 通知协调器模块配置变更
        case whereis(emqx_plugin_mongodb_coordinator) of
            Pid when is_pid(Pid) ->
                gen_server:cast(Pid, {batch_config_changed, OptimizedSize, OptimizedTimeout});
            _ ->
                ?SLOG(warning, #{msg => "coordinator_process_not_found"})
        end,

        % 通知其他相关模块
        notify_related_modules(OptimizedSize, OptimizedTimeout)
    catch
        E:R ->
            ?SLOG(error, #{
                msg => "notify_batch_config_change_failed",
                error => E,
                reason => R
            })
    end.

%% @doc 通知相关模块
notify_related_modules(OptimizedSize, OptimizedTimeout) ->
    % 通知管道模块
    case whereis(emqx_plugin_mongodb_pipeline) of
        PipelinePid when is_pid(PipelinePid) ->
            gen_server:cast(PipelinePid, {batch_config_updated, OptimizedSize, OptimizedTimeout});
        _ ->
            ok
    end,

    % 通知资源管理模块
    case whereis(emqx_plugin_mongodb_resource_manager) of
        ResourcePid when is_pid(ResourcePid) ->
            gen_server:cast(ResourcePid, {batch_optimization_completed, OptimizedSize, OptimizedTimeout});
        _ ->
            ok
    end.
