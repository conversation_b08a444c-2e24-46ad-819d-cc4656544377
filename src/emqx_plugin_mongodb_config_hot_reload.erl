%%%-------------------------------------------------------------------
%%% @doc
%%% MongoDB插件配置热重载模块
%%% 实现零停机时间的配置更新功能
%%% @end
%%%-------------------------------------------------------------------

-module(emqx_plugin_mongodb_config_hot_reload).

-include("emqx_plugin_mongodb.hrl").
-include_lib("kernel/include/file.hrl").

-behaviour(gen_server).

%% ============================================================================
%% 配置热重载API - 零停机配置更新接口
%% 这些函数提供配置热重载的核心功能
%% 类似于Java中的Spring Boot Actuator或JMX配置管理
%% ============================================================================
-export([
    start_link/0,                   % 启动配置热重载服务
                                   % 功能：启动文件监控和配置管理服务
                                   % Java等价：@PostConstruct public void startConfigWatcher()

    stop/0,                        % 停止配置热重载服务
                                   % 功能：停止文件监控，清理资源
                                   % Java等价：@PreDestroy public void stopConfigWatcher()

    reload_config/0,               % 手动重载配置
                                   % 功能：立即重新加载配置文件
                                   % Java等价：@RefreshScope public void refreshConfig()

    reload_config/1,               % 使用指定配置重载
                                   % 功能：应用指定的配置内容
                                   % Java等价：public void applyConfig(Config config)

    validate_config/1,             % 验证配置有效性
                                   % 功能：验证配置格式和内容的正确性
                                   % Java等价：@Valid public boolean validateConfig(Config config)

    rollback_config/0,             % 回滚到上一个配置
                                   % 功能：恢复到最后一个有效的配置版本
                                   % Java等价：public void rollbackToPreviousConfig()

    get_config_history/0,          % 获取配置历史
                                   % 功能：获取配置变更的历史记录
                                   % Java等价：public List<ConfigVersion> getConfigHistory()

    watch_config_file/0,           % 开始监控配置文件
                                   % 功能：启动文件系统监控，自动检测配置变更
                                   % Java等价：public void startFileWatcher()

    unwatch_config_file/0,         % 停止监控配置文件
                                   % 功能：停止文件系统监控
                                   % Java等价：public void stopFileWatcher()

    get_current_config/0,          % 获取当前配置
                                   % 功能：获取当前生效的配置内容
                                   % Java等价：public Config getCurrentConfig()

    backup_config/1,               % 备份配置
                                   % 功能：创建配置的备份版本
                                   % Java等价：public void backupConfig(String version)

    list_config_backups/0,         % 列出配置备份
                                   % 功能：获取所有可用的配置备份列表
                                   % Java等价：public List<String> listConfigBackups()

    integrate/0                    % 集成到协调器
                                   % 功能：将配置热重载服务集成到协调器系统中
                                   % Java等价：public void integrate()
]).

%% gen_server callbacks
-export([
    init/1,
    handle_call/3,
    handle_cast/2,
    handle_info/2,
    terminate/2,
    code_change/3
]).

%% 内部函数导出（用于spawn调用）
-export([
    file_watcher_loop/1,           % 文件监控循环
    config_validator_worker/2      % 配置验证工作进程
]).

%% ============================================================================
%% 宏定义 - 配置参数和常量
%% ============================================================================

%% 配置文件监控间隔（毫秒）
-define(FILE_WATCH_INTERVAL, 1000).           % 1秒检查一次
-define(CONFIG_VALIDATION_TIMEOUT, 5000).     % 5秒验证超时
-define(CONFIG_APPLY_TIMEOUT, 10000).         % 10秒应用超时

%% 配置历史保留
-define(MAX_CONFIG_HISTORY, 10).              % 最多保留10个历史版本
-define(CONFIG_BACKUP_DIR, "config_backups"). % 备份目录

%% ETS表名定义
-define(CONFIG_HISTORY_TAB, emqx_mongodb_config_history).
-define(CONFIG_WATCHER_TAB, emqx_mongodb_config_watcher).

%% ============================================================================
%% 记录定义 - 数据结构
%% ============================================================================

%% 配置版本记录
%% 用于跟踪配置的版本信息和变更历史
-record(config_version, {
    version,                       % 版本号（时间戳）
    config,                        % 配置内容
    timestamp,                     % 创建时间
    checksum,                      % 配置校验和
    status,                        % 状态：active, backup, failed
    description                    % 变更描述
}).

%% 文件监控状态记录
%% 用于跟踪配置文件的监控状态
-record(file_watcher_state, {
    file_path,                     % 监控的文件路径
    last_modified,                 % 最后修改时间
    last_size,                     % 最后文件大小
    checksum,                      % 文件校验和
    watcher_pid                    % 监控进程PID
}).

%% 热重载器状态记录
%% gen_server的状态数据结构
-record(hot_reload_state, {
    config_file,                   % 配置文件路径
    current_config,                % 当前配置
    config_history = [],           % 配置历史列表
    file_watcher_state,            % 文件监控状态
    validation_enabled = true,     % 是否启用配置验证
    auto_reload_enabled = true,    % 是否启用自动重载
    backup_enabled = true          % 是否启用配置备份
}).

%% ============================================================================
%% 公共API函数实现
%% ============================================================================

%% @doc 启动配置热重载服务
%% 启动gen_server进程，初始化配置监控系统
-spec start_link() -> {ok, pid()} | {error, term()}.
start_link() ->
    gen_server:start_link({local, ?MODULE}, ?MODULE, [], []).

%% @doc 停止配置热重载服务
%% 停止监控进程，清理资源
-spec stop() -> ok.
stop() ->
    gen_server:call(?MODULE, stop).

%% @doc 手动重载配置
%% 立即重新加载配置文件
-spec reload_config() -> {ok, map()} | {error, term()}.
reload_config() ->
    gen_server:call(?MODULE, reload_config, ?CONFIG_APPLY_TIMEOUT).

%% @doc 使用指定配置重载
%% 应用指定的配置内容
-spec reload_config(map()) -> {ok, map()} | {error, term()}.
reload_config(NewConfig) ->
    gen_server:call(?MODULE, {reload_config, NewConfig}, ?CONFIG_APPLY_TIMEOUT).

%% @doc 验证配置有效性
%% 验证配置格式和内容的正确性
-spec validate_config(map()) -> {ok, map()} | {error, term()}.
validate_config(Config) ->
    gen_server:call(?MODULE, {validate_config, Config}, ?CONFIG_VALIDATION_TIMEOUT).

%% @doc 回滚到上一个配置
%% 恢复到最后一个有效的配置版本
-spec rollback_config() -> {ok, map()} | {error, term()}.
rollback_config() ->
    gen_server:call(?MODULE, rollback_config, ?CONFIG_APPLY_TIMEOUT).

%% @doc 获取配置历史
%% 获取配置变更的历史记录
-spec get_config_history() -> {ok, [map()]} | {error, term()}.
get_config_history() ->
    gen_server:call(?MODULE, get_config_history).

%% @doc 开始监控配置文件
%% 启动文件系统监控，自动检测配置变更
-spec watch_config_file() -> ok | {error, term()}.
watch_config_file() ->
    gen_server:call(?MODULE, watch_config_file).

%% @doc 停止监控配置文件
%% 停止文件系统监控
-spec unwatch_config_file() -> ok.
unwatch_config_file() ->
    gen_server:call(?MODULE, unwatch_config_file).

%% @doc 获取当前配置
%% 获取当前生效的配置内容
-spec get_current_config() -> {ok, map()} | {error, term()}.
get_current_config() ->
    gen_server:call(?MODULE, get_current_config).

%% @doc 备份配置
%% 创建配置的备份版本
-spec backup_config(string()) -> ok | {error, term()}.
backup_config(Description) ->
    gen_server:call(?MODULE, {backup_config, Description}).

%% @doc 列出配置备份
%% 获取所有可用的配置备份列表
-spec list_config_backups() -> {ok, [map()]} | {error, term()}.
list_config_backups() ->
    gen_server:call(?MODULE, list_config_backups).

%% ============================================================================
%% gen_server回调函数实现
%% ============================================================================

%% @doc 初始化回调
init([]) ->
    try
        % 创建ETS表
        create_ets_tables(),

        % 获取配置文件路径
        ConfigFile = emqx_plugin_mongodb:mongodb_config_file(),

        % 读取当前配置
        CurrentConfig = emqx_plugin_mongodb:read_config(),

        % 初始化状态
        State = #hot_reload_state{
            config_file = ConfigFile,
            current_config = CurrentConfig,
            config_history = [],
            file_watcher_state = undefined
        },

        % 创建配置备份目录
        create_backup_directory(),

        % 备份当前配置作为初始版本
        backup_current_config(State),

        % 启动文件监控
        {ok, NewState} = start_file_watcher(State),

        ?SLOG(info, #{msg => "config_hot_reload_service_started"}),
        {ok, NewState}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "failed_to_start_config_hot_reload_service",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {stop, {init_failed, R}}
    end.

%% @doc 处理同步调用
handle_call(stop, _From, State) ->
    % 停止文件监控
    stop_file_watcher(State),
    {stop, normal, ok, State};

handle_call(reload_config, _From, State) ->
    % 手动重载配置
    case reload_config_internal(State) of
        {ok, NewState} ->
            {reply, {ok, NewState#hot_reload_state.current_config}, NewState};
        {error, Reason} ->
            {reply, {error, Reason}, State}
    end;

handle_call({reload_config, NewConfig}, _From, State) ->
    % 使用指定配置重载
    case apply_new_config(NewConfig, State) of
        {ok, NewState} ->
            {reply, {ok, NewConfig}, NewState};
        {error, Reason} ->
            {reply, {error, Reason}, State}
    end;

handle_call({validate_config, Config}, _From, State) ->
    % 验证配置
    case validate_config_internal(Config) of
        {ok, ValidatedConfig} ->
            {reply, {ok, ValidatedConfig}, State};
        {error, Reason} ->
            {reply, {error, Reason}, State}
    end;

handle_call(rollback_config, _From, State) ->
    % 回滚配置
    case rollback_config_internal(State) of
        {ok, NewState} ->
            {reply, {ok, NewState#hot_reload_state.current_config}, NewState};
        {error, Reason} ->
            {reply, {error, Reason}, State}
    end;

handle_call(get_config_history, _From, State) ->
    % 获取配置历史
    History = format_config_history(State#hot_reload_state.config_history),
    {reply, {ok, History}, State};

handle_call(watch_config_file, _From, State) ->
    % 开始监控配置文件
    case start_file_watcher(State) of
        {ok, NewState} ->
            {reply, ok, NewState};
        {error, Reason} ->
            {reply, {error, Reason}, State}
    end;

handle_call(unwatch_config_file, _From, State) ->
    % 停止监控配置文件
    NewState = stop_file_watcher(State),
    {reply, ok, NewState};

handle_call(get_current_config, _From, State) ->
    % 获取当前配置
    {reply, {ok, State#hot_reload_state.current_config}, State};

handle_call({backup_config, Description}, _From, State) ->
    % 备份配置
    case backup_config_with_description(Description, State) of
        ok ->
            {reply, ok, State};
        {error, Reason} ->
            {reply, {error, Reason}, State}
    end;

handle_call(list_config_backups, _From, State) ->
    % 列出配置备份
    case list_backups_internal() of
        {ok, Backups} ->
            {reply, {ok, Backups}, State};
        {error, Reason} ->
            {reply, {error, Reason}, State}
    end;

handle_call(_Request, _From, State) ->
    {reply, {error, unknown_request}, State}.

%% @doc 处理异步消息
handle_cast(_Msg, State) ->
    {noreply, State}.

%% @doc 处理系统消息
handle_info({file_changed, FilePath}, State) ->
    % 配置文件发生变更
    ?SLOG(info, #{msg => "config_file_changed", file => FilePath}),
    case State#hot_reload_state.auto_reload_enabled of
        true ->
            % 自动重载配置
            case reload_config_internal(State) of
                {ok, NewState} ->
                    ?SLOG(info, #{msg => "config_auto_reloaded_successfully"}),
                    {noreply, NewState};
                {error, Reason} ->
                    ?SLOG(error, #{
                        msg => "config_auto_reload_failed",
                        reason => Reason
                    }),
                    {noreply, State}
            end;
        false ->
            ?SLOG(info, #{msg => "config_file_changed_but_auto_reload_disabled"}),
            {noreply, State}
    end;

handle_info(_Info, State) ->
    {noreply, State}.

%% @doc 终止回调
terminate(_Reason, State) ->
    % 停止文件监控
    stop_file_watcher(State),
    % 清理ETS表
    cleanup_ets_tables(),
    ok.

%% @doc 代码变更回调
code_change(_OldVsn, State, _Extra) ->
    {ok, State}.

%% ============================================================================
%% 内部函数实现 - 配置热重载核心逻辑
%% ============================================================================

%% @doc 创建ETS表
%% 创建配置历史和文件监控相关的ETS表
-spec create_ets_tables() -> ok.
create_ets_tables() ->
    % 配置历史表
    case ets:info(?CONFIG_HISTORY_TAB) of
        undefined ->
            ets:new(?CONFIG_HISTORY_TAB, [
                named_table, public, ordered_set,
                {write_concurrency, true},
                {read_concurrency, true}
            ]);
        _ -> ok
    end,

    % 文件监控表
    case ets:info(?CONFIG_WATCHER_TAB) of
        undefined ->
            ets:new(?CONFIG_WATCHER_TAB, [
                named_table, public, set,
                {write_concurrency, true},
                {read_concurrency, true}
            ]);
        _ -> ok
    end,
    ok.

%% @doc 清理ETS表
%% 删除配置热重载相关的ETS表
-spec cleanup_ets_tables() -> ok.
cleanup_ets_tables() ->
    Tables = [?CONFIG_HISTORY_TAB, ?CONFIG_WATCHER_TAB],
    lists:foreach(fun(Table) ->
        case ets:info(Table) of
            undefined -> ok;
            _ ->
                ets:delete(Table),
                ?SLOG(debug, #{msg => "ets_table_deleted", table => Table})
        end
    end, Tables),
    ok.

%% @doc 创建配置备份目录
%% 确保配置备份目录存在
-spec create_backup_directory() -> ok.
create_backup_directory() ->
    BackupDir = get_backup_directory(),
    case filelib:ensure_dir(filename:join(BackupDir, "dummy")) of
        ok ->
            ?SLOG(debug, #{msg => "backup_directory_created", dir => BackupDir});
        {error, Reason} ->
            ?SLOG(warning, #{
                msg => "failed_to_create_backup_directory",
                dir => BackupDir,
                reason => Reason
            })
    end,
    ok.

%% @doc 获取备份目录路径
%% 返回配置备份的存储目录
-spec get_backup_directory() -> string().
get_backup_directory() ->
    case application:get_env(emqx_plugin_mongodb, config_backup_dir) of
        {ok, Dir} -> Dir;
        undefined ->
            % 默认使用EMQX数据目录下的备份子目录
            DataDir = emqx:get_config([node, data_dir], "data"),
            filename:join([DataDir, "mongodb_plugin", ?CONFIG_BACKUP_DIR])
    end.

%% @doc 备份当前配置
%% 将当前配置保存到历史记录中
-spec backup_current_config(#hot_reload_state{}) -> ok.
backup_current_config(State) ->
    Config = State#hot_reload_state.current_config,
    Version = erlang:system_time(millisecond),
    ConfigVersion = #config_version{
        version = Version,
        config = Config,
        timestamp = Version,
        checksum = calculate_config_checksum(Config),
        status = active,
        description = "Initial configuration"
    },
    ets:insert(?CONFIG_HISTORY_TAB, {Version, ConfigVersion}),
    ?SLOG(debug, #{msg => "current_config_backed_up", version => Version}),
    ok.

%% @doc 计算配置校验和
%% 计算配置内容的MD5校验和
-spec calculate_config_checksum(map()) -> binary().
calculate_config_checksum(Config) ->
    ConfigBinary = term_to_binary(Config),
    crypto:hash(md5, ConfigBinary).

%% @doc 启动文件监控
%% 启动配置文件的监控进程
-spec start_file_watcher(#hot_reload_state{}) -> {ok, #hot_reload_state{}} | {error, term()}.
start_file_watcher(State) ->
    ConfigFile = State#hot_reload_state.config_file,
    case filelib:is_regular(ConfigFile) of
        true ->
            % 获取文件信息
            {ok, FileInfo} = file:read_file_info(ConfigFile),
            LastModified = FileInfo#file_info.mtime,
            Size = FileInfo#file_info.size,

            % 启动监控进程
            WatcherPid = spawn_link(?MODULE, file_watcher_loop, [ConfigFile]),

            % 创建文件监控状态
            WatcherState = #file_watcher_state{
                file_path = ConfigFile,
                last_modified = LastModified,
                last_size = Size,
                checksum = calculate_file_checksum(ConfigFile),
                watcher_pid = WatcherPid
            },

            % 保存监控状态
            ets:insert(?CONFIG_WATCHER_TAB, {ConfigFile, WatcherState}),

            NewState = State#hot_reload_state{file_watcher_state = WatcherState},
            ?SLOG(info, #{msg => "file_watcher_started", file => ConfigFile}),
            {ok, NewState};
        false ->
            ?SLOG(warning, #{msg => "config_file_not_found", file => ConfigFile}),
            {error, config_file_not_found}
    end.

%% @doc 停止文件监控
%% 停止配置文件的监控进程
-spec stop_file_watcher(#hot_reload_state{}) -> #hot_reload_state{}.
stop_file_watcher(State) ->
    case State#hot_reload_state.file_watcher_state of
        undefined ->
            State;
        WatcherState ->
            % 停止监控进程
            WatcherPid = WatcherState#file_watcher_state.watcher_pid,
            case is_process_alive(WatcherPid) of
                true ->
                    exit(WatcherPid, normal),
                    ?SLOG(debug, #{msg => "file_watcher_stopped"});
                false ->
                    ok
            end,

            % 清理监控状态
            ConfigFile = State#hot_reload_state.config_file,
            ets:delete(?CONFIG_WATCHER_TAB, ConfigFile),

            State#hot_reload_state{file_watcher_state = undefined}
    end.

%% @doc 计算文件校验和
%% 计算文件内容的MD5校验和
-spec calculate_file_checksum(string()) -> binary().
calculate_file_checksum(FilePath) ->
    case file:read_file(FilePath) of
        {ok, Content} ->
            crypto:hash(md5, Content);
        {error, _} ->
            <<>>
    end.

%% @doc 文件监控循环
%% 监控配置文件变更的主循环
-spec file_watcher_loop(string()) -> no_return().
file_watcher_loop(FilePath) ->
    timer:sleep(?FILE_WATCH_INTERVAL),
    case filelib:is_regular(FilePath) of
        true ->
            case ets:lookup(?CONFIG_WATCHER_TAB, FilePath) of
                [{_, WatcherState}] ->
                    case check_file_changed(FilePath, WatcherState) of
                        {changed, NewWatcherState} ->
                            % 文件发生变更，通知主进程
                            ?MODULE ! {file_changed, FilePath},
                            ets:insert(?CONFIG_WATCHER_TAB, {FilePath, NewWatcherState}),
                            file_watcher_loop(FilePath);
                        unchanged ->
                            file_watcher_loop(FilePath)
                    end;
                [] ->
                    % 监控状态不存在，退出循环
                    exit(normal)
            end;
        false ->
            % 文件不存在，继续监控
            file_watcher_loop(FilePath)
    end.

%% @doc 检查文件是否发生变更
%% 比较文件的修改时间、大小和校验和
-spec check_file_changed(string(), #file_watcher_state{}) ->
    {changed, #file_watcher_state{}} | unchanged.
check_file_changed(FilePath, WatcherState) ->
    case file:read_file_info(FilePath) of
        {ok, FileInfo} ->
            LastModified = FileInfo#file_info.mtime,
            Size = FileInfo#file_info.size,

            % 检查修改时间和大小
            OldModified = WatcherState#file_watcher_state.last_modified,
            OldSize = WatcherState#file_watcher_state.last_size,
            case {LastModified, Size} of
                {OldModified, OldSize} ->
                    unchanged;
                _ ->
                    % 文件发生变更，计算新的校验和
                    NewChecksum = calculate_file_checksum(FilePath),
                    case NewChecksum =:= WatcherState#file_watcher_state.checksum of
                        true ->
                            % 校验和相同，可能只是时间戳变更
                            unchanged;
                        false ->
                            % 文件内容确实发生变更
                            NewWatcherState = WatcherState#file_watcher_state{
                                last_modified = LastModified,
                                last_size = Size,
                                checksum = NewChecksum
                            },
                            {changed, NewWatcherState}
                    end
            end;
        {error, _} ->
            unchanged
    end.

%% @doc 重载配置（内部实现）
%% 重新读取配置文件并应用
-spec reload_config_internal(#hot_reload_state{}) ->
    {ok, #hot_reload_state{}} | {error, term()}.
reload_config_internal(State) ->
    try
        % 读取新配置
        case emqx_plugin_mongodb:read_config() of
            {error, Reason} ->
                ?SLOG(error, #{msg => "failed_to_read_config", reason => Reason}),
                {error, {read_config_failed, Reason}};
            NewConfig ->
                % 应用新配置
                apply_new_config(NewConfig, State)
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "config_reload_exception",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {config_reload_exception, R}}
    end.

%% @doc 应用新配置
%% 验证并应用新的配置
-spec apply_new_config(map(), #hot_reload_state{}) ->
    {ok, #hot_reload_state{}} | {error, term()}.
apply_new_config(NewConfig, State) ->
    % 验证新配置
    case validate_config_internal(NewConfig) of
        {ok, ValidatedConfig} ->
            % 备份当前配置
            backup_config_version(State#hot_reload_state.current_config, State),

            % 应用新配置到各个模块
            case apply_config_to_modules(ValidatedConfig) of
                ok ->
                    % 更新状态
                    NewState = State#hot_reload_state{
                        current_config = ValidatedConfig,
                        config_history = add_to_history(ValidatedConfig, State#hot_reload_state.config_history)
                    },

                    ?SLOG(info, #{msg => "config_applied_successfully"}),
                    {ok, NewState};
                {error, Reason} ->
                    ?SLOG(error, #{msg => "failed_to_apply_config", reason => Reason}),
                    {error, {apply_config_failed, Reason}}
            end;
        {error, Reason} ->
            ?SLOG(error, #{msg => "config_validation_failed", reason => Reason}),
            {error, {validation_failed, Reason}}
    end.

%% @doc 验证配置（内部实现）
%% 使用schema验证配置的有效性
-spec validate_config_internal(map()) -> {ok, map()} | {error, term()}.
validate_config_internal(Config) ->
    try
        % 使用schema验证配置
        case emqx_config:check_config(emqx_plugin_mongodb_schema, #{plugin_mongodb => Config}) of
            {_, #{plugin_mongodb := ValidatedConfig}} ->
                {ok, ValidatedConfig};
            Error ->
                {error, {schema_validation_failed, Error}}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "config_validation_exception",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {validation_exception, R}}
    end.

%% @doc 应用配置到各个模块
%% 将新配置应用到MongoDB插件的各个功能模块
-spec apply_config_to_modules(map()) -> ok | {error, term()}.
apply_config_to_modules(Config) ->
    try
        % 更新应用环境变量
        application:set_env(emqx_plugin_mongodb, config, Config),

        % 通知各个模块配置已更新
        ModuleUpdateResults = [
            update_connection_config(Config),
            update_session_persistence_config(Config),
            update_subscription_persistence_config(Config),
            update_message_persistence_config(Config),
            update_will_persistence_config(Config),
            update_retained_persistence_config(Config),
            update_topic_filter_config(Config)
        ],

        % 检查是否有模块更新失败
        case lists:filter(fun(Result) -> Result =/= ok end, ModuleUpdateResults) of
            [] ->
                ?SLOG(info, #{msg => "all_modules_config_updated_successfully"}),
                ok;
            Errors ->
                ?SLOG(error, #{msg => "some_modules_config_update_failed", errors => Errors}),
                {error, {module_update_failed, Errors}}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "apply_config_to_modules_exception",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {apply_config_exception, R}}
    end.

%% @doc 更新连接配置
%% 更新MongoDB连接相关的配置
-spec update_connection_config(map()) -> ok | {error, term()}.
update_connection_config(Config) ->
    try
        _ConnectionConfig = maps:get(connection, Config, #{}),
        % 这里可以添加连接池重新配置的逻辑
        % 例如：调用连接器模块的配置更新函数
        ?SLOG(debug, #{msg => "connection_config_updated"}),
        ok
    catch
        E:R ->
            {error, {connection_config_update_failed, {E, R}}}
    end.

%% @doc 更新会话持久化配置
%% 更新会话持久化模块的配置
-spec update_session_persistence_config(map()) -> ok | {error, term()}.
update_session_persistence_config(Config) ->
    try
        SessionConfig = maps:get(session_persistence, Config, #{}),
        Enabled = maps:get(enabled, SessionConfig, false),
        application:set_env(emqx_plugin_mongodb, session_persistence_enabled, Enabled),
        ?SLOG(debug, #{msg => "session_persistence_config_updated", enabled => Enabled}),
        ok
    catch
        E:R ->
            {error, {session_persistence_config_update_failed, {E, R}}}
    end.

%% @doc 更新订阅持久化配置
%% 更新订阅持久化模块的配置
-spec update_subscription_persistence_config(map()) -> ok | {error, term()}.
update_subscription_persistence_config(Config) ->
    try
        SubscriptionConfig = maps:get(subscription_persistence, Config, #{}),
        Enabled = maps:get(enabled, SubscriptionConfig, false),
        application:set_env(emqx_plugin_mongodb, subscription_persistence_enabled, Enabled),
        ?SLOG(debug, #{msg => "subscription_persistence_config_updated", enabled => Enabled}),
        ok
    catch
        E:R ->
            {error, {subscription_persistence_config_update_failed, {E, R}}}
    end.

%% @doc 更新消息持久化配置
%% 更新消息持久化模块的配置
-spec update_message_persistence_config(map()) -> ok | {error, term()}.
update_message_persistence_config(Config) ->
    try
        MessageConfig = maps:get(message_persistence, Config, #{}),
        Enabled = maps:get(enabled, MessageConfig, false),
        application:set_env(emqx_plugin_mongodb, message_persistence_enabled, Enabled),
        ?SLOG(debug, #{msg => "message_persistence_config_updated", enabled => Enabled}),
        ok
    catch
        E:R ->
            {error, {message_persistence_config_update_failed, {E, R}}}
    end.

%% @doc 更新遗嘱消息持久化配置
%% 更新遗嘱消息持久化模块的配置
-spec update_will_persistence_config(map()) -> ok | {error, term()}.
update_will_persistence_config(Config) ->
    try
        WillConfig = maps:get(will_persistence, Config, #{}),
        Enabled = maps:get(enabled, WillConfig, false),
        application:set_env(emqx_plugin_mongodb, will_persistence_enabled, Enabled),
        ?SLOG(debug, #{msg => "will_persistence_config_updated", enabled => Enabled}),
        ok
    catch
        E:R ->
            {error, {will_persistence_config_update_failed, {E, R}}}
    end.

%% @doc 更新保留消息持久化配置
%% 更新保留消息持久化模块的配置
-spec update_retained_persistence_config(map()) -> ok | {error, term()}.
update_retained_persistence_config(Config) ->
    try
        RetainedConfig = maps:get(retained_persistence, Config, #{}),
        Enabled = maps:get(enabled, RetainedConfig, false),
        application:set_env(emqx_plugin_mongodb, retained_persistence_enabled, Enabled),
        ?SLOG(debug, #{msg => "retained_persistence_config_updated", enabled => Enabled}),
        ok
    catch
        E:R ->
            {error, {retained_persistence_config_update_failed, {E, R}}}
    end.

%% @doc 更新主题过滤配置
%% 更新主题过滤模块的配置
-spec update_topic_filter_config(map()) -> ok | {error, term()}.
update_topic_filter_config(Config) ->
    try
        Topics = maps:get(topics, Config, []),
        application:set_env(emqx_plugin_mongodb, topics, Topics),
        ?SLOG(debug, #{msg => "topic_filter_config_updated", topic_count => length(Topics)}),
        ok
    catch
        E:R ->
            {error, {topic_filter_config_update_failed, {E, R}}}
    end.

%% @doc 备份配置版本
%% 将指定配置保存到历史记录中
-spec backup_config_version(map(), #hot_reload_state{}) -> ok.
backup_config_version(Config, _State) ->
    Version = erlang:system_time(millisecond),
    ConfigVersion = #config_version{
        version = Version,
        config = Config,
        timestamp = Version,
        checksum = calculate_config_checksum(Config),
        status = backup,
        description = "Auto backup before config change"
    },
    ets:insert(?CONFIG_HISTORY_TAB, {Version, ConfigVersion}),

    % 清理过期的历史记录
    cleanup_old_history(),
    ok.

%% @doc 添加到配置历史
%% 将新配置添加到历史列表中
-spec add_to_history(map(), list()) -> list().
add_to_history(Config, History) ->
    Version = erlang:system_time(millisecond),
    ConfigVersion = #config_version{
        version = Version,
        config = Config,
        timestamp = Version,
        checksum = calculate_config_checksum(Config),
        status = active,
        description = "Configuration updated"
    },

    % 添加到历史列表，保持最大长度限制
    NewHistory = [ConfigVersion | History],
    case length(NewHistory) > ?MAX_CONFIG_HISTORY of
        true ->
            lists:sublist(NewHistory, ?MAX_CONFIG_HISTORY);
        false ->
            NewHistory
    end.

%% @doc 清理过期的历史记录
%% 删除超过最大保留数量的历史记录
-spec cleanup_old_history() -> ok.
cleanup_old_history() ->
    AllVersions = ets:tab2list(?CONFIG_HISTORY_TAB),
    SortedVersions = lists:sort(fun({V1, _}, {V2, _}) -> V1 > V2 end, AllVersions),

    case length(SortedVersions) > ?MAX_CONFIG_HISTORY of
        true ->
            ToDelete = lists:nthtail(?MAX_CONFIG_HISTORY, SortedVersions),
            lists:foreach(fun({Version, _}) ->
                ets:delete(?CONFIG_HISTORY_TAB, Version)
            end, ToDelete),
            ?SLOG(debug, #{msg => "old_config_history_cleaned", deleted_count => length(ToDelete)});
        false ->
            ok
    end.

%% @doc 回滚配置（内部实现）
%% 回滚到上一个有效的配置版本
-spec rollback_config_internal(#hot_reload_state{}) ->
    {ok, #hot_reload_state{}} | {error, term()}.
rollback_config_internal(State) ->
    case get_previous_config_version() of
        {ok, PreviousConfig} ->
            ?SLOG(info, #{msg => "rolling_back_to_previous_config"}),
            apply_new_config(PreviousConfig, State);
        {error, Reason} ->
            ?SLOG(error, #{msg => "rollback_failed", reason => Reason}),
            {error, Reason}
    end.

%% @doc 获取上一个配置版本
%% 从历史记录中获取上一个有效的配置
-spec get_previous_config_version() -> {ok, map()} | {error, term()}.
get_previous_config_version() ->
    AllVersions = ets:tab2list(?CONFIG_HISTORY_TAB),
    case lists:sort(fun({V1, _}, {V2, _}) -> V1 > V2 end, AllVersions) of
        [_Current, {_, #config_version{config = PreviousConfig}} | _] ->
            {ok, PreviousConfig};
        [_Current] ->
            {error, no_previous_version};
        [] ->
            {error, no_config_history}
    end.

%% @doc 格式化配置历史
%% 将配置历史记录格式化为可读的格式
-spec format_config_history(list()) -> [map()].
format_config_history(History) ->
    lists:map(fun(ConfigVersion) ->
        #{
            version => ConfigVersion#config_version.version,
            timestamp => ConfigVersion#config_version.timestamp,
            checksum => ConfigVersion#config_version.checksum,
            status => ConfigVersion#config_version.status,
            description => ConfigVersion#config_version.description
        }
    end, History).

%% @doc 备份配置（带描述）
%% 创建带有自定义描述的配置备份
-spec backup_config_with_description(string(), #hot_reload_state{}) -> ok | {error, term()}.
backup_config_with_description(Description, State) ->
    try
        Config = State#hot_reload_state.current_config,
        Version = erlang:system_time(millisecond),

        % 创建备份文件
        BackupDir = get_backup_directory(),
        BackupFileName = io_lib:format("config_backup_~p.hocon", [Version]),
        BackupFilePath = filename:join(BackupDir, BackupFileName),

        % 将配置写入备份文件
        ConfigContent = format_config_for_backup(Config),
        case file:write_file(BackupFilePath, ConfigContent) of
            ok ->
                % 保存到历史记录
                ConfigVersion = #config_version{
                    version = Version,
                    config = Config,
                    timestamp = Version,
                    checksum = calculate_config_checksum(Config),
                    status = backup,
                    description = Description
                },
                ets:insert(?CONFIG_HISTORY_TAB, {Version, ConfigVersion}),

                ?SLOG(info, #{
                    msg => "config_backup_created",
                    file => BackupFilePath,
                    description => Description
                }),
                ok;
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_create_config_backup",
                    file => BackupFilePath,
                    reason => Reason
                }),
                {error, {backup_file_write_failed, Reason}}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "backup_config_exception",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {backup_exception, R}}
    end.

%% @doc 格式化配置用于备份
%% 将配置格式化为HOCON格式的字符串
-spec format_config_for_backup(map()) -> iolist().
format_config_for_backup(Config) ->
    % 这里可以实现更复杂的HOCON格式化逻辑
    % 目前使用简单的term_to_binary转换
    io_lib:format("~p~n", [Config]).

%% @doc 列出配置备份（内部实现）
%% 获取所有可用的配置备份列表
-spec list_backups_internal() -> {ok, [map()]} | {error, term()}.
list_backups_internal() ->
    try
        BackupDir = get_backup_directory(),
        case file:list_dir(BackupDir) of
            {ok, Files} ->
                BackupFiles = lists:filter(fun(File) ->
                    lists:prefix("config_backup_", File) andalso
                    lists:suffix(".hocon", File)
                end, Files),

                Backups = lists:map(fun(File) ->
                    FilePath = filename:join(BackupDir, File),
                    case file:read_file_info(FilePath) of
                        {ok, FileInfo} ->
                            #{
                                filename => File,
                                path => FilePath,
                                size => FileInfo#file_info.size,
                                modified => FileInfo#file_info.mtime
                            };
                        {error, _} ->
                            #{
                                filename => File,
                                path => FilePath,
                                size => 0,
                                modified => undefined
                            }
                    end
                end, BackupFiles),

                {ok, Backups};
            {error, Reason} ->
                {error, {list_backup_dir_failed, Reason}}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "list_backups_exception",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {list_backups_exception, R}}
    end.

%% @doc 配置验证工作进程
%% 在独立进程中验证配置，避免阻塞主进程
-spec config_validator_worker(map(), pid()) -> ok.
config_validator_worker(Config, ReplyTo) ->
    Result = validate_config_internal(Config),
    ReplyTo ! {validation_result, Result}.

%% @doc 集成到协调器系统
%% 将配置热重载服务集成到MongoDB插件协调器中
-spec integrate() -> ok | {error, term()}.
integrate() ->
    try
        ?SLOG(info, #{msg => "integrating_config_hot_reload_to_coordinator"}),

        % 1. 注册到协调器
        case emqx_plugin_mongodb_coordinator:register_module(
            config_hot_reload,
            ?MODULE,
            #{
                priority => 17,
                type => service,
                dependencies => [],
                capabilities => [
                    config_management,
                    hot_reload,
                    file_watching,
                    config_validation,
                    config_rollback,
                    config_backup
                ]
            }
        ) of
            ok ->
                ?SLOG(info, #{msg => "config_hot_reload_registered_to_coordinator"});
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_register_config_hot_reload_to_coordinator",
                    reason => Reason
                }),
                throw({register_failed, Reason})
        end,

        % 2. 集成到错误处理系统
        case emqx_plugin_mongodb_error_handler:register_error_handler(
            config_hot_reload_errors,
            fun handle_config_hot_reload_errors/2,
            #{
                priority => high,
                retry_strategy => exponential_backoff,
                max_retries => 3
            }
        ) of
            ok ->
                ?SLOG(debug, #{msg => "config_hot_reload_error_handler_registered"});
            {error, ErrorReason} ->
                ?SLOG(warning, #{
                    msg => "failed_to_register_config_hot_reload_error_handler",
                    reason => ErrorReason
                })
        end,

        % 3. 集成到资源管理系统
        case emqx_plugin_mongodb_resource_manager:register_resource(
            config_hot_reload_service,
            #{
                type => service,
                health_check => fun() ->
                    case get_current_config() of
                        {ok, _} -> healthy;
                        {error, _} -> unhealthy
                    end
                end,
                resource_limits => #{
                    memory_limit => 50 * 1024 * 1024,  % 50MB
                    cpu_limit => 0.1                    % 10% CPU
                },
                cleanup_function => fun() ->
                    stop()
                end
            }
        ) of
            ok ->
                ?SLOG(debug, #{msg => "config_hot_reload_resource_registered"});
            {error, ResourceReason} ->
                ?SLOG(warning, #{
                    msg => "failed_to_register_config_hot_reload_resource",
                    reason => ResourceReason
                })
        end,

        % 4. 集成到监控系统
        case emqx_plugin_mongodb_monitor:register_monitor(
            config_hot_reload_monitor,
            #{
                type => service_monitor,
                check_interval => 60000,  % 1分钟检查一次
                metrics => [
                    config_reload_count,
                    config_validation_errors,
                    file_watch_events,
                    rollback_operations
                ],
                thresholds => #{
                    max_validation_errors_per_hour => 10,
                    max_rollback_operations_per_day => 5
                },
                alert_callback => fun(Alert) ->
                    ?SLOG(warning, #{
                        msg => "config_hot_reload_alert",
                        alert => Alert
                    })
                end
            }
        ) of
            ok ->
                ?SLOG(debug, #{msg => "config_hot_reload_monitor_registered"});
            {error, MonitorReason} ->
                ?SLOG(warning, #{
                    msg => "failed_to_register_config_hot_reload_monitor",
                    reason => MonitorReason
                })
        end,

        ?SLOG(info, #{msg => "config_hot_reload_integration_completed"}),
        ok
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "config_hot_reload_integration_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {integration_failed, R}}
    end.

%% @doc 处理配置热重载错误
%% 专门处理配置热重载模块产生的错误
-spec handle_config_hot_reload_errors(term(), map()) -> ok | {error, term()}.
handle_config_hot_reload_errors(Error, Context) ->
    try
        case Error of
            {config_validation_failed, Reason} ->
                ?SLOG(error, #{
                    msg => "config_validation_failed",
                    reason => Reason,
                    context => Context
                }),
                % 尝试回滚到上一个有效配置
                case rollback_config() of
                    {ok, _} ->
                        ?SLOG(info, #{msg => "config_rollback_successful"});
                    {error, RollbackReason} ->
                        ?SLOG(error, #{
                            msg => "config_rollback_failed",
                            reason => RollbackReason
                        })
                end;

            {file_watch_error, Reason} ->
                ?SLOG(warning, #{
                    msg => "config_file_watch_error",
                    reason => Reason,
                    context => Context
                }),
                % 尝试重新启动文件监控
                unwatch_config_file(),
                timer:sleep(1000),
                watch_config_file();

            {apply_config_failed, Reason} ->
                ?SLOG(error, #{
                    msg => "apply_config_failed",
                    reason => Reason,
                    context => Context
                }),
                % 配置应用失败，尝试回滚
                rollback_config();

            _ ->
                ?SLOG(warning, #{
                    msg => "unknown_config_hot_reload_error",
                    error => Error,
                    context => Context
                })
        end,
        ok
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "config_hot_reload_error_handler_exception",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {error_handler_exception, R}}
    end.

%% ============================================================================
%% 模块结束
%% ============================================================================
