%%--------------------------------------------------------------------
%% Copyright (c) 2024 EMQ Technologies Co., Ltd. All Rights Reserved.
%%
%% Licensed under the Apache License, Version 2.0 (the "License");
%% you may not use this file except in compliance with the License.
%% You may obtain a copy of the License at
%%
%%     http://www.apache.org/licenses/LICENSE-2.0
%%
%% Unless required by applicable law or agreed to in writing, software
%% distributed under the License is distributed on an "AS IS" BASIS,
%% WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
%% See the License for the specific language governing permissions and
%% limitations under the License.
%%--------------------------------------------------------------------

%% @doc EMQX MongoDB Plugin - 遗嘱消息持久化模块
%% 这个模块是MQTT遗嘱消息持久化的核心组件，提供企业级的遗嘱消息管理能力
%%
%% 功能概述：
%% 1. 遗嘱消息持久化 - 监听EMQX内置遗嘱消息的变化并持久化到MongoDB
%% 2. 系统恢复支持 - EMQX重启时从MongoDB恢复遗嘱消息并处理异常关闭场景
%% 3. 过期消息清理 - 定期清理过期的遗嘱消息，防止存储膨胀
%% 4. 查询和统计 - 提供遗嘱消息的查询和统计功能
%% 5. 异常检测 - 检测系统异常关闭，确保遗嘱消息正确发布
%% 6. 状态管理 - 跟踪遗嘱消息的发布状态和生命周期
%%
%% MQTT遗嘱消息机制：
%% - 遗嘱消息是MQTT协议的重要特性，用于处理客户端异常断开的情况
%% - 客户端连接时可以设置遗嘱消息，包括主题、内容、QoS等
%% - 正常断开时遗嘱消息不会发布，异常断开时由服务器发布遗嘱消息
%% - 遗嘱消息帮助其他客户端感知设备的异常状态
%%
%% 设计原则：
%% - 非侵入式设计：不干扰EMQX内置的遗嘱消息处理流程
%% - 职责分离：负责持久化存储和异常关闭场景下的遗嘱消息发布
%% - 事件驱动：通过钩子监听遗嘱消息的变化事件
%% - 系统集成：在系统启动时检测异常关闭并发布未处理的遗嘱消息
%% - 可靠性优先：确保遗嘱消息在各种异常情况下都能正确处理
%%
%% 使用的EMQX钩子：
%% - client.connected: 监听遗嘱消息的设置事件，并检查是否需要恢复该客户端的遗嘱消息
%% - client.disconnected: 监听客户端断开事件，正常断开时删除遗嘱消息
%% - message.publish: 监听遗嘱消息的实际发布事件（低优先级）
%%
%% Java等价概念：
%% 类似于Spring Boot中的遗嘱消息管理服务：
%% @Service
%% @Component
%% public class WillMessagePersistenceService {
%%     @Autowired private MongoTemplate mongoTemplate;
%%     @Autowired private WillMessageRepository willMessageRepository;
%%
%%     @EventListener
%%     @Async
%%     public void onClientConnected(ClientConnectedEvent event) {
%%         if (event.hasWillMessage()) {
%%             WillMessage willMessage = convertToWillMessage(event);
%%             willMessageRepository.save(willMessage);
%%         }
%%     }
%%
%%     @EventListener
%%     @Async
%%     public void onClientDisconnected(ClientDisconnectedEvent event) {
%%         if (event.isNormalDisconnection()) {
%%             willMessageRepository.deleteByClientId(event.getClientId());
%%         } else {
%%             // 异常断开，遗嘱消息由EMQX自动发布
%%             markWillMessageAsPublished(event.getClientId());
%%         }
%%     }
%%
%%     @PostConstruct
%%     public void handleAbnormalShutdown() {
%%         // 系统启动时检测异常关闭并发布未处理的遗嘱消息
%%         List<WillMessage> pendingMessages = willMessageRepository.findPendingMessages();
%%         for (WillMessage msg : pendingMessages) {
%%             publishWillMessage(msg);
%%         }
%%     }
%%
%%     @Scheduled(fixedDelay = 3600000) // 1小时清理一次
%%     public void cleanupExpiredWillMessages() {
%%         willMessageRepository.deleteExpiredMessages();
%%     }
%% }
%%
%% 设计模式：
%% - 观察者模式：监听EMQX的客户端连接和断开事件
%% - 策略模式：不同断开类型使用不同的处理策略
%% - 状态模式：遗嘱消息的状态管理
%% - 模板方法模式：统一的遗嘱消息处理流程
%% @end

-module(emqx_plugin_mongodb_will).

-include("emqx_plugin_mongodb.hrl").
-include_lib("emqx/include/emqx.hrl").

%% ============================================================================
%% EMQX钩子回调函数 - 响应MQTT客户端生命周期事件和消息发布事件
%% 这些函数作为EMQX钩子回调，监听客户端连接、断开和消息发布事件
%% 类似于Java Spring的事件监听器：
%% @EventListener
%% @Async  // 异步处理，不阻塞客户端连接和消息发布
%% public void onClientEvent(ClientEvent event) { ... }
%% ============================================================================
-export([
    on_client_connected/2,      % 客户端连接事件监听器
                               % 功能：当客户端连接时触发，检查和保存遗嘱消息
                               % Java等价：@EventListener(ClientConnectedEvent.class)

    on_client_disconnected/3,   % 客户端断开事件监听器
                               % 功能：当客户端断开时触发，处理遗嘱消息逻辑
                               % Java等价：@EventListener(ClientDisconnectedEvent.class)

    on_message_publish/1,       % 消息发布事件监听器
                               % 功能：当消息发布时触发，检测遗嘱消息发布并清理数据库
                               % Java等价：@EventListener(MessagePublishEvent.class)

    integrate/0                % 集成到协调器
                               % 功能：将遗嘱消息持久化模块注册到协调器中
                               % Java等价：@Component public void integrate()
]).

%% ============================================================================
%% 遗嘱消息管理核心API - 遗嘱消息的CRUD操作和管理功能
%% 这些函数提供遗嘱消息的完整生命周期管理
%% 类似于Java中的Repository层或Service层方法
%% ============================================================================
-export([
    store_will_message_to_mongodb/2,        % 存储遗嘱消息到MongoDB
                                           % 功能：将遗嘱消息持久化到MongoDB集合
                                           % Java等价：public void saveWillMessage(String clientId, WillMessage willMessage)

    store_will_message_to_mongodb_from_map/2, % 从Map存储遗嘱消息到MongoDB
                                             % 功能：将Map格式的遗嘱消息持久化到MongoDB
                                             % Java等价：public void saveWillMessage(String clientId, Map<String, Object> willMessageMap)

    restore_client_will_message_if_needed/1, % 恢复客户端遗嘱消息（如果需要）
                                            % 功能：检查并恢复客户端的遗嘱消息状态
                                            % Java等价：public void restoreClientWillMessageIfNeeded(String clientId)

    handle_reconnection_without_will_message/1, % 处理重连时没有遗嘱消息的情况
                                               % 功能：客户端重连时没有携带遗嘱消息，删除数据库中的旧记录
                                               % Java等价：public void handleReconnectionWithoutWillMessage(String clientId)

    handle_reconnection_without_will_message_atomic/1, % 原子性处理重连时没有遗嘱消息的情况
                                                      % 功能：防止与智能处理线程的竞态条件，使用原子性删除
                                                      % Java等价：@Synchronized public void handleReconnectionWithoutWillMessageAtomic(String clientId)



    find_will_message/1,                    % 查找遗嘱消息
                                           % 功能：根据客户端ID查找对应的遗嘱消息
                                           % Java等价：public Optional<WillMessage> findWillMessage(String clientId)

    cleanup_expired_will_messages/0,        % 清理过期的遗嘱消息
                                           % 功能：定期清理过期的遗嘱消息，防止存储膨胀
                                           % Java等价：@Scheduled public void cleanupExpiredWillMessages()

    get_will_message_stats/0                % 获取遗嘱消息统计信息
                                           % 功能：获取遗嘱消息的数量、状态分布等统计数据
                                           % Java等价：public WillMessageStats getWillMessageStats()
]).

%% ============================================================================
%% 生命周期管理API - 模块启动、配置和关闭管理
%% 这些函数管理遗嘱消息持久化模块的完整生命周期
%% 类似于Java Spring Boot的生命周期管理：
%% @PostConstruct - 初始化资源和配置
%% @PreDestroy - 清理资源和保存状态
%% @ConfigurationProperties - 配置加载和验证
%% ============================================================================
-export([
    init/0,                     % 初始化模块
                               % 功能：初始化遗嘱消息持久化模块，设置必要的资源
                               % Java等价：@PostConstruct public void init()

    load/0,                     % 加载模块（无参数版本）
                               % 功能：加载遗嘱消息持久化模块，使用默认配置
                               % Java等价：@PostConstruct public void load()

    load/1,                     % 加载模块（带配置参数）
                               % 功能：加载遗嘱消息持久化模块，使用指定配置
                               % Java等价：@ConfigurationProperties public void load(WillPersistenceConfig config)

    unload/0,                   % 卸载模块
                               % 功能：卸载遗嘱消息持久化模块，清理资源
                               % Java等价：@PreDestroy public void unload()

    start_cleanup_timer/0,      % 启动清理定时器
                               % 功能：启动过期遗嘱消息的定时清理任务
                               % Java等价：@Scheduled public void startCleanupTimer()

    stop_cleanup_timer/0        % 停止清理定时器
                               % 功能：停止过期遗嘱消息的定时清理任务
                               % Java等价：public void stopCleanupTimer()
]).

%% ============================================================================
%% 内部辅助函数 - 模块内部使用的工具和配置函数
%% 这些函数提供模块内部的辅助功能，类似于Java的private方法
%% ============================================================================
-export([
    get_will_collection/0,                  % 获取遗嘱消息集合名称 - 配置获取方法
    create_will_indexes/0,                  % 创建遗嘱消息索引 - 数据库初始化方法
    do_cleanup_expired_will_messages/0,     % 执行过期遗嘱消息清理 - 定时任务的具体实现
    parse_time_duration/1,                  % 解析时间配置 - 配置解析工具方法
    format_peerhost/1,                      % 格式化对等主机地址 - 数据格式化方法
    is_will_message_publish/1,              % 判断是否为遗嘱消息发布 - 消息类型判断方法
    update_will_message_published_status/3, % 更新遗嘱消息发布状态 - 状态管理方法
    detect_abnormal_shutdown/0,             % 检测异常关闭 - 系统状态检测方法
    handle_abnormal_shutdown_will_messages/0, % 处理异常关闭后的遗嘱消息 - 系统恢复方法
    intelligently_handle_will_messages_after_restart/1, % 智能处理EMQX重启后的遗嘱消息
                                                      % 功能：根据客户端状态智能决定是否发布遗嘱消息
                                                      % Java等价：@Async public void intelligentlyHandleWillMessagesAfterRestart(List<WillMessage> wills)

    publish_pending_will_messages_after_restart/1, % 发布EMQX重启后的待处理遗嘱消息（已废弃）
                                                  % 功能：EMQX重启时主动发布遗嘱消息（已废弃，使用智能处理替代）
                                                  % Java等价：@Deprecated @Async public void publishPendingWillMessagesAfterRestart(List<WillMessage> wills)

    publish_single_will_message_after_restart/1, % 发布单个遗嘱消息（EMQX重启后）
                                                % 功能：发布单个遗嘱消息并删除记录
                                                % Java等价：public void publishSingleWillMessageAfterRestart(WillMessage will)

    ensure_will_collection_and_indexes/1,   % 确保遗嘱消息集合和索引存在 - 集合初始化
                                                % 功能：确保遗嘱消息集合和索引存在
                                                % Java等价：public void ensureWillCollectionAndIndexes(String collection)

    is_client_connected/1,                  % 检查客户端是否已连接
                                           % 功能：检查指定客户端是否当前已连接到EMQX
                                           % Java等价：public boolean isClientConnected(String clientId)

    delete_will_message_by_id/1,           % 根据唯一ID删除遗嘱消息
                                          % 功能：使用遗嘱消息的唯一_id字段精确删除MongoDB记录
                                          % Java等价：public void deleteWillMessageById(String willId)

    extract_will_delay_interval/1,         % 提取Will Delay Interval属性
                                          % 功能：从MQTT 5.0属性中提取遗嘱消息延迟发布时间
                                          % Java等价：public int extractWillDelayInterval(Properties props)

    extract_message_expiry_interval/1,     % 提取Message Expiry Interval属性
                                          % 功能：从MQTT 5.0属性中提取消息过期时间
                                          % Java等价：public Integer extractMessageExpiryInterval(Properties props)

    calculate_will_message_expiry_time/1,  % 计算遗嘱消息过期时间
                                          % 功能：根据MQTT协议计算遗嘱消息的过期时间
                                          % Java等价：public long calculateWillMessageExpiryTime(Integer expiryInterval)

    calculate_will_publish_time/1,         % 计算遗嘱消息发布时间
                                          % 功能：根据Will Delay Interval计算发布时间
                                          % Java等价：public long calculateWillPublishTime(int delayInterval)

    log_pending_will_messages/1,            % 记录待处理的遗嘱消息 - 日志记录方法
    mark_normal_shutdown/0,                 % 标记正常关闭 - 系统状态标记方法
    clear_normal_shutdown_flag/0            % 清除正常关闭标志 - 状态清理方法
]).

%% 定时器引用
-define(CLEANUP_TIMER_REF, will_cleanup_timer).

%%--------------------------------------------------------------------
%% 生命周期管理实现 - 遗嘱消息持久化模块的启动和配置
%%--------------------------------------------------------------------

%% @doc 加载遗嘱消息持久化模块（无参数版本）
%% 这个函数是模块的主要启动入口，负责初始化所有必要的资源和服务
%%
%% 功能说明：
%% 1. 从配置文件读取遗嘱消息持久化设置
%% 2. 检查遗嘱消息持久化功能是否启用
%% 3. 设置应用环境变量，保存配置参数
%% 4. 注册EMQX钩子，监听客户端事件
%% 5. 启动过期遗嘱消息清理定时器
%% 6. 记录模块加载状态日志
%%
%% 返回值：
%% - ok: 加载成功（无论是否启用）
%%
%% Java等价概念：
%% @PostConstruct
%% @ConditionalOnProperty(name = "will.persistence.enabled", havingValue = "true")
%% public void loadWillPersistenceModule() {
%%     WillPersistenceConfig config = configurationService.getWillPersistenceConfig();
%%     if (config.isEnabled()) {
%%         registerEventListeners();
%%         startCleanupScheduler();
%%         logger.info("Will persistence module loaded successfully");
%%     }
%% }

%% @doc 初始化遗嘱消息持久化模块
%% 这个函数负责模块的基础初始化工作，包括资源检查和集合创建
%%
%% 功能说明：
%% 1. 检查遗嘱消息持久化功能是否启用
%% 2. 等待MongoDB资源就绪
%% 3. 确保必要的集合和索引存在
%% 4. 启动清理定时器
%%
%% 设计特点：
%% - 条件初始化：只有在启用遗嘱消息持久化时才执行初始化
%% - 资源等待：确保MongoDB资源可用后再进行后续操作
%% - 完整设置：一次性完成所有必要的初始化工作
init() ->
    %% 记录模块初始化开始的日志
    %% 这有助于系统启动过程的跟踪和调试
    ?SLOG(info, #{msg => "initializing_will_message_persistence_module"}),

    %% 检查遗嘱消息持久化功能是否启用
    %% 这是一个功能开关，允许运行时控制遗嘱消息持久化
    %% 在Java中相当于：
    %% @ConditionalOnProperty(name = "will.persistence.enabled", havingValue = "true")
    case application:get_env(emqx_plugin_mongodb, will_persistence_enabled, false) of
        true ->
            %% 遗嘱消息持久化已启用，开始初始化所有组件
            ?SLOG(info, #{msg => "will_persistence_enabled", module => ?MODULE}),

            %% 等待MongoDB资源就绪
            %% 确保数据库连接可用后再进行后续操作
            %% 在Java中相当于：
            %% @Autowired private MongoHealthIndicator mongoHealth;
            %% while (!mongoHealth.isHealthy()) { Thread.sleep(1000); }
            wait_for_resource(),

            %% 确保遗嘱消息相关的集合存在
            %% 创建必要的MongoDB集合和索引
            %% 在Java中相当于：
            %% mongoTemplate.createCollection(collectionName);
            %% createIndexes(collectionName);
            ensure_collections(),

            %% 启动过期遗嘱消息清理定时器
            %% 定期清理过期的持久化遗嘱消息
            %% 在Java中相当于：
            %% @Scheduled(fixedDelay = 300000)
            %% public void cleanupExpiredWillMessages() { ... }
            start_cleanup_timer();
        false ->
            %% 遗嘱消息持久化未启用，跳过初始化
            %% 记录信息日志，说明功能被禁用
            ?SLOG(info, #{msg => "will_persistence_disabled", module => ?MODULE})
    end,
    %% 无论是否启用，都返回ok，确保模块加载不会失败
    ok.

load() ->
    try
        %% 第一步：从配置中读取遗嘱消息持久化设置
        %% 获取完整的插件配置，然后提取遗嘱消息相关配置
        %% 在Java中相当于：
        %% @Autowired private ConfigurationService configurationService;
        %% WillPersistenceConfig config = configurationService.getWillPersistenceConfig();
        Config = emqx_plugin_mongodb:get_config(),
        WillPersistenceConfig = maps:get(will_persistence, Config, #{}),
        WillPersistenceEnabled = maps:get(enabled, WillPersistenceConfig, false),

        %% 记录配置检查日志，便于调试和监控
        ?SLOG(info, #{
            msg => "checking_will_persistence_config",
            enabled => WillPersistenceEnabled,
            config => WillPersistenceConfig
        }),

        %% 第二步：根据配置决定是否启用遗嘱消息持久化
        %% 在Java中相当于：
        %% @ConditionalOnProperty(name = "will.persistence.enabled", havingValue = "true")
        case WillPersistenceEnabled of
            true ->
                %% 遗嘱消息持久化已启用，开始初始化所有组件
                ?SLOG(info, #{msg => "loading_will_persistence_module"}),

                %% 第三步：保存配置到应用环境变量
                %% 将配置参数保存到Erlang应用环境中，供其他函数使用
                %% 在Java中相当于：
                %% @Value("${will.persistence.enabled:false}") private boolean enabled;
                application:set_env(emqx_plugin_mongodb, will_persistence_enabled, true),
                application:set_env(emqx_plugin_mongodb, will_expiry,
                                   parse_time_duration(maps:get(will_expiry, WillPersistenceConfig, <<"7d">>))),
                application:set_env(emqx_plugin_mongodb, will_cleanup_interval,
                                   parse_time_duration(maps:get(cleanup_interval, WillPersistenceConfig, <<"15m">>))),
                application:set_env(emqx_plugin_mongodb, will_cleanup_batch_size,
                                   maps:get(cleanup_batch_size, WillPersistenceConfig, 1000)),
                application:set_env(emqx_plugin_mongodb, will_collection,
                                   maps:get(collection, WillPersistenceConfig, <<"emqx_will_messages">>)),

                %% 第四步：注册EMQX钩子
                %% 注册客户端连接和断开事件的监听器
                %% 在Java中相当于：
                %% @EventListener public void onClientConnected(ClientConnectedEvent event) { ... }
                register_hooks(),

                %% 第五步：启动清理定时器
                %% 启动过期遗嘱消息的定时清理任务
                %% 在Java中相当于：
                %% @Scheduled(fixedDelay = cleanupInterval) public void cleanupExpiredWillMessages() { ... }
                start_cleanup_timer(),

                %% 第六步：说明遗嘱消息恢复机制
                %% 遗嘱消息恢复现在通过MongoDB连接事件触发，不再使用时间延迟
                %% 这样可以确保在MongoDB连接就绪后立即处理异常关闭的遗嘱消息
                ?SLOG(info, #{msg => "will_message_restoration_will_be_triggered_on_mongodb_connection"}),

                %% 记录模块加载成功日志
                ?SLOG(info, #{msg => "will_persistence_module_loaded"});
            false ->
                %% 遗嘱消息持久化未启用，跳过初始化
                %% 记录信息日志，说明功能被禁用
                ?SLOG(info, #{msg => "will_persistence_disabled"})
        end,
        %% 无论是否启用，都返回ok，确保模块加载不会失败
        ok
    catch
        E:R:S ->
            %% 捕获加载过程中的任何异常
            %% 记录详细的错误信息，包括堆栈跟踪
            %% 在Java中相当于：
            %% catch (Exception e) { logger.error("Error loading will persistence module", e); }
            ?SLOG(error, #{
                msg => "error_loading_will_persistence_module",
                error => E,
                reason => R,
                stacktrace => S
            }),
            %% 即使出现异常，也返回ok，避免影响整个插件的加载
            ok
    end.

%% @doc 加载遗嘱消息持久化模块（带配置参数版本）
load(Config) when is_map(Config) ->
    try
        WillPersistenceConfig = maps:get(will_persistence, Config, #{}),
        WillPersistenceEnabled = maps:get(enabled, WillPersistenceConfig, false),

        ?SLOG(info, #{
            msg => "checking_will_persistence_config_with_params",
            enabled => WillPersistenceEnabled,
            config => WillPersistenceConfig
        }),

        case WillPersistenceEnabled of
            true ->
                ?SLOG(info, #{msg => "loading_will_persistence_module_with_params"}),

                % 保存配置到应用环境变量
                application:set_env(emqx_plugin_mongodb, will_persistence_enabled, true),
                application:set_env(emqx_plugin_mongodb, will_expiry,
                                   parse_time_duration(maps:get(will_expiry, WillPersistenceConfig, <<"4h">>))),
                application:set_env(emqx_plugin_mongodb, will_cleanup_interval,
                                   parse_time_duration(maps:get(cleanup_interval, WillPersistenceConfig, <<"15m">>))),
                application:set_env(emqx_plugin_mongodb, will_cleanup_batch_size,
                                   maps:get(cleanup_batch_size, WillPersistenceConfig, 500)),
                application:set_env(emqx_plugin_mongodb, will_collection,
                                   maps:get(collection, WillPersistenceConfig, ?DEFAULT_WILL_MESSAGE_COLLECTION)),

                % 注册钩子
                register_hooks(),

                % 启动清理定时器
                start_cleanup_timer(),

                % 遗嘱消息恢复现在通过MongoDB连接事件触发，不再使用时间延迟
                ?SLOG(info, #{msg => "will_message_restoration_will_be_triggered_on_mongodb_connection"}),

                ?SLOG(info, #{msg => "will_persistence_module_loaded_successfully_with_params"}),
                ok;
            false ->
                ?SLOG(info, #{msg => "will_persistence_disabled_skipping_load_with_params"}),
                ok
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_loading_will_persistence_module_with_params",
                error => E,
                reason => R,
                stacktrace => S
            }),
            ok
    end.

%% @doc 卸载遗嘱消息持久化模块
unload() ->
    case application:get_env(emqx_plugin_mongodb, will_persistence_enabled, false) of
        true ->
            ?SLOG(info, #{msg => "unloading_will_persistence_module"}),

            % 注销钩子
            unregister_hooks(),

            % 停止清理定时器
            stop_cleanup_timer(),

            % 清理应用环境变量 - 使用安全的异步方式
            safe_unset_env_variables([
                will_persistence_enabled,
                will_expiry,
                will_cleanup_interval,
                will_cleanup_batch_size,
                will_collection
            ]),

            ?SLOG(info, #{msg => "will_persistence_module_unloaded"});
        false ->
            ok
    end.

%%--------------------------------------------------------------------
%% 钩子管理
%%--------------------------------------------------------------------

%% @doc 注册遗嘱消息相关钩子
%%
%% 功能说明：
%% 1. client.connected - 监听客户端连接，保存遗嘱消息
%% 2. client.disconnected - 监听客户端断开，处理遗嘱消息逻辑
%% 3. message.publish - 监听消息发布，检测遗嘱消息发布并清理数据库
%%
%% Java等价概念：
%% @EventListener(ClientConnectedEvent.class)
%% @EventListener(ClientDisconnectedEvent.class)
%% @EventListener(MessagePublishEvent.class)
register_hooks() ->
    %% 客户端连接完成钩子 - 监听遗嘱消息设置事件，并检查是否需要恢复该客户端的遗嘱消息
    %% 优先级50，确保在会话持久化(100)之前执行，这样会话模块可以检测到遗嘱消息
    emqx_hooks:add('client.connected', {?MODULE, on_client_connected, []}, 50),

    %% 客户端断开钩子 - 监听客户端断开事件，处理遗嘱消息逻辑
    %% 优先级150，确保在会话持久化(100)之后执行，避免冲突
    emqx_hooks:add('client.disconnected', {?MODULE, on_client_disconnected, []}, 150),

    %% 消息发布钩子 - 监听遗嘱消息的发布，发布后删除数据库记录
    %% 优先级200，确保在其他消息处理之后执行，避免影响消息发布性能
    %% 这是修复遗嘱消息持久化逻辑的关键钩子
    emqx_hooks:add('message.publish', {?MODULE, on_message_publish, []}, 200).

%% @doc 注销遗嘱消息相关钩子
unregister_hooks() ->
    emqx_hooks:del('client.connected', {?MODULE, on_client_connected}),
    emqx_hooks:del('client.disconnected', {?MODULE, on_client_disconnected}),
    emqx_hooks:del('message.publish', {?MODULE, on_message_publish}).

%%--------------------------------------------------------------------
%% 钩子回调函数
%%--------------------------------------------------------------------

%% @doc 客户端连接钩子回调 - 监听遗嘱消息设置并持久化到MongoDB
on_client_connected(ClientInfo, ConnInfo) ->
    ClientId = maps:get(clientid, ClientInfo, <<>>),

    ?SLOG(info, #{
        msg => "client_connected_will_monitoring_debug",
        client_id => ClientId,
        client_info_keys => maps:keys(ClientInfo),
        conn_info_keys => maps:keys(ConnInfo),
        client_info => ClientInfo,
        conn_info => ConnInfo
    }),

    % 异步获取遗嘱消息，避免阻塞连接过程
    spawn(fun() ->
        try
            %% 修复：首先检查ConnInfo中是否直接包含遗嘱消息
            %% 根据MQTT协议，遗嘱消息在CONNECT包中传递，应该在ConnInfo中
            case maps:get(will_msg, ConnInfo, undefined) of
                undefined ->
                    %% ConnInfo中没有遗嘱消息，尝试从通道管理器获取
                    ?SLOG(info, #{
                        msg => "no_will_message_in_conn_info_trying_channel_manager",
                        client_id => ClientId
                    }),

                    % 等待一小段时间，确保通道完全建立
                    timer:sleep(100),

                    % 尝试从通道管理器获取通道信息
                    case emqx_cm:get_chan_info(ClientId) of
                        undefined ->
                            ?SLOG(info, #{
                                msg => "no_channel_info_found_for_will_monitoring_debug",
                                client_id => ClientId
                            }),
                            %% 没有遗嘱消息，删除数据库中的旧记录（如果存在）
                            handle_reconnection_without_will_message_atomic(ClientId);
                        ChanInfo ->
                            ?SLOG(info, #{
                                msg => "channel_info_found_for_will_monitoring_debug",
                                client_id => ClientId,
                                chan_info_keys => maps:keys(ChanInfo)
                            }),

                            %% 尝试获取当前连接的遗嘱消息
                            case maps:get(will_msg, ChanInfo, undefined) of
                                undefined ->
                                    %% 当前连接没有携带遗嘱消息
                                    ?SLOG(debug, #{
                                        msg => "no_will_message_in_current_connection",
                                        client_id => ClientId,
                                        note => "client_reconnected_without_will_message"
                                    }),
                                    %% 客户端重连时没有携带遗嘱消息
                                    %% 删除数据库中的旧遗嘱消息记录（如果存在）
                                    %% 重要：这里绝不发布遗嘱消息，因为客户端已经重连
                                    %%
                                    %% 竞态条件保护：
                                    %% 此操作可能与EMQX重启后的智能处理线程并发执行
                                    %% 通过原子性删除操作确保只有一个线程能处理遗嘱消息
                                    handle_reconnection_without_will_message_atomic(ClientId);
                                WillMsg when is_map(WillMsg) ->
                                    %% 客户端携带了新的遗嘱消息（map格式）
                                    ?SLOG(info, #{
                                        msg => "new_will_message_detected_for_persistence_from_channel",
                                        client_id => ClientId,
                                        will_msg_type => "map"
                                    }),
                                    %% 保存新的遗嘱消息到数据库（会覆盖旧的）
                                    store_will_message_to_mongodb_from_map(ClientInfo, WillMsg);
                                WillMsg ->
                                    %% 客户端携带了新的遗嘱消息（message记录格式）
                                    ?SLOG(info, #{
                                        msg => "new_will_message_detected_for_persistence_from_channel",
                                        client_id => ClientId,
                                        will_msg_type => "record",
                                        will_topic => emqx_message:topic(WillMsg)
                                    }),
                                    %% 保存新的遗嘱消息到数据库（会覆盖旧的）
                                    store_will_message_to_mongodb(ClientInfo, WillMsg)
                            end
                    end;
                WillMsg when is_map(WillMsg) ->
                    %% ConnInfo中找到遗嘱消息（map格式）
                    ?SLOG(info, #{
                        msg => "will_message_found_in_conn_info",
                        client_id => ClientId,
                        will_msg_type => "map",
                        will_msg_keys => maps:keys(WillMsg)
                    }),
                    %% 保存新的遗嘱消息到数据库（会覆盖旧的）
                    store_will_message_to_mongodb_from_map(ClientInfo, WillMsg);
                WillMsg ->
                    %% ConnInfo中找到遗嘱消息（message记录格式）
                    ?SLOG(info, #{
                        msg => "will_message_found_in_conn_info",
                        client_id => ClientId,
                        will_msg_type => "record",
                        will_topic => emqx_message:topic(WillMsg)
                    }),
                    %% 保存新的遗嘱消息到数据库（会覆盖旧的）
                    store_will_message_to_mongodb(ClientInfo, WillMsg)
            end
        catch
            E:R:S ->
                ?SLOG(error, #{
                    msg => "error_monitoring_will_message",
                    client_id => ClientId,
                    error => E,
                    reason => R,
                    stacktrace => S
                })
        end
    end),
    ok.





%% @doc 客户端断开钩子回调 - 正确的遗嘱消息处理逻辑
%%
%% 功能说明：
%% 1. 正常断开：保留遗嘱消息在数据库中，等待客户端重连时恢复
%% 2. 异常断开：让EMQX自动发布遗嘱消息，通过消息发布钩子删除数据库记录
%%
%% 这是MQTT协议的正确实现：
%% - 正常断开时遗嘱消息不发布，但应保留以备重连使用
%% - 异常断开时EMQX发布遗嘱消息，插件通过消息钩子检测并清理数据库
%%
%% Java等价概念：
%% @EventListener
%% public void onClientDisconnected(ClientDisconnectedEvent event) {
%%     if (event.isNormalDisconnection()) {
%%         // 正常断开，保留遗嘱消息，不删除
%%         logger.info("Normal disconnect, keeping will message for reconnection");
%%     } else {
%%         // 异常断开，EMQX会发布遗嘱消息，等待消息发布钩子处理
%%         logger.info("Abnormal disconnect, EMQX will publish will message");
%%     }
%% }
on_client_disconnected(ClientInfo, Reason, ConnInfo) ->
    ClientId = maps:get(clientid, ClientInfo, <<>>),

    %% 异步处理断开逻辑，避免阻塞断开过程
    spawn(fun() ->
        try
            case find_will_message(ClientId) of
                {ok, _WillMsg} ->
                    %% 检查是否应该发布遗嘱消息
                    WillShouldBePublished = should_publish_will_message(Reason),
                    case WillShouldBePublished of
                        false ->
                            %% 正常断开：保留遗嘱消息在数据库中
                            %% 客户端可能会重连，重连时需要恢复遗嘱消息
                            %% 这是MQTT协议的正确行为
                            ?SLOG(info, #{
                                msg => "normal_disconnect_keeping_will_message_for_reconnection",
                                client_id => ClientId,
                                reason => Reason
                            });
                        true ->
                            %% 异常断开：EMQX会自动发布遗嘱消息
                            %% 插件不主动发布，只等待EMQX的内置遗嘱消息功能处理
                            %% 通过message.publish钩子检测遗嘱消息发布并删除数据库记录
                            ?SLOG(info, #{
                                msg => "abnormal_disconnect_detected_emqx_will_handle_will_message",
                                client_id => ClientId,
                                reason => Reason,
                                approach => "let_emqx_builtin_functionality_handle_will_message",
                                note => "will_message_will_be_deleted_when_published_by_emqx"
                            })
                    end;
                {error, not_found} ->
                    %% 没有找到遗嘱消息，无需处理
                    ?SLOG(debug, #{
                        msg => "no_will_message_found_on_disconnect",
                        client_id => ClientId
                    });
                {error, FindReason} ->
                    %% 查找遗嘱消息时出错
                    ?SLOG(error, #{
                        msg => "failed_to_find_will_message_on_disconnect",
                        client_id => ClientId,
                        reason => FindReason
                    })
            end
        catch
            E:R:S ->
                ?SLOG(error, #{
                    msg => "error_in_client_disconnected_handler",
                    client_id => ClientId,
                    error => E,
                    reason => R,
                    stacktrace => S
                })
        end
    end),
    ok.

%% @doc 会话终止钩子回调 - 清理遗嘱消息
on_session_terminated(ClientInfo, Reason, SessionInfo) ->
    ClientId = maps:get(clientid, ClientInfo, <<>>),

    % 会话终止时清理遗嘱消息
    spawn(fun() ->
        delete_will_message(ClientId)
    end),
    ok.

%% @doc 消息发布钩子回调 - 检测遗嘱消息发布并清理数据库
%% 这是修复遗嘱消息持久化逻辑的关键函数
%%
%% 功能说明：
%% 1. 监听所有消息发布事件
%% 2. 检测是否为遗嘱消息的发布
%% 3. 如果是遗嘱消息，从数据库中删除对应的记录
%% 4. 确保遗嘱消息发布后数据库状态的一致性
%%
%% 参数说明：
%% - Message: EMQX消息对象，包含消息的所有信息
%%
%% 返回值：
%% - Message: 返回原始消息，不修改消息内容
%%
%% Java等价概念：
%% @EventListener
%% @Async
%% public Message onMessagePublish(MessagePublishEvent event) {
%%     Message message = event.getMessage();
%%     if (isWillMessage(message)) {
%%         String clientId = extractClientIdFromWillMessage(message);
%%         willMessageRepository.deleteByClientId(clientId);
%%         logger.info("Will message published and deleted from database: {}", clientId);
%%     }
%%     return message;
%% }
%%
%% 设计说明：
%% - 异步处理：使用spawn避免阻塞消息发布流程
%% - 错误隔离：单个遗嘱消息处理失败不影响其他消息
%% - 日志记录：详细记录遗嘱消息的处理过程
on_message_publish(Message) ->
    %% 异步处理遗嘱消息检测和清理，避免阻塞消息发布流程
    spawn(fun() ->
        try
            %% 检查是否为恢复的消息，如果是则跳过遗嘱消息处理
            case emqx_message:get_header(skip_persistence, Message, false) of
                true ->
                    %% 这是恢复的消息，跳过遗嘱消息处理避免重复删除
                    ?SLOG(debug, #{
                        msg => "skipping_recovered_message_will_processing",
                        topic => emqx_message:topic(Message),
                        reason => "message_already_processed_during_recovery"
                    }),
                    ok;
                false ->
                    %% 检查是否为遗嘱消息
                    case is_will_message_publish(Message) of
                true ->
                    %% 这是一个遗嘱消息的发布
                    %% 需要从数据库中删除对应的遗嘱消息记录

                    %% 从消息中提取客户端ID和遗嘱消息ID
                    %% 遗嘱消息的发布者ID就是原始客户端ID
                    PublisherId = emqx_message:from(Message),
                    Topic = emqx_message:topic(Message),

                    %% 关键修复：从消息头部获取遗嘱消息ID，用于精确删除
                    case emqx_message:get_header(will_message_id, Message, undefined) of
                        undefined ->
                            %% 关键修复：没有will_message_id头部的消息不应该删除遗嘱消息记录
                            %% 这可能是EMQX内置机制发布的遗嘱消息，或者是其他来源的消息
                            %% 为了避免误删，我们不进行任何删除操作
                            ?SLOG(warning, #{
                                msg => "will_message_published_no_id_header_skipping_delete",
                                client_id => PublisherId,
                                topic => Topic,
                                reason => "no_will_message_id_header_may_not_be_from_plugin",
                                note => "skipping_delete_to_prevent_accidental_removal_of_valid_will_messages"
                            });
                        WillMessageId ->
                            %% 新版本：使用遗嘱消息ID进行精确删除
                            ?SLOG(info, #{
                                msg => "will_message_published_deleting_by_id_from_database",
                                client_id => PublisherId,
                                topic => Topic,
                                will_message_id => WillMessageId,
                                note => "using_precise_id_delete_prevents_race_condition"
                            }),
                            delete_will_message_by_id(WillMessageId)
                    end,

                    ?SLOG(info, #{
                        msg => "will_message_database_record_deleted_after_publish",
                        client_id => PublisherId,
                        topic => Topic
                    });
                false ->
                    %% 不是遗嘱消息，无需处理
                    %% 为了避免日志过多，这里不记录debug日志
                    ok
                    end
            end
        catch
            E:R:S ->
                %% 捕获处理过程中的任何异常
                %% 确保单个消息的处理失败不影响整个系统
                ?SLOG(error, #{
                    msg => "error_in_message_publish_will_handler",
                    error => E,
                    reason => R,
                    stacktrace => S,
                    message_topic => emqx_message:topic(Message),
                    message_from => emqx_message:from(Message)
                })
        end
    end),
    %% 返回原始消息，不修改消息内容
    %% 这确保消息发布流程不受影响
    Message.

%%--------------------------------------------------------------------
%% 遗嘱消息管理函数
%%--------------------------------------------------------------------

%% @doc 存储遗嘱消息到MongoDB
store_will_message_to_mongodb(ClientInfo, WillMsg) ->
    try
        ClientId = maps:get(clientid, ClientInfo, <<>>),
        Username = maps:get(username, ClientInfo, <<>>),
        PeerHostRaw = maps:get(peerhost, ClientInfo, <<>>),
        PeerHost = format_peerhost(PeerHostRaw),

        % 获取遗嘱消息属性
        Topic = emqx_message:topic(WillMsg),
        Payload = emqx_message:payload(WillMsg),
        QoS = emqx_message:qos(WillMsg),
        Retain = emqx_message:get_flag(retain, WillMsg, false),
        Properties = emqx_message:get_header(properties, WillMsg, #{}),

        % 解析MQTT 5.0遗嘱消息属性
        WillDelayInterval = extract_will_delay_interval(Properties),
        MessageExpiryInterval = extract_message_expiry_interval(Properties),
        PayloadFormatIndicator = extract_payload_format_indicator(Properties),
        ContentType = extract_content_type(Properties),
        ResponseTopic = extract_response_topic(Properties),
        CorrelationData = extract_correlation_data(Properties),
        UserProperties = extract_user_properties(Properties),

        % 计算遗嘱消息过期时间（优先使用消息自身的过期设置）
        ExpiryTime = calculate_will_message_expiry_time(MessageExpiryInterval),

        % 计算遗嘱消息发布时间（考虑Will Delay Interval）
        PublishTime = calculate_will_publish_time(WillDelayInterval),

        % 构建符合MQTT协议的遗嘱消息文档
        WillDoc = #{
            <<"_id">> => generate_will_id(ClientId),
            <<"client_id">> => ClientId,
            <<"username">> => Username,
            <<"peerhost">> => PeerHost,
            <<"topic">> => Topic,
            <<"payload">> => Payload,
            <<"qos">> => QoS,
            <<"retain">> => Retain,

            % MQTT 5.0遗嘱消息属性
            <<"will_delay_interval">> => WillDelayInterval,
            <<"message_expiry_interval">> => MessageExpiryInterval,
            <<"payload_format_indicator">> => PayloadFormatIndicator,
            <<"content_type">> => ContentType,
            <<"response_topic">> => ResponseTopic,
            <<"correlation_data">> => CorrelationData,
            <<"user_properties">> => UserProperties,

            % 时间戳和过期时间
            <<"created_at">> => erlang:system_time(millisecond),
            <<"publish_time">> => PublishTime,
            <<"expiry_time">> => ExpiryTime,

            % 移除状态字段：存在于MongoDB中即表示待发布，不存在即表示已处理

            % 兼容性：保留原始属性
            <<"properties">> => encode_properties(Properties)
        },
        
        % 保存到MongoDB
        Collection = get_will_collection(),

        % 懒初始化：确保集合和索引存在
        ensure_will_collection_and_indexes(Collection),

        ?SLOG(info, #{
            msg => "saving_will_message_to_mongodb",
            client_id => ClientId,
            topic => Topic,
            collection => Collection,
            will_doc => WillDoc
        }),

        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                               {upsert, Collection, #{<<"client_id">> => ClientId}, WillDoc}) of
            {ok, _} ->
                ?SLOG(info, #{msg => "will_message_saved_successfully", client_id => ClientId, topic => Topic, collection => Collection});
            {async_return, ok} ->
                ?SLOG(info, #{msg => "will_message_saved_async_successfully", client_id => ClientId, topic => Topic, collection => Collection});
            {async_return, {ok, _}} ->
                ?SLOG(info, #{msg => "will_message_saved_async_successfully", client_id => ClientId, topic => Topic, collection => Collection});
            {async_return, {error, Reason}} ->
                ?SLOG(error, #{msg => "failed_to_save_will_message_async", client_id => ClientId, topic => Topic, collection => Collection, reason => Reason});
            {error, Reason} ->
                ?SLOG(error, #{msg => "failed_to_save_will_message", client_id => ClientId, topic => Topic, collection => Collection, reason => Reason})
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_saving_will_message",
                error => E,
                reason => R,
                stacktrace => S,
                client_id => maps:get(clientid, ClientInfo, <<>>)
            })
    end.

%% @doc 从map格式存储遗嘱消息到MongoDB
store_will_message_to_mongodb_from_map(ClientInfo, WillMsgMap) ->
    try
        ClientId = maps:get(clientid, ClientInfo, <<>>),
        Username = maps:get(username, ClientInfo, <<>>),
        PeerHostRaw = maps:get(peerhost, ClientInfo, <<>>),
        PeerHost = format_peerhost(PeerHostRaw),

        % 从map中提取遗嘱消息属性
        Topic = maps:get(topic, WillMsgMap, <<>>),
        Payload = maps:get(payload, WillMsgMap, <<>>),
        QoS = maps:get(qos, WillMsgMap, 0),
        Retain = maps:get(retain, WillMsgMap, false),
        Properties = maps:get(properties, WillMsgMap, #{}),

        % 解析MQTT 5.0遗嘱消息属性
        WillDelayInterval = extract_will_delay_interval(Properties),
        MessageExpiryInterval = extract_message_expiry_interval(Properties),
        PayloadFormatIndicator = extract_payload_format_indicator(Properties),
        ContentType = extract_content_type(Properties),
        ResponseTopic = extract_response_topic(Properties),
        CorrelationData = extract_correlation_data(Properties),
        UserProperties = extract_user_properties(Properties),

        % 计算遗嘱消息过期时间（优先使用消息自身的过期设置）
        ExpiryTime = calculate_will_message_expiry_time(MessageExpiryInterval),

        % 计算遗嘱消息发布时间（考虑Will Delay Interval）
        PublishTime = calculate_will_publish_time(WillDelayInterval),

        % 构建符合MQTT协议的遗嘱消息文档
        WillDoc = #{
            <<"_id">> => generate_will_id(ClientId),
            <<"client_id">> => ClientId,
            <<"username">> => Username,
            <<"peerhost">> => PeerHost,
            <<"topic">> => Topic,
            <<"payload">> => Payload,
            <<"qos">> => QoS,
            <<"retain">> => Retain,

            % MQTT 5.0遗嘱消息属性
            <<"will_delay_interval">> => WillDelayInterval,
            <<"message_expiry_interval">> => MessageExpiryInterval,
            <<"payload_format_indicator">> => PayloadFormatIndicator,
            <<"content_type">> => ContentType,
            <<"response_topic">> => ResponseTopic,
            <<"correlation_data">> => CorrelationData,
            <<"user_properties">> => UserProperties,

            % 时间戳和过期时间
            <<"created_at">> => erlang:system_time(millisecond),
            <<"publish_time">> => PublishTime,
            <<"expiry_time">> => ExpiryTime,

            % 移除状态字段：存在于MongoDB中即表示待发布，不存在即表示已处理

            % 兼容性：保留原始属性
            <<"properties">> => encode_properties(Properties)
        },

        % 保存到MongoDB
        Collection = get_will_collection(),

        % 懒初始化：确保集合和索引存在
        ensure_will_collection_and_indexes(Collection),

        ?SLOG(info, #{
            msg => "saving_will_message_from_map_to_mongodb",
            client_id => ClientId,
            topic => Topic,
            collection => Collection
        }),

        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                               {upsert, Collection, #{<<"client_id">> => ClientId}, WillDoc}) of
            {ok, _} ->
                ?SLOG(info, #{msg => "will_message_saved_successfully_from_map", client_id => ClientId, topic => Topic});
            {async_return, ok} ->
                ?SLOG(info, #{msg => "will_message_saved_async_successfully_from_map", client_id => ClientId, topic => Topic});
            {async_return, {ok, _}} ->
                ?SLOG(info, #{msg => "will_message_saved_async_successfully_from_map", client_id => ClientId, topic => Topic});
            {async_return, {error, Reason}} ->
                ?SLOG(error, #{msg => "failed_to_save_will_message_from_map_async", client_id => ClientId, topic => Topic, reason => Reason});
            {error, Reason} ->
                ?SLOG(error, #{msg => "failed_to_save_will_message_from_map", client_id => ClientId, topic => Topic, reason => Reason})
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_saving_will_message_from_map",
                error => E,
                reason => R,
                stacktrace => S,
                client_id => maps:get(clientid, ClientInfo, <<>>)
            })
    end.

%% @doc 删除遗嘱消息
delete_will_message(ClientId) ->
    try
        Collection = get_will_collection(),
        % 确保客户端ID为binary格式以匹配MongoDB中的存储格式
        ClientIdBin = case ClientId of
            Bin when is_binary(Bin) -> Bin;
            List when is_list(List) -> list_to_binary(List);
            _ -> ClientId
        end,
        Filter = #{<<"client_id">> => ClientIdBin},
        
        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                               {delete, Collection, Filter}) of
            {ok, _} ->
                ?SLOG(debug, #{msg => "will_message_deleted", client_id => ClientId});
            {async_return, ok} ->
                ?SLOG(debug, #{msg => "will_message_deleted_async", client_id => ClientId});
            {async_return, {ok, _}} ->
                ?SLOG(debug, #{msg => "will_message_deleted_async", client_id => ClientId});
            {async_return, {error, Reason}} ->
                ?SLOG(warning, #{msg => "failed_to_delete_will_message_async", client_id => ClientId, reason => Reason});
            {error, Reason} ->
                ?SLOG(warning, #{msg => "failed_to_delete_will_message", client_id => ClientId, reason => Reason})
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_deleting_will_message",
                error => E,
                reason => R,
                stacktrace => S,
                client_id => ClientId
            })
    end.

%% @doc 查找遗嘱消息
find_will_message(ClientId) ->
    try
        Collection = get_will_collection(),
        % 确保客户端ID为binary格式以匹配MongoDB中的存储格式
        ClientIdBin = case ClientId of
            Bin when is_binary(Bin) -> Bin;
            List when is_list(List) -> list_to_binary(List);
            _ -> ClientId
        end,
        Filter = #{<<"client_id">> => ClientIdBin},

        ?SLOG(debug, #{
            msg => "searching_will_message",
            client_id => ClientId,
            client_id_bin => ClientIdBin,
            collection => Collection,
            filter => Filter
        }),

        QueryResult = emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                               {find, Collection, Filter, #{}, 0, 1}),

        ?SLOG(debug, #{
            msg => "will_message_query_result",
            client_id => ClientId,
            query_result => QueryResult
        }),

        case QueryResult of
            {ok, []} ->
                ?SLOG(debug, #{
                    msg => "will_message_not_found_empty_result",
                    client_id => ClientId
                }),
                {error, not_found};
            {ok, [WillDoc]} ->
                ?SLOG(debug, #{
                    msg => "will_message_found",
                    client_id => ClientId,
                    will_doc => WillDoc
                }),
                % 解析遗嘱消息文档
                WillMsg = parse_will_document(WillDoc),
                {ok, WillMsg};
            {async_return, ok} ->
                % 异步查询已提交，尝试同步查询获取实际结果
                ?SLOG(debug, #{
                    msg => "will_message_async_return_ok_fallback_to_sync",
                    client_id => ClientId
                }),
                % 使用支持的find操作，限制返回1个文档
                case emqx_resource:simple_sync_query(?PLUGIN_MONGODB_RESOURCE_ID,
                                                    {find, Collection, Filter, #{}, 0, 1}) of
                    {ok, []} ->
                        ?SLOG(debug, #{
                            msg => "will_message_sync_fallback_not_found",
                            client_id => ClientId
                        }),
                        {error, not_found};
                    {ok, [WillDoc]} when is_map(WillDoc) ->
                        ?SLOG(debug, #{
                            msg => "will_message_sync_fallback_found",
                            client_id => ClientId,
                            will_doc => WillDoc
                        }),
                        WillMsg = parse_will_document(WillDoc),
                        {ok, WillMsg};
                    {ok, [WillDoc | _]} when is_map(WillDoc) ->
                        % 如果返回多个文档，取第一个
                        ?SLOG(debug, #{
                            msg => "will_message_sync_fallback_found_multiple",
                            client_id => ClientId,
                            will_doc => WillDoc
                        }),
                        WillMsg = parse_will_document(WillDoc),
                        {ok, WillMsg};
                    {error, Reason} ->
                        ?SLOG(error, #{
                            msg => "will_message_sync_fallback_error",
                            client_id => ClientId,
                            reason => Reason
                        }),
                        {error, Reason};
                    Other ->
                        ?SLOG(warning, #{
                            msg => "will_message_sync_fallback_unexpected",
                            client_id => ClientId,
                            result => Other
                        }),
                        {error, not_found}
                end;
            {async_return, {ok, []}} ->
                ?SLOG(debug, #{
                    msg => "will_message_async_return_empty",
                    client_id => ClientId
                }),
                {error, not_found};
            {async_return, {ok, [WillDoc]}} ->
                ?SLOG(debug, #{
                    msg => "will_message_async_return_found",
                    client_id => ClientId,
                    will_doc => WillDoc
                }),
                WillMsg = parse_will_document(WillDoc),
                {ok, WillMsg};
            {async_return, {error, Reason}} ->
                ?SLOG(debug, #{
                    msg => "will_message_async_return_error",
                    client_id => ClientId,
                    reason => Reason
                }),
                {error, Reason};
            {error, Reason} ->
                ?SLOG(debug, #{
                    msg => "will_message_query_error",
                    client_id => ClientId,
                    reason => Reason
                }),
                {error, Reason};
            Other ->
                ?SLOG(debug, #{
                    msg => "will_message_unexpected_result",
                    client_id => ClientId,
                    result => Other
                }),
                {error, {unexpected_result, Other}}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_finding_will_message",
                error => E,
                reason => R,
                stacktrace => S,
                client_id => ClientId
            }),
            {error, {E, R}}
    end.



%% @doc 清理过期的遗嘱消息
cleanup_expired_will_messages() ->
    spawn(fun() ->
        do_cleanup_expired_will_messages()
    end).

%% @doc 执行过期遗嘱消息清理
do_cleanup_expired_will_messages() ->
    try
        Collection = get_will_collection(),
        CurrentTime = erlang:system_time(millisecond),

        % 查找过期的遗嘱消息
        Filter = #{
            <<"expiry_time">> => #{<<"$lt">> => CurrentTime}
        },

        BatchSize = application:get_env(emqx_plugin_mongodb, will_cleanup_batch_size, 1000),

        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                               {find, Collection, Filter, #{}, 0, BatchSize}) of
            {ok, ExpiredWills} ->
                cleanup_expired_wills(ExpiredWills);
            {async_return, {ok, ExpiredWills}} ->
                cleanup_expired_wills(ExpiredWills);
            {async_return, {error, Reason}} ->
                ?SLOG(error, #{msg => "failed_to_find_expired_wills_async", reason => Reason});
            {error, Reason} ->
                ?SLOG(error, #{msg => "failed_to_find_expired_wills", reason => Reason})
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_cleaning_expired_will_messages",
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% @doc 清理过期的遗嘱消息列表
cleanup_expired_wills([]) ->
    ok;
cleanup_expired_wills(ExpiredWills) ->
    Collection = get_will_collection(),
    ClientIds = [maps:get(<<"client_id">>, Will) || Will <- ExpiredWills],

    ?SLOG(info, #{
        msg => "cleaning_expired_will_messages",
        count => length(ExpiredWills),
        client_ids => ClientIds
    }),

    % 批量删除过期的遗嘱消息
    Filter = #{
        <<"client_id">> => #{<<"$in">> => ClientIds}
    },

    case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                           {delete, Collection, Filter}) of
        {ok, _} ->
            ?SLOG(info, #{msg => "expired_will_messages_cleaned", count => length(ExpiredWills)});
        {async_return, ok} ->
            ?SLOG(info, #{msg => "expired_will_messages_cleaned_async", count => length(ExpiredWills)});
        {async_return, {ok, _}} ->
            ?SLOG(info, #{msg => "expired_will_messages_cleaned_async", count => length(ExpiredWills)});
        {async_return, {error, Reason}} ->
            ?SLOG(error, #{msg => "failed_to_clean_expired_wills_async", reason => Reason});
        {error, Reason} ->
            ?SLOG(error, #{msg => "failed_to_clean_expired_wills", reason => Reason})
    end.

%%--------------------------------------------------------------------
%% 资源管理和初始化
%%--------------------------------------------------------------------

%% @doc 等待MongoDB资源就绪
%% 确保数据库连接可用后再进行后续操作
wait_for_resource() ->
    wait_for_resource(10).

wait_for_resource(0) ->
    ?SLOG(warning, #{msg => "mongodb_resource_not_ready_after_retries"}),
    ok;
wait_for_resource(Retries) ->
    % 使用健康检查而不是get_instance和ping查询
    case emqx_resource:health_check(?PLUGIN_MONGODB_RESOURCE_ID) of
        ok ->
            ?SLOG(info, #{msg => "mongodb_resource_ready"}),
            ok;
        {ok, _} ->
            ?SLOG(info, #{msg => "mongodb_resource_ready"}),
            ok;
        _ ->
            ?SLOG(warning, #{msg => "mongodb_resource_not_ready", retries_left => Retries}),
            timer:sleep(1000),
            wait_for_resource(Retries - 1)
    end.

%% @doc 确保遗嘱消息相关的集合存在
%% 创建必要的MongoDB集合和索引
ensure_collections() ->
    try
        % 获取集合名称
        WillMessageCollection = get_will_collection(),

        ?SLOG(info, #{
            msg => "ensuring_will_message_collections",
            will_message_collection => WillMessageCollection
        }),

        % 统一集合创建策略：主动创建集合和索引，与保留消息模块保持一致
        % 这样确保所有集合在插件启动时就存在，便于管理和调试
        ensure_will_collection_and_indexes(WillMessageCollection),

        ?SLOG(info, #{msg => "will_message_collections_ensured",
                     will_message_collection => WillMessageCollection})
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_ensuring_will_message_collections",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {ensure_collections_failed, R}}
    end.

%%--------------------------------------------------------------------
%% 定时器管理
%%--------------------------------------------------------------------

%% @doc 启动清理定时器
start_cleanup_timer() ->
    CleanupInterval = application:get_env(emqx_plugin_mongodb, will_cleanup_interval, 900000), % 15分钟
    % 使用独立进程处理定时清理，避免进程依赖问题
    Pid = spawn(fun() -> cleanup_timer_loop(CleanupInterval) end),
    application:set_env(emqx_plugin_mongodb, will_cleanup_timer_pid, Pid),
    ?SLOG(debug, #{msg => "will_cleanup_timer_started", interval => CleanupInterval}).

%% @doc 清理定时器循环
cleanup_timer_loop(Interval) ->
    timer:sleep(Interval),
    ?SLOG(info, #{msg => "cleaning_expired_will_messages"}),
    % 执行清理
    spawn(fun() -> cleanup_expired_will_messages() end),
    % 继续循环
    cleanup_timer_loop(Interval).

%% @doc 停止清理定时器
stop_cleanup_timer() ->
    try
        case application:get_env(emqx_plugin_mongodb, will_cleanup_timer_pid) of
            {ok, Pid} when is_pid(Pid) ->
                case is_process_alive(Pid) of
                    true ->
                        exit(Pid, normal),
                        % 使用异步方式清除环境变量，避免超时
                        % 使用安全的环境变量清理
                        safe_unset_env_async(will_cleanup_timer_pid),
                        ?SLOG(debug, #{msg => "will_cleanup_timer_stopped"});
                    false ->
                        % 进程已死，使用安全的环境变量清理
                        safe_unset_env_async(will_cleanup_timer_pid),
                        ?SLOG(debug, #{msg => "will_cleanup_timer_already_stopped"})
                end;
            _ ->
                ?SLOG(debug, #{msg => "no_will_cleanup_timer_to_stop"})
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_stopping_will_cleanup_timer",
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%%--------------------------------------------------------------------
%% 辅助函数
%%--------------------------------------------------------------------

%% @doc 获取遗嘱消息集合名称
get_will_collection() ->
    application:get_env(emqx_plugin_mongodb, will_collection, <<"emqx_will_messages">>).

%% @doc 判断是否应该发布遗嘱消息
should_publish_will_message(normal) -> false;
should_publish_will_message(shutdown) -> false;
should_publish_will_message(kicked) -> true;
should_publish_will_message(takenover) -> false;
should_publish_will_message(discarded) -> true;
should_publish_will_message(_) -> true.

%% @doc 生成遗嘱消息ID
%% 修复：使用固定的ID生成策略，确保同一客户端的遗嘱消息ID保持一致
%% 这样upsert操作才能正确工作，避免重复记录
generate_will_id(ClientId) ->
    % 使用客户端ID作为基础，生成固定的遗嘱消息ID
    % 这确保同一客户端的遗嘱消息始终使用相同的ID
    iolist_to_binary([<<"will_">>, ClientId]).

%% @doc 编码属性
encode_properties(Properties) when is_map(Properties) ->
    Properties;
encode_properties(_) ->
    #{}.

%%--------------------------------------------------------------------
%% MQTT 5.0遗嘱消息属性解析函数
%%--------------------------------------------------------------------

%% @doc 提取Will Delay Interval属性
%% MQTT 5.0协议：指定遗嘱消息发布的延迟时间（秒）
extract_will_delay_interval(Properties) when is_map(Properties) ->
    case maps:get('Will-Delay-Interval', Properties,
                  maps:get(<<"Will-Delay-Interval">>, Properties,
                          maps:get(will_delay_interval, Properties, 0))) of
        Interval when is_integer(Interval), Interval >= 0 -> Interval;
        _ -> 0
    end;
extract_will_delay_interval(_) -> 0.

%% @doc 提取Message Expiry Interval属性
%% MQTT 5.0协议：指定遗嘱消息在发布后的有效期（秒）
extract_message_expiry_interval(Properties) when is_map(Properties) ->
    case maps:get('Message-Expiry-Interval', Properties,
                  maps:get(<<"Message-Expiry-Interval">>, Properties,
                          maps:get(message_expiry_interval, Properties, undefined))) of
        Interval when is_integer(Interval), Interval > 0 -> Interval;
        _ -> undefined
    end;
extract_message_expiry_interval(_) -> undefined.

%% @doc 提取Payload Format Indicator属性
%% MQTT 5.0协议：指示载荷格式（0=字节，1=UTF-8字符串）
extract_payload_format_indicator(Properties) when is_map(Properties) ->
    case maps:get('Payload-Format-Indicator', Properties,
                  maps:get(<<"Payload-Format-Indicator">>, Properties,
                          maps:get(payload_format_indicator, Properties, 0))) of
        Indicator when Indicator =:= 0; Indicator =:= 1 -> Indicator;
        _ -> 0
    end;
extract_payload_format_indicator(_) -> 0.

%% @doc 提取Content Type属性
%% MQTT 5.0协议：描述载荷内容的MIME类型
extract_content_type(Properties) when is_map(Properties) ->
    case maps:get('Content-Type', Properties,
                  maps:get(<<"Content-Type">>, Properties,
                          maps:get(content_type, Properties, <<>>))) of
        ContentType when is_binary(ContentType) -> ContentType;
        ContentType when is_list(ContentType) -> list_to_binary(ContentType);
        _ -> <<>>
    end;
extract_content_type(_) -> <<>>.

%% @doc 提取Response Topic属性
%% MQTT 5.0协议：指定响应消息的主题
extract_response_topic(Properties) when is_map(Properties) ->
    case maps:get('Response-Topic', Properties,
                  maps:get(<<"Response-Topic">>, Properties,
                          maps:get(response_topic, Properties, <<>>))) of
        Topic when is_binary(Topic) -> Topic;
        Topic when is_list(Topic) -> list_to_binary(Topic);
        _ -> <<>>
    end;
extract_response_topic(_) -> <<>>.

%% @doc 提取Correlation Data属性
%% MQTT 5.0协议：用于请求/响应模式的关联数据
extract_correlation_data(Properties) when is_map(Properties) ->
    case maps:get('Correlation-Data', Properties,
                  maps:get(<<"Correlation-Data">>, Properties,
                          maps:get(correlation_data, Properties, <<>>))) of
        Data when is_binary(Data) -> Data;
        Data when is_list(Data) -> list_to_binary(Data);
        _ -> <<>>
    end;
extract_correlation_data(_) -> <<>>.

%% @doc 提取User Properties属性
%% MQTT 5.0协议：用户自定义属性键值对
extract_user_properties(Properties) when is_map(Properties) ->
    case maps:get('User-Property', Properties,
                  maps:get(<<"User-Property">>, Properties,
                          maps:get(user_properties, Properties, #{}))) of
        UserProps when is_map(UserProps) -> UserProps;
        _ -> #{}
    end;
extract_user_properties(_) -> #{}.

%% @doc 计算遗嘱消息过期时间
%% 优先使用消息自身的Message Expiry Interval，否则使用插件配置
calculate_will_message_expiry_time(undefined) ->
    % 没有指定Message Expiry Interval，使用插件配置
    WillExpiry = application:get_env(emqx_plugin_mongodb, will_expiry, 604800000), % 7天
    erlang:system_time(millisecond) + WillExpiry;
calculate_will_message_expiry_time(MessageExpiryInterval) when is_integer(MessageExpiryInterval) ->
    % 使用消息指定的过期时间（转换为毫秒）
    erlang:system_time(millisecond) + (MessageExpiryInterval * 1000);
calculate_will_message_expiry_time(_) ->
    % 无效值，使用插件配置
    WillExpiry = application:get_env(emqx_plugin_mongodb, will_expiry, 604800000),
    erlang:system_time(millisecond) + WillExpiry.

%% @doc 计算遗嘱消息发布时间
%% 根据Will Delay Interval计算何时应该发布遗嘱消息
calculate_will_publish_time(0) ->
    % Will Delay Interval = 0，立即发布
    erlang:system_time(millisecond);
calculate_will_publish_time(WillDelayInterval) when is_integer(WillDelayInterval), WillDelayInterval > 0 ->
    % 延迟发布（转换为毫秒）
    erlang:system_time(millisecond) + (WillDelayInterval * 1000);
calculate_will_publish_time(_) ->
    % 无效值，立即发布
    erlang:system_time(millisecond).

%% @doc 解析遗嘱消息文档
parse_will_document(WillDoc) ->
    Topic = maps:get(<<"topic">>, WillDoc, <<>>),
    Payload = maps:get(<<"payload">>, WillDoc, <<>>),
    QoS = maps:get(<<"qos">>, WillDoc, 0),
    Retain = maps:get(<<"retain">>, WillDoc, false),
    Properties = maps:get(<<"properties">>, WillDoc, #{}),

    % 构建消息记录
    Msg = emqx_message:make(Topic, Payload),
    Msg#message{
        qos = QoS,
        flags = #{retain => Retain},
        headers = #{properties => Properties}
    }.



%% @doc 判断是否为遗嘱消息
is_will_message(Message) ->
    % 检查消息头部是否包含遗嘱消息标识
    % EMQX在发布遗嘱消息时会在headers中添加特殊标识
    Headers = emqx_message:get_headers(Message),
    case maps:get(will_msg, Headers, undefined) of
        true -> true;
        _ ->
            % 也检查其他可能的遗嘱消息标识
            case maps:get(is_will_msg, Headers, undefined) of
                true -> true;
                _ ->
                    % 检查消息来源是否为系统（遗嘱消息通常由系统发布）
                    From = emqx_message:from(Message),
                    case From of
                        undefined -> true;  % 系统消息
                        <<"$SYS">> -> false; % 系统主题，不是遗嘱消息
                        _ -> false  % 普通客户端消息
                    end
            end
    end.

%% @doc 记录遗嘱消息发布历史
record_will_publish_history(ClientId, Message) ->
    try
        % 使用配置的集合名称，而不是硬编码
        Collection = application:get_env(emqx_plugin_mongodb, will_history_collection, <<"emqx_will_publish_history">>),
        Topic = emqx_message:topic(Message),
        Payload = emqx_message:payload(Message),

        HistoryDoc = #{
            <<"client_id">> => ClientId,
            <<"topic">> => Topic,
            <<"payload">> => Payload,
            <<"qos">> => emqx_message:qos(Message),
            <<"retain">> => emqx_message:get_flag(retain, Message, false),
            <<"published_at">> => erlang:system_time(millisecond),
            <<"event">> => <<"will_message_published">>
        },

        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                               {insert, Collection, HistoryDoc}) of
            {ok, _} ->
                ?SLOG(debug, #{msg => "will_publish_history_recorded", client_id => ClientId});
            {async_return, ok} ->
                ?SLOG(debug, #{msg => "will_publish_history_recorded_async", client_id => ClientId});
            {async_return, {ok, _}} ->
                ?SLOG(debug, #{msg => "will_publish_history_recorded_async", client_id => ClientId});
            {async_return, {error, Reason}} ->
                ?SLOG(warning, #{msg => "failed_to_record_will_history_async", client_id => ClientId, reason => Reason});
            {error, Reason} ->
                ?SLOG(warning, #{msg => "failed_to_record_will_history", client_id => ClientId, reason => Reason})
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_recording_will_publish_history",
                error => E,
                reason => R,
                stacktrace => S,
                client_id => ClientId
            })
    end.

%% @doc 获取遗嘱消息统计
get_will_message_stats() ->
    try
        Collection = get_will_collection(),

        % 统计遗嘱消息总数
        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                               {count, Collection, #{}}) of
            {ok, TotalCount} ->
                {ok, #{
                    total_wills => TotalCount
                }};
            {error, Reason} ->
                {error, {count_failed, Reason}}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_getting_will_message_stats",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%% @doc 格式化原因
format_reason(Reason) when is_atom(Reason) ->
    atom_to_binary(Reason, utf8);
format_reason(Reason) when is_binary(Reason) ->
    Reason;
format_reason(Reason) ->
    try
        iolist_to_binary(io_lib:format("~p", [Reason]))
    catch
        _:_ -> <<"unknown">>
    end.

%% @doc 格式化对等主机地址
format_peerhost(PeerHost) when is_binary(PeerHost) ->
    PeerHost;
format_peerhost({A, B, C, D}) when is_integer(A), is_integer(B), is_integer(C), is_integer(D) ->
    iolist_to_binary(io_lib:format("~w.~w.~w.~w", [A, B, C, D]));
format_peerhost({A, B, C, D, E, F, G, H}) ->
    iolist_to_binary(io_lib:format("~w:~w:~w:~w:~w:~w:~w:~w", [A, B, C, D, E, F, G, H]));
format_peerhost(PeerHost) ->
    try
        iolist_to_binary(io_lib:format("~p", [PeerHost]))
    catch
        _:_ -> <<"unknown">>
    end.

%% @doc 解码消息属性
decode_properties(Properties) when is_map(Properties) ->
    Properties;
decode_properties(_) ->
    #{}.

%% @doc 解析时间持续时间字符串为毫秒
parse_time_duration(Duration) when is_integer(Duration) ->
    Duration;
parse_time_duration(Duration) when is_binary(Duration) ->
    parse_time_duration(binary_to_list(Duration));
parse_time_duration(Duration) when is_list(Duration) ->
    case lists:reverse(Duration) of
        [$s | Rest] ->
            list_to_integer(lists:reverse(Rest)) * 1000;
        [$m | Rest] ->
            list_to_integer(lists:reverse(Rest)) * 60 * 1000;
        [$h | Rest] ->
            list_to_integer(lists:reverse(Rest)) * 60 * 60 * 1000;
        [$d | Rest] ->
            list_to_integer(lists:reverse(Rest)) * 24 * 60 * 60 * 1000;
        _ ->
            % 默认当作毫秒
            try
                list_to_integer(Duration)
            catch
                _:_ -> 604800000  % 默认7天
            end
    end;
parse_time_duration(_) ->
    604800000.  % 默认7天

%% @doc 格式化索引键为字符串
format_index_key(IndexKey) when is_map(IndexKey) ->
    Parts = maps:fold(fun(Key, Value, Acc) ->
        KeyStr = if
            is_binary(Key) -> binary_to_list(Key);
            is_atom(Key) -> atom_to_list(Key);
            true -> io_lib:format("~p", [Key])
        end,
        ValueStr = if
            is_integer(Value) -> integer_to_list(Value);
            is_binary(Value) -> binary_to_list(Value);
            is_atom(Value) -> atom_to_list(Value);
            true -> io_lib:format("~p", [Value])
        end,
        [KeyStr ++ "_" ++ ValueStr | Acc]
    end, [], IndexKey),
    string:join(Parts, "_");
format_index_key(_) ->
    "unknown_index".

%% @doc 懒初始化：确保遗嘱消息集合和索引存在
ensure_will_collection_and_indexes(Collection) ->
    % 使用进程字典缓存，避免重复检查
    case get({will_collection_initialized, Collection}) of
        true ->
            % 已经初始化过，直接返回
            ok;
        _ ->
            % 第一次访问，需要初始化
            ?SLOG(info, #{
                msg => "lazy_initializing_will_collection",
                collection => Collection
            }),
            create_will_indexes_for_collection(Collection),
            % 标记为已初始化
            put({will_collection_initialized, Collection}, true),
            ok
    end.

%% @doc 为指定集合创建遗嘱消息索引
create_will_indexes_for_collection(Collection) ->
    try
        % 生成集合缩写
        CollectionAbbr = generate_collection_abbreviation(Collection),

        ?SLOG(info, #{
            msg => "creating_will_message_indexes_lazy",
            collection => Collection,
            collection_abbr => CollectionAbbr
        }),

        % 定义需要的索引（使用改进的命名）
        RequiredIndexes = [
            % 客户端ID唯一索引
            #{
                <<"key">> => #{<<"client_id">> => 1},
                <<"unique">> => true,
                <<"name">> => <<CollectionAbbr/binary, "_client_id_unique">>
            },
            % 过期时间索引
            #{
                <<"key">> => #{<<"expiry_time">> => 1},
                <<"name">> => <<CollectionAbbr/binary, "_expiry_time_asc">>
            },
            % 主题索引
            #{
                <<"key">> => #{<<"topic">> => 1},
                <<"name">> => <<CollectionAbbr/binary, "_topic_asc">>
            }
        ],

        % 逐个创建索引，使用智能重命名机制
        lists:foreach(fun(IndexSpec) ->
            create_index_with_smart_check(Collection, IndexSpec)
        end, RequiredIndexes)
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_creating_will_indexes_lazy",
                collection => Collection,
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% @doc 创建遗嘱消息索引（已弃用，保留用于兼容性）
%% 现在使用懒初始化方式
create_will_indexes() ->
    Collection = get_will_collection(),
    ?SLOG(info, #{
        msg => "create_will_indexes_called_using_lazy_initialization",
        collection => Collection
    }),
    create_will_indexes_for_collection(Collection).

%% @doc 生成集合名称缩写
generate_collection_abbreviation(Collection) when is_binary(Collection) ->
    % 将集合名称转换为缩写
    % emqx_mqtt_will_messages -> emwm
    % emqx_will_messages -> ewm
    Parts = binary:split(Collection, <<"_">>, [global]),
    Abbreviation = lists:foldl(fun(Part, Acc) ->
        case Part of
            <<>> -> Acc;
            <<First:8, _/binary>> -> <<Acc/binary, First>>
        end
    end, <<>>, Parts),

    % 确保缩写不为空，至少有2个字符
    case byte_size(Abbreviation) of
        0 -> <<"idx">>;
        1 -> <<Abbreviation/binary, "x">>;
        _ -> Abbreviation
    end;
generate_collection_abbreviation(_) ->
    <<"idx">>.

%% @doc 处理索引创建错误
handle_index_creation_error(Collection, IndexName, IndexKey, Reason) ->
    % 检查是否是索引已存在的错误
    case is_index_exists_error(Reason) of
        true ->
            ?SLOG(info, #{
                msg => "will_message_index_already_exists",
                collection => Collection,
                index_name => IndexName,
                index_key => IndexKey
            });
        false ->
            % 提取详细的错误信息
            {ErrorType, ErrorDetails} = extract_error_details(Reason),
            case ErrorType of
                indexes_already_exist ->
                    ?SLOG(info, #{
                        msg => "will_message_indexes_already_exist",
                        collection => Collection,
                        index_name => IndexName,
                        index_key => IndexKey,
                        details => ErrorDetails
                    });
                indexes_created_successfully ->
                    ?SLOG(info, #{
                        msg => "will_message_indexes_created_successfully",
                        collection => Collection,
                        index_name => IndexName,
                        index_key => IndexKey,
                        details => ErrorDetails
                    });
                _ ->
                    ?SLOG(warning, #{
                        msg => "failed_to_create_will_index",
                        collection => Collection,
                        index_name => IndexName,
                        index_key => IndexKey,
                        error_type => ErrorType,
                        error_details => ErrorDetails,
                        raw_reason => Reason
                    })
            end
    end.

%% @doc 检查是否是索引已存在的错误
is_index_exists_error(Reason) ->
    case Reason of
        {error, {badmatch, {error, {error, {error, {op_msg_response, Response}}}}}} ->
            check_index_conflict_in_response(Response);
        {error, {error, {error, {op_msg_response, Response}}}} ->
            check_index_conflict_in_response(Response);
        {error, {error, {op_msg_response, Response}}} ->
            check_index_conflict_in_response(Response);
        {error, {op_msg_response, Response}} ->
            check_index_conflict_in_response(Response);
        _ ->
            false
    end.

%% @doc 检查MongoDB响应中是否包含索引冲突信息
check_index_conflict_in_response(Response) when is_map(Response) ->
    case maps:get(<<"codeName">>, Response, undefined) of
        <<"IndexOptionsConflict">> -> true;
        <<"IndexKeySpecsConflict">> -> true;
        _ ->
            case maps:get(<<"code">>, Response, undefined) of
                85 -> true;  % IndexOptionsConflict
                86 -> true;  % IndexKeySpecsConflict
                _ -> false
            end
    end;
check_index_conflict_in_response(_) ->
    false.

%% @doc 智能索引创建 - 检查存在性并自动重命名
create_index_with_smart_check(Collection, IndexSpec) ->
    IndexName = maps:get(<<"name">>, IndexSpec),

    % 首先尝试直接创建索引
    case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                           {create_index, Collection, IndexSpec}) of
        {ok, _} ->
            ?SLOG(info, #{
                msg => "will_message_index_created",
                collection => Collection,
                index_name => IndexName
            });
        {async_return, ok} ->
            ?SLOG(info, #{
                msg => "will_message_index_created_async",
                collection => Collection,
                index_name => IndexName
            });
        {async_return, {ok, _}} ->
            ?SLOG(info, #{
                msg => "will_message_index_created_async",
                collection => Collection,
                index_name => IndexName
            });
        {async_return, {error, Reason}} ->
            handle_index_creation_with_retry(Collection, IndexSpec, Reason);
        {error, Reason} ->
            handle_index_creation_with_retry(Collection, IndexSpec, Reason)
    end.

%% @doc 处理索引创建失败，尝试重命名
handle_index_creation_with_retry(Collection, IndexSpec, Reason) ->
    IndexName = maps:get(<<"name">>, IndexSpec),

    case is_index_exists_error(Reason) of
        true ->
            % 索引已存在，尝试使用新名称
            NewIndexName = generate_unique_will_index_name(IndexName),
            NewIndexSpec = IndexSpec#{<<"name">> => NewIndexName},

            ?SLOG(info, #{
                msg => "retrying_will_index_creation_with_new_name",
                collection => Collection,
                original_name => IndexName,
                new_name => NewIndexName
            }),

            case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                                   {create_index, Collection, NewIndexSpec}) of
                {ok, _} ->
                    ?SLOG(info, #{
                        msg => "will_message_index_created_with_new_name",
                        collection => Collection,
                        index_name => NewIndexName
                    });
                {async_return, ok} ->
                    ?SLOG(info, #{
                        msg => "will_message_index_created_async_with_new_name",
                        collection => Collection,
                        index_name => NewIndexName
                    });
                {async_return, {ok, _}} ->
                    ?SLOG(info, #{
                        msg => "will_message_index_created_async_with_new_name",
                        collection => Collection,
                        index_name => NewIndexName
                    });
                {error, NewReason} ->
                    ?SLOG(warning, #{
                        msg => "failed_to_create_will_index_even_with_new_name",
                        collection => Collection,
                        index_name => NewIndexName,
                        reason => NewReason
                    })
            end;
        false ->
            handle_index_creation_error(Collection, IndexName, maps:get(<<"key">>, IndexSpec), Reason)
    end.

%% @doc 生成唯一的遗嘱消息索引名称
generate_unique_will_index_name(BaseIndexName) ->
    Timestamp = integer_to_binary(erlang:system_time(millisecond)),
    <<BaseIndexName/binary, "_", Timestamp/binary>>.

%% @doc 提取错误详情
extract_error_details(Reason) ->
    case Reason of
        #{code := Code, message := Msg, code_name := CodeName} ->
            {mongodb_error, #{code => Code, message => Msg, code_name => CodeName}};
        {error, #{code := Code, message := Msg}} ->
            {mongodb_error, #{code => Code, message => Msg}};
        {unexpected_result, {ok, #{<<"note">> := <<"all indexes already exist">>} = Response}} ->
            % 这实际上是成功的情况，索引已存在
            {indexes_already_exist, Response};
        {unexpected_result, {ok, #{<<"ok">> := 1.0, <<"numIndexesBefore">> := Before, <<"numIndexesAfter">> := After} = Response}}
            when is_integer(Before), is_integer(After), After > Before ->
            % 这实际上是成功的情况，索引创建成功
            {indexes_created_successfully, Response};
        {unexpected_result, Result} ->
            {unexpected_result, Result};
        {E, R} when is_atom(E) ->
            {exception, #{error => E, reason => R}};
        unknown_error ->
            {unknown_error, #{}};
        Other ->
            {other_error, Other}
    end.

%% @doc 检查消息是否为遗嘱消息的发布
%% 修复：改进遗嘱消息检测逻辑
%%
%% 功能说明：
%% 1. 检查消息是否包含遗嘱消息标识
%% 2. 避免依赖MongoDB查询来判断，提高准确性和性能
%% 3. 使用消息头部信息进行更可靠的识别
%%
%% 修复说明：
%% - 原方案：通过MongoDB查询判断 → 不准确，性能差
%% - 新方案：通过消息标识判断 → 更准确，性能好
%%
%% Java等价概念：
%% public boolean isWillMessagePublish(Message message) {
%%     // 检查消息是否包含遗嘱消息标识
%%     return message.getHeaders().containsKey("will_message") &&
%%            Boolean.TRUE.equals(message.getHeaders().get("will_message"));
%% }
is_will_message_publish(Message) ->
    try
        %% 方法1：检查消息头部是否包含遗嘱消息标识
        %% 这是更可靠的方法，因为我们在发布遗嘱消息时会设置这个标识
        case emqx_message:get_header(will_message, Message, false) of
            true ->
                %% 消息头部明确标识为遗嘱消息
                true;
            _ ->
                %% 方法2：检查发布者是否为系统或特殊标识
                Publisher = emqx_message:from(Message),
                Topic = emqx_message:topic(Message),

                %% 关键修复：更严格的系统消息过滤
                case Publisher of
                    <<"$SYS/", _/binary>> ->
                        false; %% 系统消息，不是遗嘱消息
                    <<"mongodb_restore">> ->
                        false; %% 插件恢复的消息，不是遗嘱消息
                    emqx_sys ->
                        false; %% EMQX系统进程发布的消息，不是遗嘱消息
                    _ ->
                        %% 检查主题是否为系统主题
                        case Topic of
                            <<"$SYS/", _/binary>> ->
                                false; %% 系统主题，不是遗嘱消息
                            _ ->
                                %% 关键修复：不再使用MongoDB查询作为fallback
                                %% 只有明确标记为遗嘱消息的消息才会被处理
                                %% 这避免了大量不必要的数据库查询
                                ?SLOG(debug, #{
                                    msg => "message_not_marked_as_will_message_skipping",
                                    publisher => Publisher,
                                    topic => Topic,
                                    note => "only_messages_with_will_message_header_will_be_processed"
                                }),
                                false
                        end
                end
        end
    catch
        E:R:S ->
            ?SLOG(debug, #{
                msg => "error_checking_will_message_publish",
                error => E,
                reason => R,
                stacktrace => S,
                publisher => emqx_message:from(Message),
                topic => emqx_message:topic(Message)
            }),
            false
    end.

%% @doc 检查MongoDB中是否存在对应的遗嘱消息记录
check_will_message_exists_in_mongodb(ClientId, Topic) ->
    try
        Collection = get_will_collection(),
        % 确保客户端ID为binary格式以匹配MongoDB中的存储格式
        ClientIdBin = case ClientId of
            Bin when is_binary(Bin) -> Bin;
            List when is_list(List) -> list_to_binary(List);
            _ -> ClientId
        end,
        Filter = #{
            <<"client_id">> => ClientIdBin,
            <<"topic">> => Topic
        },

        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                               {find, Collection, Filter, #{}, 0, 1}) of
            {ok, [_Doc]} ->
                true;  % 找到了对应的遗嘱消息记录
            {ok, []} ->
                false; % 没有找到
            {async_return, {ok, [_Doc]}} ->
                true;
            {async_return, {ok, []}} ->
                false;
            _ ->
                false  % 其他情况都认为不是遗嘱消息
        end
    catch
        _:_ ->
            false
    end.

%% @doc 检查并恢复特定客户端的遗嘱消息
restore_client_will_message_if_needed(ClientId) ->
    try
        ?SLOG(debug, #{
            msg => "checking_client_will_message_restoration",
            client_id => ClientId
        }),

        Collection = get_will_collection(),

        % 确保客户端ID为binary格式以匹配MongoDB中的存储格式
        ClientIdBin = case ClientId of
            Bin when is_binary(Bin) -> Bin;
            List when is_list(List) -> list_to_binary(List);
            _ -> ClientId
        end,

        % 查询该客户端是否有遗嘱消息（简化：移除状态检查）
        Now = erlang:system_time(millisecond),
        Filter = #{
            <<"client_id">> => ClientIdBin,
            <<"$or">> => [
                #{<<"expiry_time">> => #{<<"$gt">> => Now}}, % 未过期
                #{<<"expiry_time">> => null}                  % 永不过期
            ]
        },

        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                               {find, Collection, Filter, #{}, 0, 1}) of % 只查询一条
            {ok, [Doc]} ->
                %% 找到了该客户端的遗嘱消息，记录信息（不直接发布）
                ?SLOG(info, #{
                    msg => "found_client_will_message_after_abnormal_shutdown",
                    client_id => ClientId,
                    approach => "pure_persistence_logging_only"
                }),
                log_single_will_message_info(Doc);
            {async_return, {ok, [Doc]}} ->
                ?SLOG(info, #{
                    msg => "found_client_will_message_after_abnormal_shutdown_async",
                    client_id => ClientId,
                    approach => "pure_persistence_logging_only"
                }),
                log_single_will_message_info(Doc);
            {ok, []} ->
                ?SLOG(debug, #{
                    msg => "no_will_message_to_restore_for_client",
                    client_id => ClientId
                });
            {async_return, {ok, []}} ->
                ?SLOG(debug, #{
                    msg => "no_will_message_to_restore_for_client_async",
                    client_id => ClientId
                });
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_query_client_will_message_for_restoration",
                    client_id => ClientId,
                    reason => Reason
                });
            {async_return, {error, Reason}} ->
                ?SLOG(error, #{
                    msg => "failed_to_query_client_will_message_for_restoration_async",
                    client_id => ClientId,
                    reason => Reason
                })
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_checking_client_will_message_restoration",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% @doc 处理客户端重连时没有携带遗嘱消息的情况
%% 这个函数实现了MQTT协议的正确语义：
%% 如果客户端重连时没有携带遗嘱消息，但数据库中有该客户端的旧遗嘱消息，
%% 应该删除数据库中的旧记录，因为客户端已经明确表示不再需要遗嘱消息
%%
%% 功能说明：
%% 1. 检查数据库中是否存在该客户端的遗嘱消息
%% 2. 如果存在，删除旧的遗嘱消息记录
%% 3. 记录相应的日志信息
%%
%% 参数说明：
%% - ClientId: 客户端ID
%%
%% Java等价概念：
%% public void handleReconnectionWithoutWillMessage(String clientId) {
%%     Optional<WillMessage> existingWill = willMessageRepository.findByClientId(clientId);
%%     if (existingWill.isPresent()) {
%%         willMessageRepository.deleteByClientId(clientId);
%%         logger.info("Deleted old will message for client reconnection without will: {}", clientId);
%%     }
%% }
%%
%% MQTT协议语义：
%% - 客户端重连时可以选择是否携带遗嘱消息
%% - 如果不携带，表示客户端不再需要遗嘱消息功能
%% - 服务器应该清理该客户端之前的遗嘱消息设置
handle_reconnection_without_will_message(ClientId) ->
    try
        ?SLOG(debug, #{
            msg => "checking_old_will_message_for_cleanup_on_reconnection",
            client_id => ClientId
        }),

        %% 检查数据库中是否存在该客户端的遗嘱消息
        case find_will_message(ClientId) of
            {ok, _WillMsg} ->
                %% 找到了旧的遗嘱消息，需要删除
                %% 因为客户端重连时没有携带新的遗嘱消息
                ?SLOG(info, #{
                    msg => "deleting_old_will_message_client_reconnected_without_will",
                    client_id => ClientId
                }),
                delete_will_message(ClientId),
                ?SLOG(info, #{
                    msg => "old_will_message_deleted_for_reconnection",
                    client_id => ClientId
                });
            {error, not_found} ->
                %% 没有找到旧的遗嘱消息，无需处理
                ?SLOG(debug, #{
                    msg => "no_old_will_message_found_for_cleanup",
                    client_id => ClientId
                });
            {error, Reason} ->
                %% 查找过程中出错
                ?SLOG(error, #{
                    msg => "failed_to_check_old_will_message_for_cleanup",
                    client_id => ClientId,
                    reason => Reason
                })
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_handling_reconnection_without_will_message",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% @doc 原子性处理客户端重连但无遗嘱消息的情况
%% 修复竞态条件：防止与EMQX重启后的智能处理线程冲突
%%
%% 竞态条件场景：
%% 1. EMQX重启后智能处理线程检查遗嘱消息，准备发布
%% 2. 客户端同时重连，尝试删除遗嘱消息记录
%% 3. 可能导致遗嘱消息被错误发布或重复处理
%%
%% 修复方案：
%% 1. 使用原子性删除操作
%% 2. 记录详细的竞态条件处理日志
%% 3. 确保只有一个线程能成功处理遗嘱消息
handle_reconnection_without_will_message_atomic(ClientId) ->
    try
        ?SLOG(info, #{
            msg => "atomic_cleanup_old_will_message_on_client_reconnection",
            client_id => ClientId,
            race_condition_protection => "using_atomic_delete_to_prevent_concurrent_processing"
        }),

        Collection = get_will_collection(),
        Filter = #{<<"client_id">> => ClientId},

        %% 使用原子性删除操作
        %% 如果智能处理线程已经处理了这个遗嘱消息，这里的删除会返回0
        %% 如果智能处理线程还没处理，这里会成功删除并阻止智能处理线程发布
        case emqx_plugin_mongodb_api:delete_many(Collection, Filter) of
            {ok, #{<<"deletedCount">> := DeletedCount}} when DeletedCount > 0 ->
                ?SLOG(info, #{
                    msg => "successfully_deleted_old_will_messages_on_client_reconnection",
                    client_id => ClientId,
                    deleted_count => DeletedCount,
                    race_condition => "prevented_intelligent_handler_from_publishing_will_messages"
                });
            {ok, #{<<"deletedCount">> := 0}} ->
                ?SLOG(info, #{
                    msg => "no_will_messages_found_for_cleanup_on_client_reconnection",
                    client_id => ClientId,
                    possible_reason => "already_processed_by_intelligent_handler_or_no_previous_will_message"
                });
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_delete_old_will_messages_on_client_reconnection",
                    client_id => ClientId,
                    reason => Reason
                })
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_in_atomic_cleanup_old_will_message_on_reconnection",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% @doc 记录单个遗嘱消息信息（不直接发布）
%% 修复：采用方案A - 纯持久化方案
%%
%% 功能说明：
%% 1. 不直接发布遗嘱消息，避免与EMQX内置功能冲突
%% 2. 记录遗嘱消息的详细信息供管理员参考
%% 3. 让EMQX的内置功能在适当时机处理遗嘱消息
%%
%% 修复说明：
%% - 原方案：直接发布遗嘱消息 → 可能与EMQX内置功能冲突
%% - 新方案：只记录信息 → 让EMQX内置功能处理
%%
%% Java等价概念：
%% public void logSingleWillMessage(WillMessageDoc doc) {
%%     logger.warn("Will message found after abnormal shutdown: client={}, topic={}",
%%                doc.getClientId(), doc.getTopic());
%%     // 不直接发布，让MQTT broker的内置功能处理
%% }
log_single_will_message_info(Doc) ->
    try
        %% 解析遗嘱消息文档
        ClientId = maps:get(<<"client_id">>, Doc, <<>>),
        Topic = maps:get(<<"topic">>, Doc, <<>>),
        Payload = maps:get(<<"payload">>, Doc, <<>>),
        QoS = maps:get(<<"qos">>, Doc, 0),
        Retain = maps:get(<<"retain">>, Doc, false),
        CreatedAt = maps:get(<<"created_at">>, Doc, 0),

        %% 验证关键字段不为空
        case {ClientId, Topic} of
            {<<>>, _} ->
                ?SLOG(warning, #{
                    msg => "skipping_will_message_log_empty_client_id",
                    doc => Doc
                });
            {_, <<>>} ->
                ?SLOG(warning, #{
                    msg => "skipping_will_message_log_empty_topic",
                    client_id => ClientId,
                    doc => Doc
                });
            {_, _} ->
                %% 记录遗嘱消息信息，但不发布
                ?SLOG(warning, #{
                    msg => "will_message_found_after_abnormal_shutdown",
                    client_id => ClientId,
                    topic => Topic,
                    payload_size => byte_size(Payload),
                    qos => QoS,
                    retain => Retain,
                    created_at => CreatedAt,
                    note => "will_be_handled_by_emqx_when_client_truly_disconnects",
                    approach => "pure_persistence_no_direct_publish"
                })
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_restoring_single_client_will_message",
                error => E,
                reason => R,
                stacktrace => S,
                doc => Doc
            })
    end.

%% @doc 删除已发布的遗嘱消息（发布后清理）
update_will_message_published_status(PublisherId, Topic, Message) ->
    try
        Collection = get_will_collection(),

        % 构建查询条件
        Filter = #{
            <<"client_id">> => PublisherId,
            <<"topic">> => Topic
        },

        % 遗嘱消息发布后应该删除，而不是仅仅更新状态
        % 这符合MQTT协议：遗嘱消息只发布一次
        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                               {delete, Collection, Filter}) of
            {ok, _} ->
                ?SLOG(info, #{
                    msg => "will_message_deleted_after_publish",
                    client_id => PublisherId,
                    topic => Topic
                });
            {async_return, ok} ->
                ?SLOG(info, #{
                    msg => "will_message_deleted_after_publish_async",
                    client_id => PublisherId,
                    topic => Topic
                });
            {async_return, {ok, _}} ->
                ?SLOG(info, #{
                    msg => "will_message_deleted_after_publish_async_with_result",
                    client_id => PublisherId,
                    topic => Topic
                });
            {async_return, {error, Reason}} ->
                ?SLOG(error, #{
                    msg => "failed_to_delete_will_message_after_publish_async",
                    client_id => PublisherId,
                    topic => Topic,
                    reason => Reason
                });
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_delete_will_message_after_publish",
                    client_id => PublisherId,
                    topic => Topic,
                    reason => Reason
                });
            Other ->
                ?SLOG(warning, #{
                    msg => "unexpected_delete_will_message_response",
                    client_id => PublisherId,
                    topic => Topic,
                    response => Other
                })
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_updating_will_message_published_status",
                client_id => PublisherId,
                topic => Topic,
                error => E,
                reason => R,
                stacktrace => S
            })
    end.



%% @doc 检测是否为异常关闭
detect_abnormal_shutdown() ->
    try
        % 检查是否存在正常关闭标记
        case application:get_env(emqx_plugin_mongodb, normal_shutdown_flag, undefined) of
            true ->
                % 上次是正常关闭
                false;
            _ ->
                % 上次可能是异常关闭，进一步检查
                check_will_messages_need_publishing()
        end
    catch
        _:_ ->
            % 出错时假设为异常关闭
            true
    end.

%% @doc 检查是否有遗嘱消息需要发布
check_will_messages_need_publishing() ->
    try
        Collection = get_will_collection(),

        % 查询所有未过期的遗嘱消息（不依赖status字段，因为发布后消息会被删除）
        Now = erlang:system_time(millisecond),
        Filter = #{
            <<"$or">> => [
                #{<<"expiry_time">> => #{<<"$gt">> => Now}}, % 未过期
                #{<<"expiry_time">> => null}                  % 永不过期
            ]
        },

        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                               {count_documents, Collection, Filter}) of
            {ok, Count} when Count > 0 ->
                ?SLOG(info, #{
                    msg => "found_pending_will_messages",
                    count => Count
                }),
                true;
            {async_return, {ok, Count}} when Count > 0 ->
                ?SLOG(info, #{
                    msg => "found_pending_will_messages_async",
                    count => Count
                }),
                true;
            _ ->
                false
        end
    catch
        _:_ ->
            % 出错时假设需要处理
            true
    end.

%% @doc 处理系统异常关闭后的遗嘱消息恢复
%% 修复：采用方案A - 纯持久化方案
%%
%% 功能说明：
%% 1. 不直接发布遗嘱消息，避免与EMQX内置功能冲突
%% 2. 将遗嘱消息信息记录到日志，供管理员手动处理
%% 3. 让EMQX的内置遗嘱消息功能在客户端真正异常断开时自动处理
%%
%% 设计原理：
%% - 插件只负责持久化，不主动发布遗嘱消息
%% - EMQX的内置遗嘱消息功能会在客户端真正异常断开时自动处理
%% - 避免重复发布和与内置功能的冲突
%%
%% 修复说明：
%% - 原方案：插件主动发布遗嘱消息 → 可能与EMQX内置功能冲突
%% - 新方案：插件只记录和标记 → 让EMQX内置功能处理
%%
%% Java等价概念：
%% @PostConstruct
%% public void handleAbnormalShutdownWillMessages() {
%%     List<WillMessage> pendingWills = willMessageRepository.findAllValid();
%%
%%     // 不直接发布，而是记录日志供管理员处理
%%     for (WillMessage will : pendingWills) {
%%         logger.warn("Found pending will message after abnormal shutdown: client={}, topic={}",
%%                    will.getClientId(), will.getTopic());
%%     }
%% }
handle_abnormal_shutdown_will_messages() ->
    %% 日志：函数开始执行
    ?SLOG(info, #{
        msg => "will_message_recovery_function_started",
        function => "handle_abnormal_shutdown_will_messages",
        timestamp => erlang:system_time(millisecond)
    }),

    try
        Collection = get_will_collection(),

        %% 日志：获取集合名称成功
        ?SLOG(info, #{
            msg => "will_collection_obtained",
            collection => Collection
        }),

        %% 修复：按照MQTT协议正确处理遗嘱消息
        %% EMQX异常重启场景：所有客户端连接异常断开，触发遗嘱消息发布条件
        %% 应该立即发布所有有效（未过期）的遗嘱消息，符合MQTT协议语义
        Now = erlang:system_time(millisecond),

        %% 修复：查询未过期的遗嘱消息
        %% 按照MQTT协议，只有未过期的遗嘱消息才应该被发布
        %% 过期的遗嘱消息应该被清理，不应该发布
        Filter = #{
            <<"expiry_time">> => #{<<"$gt">> => Now}
        },

        ?SLOG(info, #{
            msg => "querying_valid_will_messages_after_emqx_restart",
            collection => Collection,
            approach => "find_unexpired_will_messages_per_mqtt_protocol",
            filter => Filter,
            current_time => Now,
            note => "only_unexpired_will_messages_should_be_published"
        }),

        ?SLOG(info, #{
            msg => "checking_will_messages_after_emqx_restart",
            collection => Collection,
            approach => "mqtt_protocol_compliant_will_message_processing",
            filter => Filter,
            current_time => Now
        }),

        %% 关键修复：添加详细的查询调试信息
        QueryResult = emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                                        {find, Collection, Filter, #{}, 0, 0}),

        ?SLOG(debug, #{
            msg => "mongodb_query_result_detailed",
            collection => Collection,
            filter => Filter,
            query_result => QueryResult,
            query_type => "find_will_messages_after_restart"
        }),

        case QueryResult of
            {ok, Docs} when is_list(Docs) ->
                PendingCount = length(Docs),

                ?SLOG(info, #{
                    msg => "will_messages_query_successful",
                    collection => Collection,
                    pending_count => PendingCount,
                    docs_sample => case Docs of
                        [] -> [];
                        [FirstDoc|_] -> [maps:get(<<"client_id">>, FirstDoc, <<"unknown">>)]
                    end
                }),

                case PendingCount of
                    0 ->
                        %% 没有找到未过期的遗嘱消息，这是正常情况
                        ?SLOG(info, #{
                            msg => "no_unexpired_will_messages_found_after_emqx_restart",
                            note => "all_will_messages_may_have_expired_or_none_exist"
                        });
                    _ ->
                        %% 找到了未过期的遗嘱消息，按照MQTT协议立即发布
                        %%
                        %% MQTT协议要求：
                        %% 1. 客户端异常断开时应该发布遗嘱消息
                        %% 2. EMQX异常关闭导致所有客户端异常断开
                        %% 3. 因此应该立即发布所有未过期的遗嘱消息

                        ?SLOG(info, #{
                            msg => "found_unexpired_will_messages_after_emqx_restart",
                            pending_count => PendingCount,
                            note => "will_publish_immediately_per_mqtt_protocol"
                        }),

                        %% 异步处理遗嘱消息发布，避免阻塞启动过程
                        spawn(fun() ->
                            ?SLOG(info, #{
                                msg => "starting_will_message_processing_after_emqx_restart",
                                message_count => PendingCount,
                                approach => "mqtt_protocol_compliant_immediate_publish"
                            }),

                            %% 处理遗嘱消息发布
                            intelligently_handle_will_messages_after_restart(Docs)
                        end)
                end;
            {async_return, {ok, Docs}} when is_list(Docs) ->
                PendingCount = length(Docs),
                case PendingCount of
                    0 ->
                        ?SLOG(info, #{
                            msg => "no_pending_will_messages_found_after_abnormal_shutdown_async"
                        });
                    _ ->
                        log_pending_will_messages(Docs),
                        ?SLOG(warning, #{
                            msg => "found_pending_will_messages_after_abnormal_shutdown_async",
                            pending_count => PendingCount,
                            note => "messages_will_be_handled_by_emqx_builtin_functionality"
                        })
                end;
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_query_pending_will_messages_after_abnormal_shutdown",
                    reason => Reason
                });
            {async_return, {error, Reason}} ->
                ?SLOG(error, #{
                    msg => "failed_to_query_pending_will_messages_after_abnormal_shutdown_async",
                    reason => Reason
                });
            {async_return, ok} ->
                %% 处理异步返回的简单成功响应（无数据）
                ?SLOG(info, #{
                    msg => "no_pending_will_messages_after_abnormal_shutdown_async_ok"
                });
            {async_return, Result} ->
                %% 处理其他异步返回格式
                ?SLOG(warning, #{
                    msg => "unexpected_async_return_format_checking_will_messages",
                    result => Result
                })
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_handling_abnormal_shutdown_will_messages",
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% wait_for_system_ready函数已删除 - 直接处理遗嘱消息恢复

%% @doc 智能处理EMQX重启后的遗嘱消息
%% 修复：根据客户端实际状态决定是否发布遗嘱消息
%%
%% 功能说明：
%% 1. 检查每个遗嘱消息对应的客户端当前连接状态
%% 2. 如果客户端已重连：删除旧的遗嘱消息记录，不发布
%% 3. 如果客户端未连接：检查是否因EMQX异常关闭导致，决定是否发布
%% 4. 避免错误发布遗嘱消息，确保MQTT协议语义正确
%%
%% 设计原理：
%% - 遗嘱消息只应在客户端真正异常断开时发布
%% - EMQX重启不等于客户端异常断开
%% - 需要区分"EMQX异常关闭"和"客户端异常断开"
%%
%% Java等价概念：
%% @Async
%% public void intelligentlyHandleWillMessagesAfterRestart(List<WillMessage> willMessages) {
%%     for (WillMessage will : willMessages) {
%%         if (clientManager.isClientConnected(will.getClientId())) {
%%             // 客户端已重连，删除旧遗嘱消息
%%             willMessageRepository.deleteById(will.getId());
%%             logger.info("Client reconnected, deleted old will message: {}", will.getClientId());
%%         } else {
%%             // 客户端未连接，检查是否需要发布遗嘱消息
%%             if (shouldPublishWillMessageAfterRestart(will)) {
%%                 publishWillMessage(will);
%%                 logger.info("Published will message for disconnected client: {}", will.getClientId());
%%             } else {
%%                 logger.info("Will message not published, client may reconnect: {}", will.getClientId());
%%             }
%%         }
%%     }
%% }
intelligently_handle_will_messages_after_restart(WillMessageDocs) ->
    try
        ?SLOG(info, #{
            msg => "starting_intelligent_will_message_handling_after_restart",
            message_count => length(WillMessageDocs),
            approach => "check_client_status_before_publishing"
        }),

        %% 统计处理结果
        {PublishedCount, DeletedCount, SkippedCount} = lists:foldl(
            fun(WillDoc, {Published, Deleted, Skipped}) ->
                try
                    case intelligently_handle_single_will_message_after_restart(WillDoc) of
                        published -> {Published + 1, Deleted, Skipped};
                        deleted -> {Published, Deleted + 1, Skipped};
                        skipped -> {Published, Deleted, Skipped + 1}
                    end
                catch
                    E:R:S ->
                        ClientIdForError = maps:get(<<"client_id">>, WillDoc, <<"unknown">>),
                        ?SLOG(error, #{
                            msg => "failed_to_handle_single_will_message_after_restart",
                            client_id => ClientIdForError,
                            error => E,
                            reason => R,
                            stacktrace => S
                        }),
                        {Published, Deleted, Skipped + 1}
                end
            end,
            {0, 0, 0},
            WillMessageDocs
        ),

        ?SLOG(info, #{
            msg => "completed_intelligent_will_message_handling_after_restart",
            total_messages => length(WillMessageDocs),
            published_count => PublishedCount,
            deleted_count => DeletedCount,
            skipped_count => SkippedCount
        })
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_in_intelligent_will_message_handling_after_restart",
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% @doc 智能处理单个遗嘱消息
%% 返回值：published | deleted | skipped
intelligently_handle_single_will_message_after_restart(WillDoc) ->
    try
        ClientId = maps:get(<<"client_id">>, WillDoc, <<>>),
        WillId = maps:get(<<"_id">>, WillDoc, <<>>),
        Topic = maps:get(<<"topic">>, WillDoc, <<>>),

        %% 修复：EMQX异常重启场景的正确处理逻辑
        %% 根据MQTT协议，异常断开时应该立即发布遗嘱消息
        %% EMQX异常关闭时，所有客户端连接都被强制异常断开
        %% 这正是遗嘱消息的触发条件，应该立即发布所有有效的遗嘱消息
        ?SLOG(info, #{
            msg => "emqx_restart_scenario_will_message_processing",
            client_id => ClientId,
            topic => Topic,
            will_id => WillId,
            reason => "emqx_abnormal_shutdown_triggers_will_message_per_mqtt_protocol",
            approach => "immediate_publish_per_mqtt_semantics"
        }),

        %% 检查遗嘱消息是否过期
        case should_publish_will_message_after_emqx_restart(WillDoc) of
            true ->
                %% 发布遗嘱消息
                case publish_will_message_for_disconnected_client(WillDoc) of
                    ok ->
                        ?SLOG(info, #{
                            msg => "will_message_published_after_emqx_restart",
                            client_id => ClientId,
                            topic => Topic,
                            will_id => WillId
                        }),
                        published;
                    {skipped, Reason} ->
                        ?SLOG(info, #{
                            msg => "will_message_skipped_during_emqx_restart",
                            client_id => ClientId,
                            topic => Topic,
                            will_id => WillId,
                            reason => Reason
                        }),
                        skipped;
                    {error, Reason} ->
                        ?SLOG(error, #{
                            msg => "failed_to_publish_will_message_after_emqx_restart",
                            client_id => ClientId,
                            topic => Topic,
                            will_id => WillId,
                            reason => Reason
                        }),
                        skipped
                end;
            false ->
                %% 遗嘱消息已过期，需要删除
                ?SLOG(info, #{
                    msg => "will_message_expired_deleting_during_emqx_restart",
                    client_id => ClientId,
                    topic => Topic,
                    will_id => WillId
                }),
                delete_will_message_by_id(WillId),
                deleted
        end
    catch
        E:R:S ->
            ClientIdForCatch = maps:get(<<"client_id">>, WillDoc, <<"unknown">>),
            WillIdForCatch = maps:get(<<"_id">>, WillDoc, <<"unknown">>),
            ?SLOG(error, #{
                msg => "error_handling_single_will_message_after_restart",
                client_id => ClientIdForCatch,
                will_id => WillIdForCatch,
                error => E,
                reason => R,
                stacktrace => S
            }),
            skipped
    end.

%% @doc 判断是否应该在EMQX重启后发布遗嘱消息
%% 修复：添加更智能的判断逻辑
should_publish_will_message_after_emqx_restart(WillDoc) ->
    try
        ClientId = maps:get(<<"client_id">>, WillDoc, <<>>),
        CreatedAt = maps:get(<<"created_at">>, WillDoc, 0),
        Now = erlang:system_time(millisecond),

        %% 日志：函数开始执行
        ?SLOG(debug, #{
            msg => "should_publish_function_started",
            client_id => ClientId,
            created_at => CreatedAt,
            current_time => Now
        }),

        %% 修复：简化判断逻辑，在EMQX重启场景下应该发布所有有效的遗嘱消息
        %%
        %% 原逻辑问题：
        %% 1. 设置了1小时的等待时间，导致测试场景中的遗嘱消息被跳过
        %% 2. 过于保守的策略不适合EMQX异常关闭重启的场景
        %%
        %% 新逻辑：
        %% 1. 检查遗嘱消息是否过期（基于消息本身的过期时间）
        %% 2. 如果未过期，则应该发布（因为这是EMQX异常关闭重启场景）

        TimeSinceCreated = Now - CreatedAt,
        ExpiryTime = maps:get(<<"expiry_time">>, WillDoc, null),

        %% 日志：关键变量值
        ?SLOG(debug, #{
            msg => "should_publish_key_variables",
            client_id => ClientId,
            time_since_created => TimeSinceCreated,
            expiry_time => ExpiryTime,
            expiry_time_is_null => (ExpiryTime =:= null),
            expiry_time_is_integer => is_integer(ExpiryTime)
        }),

        case ExpiryTime of
            null ->
                %% 永不过期的遗嘱消息，应该发布
                ?SLOG(info, #{
                    msg => "will_message_never_expires_should_publish",
                    client_id => ClientId,
                    time_since_created => TimeSinceCreated
                }),
                true;
            ExpiryTime when is_integer(ExpiryTime) ->
                case Now < ExpiryTime of
                    true ->
                        %% 遗嘱消息未过期，应该发布
                        ?SLOG(info, #{
                            msg => "will_message_not_expired_should_publish",
                            client_id => ClientId,
                            time_since_created => TimeSinceCreated,
                            expiry_time => ExpiryTime,
                            current_time => Now
                        }),
                        true;
                    false ->
                        %% 遗嘱消息已过期，不应该发布
                        %% 注意：删除操作由调用方处理，这里只负责判断
                        ?SLOG(info, #{
                            msg => "will_message_expired_should_not_publish",
                            client_id => ClientId,
                            time_since_created => TimeSinceCreated,
                            expiry_time => ExpiryTime,
                            current_time => Now
                        }),
                        false
                end;
            _ ->
                %% 无效的过期时间，保守处理：发布消息
                ?SLOG(warning, #{
                    msg => "invalid_expiry_time_defaulting_to_publish",
                    client_id => ClientId,
                    expiry_time => ExpiryTime
                }),
                true
        end
    catch
        E:R:S ->
            ClientIdForCatch = maps:get(<<"client_id">>, WillDoc, <<"unknown">>),
            ?SLOG(error, #{
                msg => "error_checking_should_publish_will_message_after_restart",
                client_id => ClientIdForCatch,
                error => E,
                reason => R,
                stacktrace => S
            }),
            %% 出错时保守处理，不发布
            false
    end.

%% @doc 为断开连接的客户端发布遗嘱消息（原子性操作）
%% 修复竞态条件：确保发布和删除操作的原子性
%%
%% 竞态条件场景：
%% 1. 智能处理线程检查客户端未连接，决定发布遗嘱消息
%% 2. 客户端重连线程同时连接并删除遗嘱消息记录
%% 3. 智能处理线程继续发布遗嘱消息 → BUG：客户端已重连但遗嘱消息仍被发布
%%
%% 修复方案：
%% 1. 在发布前再次检查客户端连接状态
%% 2. 使用MongoDB的原子性删除操作
%% 3. 只有成功删除记录才发布遗嘱消息
publish_will_message_for_disconnected_client(WillDoc) ->
    try
        ClientId = maps:get(<<"client_id">>, WillDoc, <<>>),
        WillId = maps:get(<<"_id">>, WillDoc, <<>>),
        Topic = maps:get(<<"topic">>, WillDoc, <<>>),
        Payload = maps:get(<<"payload">>, WillDoc, <<>>),
        QoS = maps:get(<<"qos">>, WillDoc, 0),
        Retain = maps:get(<<"retain">>, WillDoc, false),

        %% EMQX异常重启场景：直接发布遗嘱消息，不检查客户端连接状态
        %% 原因：EMQX异常关闭时，所有客户端连接都被强制断开
        ?SLOG(info, #{
            msg => "emqx_restart_scenario_publishing_will_message_directly",
            client_id => ClientId,
            topic => Topic,
            will_id => WillId,
            reason => "emqx_abnormal_shutdown_all_clients_disconnected"
        }),

        %% 直接发布遗嘱消息，使用简化的原子性删除操作
        case atomic_delete_and_publish_will_message_simple(WillId, ClientId, Topic, Payload, QoS, Retain) of
            ok ->
                ok;
            {skipped, Reason} ->
                {skipped, Reason};
            {error, Reason} ->
                {error, Reason}
        end
    catch
        E:R:S ->
            ClientIdForCatch = maps:get(<<"client_id">>, WillDoc, <<"unknown">>),
            ?SLOG(error, #{
                msg => "error_publishing_will_message_for_disconnected_client",
                client_id => ClientIdForCatch,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%% @doc 修复版本：安全的遗嘱消息发布（用于EMQX重启场景）
%% 关键修复：先发布消息，成功后再删除记录，避免消息丢失
atomic_delete_and_publish_will_message_simple(WillId, ClientId, Topic, Payload, QoS, Retain) ->
    try
        Collection = get_will_collection(),
        Filter = #{<<"_id">> => WillId},

        ?SLOG(info, #{
            msg => "attempting_will_message_publish_after_emqx_restart",
            client_id => ClientId,
            topic => Topic,
            will_id => WillId,
            qos => QoS,
            retain => Retain,
            approach => "publish_first_then_delete_for_safety"
        }),

        %% 修复：按照MQTT协议正确构建和发布遗嘱消息
        %% 关键修复：使用原始客户端ID作为发布者，保持MQTT协议语义
        Message = emqx_message:make(ClientId, QoS, Topic, Payload),

        %% 关键修复：使用原始的retain设置，不强制修改
        Message2 = emqx_message:set_flag(retain, Retain, Message),

        %% 设置遗嘱消息标识，用于内部跟踪
        Message3 = emqx_message:set_header(will_message, true, Message2),
        Message4 = emqx_message:set_header(will_message_id, WillId, Message3),
        Message5 = emqx_message:set_header(will_message_timestamp, erlang:system_time(millisecond), Message4),
        %% 修复：标记为恢复的遗嘱消息，避免触发消息持久化和主题过滤
        Message6 = emqx_message:set_header(restored_will_message, true, Message5),
        Message7 = emqx_message:set_header(skip_persistence, true, Message6),

        %% 关键修复：先尝试发布消息，成功后再删除记录
        %% 这样可以避免删除记录后发布失败导致的消息丢失
        ?SLOG(info, #{
            msg => "publishing_will_message_before_deletion",
            client_id => ClientId,
            topic => Topic,
            will_id => WillId,
            safety_approach => "publish_first_delete_after_success"
        }),

        %% 使用标准的EMQX发布API
        PublishResult = emqx:publish(Message7),
        case PublishResult of
            ok ->
                %% 发布成功，现在安全删除记录
                ?SLOG(info, #{
                    msg => "will_message_published_successfully_now_deleting_record",
                    client_id => ClientId,
                    topic => Topic,
                    will_id => WillId
                }),

                %% 原子性删除操作
                case emqx_plugin_mongodb_api:delete_one(Collection, Filter) of
                    {ok, #{<<"deletedCount">> := 1}} ->
                        ?SLOG(info, #{
                            msg => "will_message_record_deleted_after_successful_publish",
                            client_id => ClientId,
                            topic => Topic,
                            will_id => WillId,
                            method => "safe_publish_then_delete"
                        }),
                        ok;
                    {ok, #{<<"deletedCount">> := 0}} ->
                        %% 记录已被其他进程删除，但消息已发布，这是可接受的
                        ?SLOG(warning, #{
                            msg => "will_message_record_already_deleted_but_message_published",
                            client_id => ClientId,
                            topic => Topic,
                            will_id => WillId,
                            note => "message_published_successfully_despite_record_deletion"
                        }),
                        ok;
                    {error, DeleteReason} ->
                        %% 删除失败，但消息已发布，记录警告
                        ?SLOG(warning, #{
                            msg => "will_message_published_but_record_deletion_failed",
                            client_id => ClientId,
                            topic => Topic,
                            will_id => WillId,
                            delete_reason => DeleteReason,
                            note => "message_delivered_successfully_manual_cleanup_may_be_needed"
                        }),
                        ok
                end;
            [] ->
                %% 没有订阅者，但发布成功（EMQX返回空列表表示没有订阅者）
                ?SLOG(info, #{
                    msg => "will_message_published_no_current_subscribers",
                    client_id => ClientId,
                    topic => Topic,
                    will_id => WillId,
                    retain => Retain,
                    note => case Retain of
                        true -> "message_retained_by_emqx_for_future_subscribers";
                        false -> "message_processed_per_mqtt_protocol_no_subscribers"
                    end
                }),

                %% 即使没有订阅者，也删除记录（消息已被EMQX处理）
                ?SLOG(debug, #{
                    msg => "attempting_will_message_deletion_after_no_subscribers",
                    client_id => ClientId,
                    topic => Topic,
                    will_id => WillId,
                    collection => Collection,
                    filter => Filter
                }),
                case emqx_plugin_mongodb_api:delete_one(Collection, Filter) of
                    {ok, #{<<"deletedCount">> := 1}} ->
                        ?SLOG(info, #{
                            msg => "will_message_record_deleted_after_no_subscribers_publish",
                            client_id => ClientId,
                            topic => Topic,
                            will_id => WillId
                        }),
                        ok;
                    {ok, #{<<"deletedCount">> := 0}} ->
                        ?SLOG(warning, #{
                            msg => "will_message_record_not_found_for_deletion",
                            client_id => ClientId,
                            topic => Topic,
                            will_id => WillId,
                            reason => "record_may_have_been_deleted_by_another_process"
                        }),
                        ok;
                    {error, Reason} ->
                        ?SLOG(error, #{
                            msg => "will_message_deletion_failed_with_error",
                            client_id => ClientId,
                            topic => Topic,
                            will_id => WillId,
                            error_reason => Reason
                        }),
                        ok;
                    Other ->
                        ?SLOG(warning, #{
                            msg => "will_message_processed_but_record_deletion_failed",
                            client_id => ClientId,
                            topic => Topic,
                            will_id => WillId,
                            unexpected_result => Other
                        }),
                        ok
                end;
            {error, Reason} ->
                %% 发布失败，不删除记录，以便后续重试
                ?SLOG(error, #{
                    msg => "will_message_publish_failed_keeping_record_for_retry",
                    client_id => ClientId,
                    topic => Topic,
                    will_id => WillId,
                    reason => Reason,
                    safety_measure => "record_preserved_for_potential_retry"
                }),
                        {error, Reason};
            Other ->
                %% 其他返回值，记录并视为成功
                ?SLOG(info, #{
                    msg => "will_message_publish_other_result_treating_as_success",
                    client_id => ClientId,
                    topic => Topic,
                    will_id => WillId,
                    result => Other,
                    note => "treating_as_successful_publish"
                }),

                %% 删除记录
                case emqx_plugin_mongodb_api:delete_one(Collection, Filter) of
                    {ok, #{<<"deletedCount">> := 1}} ->
                        ?SLOG(info, #{
                            msg => "will_message_record_deleted_after_other_result",
                            client_id => ClientId,
                            topic => Topic,
                            will_id => WillId
                        }),
                        ok;
                    _ ->
                        ?SLOG(warning, #{
                            msg => "will_message_processed_but_record_deletion_failed_other_result",
                            client_id => ClientId,
                            topic => Topic,
                            will_id => WillId
                        }),
                        ok
                end
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_in_atomic_delete_and_publish_emqx_restart",
                client_id => ClientId,
                topic => Topic,
                will_id => WillId,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%% @doc 原子性删除遗嘱消息记录并发布消息（原始版本，保留用于其他场景）
%% 确保只有成功删除记录的线程才能发布遗嘱消息
%% 这防止了多个线程同时发布同一个遗嘱消息的竞态条件
atomic_delete_and_publish_will_message(WillId, ClientId, Topic, Payload, QoS, Retain) ->
    try
        Collection = get_will_collection(),
        Filter = #{<<"_id">> => WillId},

        %% 原子性删除操作：只有成功删除记录的线程才能继续发布
        case emqx_plugin_mongodb_api:delete_one(Collection, Filter) of
            {ok, #{<<"deletedCount">> := 1}} ->
                %% 成功删除记录，但需要二次验证客户端状态
                %% 关键修复：防止客户端在删除后、发布前重连的竞态条件
                case is_client_connected(ClientId) of
                    true ->
                        %% 客户端在删除后重连了，不应该发布遗嘱消息
                        ?SLOG(warning, #{
                            msg => "client_reconnected_after_atomic_delete_skipping_will_publish",
                            client_id => ClientId,
                            topic => Topic,
                            will_id => WillId,
                            race_condition => "client_reconnected_between_delete_and_publish"
                        }),
                        {skipped, client_reconnected_after_delete};
                    false ->
                        %% 客户端仍未连接，可以安全发布遗嘱消息
                        ?SLOG(info, #{
                            msg => "atomic_delete_successful_client_still_disconnected_proceeding_with_publish",
                            client_id => ClientId,
                            topic => Topic,
                            will_id => WillId
                        }),

                        %% 构建并发布EMQX消息
                %% 修复：使用真实的客户端ID作为发布者，便于消息追踪和持久化
                Message = emqx_message:make(ClientId, QoS, Topic, Payload),
                Message2 = emqx_message:set_flag(retain, Retain, Message),
                Message3 = emqx_message:set_header(will_message, true, Message2),
                Message4 = emqx_message:set_header(original_client_id, ClientId, Message3),
                %% 关键修复：添加遗嘱消息ID到消息头部，用于消息发布钩子的精确删除
                Message5 = emqx_message:set_header(will_message_id, WillId, Message4),
                %% 修复：标记为恢复的遗嘱消息，避免触发消息持久化和主题过滤
                Message6 = emqx_message:set_header(restored_will_message, true, Message5),
                Message7 = emqx_message:set_header(skip_persistence, true, Message6),

                case emqx:publish(Message7) of
                    ok ->
                        ?SLOG(info, #{
                            msg => "will_message_published_successfully_after_atomic_delete",
                            client_id => ClientId,
                            topic => Topic,
                            will_id => WillId
                        }),
                        ok;
                    [] ->
                        %% 没有订阅者，但消息已发布
                        ?SLOG(info, #{
                            msg => "will_message_published_no_subscribers",
                            client_id => ClientId,
                            topic => Topic,
                            will_id => WillId
                        }),
                        ok;
                    {error, Reason} ->
                        ?SLOG(error, #{
                            msg => "failed_to_publish_will_message_after_atomic_delete",
                            client_id => ClientId,
                            topic => Topic,
                            will_id => WillId,
                            reason => Reason
                        }),
                        {error, Reason};
                    Other ->
                        %% 其他返回值，假设发布成功
                        ?SLOG(warning, #{
                            msg => "unexpected_publish_result_assuming_success",
                            client_id => ClientId,
                            topic => Topic,
                            will_id => WillId,
                            result => Other
                        }),
                        ok
                end
                end;

            {ok, #{<<"deletedCount">> := 0}} ->
                %% 记录已被其他线程删除（可能是客户端重连处理）
                ?SLOG(info, #{
                    msg => "will_message_record_already_deleted_by_another_thread",
                    client_id => ClientId,
                    topic => Topic,
                    will_id => WillId,
                    race_condition => "record_deleted_by_client_reconnection_or_another_thread"
                }),
                {skipped, already_deleted};

            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_delete_will_message_record_atomically",
                    client_id => ClientId,
                    topic => Topic,
                    will_id => WillId,
                    reason => Reason
                }),
                {error, Reason}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_in_atomic_delete_and_publish_will_message",
                client_id => ClientId,
                topic => Topic,
                will_id => WillId,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%% @doc 发布EMQX重启后的待处理遗嘱消息（已废弃）
%% 警告：此函数已废弃，使用intelligently_handle_will_messages_after_restart/1替代
%%
%% 功能说明：
%% 这是遗嘱消息持久化的特殊场景处理函数
%%
%% 场景分析：
%% 1. EMQX异常关闭时，客户端异常断开，遗嘱消息应该被发布
%% 2. 但由于EMQX异常关闭，遗嘱消息没有被发布，只是保存在MongoDB中
%% 3. EMQX重启后，如果客户端不重连，这些遗嘱消息永远不会被发布
%% 4. 这违反了MQTT协议的遗嘱消息语义
%%
%% 解决方案：
%% 在EMQX重启时，插件主动发布这些遗嘱消息，确保MQTT协议语义正确
%%
%% 设计考虑：
%% - 这是遗嘱消息持久化唯一需要偏离方案A的场景
%% - 其他持久化功能仍然采用方案A（纯持久化）
%% - 这确保了MQTT协议的正确性和完整性
publish_pending_will_messages_after_restart(WillMessageDocs) ->
    try
        ?SLOG(info, #{
            msg => "starting_to_publish_pending_will_messages_after_restart",
            message_count => length(WillMessageDocs),
            note => "special_handling_for_emqx_restart_scenario"
        }),

        %% 逐个发布遗嘱消息
        lists:foreach(fun(WillDoc) ->
            try
                publish_single_will_message_after_restart(WillDoc)
            catch
                E:R:S ->
                    ClientIdForError = maps:get(<<"client_id">>, WillDoc, <<"unknown">>),
                    ?SLOG(error, #{
                        msg => "failed_to_publish_single_will_message_after_restart",
                        client_id => ClientIdForError,
                        error => E,
                        reason => R,
                        stacktrace => S
                    })
            end
        end, WillMessageDocs),

        ?SLOG(info, #{
            msg => "completed_publishing_pending_will_messages_after_restart",
            message_count => length(WillMessageDocs)
        })
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_publishing_pending_will_messages_after_restart",
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% @doc 发布单个遗嘱消息（EMQX重启后）
%%
%% 修复：解决竞态条件问题
%%
%% 功能说明：
%% 1. 检查客户端是否已重新连接
%% 2. 如果客户端已连接，不发布遗嘱消息
%% 3. 使用唯一ID精确删除遗嘱消息记录
%% 4. 避免删除新连接的遗嘱消息
%%
%% 竞态条件解决方案：
%% - 发布前检查客户端连接状态
%% - 使用文档的唯一_id进行精确删除
%% - 避免误删新连接的遗嘱消息
%%
%% 注意：
%% 这是遗嘱消息持久化唯一需要直接发布消息的场景
%% 其他场景都应该让EMQX的内置功能处理
publish_single_will_message_after_restart(WillDoc) ->
    try
        ClientId = maps:get(<<"client_id">>, WillDoc, <<>>),
        WillId = maps:get(<<"_id">>, WillDoc, <<>>),
        Topic = maps:get(<<"topic">>, WillDoc, <<>>),
        Payload = maps:get(<<"payload">>, WillDoc, <<>>),
        QoS = maps:get(<<"qos">>, WillDoc, 0),
        Retain = maps:get(<<"retain">>, WillDoc, false),

        %% 关键修复：检查客户端是否已重新连接
        %% 如果客户端已连接，则不应该发布遗嘱消息
        case is_client_connected(ClientId) of
            true ->
                ?SLOG(info, #{
                    msg => "client_already_reconnected_skipping_will_message_publish",
                    client_id => ClientId,
                    topic => Topic,
                    will_id => WillId,
                    note => "client_reconnected_will_message_not_needed"
                }),

                %% 客户端已重连，删除旧的遗嘱消息记录（使用精确ID）
                delete_will_message_by_id(WillId);
            false ->
                %% 客户端未连接，发布遗嘱消息
                ?SLOG(info, #{
                    msg => "client_not_connected_publishing_will_message_after_restart",
                    client_id => ClientId,
                    topic => Topic,
                    will_id => WillId,
                    qos => QoS,
                    retain => Retain
                }),

                %% 构建EMQX消息
                %% 注意：遗嘱消息的发布者应该是系统，而不是客户端ID
                Message = emqx_message:make(<<"$will_message_system">>, QoS, Topic, Payload),
                Message2 = emqx_message:set_flag(retain, Retain, Message),

                %% 添加遗嘱消息标识，确保消息发布钩子能正确识别
                Message3 = emqx_message:set_header(will_message, true, Message2),
                Message4 = emqx_message:set_header(restart_recovery, true, Message3),
                Message5 = emqx_message:set_header(original_client_id, ClientId, Message4),
                %% 修复：标记为恢复的遗嘱消息，避免触发消息持久化和主题过滤
                Message6 = emqx_message:set_header(restored_will_message, true, Message5),
                Message7 = emqx_message:set_header(skip_persistence, true, Message6),

                %% 发布遗嘱消息
                case emqx:publish(Message7) of
                    ok ->
                        ?SLOG(info, #{
                            msg => "will_message_published_successfully_after_restart",
                            client_id => ClientId,
                            topic => Topic,
                            will_id => WillId
                        }),

                        %% 发布成功后使用精确ID删除MongoDB记录
                        delete_will_message_by_id(WillId);
                    {error, Reason} ->
                        ?SLOG(error, #{
                            msg => "failed_to_publish_will_message_after_restart",
                            client_id => ClientId,
                            topic => Topic,
                            will_id => WillId,
                            reason => Reason
                        })
                end
        end
    catch
        E:R:S ->
            ClientIdForCatch = maps:get(<<"client_id">>, WillDoc, <<"unknown">>),
            WillIdForCatch = maps:get(<<"_id">>, WillDoc, <<"unknown">>),
            ?SLOG(error, #{
                msg => "error_publishing_single_will_message_after_restart",
                client_id => ClientIdForCatch,
                will_id => WillIdForCatch,
                error => E,
                reason => R,
                stacktrace => S,
                will_doc => WillDoc
            })
    end.

%% @doc 检查客户端是否已连接
%%
%% 功能说明：
%% 检查指定客户端是否当前已连接到EMQX
%% 用于避免在客户端已重连时发布遗嘱消息
%%
%% 参数：
%% - ClientId: 客户端ID
%%
%% 返回值：
%% - true: 客户端已连接
%% - false: 客户端未连接
%%
%% Java等价概念：
%% public boolean isClientConnected(String clientId) {
%%     return clientManager.isClientConnected(clientId);
%% }
is_client_connected(ClientId) ->
    try
        %% 修复：在EMQX重启后的短时间内，使用更严格的客户端连接检查
        %% 避免因为系统状态不稳定导致的误判

        %% 第一步：检查通道管理器中的连接
        case emqx_cm:lookup_channels(ClientId) of
            [] ->
                false; % 客户端未连接
            Channels ->
                %% 第二步：验证通道是否真正活跃
                %% 检查通道信息是否完整且有效
                case verify_channel_active(Channels) of
                    true ->
                        %% 第三步：额外验证 - 检查客户端信息
                        case emqx_cm:get_chan_info(ClientId) of
                            undefined ->
                                ?SLOG(debug, #{
                                    msg => "channel_exists_but_no_chan_info",
                                    client_id => ClientId,
                                    note => "possible_system_inconsistency"
                                }),
                                false; % 通道存在但无客户端信息，可能是系统不一致
                            _ChanInfo ->
                                true   % 客户端确实已连接
                        end;
                    false ->
                        false % 通道不活跃
                end
        end
    catch
        E:R ->
            ?SLOG(debug, #{
                msg => "error_checking_client_connection_status",
                client_id => ClientId,
                error => E,
                reason => R,
                note => "assuming_client_not_connected"
            }),
            false % 出错时假设客户端未连接
    end.

%% @doc 验证通道是否真正活跃
%% 在EMQX重启后，通道列表可能包含无效的通道引用
verify_channel_active([]) ->
    false;
verify_channel_active([Channel | _Rest]) ->
    try
        %% 尝试获取通道的基本信息来验证其有效性
        case erlang:is_process_alive(Channel) of
            true ->
                %% 进程存在，进一步检查是否为有效的MQTT通道
                case erlang:process_info(Channel, [status, message_queue_len]) of
                    undefined ->
                        false; % 进程信息无法获取
                    ProcessInfo ->
                        %% 检查进程状态
                        Status = proplists:get_value(status, ProcessInfo, undefined),
                        case Status of
                            running -> true;
                            waiting -> true;
                            _ -> false
                        end
                end;
            false ->
                false % 进程不存在
        end
    catch
        _:_ ->
            false % 检查过程中出错
    end.

%% @doc 根据唯一ID删除遗嘱消息
%%
%% 功能说明：
%% 使用遗嘱消息的唯一_id字段精确删除MongoDB记录
%% 避免误删其他遗嘱消息记录
%%
%% 参数：
%% - WillId: 遗嘱消息的唯一ID
%%
%% 设计原理：
%% - 使用_id字段确保精确删除
%% - 避免竞态条件导致的误删
%% - 比按client_id删除更安全
%%
%% Java等价概念：
%% public void deleteWillMessageById(String willId) {
%%     willMessageRepository.deleteById(willId);
%% }
delete_will_message_by_id(WillId) ->
    try
        Collection = get_will_collection(),
        Filter = #{<<"_id">> => WillId},

        ?SLOG(debug, #{
            msg => "deleting_will_message_by_id",
            will_id => WillId
        }),

        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                               {delete, Collection, Filter}) of
            {ok, _} ->
                ?SLOG(debug, #{
                    msg => "will_message_deleted_by_id",
                    will_id => WillId
                });
            {async_return, ok} ->
                ?SLOG(debug, #{
                    msg => "will_message_deleted_by_id_async",
                    will_id => WillId
                });
            {async_return, {ok, _}} ->
                ?SLOG(debug, #{
                    msg => "will_message_deleted_by_id_async",
                    will_id => WillId
                });
            {async_return, {error, Reason}} ->
                ?SLOG(warning, #{
                    msg => "failed_to_delete_will_message_by_id_async",
                    will_id => WillId,
                    reason => Reason
                });
            {error, Reason} ->
                ?SLOG(warning, #{
                    msg => "failed_to_delete_will_message_by_id",
                    will_id => WillId,
                    reason => Reason
                })
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_deleting_will_message_by_id",
                will_id => WillId,
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% @doc 记录待处理的遗嘱消息信息
%% 修复：采用方案A - 纯持久化方案
%%
%% 功能说明：
%% 1. 记录系统异常关闭后发现的待处理遗嘱消息
%% 2. 不直接发布，而是记录详细信息供管理员参考
%% 3. 让EMQX的内置功能在适当时机处理这些遗嘱消息
%%
%% 参数说明：
%% - Docs: MongoDB中的遗嘱消息文档列表
%%
%% Java等价概念：
%% public void logPendingWillMessages(List<WillMessageDoc> docs) {
%%     for (WillMessageDoc doc : docs) {
%%         logger.warn("Pending will message found after abnormal shutdown: " +
%%                    "client={}, topic={}, payload_size={}, created_at={}",
%%                    doc.getClientId(), doc.getTopic(),
%%                    doc.getPayload().length, doc.getCreatedAt());
%%     }
%% }
log_pending_will_messages(Docs) ->
    lists:foreach(fun(Doc) ->
        try
            ClientId = maps:get(<<"client_id">>, Doc, <<"unknown">>),
            Topic = maps:get(<<"topic">>, Doc, <<"unknown">>),
            Payload = maps:get(<<"payload">>, Doc, <<>>),
            CreatedAt = maps:get(<<"created_at">>, Doc, 0),
            QoS = maps:get(<<"qos">>, Doc, 0),

            ?SLOG(warning, #{
                msg => "pending_will_message_found_after_abnormal_shutdown",
                client_id => ClientId,
                topic => Topic,
                payload_size => byte_size(Payload),
                qos => QoS,
                created_at => CreatedAt,
                note => "will_be_handled_by_emqx_when_client_truly_disconnects"
            })
        catch
            E:R:S ->
                ?SLOG(error, #{
                    msg => "error_logging_pending_will_message",
                    error => E,
                    reason => R,
                    stacktrace => S,
                    doc => Doc
                })
        end
    end, Docs).

%% @doc 批量发布遗嘱消息
%% 警告：这个函数在方案A中不应该被调用！
%%
%% 修复说明：
%% - 这个函数违反了方案A的设计原则（插件不应该直接发布遗嘱消息）
%% - 保留此函数仅用于向后兼容，但会记录警告日志
%% - 在新的方案A中，应该让EMQX的内置功能处理遗嘱消息发布
%%
%% 如果这个函数被调用，说明代码中还有地方没有按照方案A修复
publish_will_messages_batch(Docs) ->
    %% 记录警告：这个函数不应该在方案A中被调用
    ?SLOG(warning, #{
        msg => "publish_will_messages_batch_called_violates_plan_a",
        docs_count => length(Docs),
        note => "this_function_should_not_be_called_in_pure_persistence_approach"
    }),

    lists:foldl(fun(Doc, Count) ->
        try
            %% 解析遗嘱消息文档
            ClientId = maps:get(<<"client_id">>, Doc, <<>>),
            Topic = maps:get(<<"topic">>, Doc, <<>>),
            Payload = maps:get(<<"payload">>, Doc, <<>>),
            QoS = maps:get(<<"qos">>, Doc, 0),
            Retain = maps:get(<<"retain">>, Doc, false),
            Properties = decode_properties(maps:get(<<"properties">>, Doc, #{})),

            % 验证关键字段不为空
            case {ClientId, Topic} of
                {<<>>, _} ->
                    ?SLOG(warning, #{
                        msg => "skipping_will_message_empty_client_id",
                        doc => Doc
                    }),
                    Count;
                {_, <<>>} ->
                    ?SLOG(warning, #{
                        msg => "skipping_will_message_empty_topic",
                        client_id => ClientId,
                        doc => Doc
                    }),
                    Count;
                {_, _} ->
                    % 构建EMQX消息
                    % 注意：遗嘱消息的发布者应该是系统，而不是客户端ID
                    Message = emqx_message:make(<<"$will_message_system">>, QoS, Topic, Payload),
                    Message1 = emqx_message:set_header(properties, Properties, Message),
                    Message2 = emqx_message:set_flag(retain, Retain, Message1),
                    Message3 = emqx_message:set_header(will_message, true, Message2),
                    Message4 = emqx_message:set_header(original_client_id, ClientId, Message3),
                    %% 修复：标记为恢复的遗嘱消息，避免触发消息持久化和主题过滤
                    Message5 = emqx_message:set_header(restored_will_message, true, Message4),
                    Message6 = emqx_message:set_header(skip_persistence, true, Message5),

                    ?SLOG(debug, #{
                        msg => "publishing_will_message",
                        client_id => ClientId,
                        topic => Topic,
                        payload_size => byte_size(Payload),
                        qos => QoS,
                        retain => Retain
                    }),

                    % 发布遗嘱消息
                    case emqx:publish(Message6) of
                        ok ->
                            ?SLOG(info, #{
                                msg => "will_message_published_due_to_abnormal_shutdown",
                                client_id => ClientId,
                                topic => Topic
                            }),

                            % 更新MongoDB中的状态
                            update_will_message_published_status(ClientId, Topic, Message4),

                            Count + 1;
                        [] ->
                            % EMQX返回空列表，可能表示没有订阅者，但消息已发布
                            ?SLOG(info, #{
                                msg => "will_message_published_no_subscribers",
                                client_id => ClientId,
                                topic => Topic
                            }),

                            % 更新MongoDB中的状态
                            update_will_message_published_status(ClientId, Topic, Message4),

                            Count + 1;
                        {error, Reason} ->
                            ?SLOG(error, #{
                                msg => "failed_to_publish_will_message",
                                client_id => ClientId,
                                topic => Topic,
                                reason => Reason
                            }),
                            Count;
                        Other ->
                            % 处理其他可能的返回值
                            ?SLOG(warning, #{
                                msg => "unexpected_publish_result",
                                client_id => ClientId,
                                topic => Topic,
                                result => Other
                            }),

                            % 假设消息已发布，更新状态
                            update_will_message_published_status(ClientId, Topic, Message3),

                            Count + 1
                    end
            end
        catch
            E:R:S ->
                ?SLOG(error, #{
                    msg => "error_publishing_single_will_message",
                    error => E,
                    reason => R,
                    stacktrace => S,
                    doc => Doc
                }),
                Count
        end
    end, 0, Docs).

%% @doc 为特定客户端发布遗嘱消息（异常断开时立即处理）
publish_will_message_for_client(ClientId) ->
    try
        case find_will_message(ClientId) of
            {ok, WillDoc} ->
                % 解析遗嘱消息文档
                Topic = maps:get(<<"topic">>, WillDoc, <<>>),
                Payload = maps:get(<<"payload">>, WillDoc, <<>>),
                QoS = maps:get(<<"qos">>, WillDoc, 0),
                Retain = maps:get(<<"retain">>, WillDoc, false),
                Properties = decode_properties(maps:get(<<"properties">>, WillDoc, #{})),

                % 构建EMQX消息
                % 注意：遗嘱消息的发布者应该是系统，而不是客户端ID
                Message = emqx_message:make(<<"$will_message_system">>, QoS, Topic, Payload),
                Message1 = emqx_message:set_header(properties, Properties, Message),
                Message2 = emqx_message:set_flag(retain, Retain, Message1),
                Message3 = emqx_message:set_header(will_message, true, Message2),
                Message4 = emqx_message:set_header(original_client_id, ClientId, Message3),
                %% 修复：标记为恢复的遗嘱消息，避免触发消息持久化和主题过滤
                Message5 = emqx_message:set_header(restored_will_message, true, Message4),
                Message6 = emqx_message:set_header(skip_persistence, true, Message5),

                % 发布遗嘱消息
                ?SLOG(info, #{
                    msg => "attempting_to_publish_will_message_after_restart",
                    client_id => ClientId,
                    topic => Topic,
                    payload_size => byte_size(Payload),
                    qos => QoS,
                    retain => Retain,
                    publisher => <<"$will_message_system">>,
                    headers => emqx_message:get_headers(Message6)
                }),
                case emqx:publish(Message6) of
                    ok ->
                        ?SLOG(info, #{
                            msg => "will_message_published_for_abnormal_disconnect",
                            client_id => ClientId,
                            topic => Topic
                        }),

                        % 发布成功后删除遗嘱消息记录
                        update_will_message_published_status(ClientId, Topic, Message3);
                    [] ->
                        % EMQX返回空列表，可能表示没有订阅者，但消息已发布
                        ?SLOG(info, #{
                            msg => "will_message_published_for_abnormal_disconnect_no_subscribers",
                            client_id => ClientId,
                            topic => Topic
                        }),

                        % 发布成功后删除遗嘱消息记录
                        update_will_message_published_status(ClientId, Topic, Message3);
                    {error, Reason} ->
                        ?SLOG(error, #{
                            msg => "failed_to_publish_will_message_for_abnormal_disconnect",
                            client_id => ClientId,
                            topic => Topic,
                            reason => Reason
                        });
                    Other ->
                        % 处理其他可能的返回值
                        ?SLOG(warning, #{
                            msg => "unexpected_publish_result_for_abnormal_disconnect",
                            client_id => ClientId,
                            topic => Topic,
                            result => Other
                        }),

                        % 假设消息已发布，删除记录
                        update_will_message_published_status(ClientId, Topic, Message3)
                end;
            {error, not_found} ->
                ?SLOG(debug, #{
                    msg => "no_will_message_found_for_abnormal_disconnect",
                    client_id => ClientId
                });
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_find_will_message_for_abnormal_disconnect",
                    client_id => ClientId,
                    reason => Reason
                })
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_publishing_will_message_for_client",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% @doc 标记正常关闭
mark_normal_shutdown() ->
    application:set_env(emqx_plugin_mongodb, normal_shutdown_flag, true).

%% @doc 清除正常关闭标记（在正常关闭时调用）
clear_normal_shutdown_flag() ->
    try
        % 使用异步方式清除环境变量，避免超时
        spawn(fun() ->
            try
                % 使用带超时的调用
                case catch gen_server:call(application_controller,
                                         {unset_env, emqx_plugin_mongodb, normal_shutdown_flag, []},
                                         2000) of
                    ok ->
                        ?SLOG(debug, #{msg => "normal_shutdown_flag_cleared"});
                    {'EXIT', {timeout, _}} ->
                        ?SLOG(warning, #{msg => "normal_shutdown_flag_clear_timeout"});
                    Other ->
                        ?SLOG(debug, #{msg => "normal_shutdown_flag_clear_result", result => Other})
                end
            catch
                E:R:S ->
                    ?SLOG(warning, #{
                        msg => "failed_to_unset_normal_shutdown_flag_env",
                        error => E,
                        reason => R,
                        stacktrace => S
                    })
            end
        end),
        ?SLOG(debug, #{msg => "normal_shutdown_flag_cleanup_started_async"})
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_clearing_normal_shutdown_flag",
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% @doc 尝试发布遗嘱消息，带有回退机制
%% 这个函数提供多种发布方式，确保遗嘱消息能够被成功发布
-spec try_publish_will_message_with_fallback(emqx_types:message(), binary(), binary(), binary()) -> ok | {error, term()}.
try_publish_will_message_with_fallback(Message, ClientId, Topic, WillId) ->
    try
        %% 方法1：直接发布
        case emqx:publish(Message) of
            [] ->
                %% 没有订阅者，但消息已发布成功
                ?SLOG(info, #{
                    msg => "will_message_published_no_subscribers",
                    client_id => ClientId,
                    topic => Topic,
                    will_id => WillId,
                    method => "direct_publish"
                }),
                ok;
            [_|_] ->
                %% 有订阅者，消息发布成功
                ?SLOG(info, #{
                    msg => "will_message_published_with_subscribers",
                    client_id => ClientId,
                    topic => Topic,
                    will_id => WillId,
                    method => "direct_publish"
                }),
                ok;
            {error, Reason} ->
                %% 直接发布失败，尝试回退方法
                ?SLOG(warning, #{
                    msg => "direct_publish_failed_trying_fallback",
                    client_id => ClientId,
                    topic => Topic,
                    will_id => WillId,
                    reason => Reason
                }),
                try_publish_will_message_fallback_methods(Message, ClientId, Topic, WillId);
            Other ->
                %% 其他返回值，记录并假设成功
                ?SLOG(info, #{
                    msg => "will_message_publish_other_result_assuming_success",
                    client_id => ClientId,
                    topic => Topic,
                    will_id => WillId,
                    result => Other,
                    method => "direct_publish"
                }),
                ok
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "exception_in_direct_publish_trying_fallback",
                client_id => ClientId,
                topic => Topic,
                will_id => WillId,
                error => E,
                reason => R,
                stacktrace => S
            }),
            try_publish_will_message_fallback_methods(Message, ClientId, Topic, WillId)
    end.

%% @doc 尝试回退发布方法
-spec try_publish_will_message_fallback_methods(emqx_types:message(), binary(), binary(), binary()) -> ok | {error, term()}.
try_publish_will_message_fallback_methods(Message, ClientId, Topic, WillId) ->
    %% 方法2：使用emqx_broker:publish
    try
        case emqx_broker:publish(Message) of
            ok ->
                ?SLOG(info, #{
                    msg => "will_message_published_via_broker",
                    client_id => ClientId,
                    topic => Topic,
                    will_id => WillId,
                    method => "broker_publish"
                }),
                ok;
            {error, Reason} ->
                ?SLOG(warning, #{
                    msg => "broker_publish_failed_trying_next_fallback",
                    client_id => ClientId,
                    topic => Topic,
                    will_id => WillId,
                    reason => Reason
                }),
                try_publish_will_message_final_fallback(Message, ClientId, Topic, WillId);
            Other ->
                ?SLOG(info, #{
                    msg => "broker_publish_other_result_assuming_success",
                    client_id => ClientId,
                    topic => Topic,
                    will_id => WillId,
                    result => Other,
                    method => "broker_publish"
                }),
                ok
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "exception_in_broker_publish_trying_final_fallback",
                client_id => ClientId,
                topic => Topic,
                will_id => WillId,
                error => E,
                reason => R,
                stacktrace => S
            }),
            try_publish_will_message_final_fallback(Message, ClientId, Topic, WillId)
    end.

%% @doc 最终回退发布方法
-spec try_publish_will_message_final_fallback(emqx_types:message(), binary(), binary(), binary()) -> ok | {error, term()}.
try_publish_will_message_final_fallback(Message, ClientId, Topic, WillId) ->
    %% 方法3：使用消息路由器直接路由
    try
        case emqx_router:do_route(Message) of
            ok ->
                ?SLOG(info, #{
                    msg => "will_message_routed_successfully",
                    client_id => ClientId,
                    topic => Topic,
                    will_id => WillId,
                    method => "router_route"
                }),
                ok;
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "all_publish_methods_failed",
                    client_id => ClientId,
                    topic => Topic,
                    will_id => WillId,
                    final_reason => Reason
                }),
                {error, {all_methods_failed, Reason}};
            Other ->
                ?SLOG(info, #{
                    msg => "router_route_other_result_assuming_success",
                    client_id => ClientId,
                    topic => Topic,
                    will_id => WillId,
                    result => Other,
                    method => "router_route"
                }),
                ok
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "all_publish_methods_failed_with_exception",
                client_id => ClientId,
                topic => Topic,
                will_id => WillId,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {all_methods_failed, {E, R}}}
    end.

%%--------------------------------------------------------------------
%% 安全的环境变量清理函数
%%--------------------------------------------------------------------

%% @doc 安全地清理多个环境变量
%% 使用异步方式和超时处理，避免阻塞插件停止过程
safe_unset_env_variables(EnvKeys) ->
    spawn(fun() ->
        lists:foreach(fun(Key) ->
            safe_unset_env_async(Key)
        end, EnvKeys)
    end).

%% @doc 安全地异步清理单个环境变量
%% 使用超时机制避免无限等待
safe_unset_env_async(Key) ->
    spawn(fun() ->
        try
            % 使用超时的gen_server调用
            case catch gen_server:call(application_controller,
                                     {unset_env, emqx_plugin_mongodb, Key, []},
                                     2000) of  % 2秒超时
                ok ->
                    ?SLOG(debug, #{
                        msg => "env_variable_unset_successfully",
                        key => Key
                    });
                {'EXIT', {timeout, _}} ->
                    ?SLOG(warning, #{
                        msg => "env_variable_unset_timeout",
                        key => Key,
                        note => "application_controller_busy_but_continuing"
                    });
                Error ->
                    ?SLOG(warning, #{
                        msg => "env_variable_unset_failed",
                        key => Key,
                        error => Error
                    })
            end
        catch
            E:R:S ->
                ?SLOG(warning, #{
                    msg => "env_variable_unset_exception",
                    key => Key,
                    error => E,
                    reason => R,
                    stacktrace => S
                })
        end
    end).

%% @doc 集成到协调器
%% 这个函数将遗嘱消息持久化模块注册到模块协调器中
%% 使模块能够参与系统级的协调和通信
%%
%% 功能说明：
%% 1. 向协调器注册模块信息和元数据
%% 2. 声明模块的功能特性和优先级
%% 3. 建立与其他模块的通信通道
%% 4. 启用协调器的监控和管理功能
%%
%% 返回值：
%% - ok: 集成成功
%% - {error, Reason}: 集成失败
%%
%% Java等价概念：
%% 类似于Spring的@Component注册
%% 或者OSGi Bundle的激活
%% 或者微服务的服务注册
integrate() ->
    try
        % 定义模块信息和元数据
        ModuleInfo = #{
            priority => high,                      % 高优先级
            description => <<"Will message persistence module">>,
            features => [
                will_message_persistence,          % 遗嘱消息持久化功能
                abnormal_disconnection_handling,   % 异常断开处理功能
                system_recovery,                   % 系统恢复功能
                expiry_cleanup                     % 过期清理功能
            ],
            dependencies => [
                emqx_plugin_mongodb_api,           % 依赖MongoDB API模块
                emqx_plugin_mongodb_session        % 依赖会话持久化模块
            ],
            version => <<"1.0.0">>,
            status => active
        },

        % 向协调器注册模块
        case emqx_plugin_mongodb_coordinator:register_module(?MODULE, ModuleInfo) of
            ok ->
                ?SLOG(info, #{
                    msg => "will_persistence_module_integrated_successfully",
                    module => ?MODULE,
                    features => maps:get(features, ModuleInfo)
                }),
                ok;
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_integrate_will_persistence_module",
                    module => ?MODULE,
                    reason => Reason
                }),
                {error, Reason}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "will_persistence_integration_exception",
                module => ?MODULE,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {integration_exception, R}}
    end.