%% @doc EMQX MongoDB Plugin - 保留消息持久化模块
%% 这个模块是MQTT保留消息持久化的核心组件，提供企业级的保留消息管理能力
%%
%% 功能概述：
%% 1. 保留消息持久化 - 监听EMQX内置保留消息的变化并持久化到MongoDB
%% 2. 系统恢复支持 - EMQX重启时从MongoDB恢复保留消息到内存
%% 3. 过期消息清理 - 定期清理过期的保留消息，防止存储膨胀
%% 4. 查询和统计 - 提供保留消息的查询和统计功能
%% 5. 主题过滤 - 自动过滤$SYS系统主题，避免系统消息持久化
%% 6. 性能优化 - 支持批量操作和索引优化，提高查询性能
%%
%% MQTT保留消息机制：
%% - 保留消息是MQTT协议的核心特性，用于为新订阅者提供最新的主题状态
%% - 每个主题只能有一个保留消息，新的保留消息会覆盖旧的
%% - 发布空消息（payload为空）可以删除主题的保留消息
%% - 保留消息在订阅者连接时立即发送，无需等待新的发布
%%
%% 设计原则：
%% - 非侵入式设计：不干扰EMQX内置的保留消息处理流程
%% - 职责分离：只负责持久化存储，不负责消息路由和发送
%% - 事件驱动：通过钩子监听保留消息的变化事件
%% - 系统集成：在系统启动时恢复保留消息到EMQX内存
%% - 性能优先：使用高效的MongoDB操作和索引策略
%%
%% 使用的EMQX钩子：
%% - message.publish: 监听消息发布事件，检测和处理保留消息
%%
%% Java等价概念：
%% 类似于Spring Boot中的消息持久化服务：
%% @Service
%% @Component
%% public class RetainedMessagePersistenceService {
%%     @Autowired private MongoTemplate mongoTemplate;
%%     @Autowired private RetainedMessageRepository repository;
%%
%%     @EventListener
%%     @Async
%%     public void onMessagePublish(MessagePublishEvent event) {
%%         if (event.getMessage().isRetained()) {
%%             if (event.getMessage().getPayload().isEmpty()) {
%%                 deleteRetainedMessage(event.getTopic());
%%             } else {
%%                 storeRetainedMessage(event.getMessage());
%%             }
%%         }
%%     }
%%
%%     @PostConstruct
%%     public void restoreRetainedMessages() {
%%         List<RetainedMessage> messages = repository.findAll();
%%         for (RetainedMessage msg : messages) {
%%             emqxRetainedMessageStore.put(msg.getTopic(), msg);
%%         }
%%     }
%%
%%     @Scheduled(fixedDelay = 3600000) // 1小时清理一次
%%     public void cleanupExpiredMessages() {
%%         repository.deleteExpiredMessages();
%%     }
%% }
%%
%% 设计模式：
%% - 观察者模式：监听EMQX的消息发布事件
%% - 策略模式：不同类型消息的处理策略
%% - 模板方法模式：统一的消息处理流程
%% - 单例模式：全局唯一的持久化服务
%% @end

-module(emqx_plugin_mongodb_retained).

-include("emqx_plugin_mongodb.hrl").

%% ============================================================================
%% 插件生命周期管理函数 - 模块启动、配置和关闭管理
%% 这些函数管理保留消息持久化模块的完整生命周期
%% 类似于Java Spring Boot的生命周期管理：
%% @PostConstruct - 初始化资源和配置
%% @PreDestroy - 清理资源和保存状态
%% @ConfigurationProperties - 配置加载和验证
%% ============================================================================
-export([
    init/0,                 % 初始化模块 - 类似于@PostConstruct，设置基础环境
    load/0,                 % 加载模块（无参数版本）
    load/1,                 % 加载模块（带配置参数）- 类似于@ConfigurationProperties
    unload/0,               % 卸载模块 - 类似于@PreDestroy，清理资源和保存状态
    integrate/0             % 集成到协调器 - 类似于@Component注册
]).

%% ============================================================================
%% EMQX钩子回调函数 - 响应MQTT消息生命周期事件
%% 这些函数作为EMQX钩子回调，监听消息发布事件
%% 类似于Java Spring的事件监听器：
%% @EventListener
%% @Async  // 异步处理，不阻塞消息传递
%% public void onMessageEvent(MessageEvent event) { ... }
%% ============================================================================
-export([
    on_message_publish/1    % 消息发布事件监听器
                           % 功能：当消息发布时触发，检测和处理保留消息
                           % Java等价：@EventListener(MessagePublishEvent.class)
]).

%% ============================================================================
%% 保留消息管理核心API - 保留消息的CRUD操作和管理功能
%% 这些函数提供保留消息的完整生命周期管理
%% 类似于Java中的Repository层或Service层方法
%% ============================================================================
-export([
    store_retained_message_to_mongodb/1,    % 存储保留消息到MongoDB
                                           % 功能：将保留消息持久化到MongoDB集合
                                           % Java等价：public void saveRetainedMessage(RetainedMessage message)

    delete_retained_message_from_mongodb/1, % 从MongoDB删除保留消息
                                           % 功能：删除指定主题的保留消息
                                           % Java等价：public void deleteRetainedMessage(String topic)

    restore_retained_messages_from_mongodb/0, % 从MongoDB恢复保留消息
                                             % 功能：系统启动时从MongoDB恢复所有保留消息到EMQX内存
                                             % Java等价：@PostConstruct public void restoreRetainedMessages()

    find_retained_messages/1,               % 查找保留消息
                                           % 功能：根据主题模式查找匹配的保留消息
                                           % Java等价：public List<RetainedMessage> findRetainedMessages(String topicFilter)

    get_retained_message/1,                 % 获取单个保留消息
                                           % 功能：获取指定主题的保留消息
                                           % Java等价：public Optional<RetainedMessage> getRetainedMessage(String topic)

    cleanup_expired_retained_messages/0,    % 清理过期的保留消息
                                           % 功能：定期清理过期的保留消息，防止存储膨胀
                                           % Java等价：@Scheduled public void cleanupExpiredMessages()

    get_retained_message_stats/0,           % 获取保留消息统计信息
                                           % 功能：获取保留消息的数量、大小等统计数据
                                           % Java等价：public RetainedMessageStats getStats()

    validate_mqtt_topic_filter/1,         % 验证MQTT主题过滤器
                                         % 功能：根据MQTT协议规范验证主题过滤器的有效性
                                         % Java等价：public ValidationResult validateMqttTopicFilter(String topicFilter)

    extract_message_expiry_interval/1,    % 提取Message Expiry Interval属性
                                         % 功能：从MQTT 5.0属性中提取消息过期时间
                                         % Java等价：public Integer extractMessageExpiryInterval(Properties props)

    calculate_retained_message_expiry_time/1, % 计算保留消息过期时间
                                             % 功能：根据MQTT协议计算保留消息的过期时间
                                             % Java等价：public long calculateRetainedMessageExpiryTime(Integer expiryInterval)

    is_retained_message_expired/1,        % 检查保留消息是否过期
                                         % 功能：考虑MQTT 5.0 Message Expiry Interval的动态过期
                                         % Java等价：public boolean isRetainedMessageExpired(RetainedMessage message)

    update_message_expiry_interval/1      % 更新保留消息的过期时间
                                         % 功能：根据MQTT 5.0协议，在消息传输过程中更新过期时间
                                         % Java等价：public RetainedMessage updateMessageExpiryInterval(RetainedMessage message)
]).

%% ============================================================================
%% 内部辅助函数 - 模块内部使用的工具和配置函数
%% 这些函数提供模块内部的辅助功能，类似于Java的private方法
%% ============================================================================
-export([
    ensure_retained_collection_and_indexes/1, % 确保集合和索引存在 - 类似于@PostConstruct初始化方法
    get_retained_collection/0,              % 获取保留消息集合名称 - 配置获取方法
    wait_for_resource/0,                    % 等待MongoDB资源可用 - 资源就绪检查
    start_cleanup_timer/0,                  % 启动清理定时器 - 类似于@Scheduled任务启动
    stop_cleanup_timer/0,                   % 停止清理定时器 - 资源清理方法
    parse_time_duration/1                   % 解析时间配置 - 配置解析工具方法
]).

%% 注意：MongoDB集合名称现在统一定义在 emqx_plugin_mongodb.hrl 中
%% 这样可以保持整个项目的命名一致性和集中管理

%% @doc 初始化保留消息持久化模块
%% 这个函数是模块的启动入口，负责初始化所有必要的资源和服务
%%
%% 功能说明：
%% 1. 检查保留消息持久化功能是否启用
%% 2. 等待MongoDB资源就绪
%% 3. 创建必要的集合和索引
%% 4. 记录初始化状态日志
%%
%% 返回值：
%% - ok: 初始化成功
%%
%% Java等价概念：
%% @PostConstruct
%% @ConditionalOnProperty(name = "retained.persistence.enabled", havingValue = "true")
%% public void initializeRetainedMessagePersistence() {
%%     if (retainedPersistenceEnabled) {
%%         waitForMongoDBReady();
%%         createCollectionsAndIndexes();
%%         logger.info("Retained message persistence initialized successfully");
%%     }
%% }
%%
%% 设计特点：
%% - 条件初始化：只有在启用保留消息持久化时才执行初始化
%% - 资源等待：确保MongoDB资源可用后再进行后续操作
%% - 完整设置：一次性完成所有必要的初始化工作
init() ->
    %% 记录模块初始化开始的日志
    %% 这有助于系统启动过程的跟踪和调试
    ?SLOG(info, #{msg => "initializing_retained_message_persistence_module"}),

    %% 检查保留消息持久化功能是否启用
    %% 这是一个功能开关，允许运行时控制保留消息持久化
    %% 在Java中相当于：
    %% @ConditionalOnProperty(name = "retained.persistence.enabled", havingValue = "true")
    case application:get_env(emqx_plugin_mongodb, retained_persistence_enabled, false) of
        true ->
            %% 保留消息持久化已启用，开始初始化所有组件
            ?SLOG(info, #{msg => "retained_persistence_enabled", module => ?MODULE}),

            %% 等待MongoDB资源就绪
            %% 确保数据库连接可用后再进行后续操作
            %% 在Java中相当于：
            %% @Autowired private MongoHealthIndicator mongoHealth;
            %% while (!mongoHealth.isHealthy()) { Thread.sleep(1000); }
            wait_for_resource(),

            %% 获取保留消息集合名称并确保集合和索引存在
            %% 在Java中相当于：
            %% String collectionName = getRetainedMessageCollectionName();
            %% mongoTemplate.createCollection(collectionName);
            %% createIndexes(collectionName);
            Collection = get_retained_collection(),
            ensure_retained_collection_and_indexes(Collection),

            %% 记录初始化成功的日志
            ?SLOG(info, #{msg => "retained_message_persistence_module_initialized_successfully"});
        false ->
            %% 保留消息持久化未启用，跳过初始化
            %% 记录信息日志，说明功能被禁用
            ?SLOG(info, #{msg => "retained_message_persistence_disabled_skipping_initialization"})
    end.

%% @doc 加载保留消息持久化模块（带配置参数）
load(Env) ->
    try
        % 从传入的配置中读取保留消息持久化设置
        RetainedPersistenceConfig = maps:get(retained_persistence, Env, #{}),
        RetainedPersistenceEnabled = maps:get(enabled, RetainedPersistenceConfig, false),

        ?SLOG(info, #{
            msg => "checking_retained_persistence_config",
            enabled => RetainedPersistenceEnabled,
            config => RetainedPersistenceConfig
        }),

        case RetainedPersistenceEnabled of
            true ->
                ?SLOG(info, #{msg => "loading_retained_persistence_module"}),

                % 保存配置到应用环境变量
                application:set_env(emqx_plugin_mongodb, retained_persistence_enabled, true),
                application:set_env(emqx_plugin_mongodb, retained_expiry,
                                   parse_time_duration(maps:get(retained_expiry, RetainedPersistenceConfig, <<"7d">>))),
                application:set_env(emqx_plugin_mongodb, retained_cleanup_interval,
                                   parse_time_duration(maps:get(cleanup_interval, RetainedPersistenceConfig, <<"1h">>))),
                application:set_env(emqx_plugin_mongodb, retained_cleanup_batch_size,
                                   maps:get(cleanup_batch_size, RetainedPersistenceConfig, 1000)),
                application:set_env(emqx_plugin_mongodb, retained_collection,
                                   maps:get(collection, RetainedPersistenceConfig, ?DEFAULT_RETAINED_MESSAGE_COLLECTION)),
                application:set_env(emqx_plugin_mongodb, retained_max_messages,
                                   maps:get(max_retained_messages, RetainedPersistenceConfig, 100000)),
                application:set_env(emqx_plugin_mongodb, retained_topic_filters,
                                   maps:get(topic_filters, RetainedPersistenceConfig, [])),
                application:set_env(emqx_plugin_mongodb, retained_enable_topic_filters,
                                   maps:get(enable_topic_filters, RetainedPersistenceConfig, false)),

                % 注册钩子
                register_hooks(),

                % 启动清理定时器
                start_cleanup_timer(),

                % 保留消息恢复现在通过MongoDB连接事件触发，不再使用时间延迟
                ?SLOG(info, #{msg => "retained_message_restoration_will_be_triggered_on_mongodb_connection"}),

                ?SLOG(info, #{msg => "retained_persistence_module_loaded_successfully"});
            false ->
                ?SLOG(info, #{msg => "retained_persistence_disabled_skipping_load"})
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_loading_retained_persistence_module",
                error => E,
                reason => R,
                stacktrace => S
            }),
            ok
    end.

%% @doc 加载保留消息持久化模块（无参数版本，向后兼容）
load() ->
    try
        % 从配置中读取保留消息持久化设置
        Config = emqx_plugin_mongodb:get_config(),
        load(Config)
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_loading_retained_persistence_module_no_params",
                error => E,
                reason => R,
                stacktrace => S
            }),
            ok
    end.

%% @doc 卸载保留消息持久化模块
unload() ->
    case application:get_env(emqx_plugin_mongodb, retained_persistence_enabled, false) of
        true ->
            ?SLOG(info, #{msg => "unloading_retained_persistence_module"}),

            % 注销钩子
            unregister_hooks(),

            % 停止清理定时器
            stop_cleanup_timer(),

            ?SLOG(info, #{msg => "retained_persistence_module_unloaded_successfully"});
        false ->
            ?SLOG(info, #{msg => "retained_persistence_not_enabled_skipping_unload"})
    end.

%% @doc 注册保留消息相关钩子
register_hooks() ->
    % 消息发布钩子 - 监听保留消息存储事件（低优先级，不干扰EMQX处理）
    emqx_hooks:add('message.publish', {?MODULE, on_message_publish, []}, 1000).

%% @doc 注销保留消息相关钩子
unregister_hooks() ->
    emqx_hooks:del('message.publish', {?MODULE, on_message_publish}).

%% @doc 消息发布钩子回调 - 监听保留消息并持久化到MongoDB
%% 修复：采用方案A - 纯持久化方案
%%
%% 功能说明：
%% 1. 监听所有消息发布事件
%% 2. 检测保留消息标志
%% 3. 仅负责持久化到MongoDB，不干扰EMQX内置处理
%% 4. 异步处理，确保不阻塞消息发布流程
%%
%% 设计原理：
%% - 插件只做持久化，让EMQX处理所有保留消息逻辑
%% - 空载荷保留消息：删除MongoDB中的记录
%% - 非空载荷保留消息：存储到MongoDB
%% - 不修改消息内容，确保EMQX正常处理
%%
%% Java等价概念：
%% @EventListener
%% @Async
%% public Message onMessagePublish(MessagePublishEvent event) {
%%     Message message = event.getMessage();
%%     if (message.isRetain()) {
%%         // 异步持久化，不阻塞消息发布
%%         CompletableFuture.runAsync(() -> {
%%             if (message.getPayload().length == 0) {
%%                 retainedMessageRepository.deleteByTopic(message.getTopic());
%%             } else {
%%                 retainedMessageRepository.save(message);
%%             }
%%         });
%%     }
%%     // 不修改消息，让MQTT broker正常处理
%%     return message;
%% }
on_message_publish(Message) ->
    try
        % 检查是否为恢复的消息，如果是则跳过保留消息处理
        case emqx_message:get_header(skip_persistence, Message, false) of
            true ->
                % 这是恢复的消息，跳过保留消息处理避免重复处理
                ?SLOG(debug, #{
                    msg => "skipping_recovered_message_retained_processing",
                    topic => emqx_message:topic(Message),
                    reason => "message_already_processed_during_recovery"
                }),
                {ok, Message};
            false ->
                %% 检查是否为保留消息
                case emqx_message:get_flag(retain, Message) of
            true ->
                %% 是保留消息，先检查主题过滤
                Topic = emqx_message:topic(Message),

                %% 过滤系统主题 - 使用EMQX内置函数检查系统消息
                %% 系统主题通常包含EMQX内部状态，不应该持久化
                ?SLOG(debug, #{
                    msg => "retained_message_topic_check",
                    topic => Topic,
                    is_sys_message => emqx_message:is_sys(Message)
                }),

                case emqx_message:is_sys(Message) of
                    true ->
                        %% 跳过系统消息的保留消息
                        ?SLOG(debug, #{
                            msg => "retained_message_sys_message_filtered",
                            topic => Topic,
                            reason => "sys_messages_not_persisted"
                        }),
                        ok;
                    false ->
                        %% 非系统消息，进行持久化处理
                        Payload = emqx_message:payload(Message),

                        ?SLOG(debug, #{
                            msg => "retained_message_detected_for_persistence",
                            topic => Topic,
                            payload_size => byte_size(Payload),
                            processing => "async_persistence_only"
                        }),

                        %% 异步处理，确保不阻塞消息发布流程
                        %% 这是关键：插件的处理不能影响EMQX的正常消息发布
                        spawn(fun() ->
                            case byte_size(Payload) of
                                0 ->
                                    %% 空载荷保留消息：根据MQTT协议，删除该主题的保留消息
                                    ?SLOG(debug, #{
                                        msg => "processing_empty_payload_retained_message",
                                        topic => Topic,
                                        action => "delete_from_mongodb"
                                    }),
                                    delete_retained_message_from_mongodb(Topic);
                                _ ->
                                    %% 非空载荷保留消息：存储到MongoDB
                                    ?SLOG(debug, #{
                                        msg => "processing_non_empty_payload_retained_message",
                                        topic => Topic,
                                        payload_size => byte_size(Payload),
                                        action => "store_to_mongodb"
                                    }),
                                    store_retained_message_to_mongodb(Message)
                            end
                        end)
                end;
                false ->
                    %% 不是保留消息，插件不需要处理
                    %% 这里不记录debug日志，避免日志过多
                    ok
                end
        end
    catch
        E:R:S ->
            %% 捕获所有异常，确保插件的错误不影响EMQX的消息处理
            ?SLOG(error, #{
                msg => "error_in_retained_message_persistence_hook",
                error => E,
                reason => R,
                stacktrace => S,
                topic => emqx_message:topic(Message),
                note => "plugin_error_does_not_affect_emqx_processing"
            })
    end,
    %% 重要：始终返回原始消息，不做任何修改
    %% 这确保EMQX的内置保留消息功能正常工作
    {ok, Message}.



%% @doc 存储保留消息到MongoDB
%% 修复：采用方案A - 纯持久化方案
%%
%% 功能说明：
%% 1. 将保留消息持久化到MongoDB集合
%% 2. 使用主题作为唯一标识，支持upsert操作
%% 3. 包含完整的消息信息：载荷、QoS、属性、发布者等
%% 4. 支持消息过期时间计算
%%
%% 设计原理：
%% - 插件只负责持久化，不影响EMQX的保留消息处理
%% - 使用upsert确保同一主题的保留消息被正确替换
%% - 异步处理，不阻塞EMQX的消息发布流程
%% - 完整的错误处理，确保插件错误不影响EMQX
%%
%% Java等价概念：
%% @Async
%% public void storeRetainedMessage(Message message) {
%%     try {
%%         RetainedMessageEntity entity = RetainedMessageEntity.builder()
%%             .topic(message.getTopic())
%%             .payload(message.getPayload())
%%             .qos(message.getQos())
%%             .properties(message.getProperties())
%%             .publisherId(message.getFrom())
%%             .publishedAt(System.currentTimeMillis())
%%             .expiresAt(calculateExpiryTime(message.getProperties()))
%%             .build();
%%
%%         // 使用upsert操作，替换已存在的保留消息
%%         retainedMessageRepository.upsert(entity);
%%     } catch (Exception e) {
%%         logger.error("Failed to store retained message", e);
%%     }
%% }
store_retained_message_to_mongodb(Message) ->
    try
        %% 提取消息信息
        Topic = emqx_message:topic(Message),
        Payload = emqx_message:payload(Message),
        QoS = emqx_message:qos(Message),
        Properties = emqx_message:get_header(properties, Message, #{}),
        PublisherId = emqx_message:from(Message),

        %% 验证关键字段
        case {Topic, byte_size(Topic)} of
            {<<>>, _} ->
                ?SLOG(warning, #{
                    msg => "skipping_retained_message_empty_topic",
                    publisher_id => PublisherId
                });
            {_, 0} ->
                ?SLOG(warning, #{
                    msg => "skipping_retained_message_empty_topic",
                    publisher_id => PublisherId
                });
            {ValidTopic, _} ->
                % 解析MQTT 5.0属性
                MessageExpiryInterval = extract_message_expiry_interval(Properties),
                PayloadFormatIndicator = extract_payload_format_indicator(Properties),
                ContentType = extract_content_type(Properties),
                ResponseTopic = extract_response_topic(Properties),
                CorrelationData = extract_correlation_data(Properties),
                UserProperties = extract_user_properties(Properties),

                % 计算消息过期时间（考虑MQTT 5.0 Message Expiry Interval）
                ExpiryTime = calculate_retained_message_expiry_time(MessageExpiryInterval),

                %% 构建符合MQTT协议的保留消息文档
                RetainedDoc = #{
                    <<"_id">> => ValidTopic,  %% 使用主题作为唯一标识，支持upsert
                    <<"topic">> => ValidTopic,
                    <<"payload">> => Payload,
                    <<"qos">> => QoS,
                    <<"retain">> => true,  % 明确标记为保留消息

                    % MQTT 5.0消息属性
                    <<"message_expiry_interval">> => MessageExpiryInterval,
                    <<"payload_format_indicator">> => PayloadFormatIndicator,
                    <<"content_type">> => ContentType,
                    <<"response_topic">> => ResponseTopic,
                    <<"correlation_data">> => CorrelationData,
                    <<"user_properties">> => UserProperties,

                    % 发布者和时间信息
                    <<"publisher_id">> => format_publisher_id(PublisherId),
                    <<"published_at">> => erlang:system_time(millisecond),
                    <<"expires_at">> => ExpiryTime,
                    <<"last_updated">> => erlang:system_time(millisecond),

                    % 节点信息
                    <<"node">> => atom_to_binary(node(), utf8),

                    % 兼容性：保留原始属性
                    <<"properties">> => encode_properties(Properties)
                },

                %% 获取集合名称
                Collection = get_retained_collection(),

                %% 懒初始化：确保集合和索引存在
                ensure_retained_collection_and_indexes(Collection),

                ?SLOG(debug, #{
                    msg => "storing_retained_message_to_mongodb",
                    topic => ValidTopic,
                    collection => Collection,
                    payload_size => byte_size(Payload),
                    publisher_id => PublisherId,
                    qos => QoS,
                    doc_id => ValidTopic,  % 添加调试信息
                    doc_keys => maps:keys(RetainedDoc)  % 添加文档键调试信息
                }),

                %% 使用upsert操作，替换已存在的保留消息
                %% 这确保同一主题只有一个保留消息，符合MQTT协议
                case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                                       {upsert, Collection, #{<<"_id">> => ValidTopic}, RetainedDoc}) of
                    {ok, _} ->
                        ?SLOG(debug, #{
                            msg => "retained_message_stored_successfully",
                            topic => ValidTopic,
                            method => "sync"
                        });
                    {async_return, ok} ->
                        ?SLOG(debug, #{
                            msg => "retained_message_stored_successfully",
                            topic => ValidTopic,
                            method => "async"
                        });
                    {async_return, {ok, _}} ->
                        ?SLOG(debug, #{
                            msg => "retained_message_stored_successfully",
                            topic => ValidTopic,
                            method => "async"
                        });
                    {async_return, {error, Reason}} ->
                        ?SLOG(error, #{
                            msg => "failed_to_store_retained_message",
                            topic => ValidTopic,
                            reason => Reason,
                            method => "async"
                        });
                    {error, Reason} ->
                        ?SLOG(error, #{
                            msg => "failed_to_store_retained_message",
                            topic => ValidTopic,
                            reason => Reason,
                            method => "sync"
                        })
                end
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_storing_retained_message",
                error => E,
                reason => R,
                stacktrace => S,
                topic => emqx_message:topic(Message),
                note => "plugin_error_does_not_affect_emqx_processing"
            })
    end.

%% @doc 从MongoDB删除保留消息
%% 修复：采用方案A - 纯持久化方案
%%
%% 功能说明：
%% 1. 根据MQTT协议，空载荷保留消息表示删除该主题的保留消息
%% 2. 从MongoDB集合中删除指定主题的保留消息记录
%% 3. 插件只负责持久化层面的删除，不影响EMQX内置处理
%%
%% 设计原理：
%% - 插件只处理MongoDB中的持久化数据
%% - EMQX的内置保留消息功能会自动处理内存中的保留消息
%% - 异步处理，不阻塞EMQX的消息发布流程
%% - 完整的错误处理，确保插件错误不影响EMQX
%%
%% Java等价概念：
%% @Async
%% public void deleteRetainedMessage(String topic) {
%%     try {
%%         // 从数据库删除保留消息记录
%%         retainedMessageRepository.deleteByTopic(topic);
%%         logger.debug("Retained message deleted from database: {}", topic);
%%     } catch (Exception e) {
%%         logger.error("Failed to delete retained message from database", e);
%%     }
%% }
delete_retained_message_from_mongodb(Topic) ->
    try
        %% 验证主题
        case {Topic, byte_size(Topic)} of
            {<<>>, _} ->
                ?SLOG(warning, #{
                    msg => "skipping_delete_retained_message_empty_topic"
                });
            {_, 0} ->
                ?SLOG(warning, #{
                    msg => "skipping_delete_retained_message_empty_topic"
                });
            {ValidTopic, _} ->
                Collection = get_retained_collection(),

                ?SLOG(debug, #{
                    msg => "deleting_retained_message_from_mongodb",
                    topic => ValidTopic,
                    collection => Collection,
                    reason => "empty_payload_retain_message"
                }),

                %% 使用主题作为_id进行删除
                case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                                       {delete, Collection, #{<<"_id">> => ValidTopic}}) of
                    {ok, _} ->
                        ?SLOG(debug, #{
                            msg => "retained_message_deleted_successfully",
                            topic => ValidTopic,
                            method => "sync"
                        });
                    {async_return, ok} ->
                        ?SLOG(debug, #{
                            msg => "retained_message_deleted_successfully",
                            topic => ValidTopic,
                            method => "async"
                        });
                    {async_return, {ok, _}} ->
                        ?SLOG(debug, #{
                            msg => "retained_message_deleted_successfully",
                            topic => ValidTopic,
                            method => "async"
                        });
                    {async_return, {error, Reason}} ->
                        ?SLOG(error, #{
                            msg => "failed_to_delete_retained_message",
                            topic => ValidTopic,
                            reason => Reason,
                            method => "async"
                        });
                    {error, Reason} ->
                        ?SLOG(error, #{
                            msg => "failed_to_delete_retained_message",
                            topic => ValidTopic,
                            reason => Reason,
                            method => "sync"
                        })
                end
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_deleting_retained_message",
                error => E,
                reason => R,
                stacktrace => S,
                topic => Topic,
                note => "plugin_error_does_not_affect_emqx_processing"
            })
    end.

%% @doc 查找匹配主题过滤器的保留消息
find_retained_messages(TopicFilter) ->
    try
        Collection = get_retained_collection(),

        % 构建MongoDB查询
        % 对于MQTT主题过滤器，需要转换为MongoDB正则表达式
        RegexPattern = topic_filter_to_regex(TopicFilter),
        Filter = #{<<"topic">> => #{<<"$regex">> => RegexPattern}},

        ?SLOG(debug, #{
            msg => "finding_retained_messages",
            topic_filter => TopicFilter,
            regex_pattern => RegexPattern,
            collection => Collection
        }),

        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                               {find, Collection, Filter, #{}, 0, 1000}) of  % 限制最多1000条
            {ok, Docs} when is_list(Docs) ->
                ?SLOG(debug, #{
                    msg => "retained_messages_found",
                    topic_filter => TopicFilter,
                    count => length(Docs)
                }),
                % 解析保留消息文档
                Messages = [parse_retained_document(Doc) || Doc <- Docs],
                {ok, Messages};
            {async_return, {ok, Docs}} when is_list(Docs) ->
                Messages = [parse_retained_document(Doc) || Doc <- Docs],
                {ok, Messages};
            {async_return, ok} ->
                ?SLOG(debug, #{
                    msg => "async_return_ok_fallback_to_sync_find",
                    topic_filter => TopicFilter
                }),
                % 异步查询返回ok，尝试同步查询
                case emqx_resource:simple_sync_query(?PLUGIN_MONGODB_RESOURCE_ID,
                                                    {find, Collection, Filter, #{}, 0, 1000}) of
                    {ok, Docs} when is_list(Docs) ->
                        Messages = [parse_retained_document(Doc) || Doc <- Docs],
                        {ok, Messages};
                    {error, Reason} ->
                        {error, Reason};
                    Other ->
                        ?SLOG(warning, #{
                            msg => "sync_find_unexpected_result",
                            topic_filter => TopicFilter,
                            result => Other
                        }),
                        {ok, []}
                end;
            {async_return, {error, Reason}} ->
                {error, Reason};
            {error, Reason} ->
                ?SLOG(warning, #{
                    msg => "retained_messages_query_error",
                    topic_filter => TopicFilter,
                    reason => Reason
                }),
                {error, Reason};
            Other ->
                ?SLOG(warning, #{
                    msg => "unexpected_find_result",
                    topic_filter => TopicFilter,
                    result => Other
                }),
                {ok, []}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_finding_retained_messages",
                error => E,
                reason => R,
                stacktrace => S,
                topic_filter => TopicFilter
            }),
            {error, {E, R}}
    end.

%% @doc 获取指定主题的保留消息
get_retained_message(Topic) ->
    try
        Collection = get_retained_collection(),
        Filter = #{<<"_id">> => Topic},

        ?SLOG(debug, #{
            msg => "getting_retained_message",
            topic => Topic,
            collection => Collection,
            filter => Filter
        }),

        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                               {find_one, Collection, Filter, #{}}) of
            {ok, Doc} when is_map(Doc) ->
                ?SLOG(debug, #{
                    msg => "retained_message_found",
                    topic => Topic,
                    doc_keys => maps:keys(Doc)
                }),
                Message = parse_retained_document(Doc),
                {ok, Message};
            {ok, null} ->
                ?SLOG(debug, #{
                    msg => "retained_message_not_found",
                    topic => Topic
                }),
                {error, not_found};
            {async_return, {ok, Doc}} when is_map(Doc) ->
                Message = parse_retained_document(Doc),
                {ok, Message};
            {async_return, ok} ->
                ?SLOG(debug, #{
                    msg => "async_return_ok_fallback_to_sync",
                    topic => Topic
                }),
                % 异步查询返回ok，尝试同步查询
                case emqx_resource:simple_sync_query(?PLUGIN_MONGODB_RESOURCE_ID,
                                                    {find_one, Collection, Filter, #{}}) of
                    {ok, Doc} when is_map(Doc) ->
                        Message = parse_retained_document(Doc),
                        {ok, Message};
                    {ok, null} ->
                        {error, not_found};
                    {error, Reason} ->
                        {error, Reason};
                    Other ->
                        ?SLOG(warning, #{
                            msg => "sync_query_unexpected_result",
                            topic => Topic,
                            result => Other
                        }),
                        {error, not_found}
                end;
            {async_return, {error, Reason}} ->
                {error, Reason};
            {error, Reason} ->
                ?SLOG(warning, #{
                    msg => "retained_message_query_error",
                    topic => Topic,
                    reason => Reason
                }),
                {error, Reason};
            Other ->
                ?SLOG(warning, #{
                    msg => "unexpected_query_result",
                    topic => Topic,
                    result => Other
                }),
                {error, not_found}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_getting_retained_message",
                error => E,
                reason => R,
                stacktrace => S,
                topic => Topic
            }),
            {error, {E, R}}
    end.

%% @doc 清理过期的保留消息
cleanup_expired_retained_messages() ->
    try
        Collection = get_retained_collection(),
        Now = erlang:system_time(millisecond),

        % 查找过期的保留消息
        Filter = #{
            <<"expires_at">> => #{
                <<"$lt">> => Now,
                <<"$ne">> => null  % 排除永不过期的消息
            }
        },

        ?SLOG(info, #{
            msg => "cleaning_up_expired_retained_messages",
            collection => Collection,
            current_time => Now
        }),

        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                               {delete_many, Collection, Filter}) of
            {ok, #{<<"deletedCount">> := Count}} ->
                ?SLOG(info, #{
                    msg => "expired_retained_messages_cleaned_up",
                    deleted_count => Count
                }),
                {ok, Count};
            {async_return, {ok, #{<<"deletedCount">> := Count}}} ->
                ?SLOG(info, #{
                    msg => "expired_retained_messages_cleaned_up_async",
                    deleted_count => Count
                }),
                {ok, Count};
            {async_return, ok} ->
                {ok, 0};
            {async_return, {error, Reason}} ->
                {error, Reason};
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_cleanup_expired_retained_messages",
                    reason => Reason
                }),
                {error, Reason}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_cleaning_up_expired_retained_messages",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%% @doc 获取保留消息统计信息
get_retained_message_stats() ->
    try
        Collection = get_retained_collection(),

        % 获取总数
        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                               {count, Collection, #{}}) of
            {ok, TotalCount} ->
                % 获取过期消息数量
                Now = erlang:system_time(millisecond),
                ExpiredFilter = #{
                    <<"expires_at">> => #{
                        <<"$lt">> => Now,
                        <<"$ne">> => null
                    }
                },

                case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                                       {count, Collection, ExpiredFilter}) of
                    {ok, ExpiredCount} ->
                        {ok, #{
                            total_count => TotalCount,
                            expired_count => ExpiredCount,
                            active_count => TotalCount - ExpiredCount
                        }};
                    {async_return, {ok, ExpiredCount}} ->
                        {ok, #{
                            total_count => TotalCount,
                            expired_count => ExpiredCount,
                            active_count => TotalCount - ExpiredCount
                        }};
                    {error, Reason} ->
                        {error, Reason}
                end;
            {async_return, {ok, TotalCount}} ->
                {ok, #{total_count => TotalCount, expired_count => 0, active_count => TotalCount}};
            {error, Reason} ->
                {error, Reason}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_getting_retained_message_stats",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.



%% @doc 获取保留消息集合名称
get_retained_collection() ->
    application:get_env(emqx_plugin_mongodb, retained_collection, ?DEFAULT_RETAINED_MESSAGE_COLLECTION).

%% @doc 确保保留消息集合和索引存在
ensure_retained_collection_and_indexes(Collection) ->
    try
        % 创建索引
        Indexes = [
            % 主题索引 - 用于快速查找
            #{
                <<"key">> => #{<<"topic">> => 1},
                <<"name">> => <<"rtm_topic_1_index">>,
                <<"unique">> => true
            },
            % 过期时间索引 - 用于TTL清理
            #{
                <<"key">> => #{<<"expires_at">> => 1},
                <<"name">> => <<"rtm_expires_at_1_index">>,
                <<"expireAfterSeconds">> => 0  % MongoDB TTL索引
            },
            % 发布时间索引 - 用于时间范围查询
            #{
                <<"key">> => #{<<"published_at">> => 1},
                <<"name">> => <<"rtm_published_at_1_index">>
            },
            % 发布者索引 - 用于按发布者查询
            #{
                <<"key">> => #{<<"publisher_id">> => 1},
                <<"name">> => <<"rtm_publisher_id_1_index">>
            }
        ],

        % 创建索引
        lists:foreach(fun(IndexSpec) ->
            try
                case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                                       {create_index, Collection, IndexSpec}) of
                    {ok, _} ->
                        ?SLOG(debug, #{
                            msg => "retained_message_index_created",
                            collection => Collection,
                            index => maps:get(<<"name">>, IndexSpec)
                        });
                    {async_return, ok} ->
                        ?SLOG(debug, #{
                            msg => "retained_message_index_created_async",
                            collection => Collection,
                            index => maps:get(<<"name">>, IndexSpec)
                        });
                    {async_return, {ok, _}} ->
                        ?SLOG(debug, #{
                            msg => "retained_message_index_created_async",
                            collection => Collection,
                            index => maps:get(<<"name">>, IndexSpec)
                        });
                    {error, Reason} ->
                        ?SLOG(warning, #{
                            msg => "failed_to_create_retained_message_index",
                            collection => Collection,
                            index => maps:get(<<"name">>, IndexSpec),
                            reason => Reason
                        });
                    {async_return, {error, Reason}} ->
                        ?SLOG(warning, #{
                            msg => "failed_to_create_retained_message_index_async",
                            collection => Collection,
                            index => maps:get(<<"name">>, IndexSpec),
                            reason => Reason
                        })
                end
            catch
                E:R:S ->
                    ?SLOG(error, #{
                        msg => "error_creating_retained_message_index",
                        collection => Collection,
                        index => maps:get(<<"name">>, IndexSpec),
                        error => E,
                        reason => R,
                        stacktrace => S
                    })
            end
        end, Indexes)
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_ensuring_retained_message_collection_and_indexes",
                collection => Collection,
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% @doc 将MQTT主题过滤器转换为MongoDB正则表达式
%% 修复：实现符合MQTT协议规范的主题过滤器转换
topic_filter_to_regex(TopicFilter) ->
    % 验证主题过滤器的有效性
    case validate_mqtt_topic_filter(TopicFilter) of
        {ok, ValidatedFilter} ->
            convert_mqtt_filter_to_regex(ValidatedFilter);
        {error, Reason} ->
            ?SLOG(warning, #{
                msg => "invalid_mqtt_topic_filter",
                topic_filter => TopicFilter,
                reason => Reason
            }),
            % 返回一个永远不匹配的正则表达式
            <<"^$a">>
    end.

%% @doc 验证MQTT主题过滤器
%% 根据MQTT协议规范验证主题过滤器的有效性
validate_mqtt_topic_filter(TopicFilter) when is_binary(TopicFilter) ->
    % 检查多级通配符 # 的位置
    case binary:matches(TopicFilter, <<"#">>) of
        [] ->
            % 没有多级通配符，只需验证单级通配符
            validate_single_level_wildcards(TopicFilter);
        Matches ->
            % 有多级通配符，验证其位置
            validate_multi_level_wildcard(TopicFilter, Matches)
    end;
validate_mqtt_topic_filter(_) ->
    {error, invalid_type}.

%% @doc 验证多级通配符的位置
validate_multi_level_wildcard(TopicFilter, Matches) ->
    case length(Matches) of
        1 ->
            % 只有一个 #，检查是否在末尾
            [{Pos, 1}] = Matches,
            FilterSize = byte_size(TopicFilter),
            case Pos of
                Pos when Pos =:= FilterSize - 1 ->
                    % # 在末尾，检查前面是否有分隔符
                    case Pos of
                        0 ->
                            % # 是整个过滤器
                            {ok, TopicFilter};
                        _ ->
                            % 检查 # 前面的字符
                            PrevChar = binary:at(TopicFilter, Pos - 1),
                            case PrevChar of
                                $/ ->
                                    % # 前面是 /，符合协议
                                    validate_single_level_wildcards(binary:part(TopicFilter, 0, Pos - 1));
                                _ ->
                                    {error, invalid_multi_level_wildcard_position}
                            end
                    end;
                _ ->
                    {error, multi_level_wildcard_not_at_end}
            end;
        _ ->
            {error, multiple_multi_level_wildcards}
    end.

%% @doc 验证单级通配符
validate_single_level_wildcards(TopicFilter) ->
    % 单级通配符 + 可以出现在任何位置，但必须占据整个主题级别
    case validate_plus_wildcards(TopicFilter, 0) of
        ok -> {ok, TopicFilter};
        Error -> Error
    end.

%% @doc 验证 + 通配符的位置
validate_plus_wildcards(TopicFilter, Pos) when Pos < byte_size(TopicFilter) ->
    case binary:at(TopicFilter, Pos) of
        $+ ->
            % 找到 +，检查其前后字符
            case validate_plus_position(TopicFilter, Pos) of
                ok -> validate_plus_wildcards(TopicFilter, Pos + 1);
                Error -> Error
            end;
        _ ->
            validate_plus_wildcards(TopicFilter, Pos + 1)
    end;
validate_plus_wildcards(_, _) ->
    ok.

%% @doc 验证单个 + 的位置
validate_plus_position(TopicFilter, Pos) ->
    FilterSize = byte_size(TopicFilter),

    % 检查前一个字符
    PrevOk = case Pos of
        0 -> true;  % 在开头
        _ ->
            PrevChar = binary:at(TopicFilter, Pos - 1),
            PrevChar =:= $/
    end,

    % 检查后一个字符
    NextOk = case Pos of
        Pos when Pos =:= FilterSize - 1 -> true;  % 在末尾
        _ ->
            NextChar = binary:at(TopicFilter, Pos + 1),
            NextChar =:= $/
    end,

    case PrevOk andalso NextOk of
        true -> ok;
        false -> {error, invalid_single_level_wildcard_position}
    end.

%% @doc 将验证过的MQTT过滤器转换为正则表达式
convert_mqtt_filter_to_regex(TopicFilter) ->
    % 转义MongoDB正则表达式特殊字符（除了MQTT通配符）
    EscapedFilter = escape_regex_chars(TopicFilter),

    % 将MQTT通配符转换为正则表达式
    % + 匹配单级主题（可以为空）
    % # 匹配多级主题（包括零级）
    Step1 = re:replace(EscapedFilter, "\\+", "([^/]*)", [global, {return, binary}]),
    Step2 = re:replace(Step1, "#", "(.*)", [global, {return, binary}]),

    % 添加行首和行尾锚点
    <<"^", Step2/binary, "$">>.

%% @doc 转义正则表达式特殊字符（保留MQTT通配符）
escape_regex_chars(TopicFilter) ->
    % 转义除了 + 和 # 之外的正则表达式特殊字符
    re:replace(TopicFilter, "[\\^\\$\\.\\*\\?\\(\\)\\[\\]\\{\\}\\|\\\\]", "\\\\&", [global, {return, binary}]).

%% @doc 解析保留消息文档
parse_retained_document(Doc) ->
    try
        Topic = maps:get(<<"topic">>, Doc, <<>>),
        Payload = maps:get(<<"payload">>, Doc, <<>>),
        QoS = maps:get(<<"qos">>, Doc, 0),
        PublisherId = maps:get(<<"publisher_id">>, Doc, <<"unknown">>),
        PublishedAt = maps:get(<<"published_at">>, Doc, erlang:system_time(millisecond)),

        % 构建MQTT 5.0属性，正确处理null值和空字符串
        Properties = build_mqtt5_properties_from_doc(Doc),

        % 构建EMQX消息
        Message = emqx_message:make(PublisherId, QoS, Topic, Payload),

        % 设置属性和标志
        Message1 = emqx_message:set_header(properties, Properties, Message),
        Message2 = emqx_message:set_header(timestamp, PublishedAt, Message1),
        Message3 = emqx_message:set_flag(retain, true, Message2),

        ?SLOG(debug, #{
            msg => "parsed_retained_document",
            topic => Topic,
            payload_size => byte_size(Payload),
            qos => QoS,
            properties_keys => maps:keys(Properties)
        }),

        Message3
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_parsing_retained_document",
                error => E,
                reason => R,
                stacktrace => S,
                doc => Doc
            }),
            % 返回一个基本的消息
            emqx_message:make(<<"unknown">>, 0, <<"unknown">>, <<>>)
    end.

%% @doc 从MongoDB文档构建MQTT 5.0属性
%% 正确处理null值和空字符串，避免恢复时的问题
build_mqtt5_properties_from_doc(Doc) ->
    Properties = #{},

    % 处理Message Expiry Interval
    Properties1 = case maps:get(<<"message_expiry_interval">>, Doc, null) of
        null -> Properties;
        undefined -> Properties;
        Interval when is_integer(Interval), Interval > 0 ->
            Properties#{'Message-Expiry-Interval' => Interval};
        _ -> Properties
    end,

    % 处理Payload Format Indicator
    Properties2 = case maps:get(<<"payload_format_indicator">>, Doc, null) of
        null -> Properties1;
        undefined -> Properties1;
        Indicator when is_integer(Indicator) ->
            Properties1#{'Payload-Format-Indicator' => Indicator};
        _ -> Properties1
    end,

    % 处理Content Type（正确处理空字符串和null）
    Properties3 = case maps:get(<<"content_type">>, Doc, null) of
        null -> Properties2;
        undefined -> Properties2;
        <<>> -> Properties2;  % 空字符串不设置属性
        "" -> Properties2;    % 空字符串不设置属性
        ContentType when is_binary(ContentType), byte_size(ContentType) > 0 ->
            Properties2#{'Content-Type' => ContentType};
        _ -> Properties2
    end,

    % 处理Response Topic（正确处理空字符串和null）
    Properties4 = case maps:get(<<"response_topic">>, Doc, null) of
        null -> Properties3;
        undefined -> Properties3;
        <<>> -> Properties3;  % 空字符串不设置属性
        "" -> Properties3;    % 空字符串不设置属性
        ResponseTopic when is_binary(ResponseTopic), byte_size(ResponseTopic) > 0 ->
            Properties3#{'Response-Topic' => ResponseTopic};
        _ -> Properties3
    end,

    % 处理Correlation Data（正确处理空字符串和null）
    Properties5 = case maps:get(<<"correlation_data">>, Doc, null) of
        null -> Properties4;
        undefined -> Properties4;
        <<>> -> Properties4;  % 空字符串不设置属性
        "" -> Properties4;    % 空字符串不设置属性
        CorrelationData when is_binary(CorrelationData), byte_size(CorrelationData) > 0 ->
            Properties4#{'Correlation-Data' => CorrelationData};
        _ -> Properties4
    end,

    % 处理User Properties
    Properties6 = case maps:get(<<"user_properties">>, Doc, null) of
        null -> Properties5;
        undefined -> Properties5;
        UserProps when is_map(UserProps), map_size(UserProps) > 0 ->
            Properties5#{'User-Property' => UserProps};
        _ -> Properties5
    end,

    Properties6.

%% @doc 编码消息属性
encode_properties(Properties) when is_map(Properties) ->
    % 将Erlang term转换为可存储的格式
    try
        maps:fold(fun(K, V, Acc) ->
            EncodedKey = case is_atom(K) of
                true -> atom_to_binary(K, utf8);
                false -> K
            end,
            EncodedValue = case is_atom(V) of
                true -> atom_to_binary(V, utf8);
                false -> V
            end,
            Acc#{EncodedKey => EncodedValue}
        end, #{}, Properties)
    catch
        _:_ ->
            #{}
    end;
encode_properties(_) ->
    #{}.

%% @doc 解码消息属性
decode_properties(Properties) when is_map(Properties) ->
    Properties;
decode_properties(_) ->
    #{}.

%% @doc 格式化发布者ID
format_publisher_id(PublisherId) when is_binary(PublisherId) ->
    PublisherId;
format_publisher_id(PublisherId) when is_atom(PublisherId) ->
    atom_to_binary(PublisherId, utf8);
format_publisher_id(PublisherId) ->
    iolist_to_binary(io_lib:format("~p", [PublisherId])).

%% @doc 计算消息过期时间
calculate_expiry_time(Properties) ->
    DefaultExpiry = application:get_env(emqx_plugin_mongodb, retained_expiry, 604800000), % 7天
    try
        case maps:get('Message-Expiry-Interval', Properties, undefined) of
            undefined ->
                % 没有设置过期时间，使用默认配置
                erlang:system_time(millisecond) + DefaultExpiry;
            ExpiryInterval when is_integer(ExpiryInterval), ExpiryInterval > 0 ->
                % 使用消息中的过期时间（秒转毫秒）
                erlang:system_time(millisecond) + (ExpiryInterval * 1000);
            _ ->
                % 永不过期
                null
        end
    catch
        _:_ ->
            % 出错时使用默认过期时间
            erlang:system_time(millisecond) + DefaultExpiry
    end.

%% @doc 等待MongoDB资源就绪
wait_for_resource() ->
    wait_for_resource(30).

wait_for_resource(0) ->
    ?SLOG(error, #{msg => "mongodb_resource_not_ready_timeout"}),
    error(mongodb_resource_not_ready);
wait_for_resource(Retries) ->
    case emqx_resource:health_check(?PLUGIN_MONGODB_RESOURCE_ID) of
        {ok, _} ->
            ?SLOG(debug, #{msg => "mongodb_resource_ready"});
        _ ->
            timer:sleep(1000),
            wait_for_resource(Retries - 1)
    end.

%% @doc 启动清理定时器
start_cleanup_timer() ->
    CleanupInterval = application:get_env(emqx_plugin_mongodb, retained_cleanup_interval, 3600000), % 1小时
    case CleanupInterval > 0 of
        true ->
            % 使用独立进程处理定时清理，避免进程依赖问题
            Pid = spawn(fun() -> cleanup_timer_loop(CleanupInterval) end),
            application:set_env(emqx_plugin_mongodb, retained_cleanup_timer_pid, Pid),
            ?SLOG(info, #{msg => "retained_cleanup_timer_started", interval => CleanupInterval});
        false ->
            ?SLOG(info, #{msg => "retained_cleanup_timer_disabled"})
    end.

%% @doc 清理定时器循环
cleanup_timer_loop(Interval) ->
    timer:sleep(Interval),
    ?SLOG(info, #{msg => "cleaning_expired_retained_messages"}),
    % 执行清理
    spawn(fun() -> cleanup_expired_retained_messages() end),
    % 继续循环
    cleanup_timer_loop(Interval).

%% @doc 停止清理定时器
stop_cleanup_timer() ->
    case application:get_env(emqx_plugin_mongodb, retained_cleanup_timer_pid) of
        {ok, Pid} when is_pid(Pid) ->
            case is_process_alive(Pid) of
                true ->
                    exit(Pid, normal),
                    % 使用异步方式清理环境变量，避免application_controller超时
                    spawn(fun() ->
                        timer:sleep(100),  % 短暂延迟确保进程已停止
                        try
                            application:unset_env(emqx_plugin_mongodb, retained_cleanup_timer_pid)
                        catch
                            _:_ -> ok  % 忽略清理失败
                        end
                    end),
                    ?SLOG(info, #{msg => "retained_cleanup_timer_stopped"});
                false ->
                    % 异步清理环境变量
                    spawn(fun() ->
                        try
                            application:unset_env(emqx_plugin_mongodb, retained_cleanup_timer_pid)
                        catch
                            _:_ -> ok
                        end
                    end),
                    ?SLOG(debug, #{msg => "retained_cleanup_timer_already_stopped"})
            end;
        _ ->
            ?SLOG(debug, #{msg => "no_retained_cleanup_timer_to_stop"})
    end.

%% @doc 解析时间持续时间
parse_time_duration(Duration) when is_binary(Duration) ->
    try
        emqx_schema:to_duration_ms(Duration)
    catch
        _:_ ->
            ?SLOG(warning, #{msg => "invalid_time_duration", duration => Duration}),
            604800000 % 默认7天
    end;
parse_time_duration(Duration) when is_integer(Duration) ->
    Duration;
parse_time_duration(_) ->
    604800000. % 默认7天



%% @doc 从MongoDB恢复保留消息到EMQX
%% 修复：采用方案A - 纯持久化方案
%%
%% 功能说明：
%% 1. 从MongoDB查询所有有效的保留消息
%% 2. 通过emqx:publish发布这些消息（设置retain标志）
%% 3. 让EMQX的内置保留消息功能自然处理
%% 4. 避免直接调用emqx_retainer:store_retained，防止冲突
%%
%% 设计原理：
%% - 插件只负责持久化存储，不干扰EMQX内置功能
%% - 系统重启时，通过发布消息的方式"重放"保留消息
%% - EMQX会自动识别retain标志并正确处理保留消息
%% - 保证了与EMQX内置保留消息功能的完全兼容
%%
%% 与原方案的区别：
%% - 原方案：直接存储到EMQX内存 → 可能与内置功能冲突
%% - 新方案：通过发布消息恢复 → 完全兼容内置功能
%%
%% Java等价概念：
%% @PostConstruct
%% public void restoreRetainedMessagesFromDatabase() {
%%     List<RetainedMessage> messages = retainedMessageRepository.findAllValid();
%%     for (RetainedMessage msg : messages) {
%%         // 通过MQTT发布恢复保留消息，让broker自然处理
%%         MqttMessage mqttMsg = buildMqttMessage(msg);
%%         mqttMsg.setRetain(true);
%%         mqttPublisher.publish(msg.getTopic(), mqttMsg);
%%     }
%% }
restore_retained_messages_from_mongodb() ->
    try
        ?SLOG(info, #{
            msg => "starting_retained_messages_restoration_via_retainer_api",
            method => "emqx_retainer_store_retained"
        }),

        Collection = get_retained_collection(),

        %% 查询所有有效的保留消息（未过期的）
        Now = erlang:system_time(millisecond),
        Filter = #{
            <<"$or">> => [
                #{<<"expires_at">> => #{<<"$gt">> => Now}}, %% 未过期
                #{<<"expires_at">> => null}                  %% 永不过期
            ]
        },

        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                               {find, Collection, Filter, #{}, 0, 0}) of % 0表示无限制
            {ok, Docs} when is_list(Docs) ->
                RestoredCount = restore_messages_to_emqx(Docs),
                ?SLOG(info, #{
                    msg => "retained_messages_restoration_completed_via_retainer_api",
                    restored_count => RestoredCount,
                    method => "emqx_retainer_store_retained"
                });
            {async_return, {ok, Docs}} when is_list(Docs) ->
                RestoredCount = restore_messages_to_emqx(Docs),
                ?SLOG(info, #{
                    msg => "retained_messages_restoration_completed_via_retainer_api_async",
                    restored_count => RestoredCount,
                    method => "emqx_retainer_store_retained"
                });
            {async_return, ok} ->
                ?SLOG(debug, #{
                    msg => "async_return_ok_fallback_to_sync_restore"
                }),
                % 异步查询返回ok，尝试同步查询
                case emqx_resource:simple_sync_query(?PLUGIN_MONGODB_RESOURCE_ID,
                                                    {find, Collection, Filter, #{}, 0, 0}) of
                    {ok, Docs} when is_list(Docs) ->
                        RestoredCount = restore_messages_to_emqx(Docs),
                        ?SLOG(info, #{
                            msg => "retained_messages_restoration_completed_via_retainer_api_sync_fallback",
                            restored_count => RestoredCount,
                            method => "emqx_retainer_store_retained"
                        });
                    {error, Reason} ->
                        ?SLOG(error, #{
                            msg => "failed_to_sync_query_retained_messages_for_restoration",
                            reason => Reason
                        });
                    Other ->
                        ?SLOG(warning, #{
                            msg => "sync_restore_unexpected_result",
                            result => Other
                        })
                end;
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_query_retained_messages_for_restoration",
                    reason => Reason
                });
            {async_return, {error, Reason}} ->
                ?SLOG(error, #{
                    msg => "failed_to_query_retained_messages_for_restoration_async",
                    reason => Reason
                });
            Other ->
                ?SLOG(warning, #{
                    msg => "unexpected_restore_result",
                    result => Other
                })
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_restoring_retained_messages_from_mongodb",
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% @doc 将MongoDB中的保留消息直接恢复到EMQX retainer存储
%% 修复：使用emqx_retainer:store_retained/2 API直接存储到EMQX保留消息存储
%%
%% 功能说明：
%% 1. 从MongoDB文档解析保留消息
%% 2. 使用emqx_retainer:store_retained/2直接存储到EMQX retainer
%% 3. 比通过发布更高效，直接操作EMQX保留消息存储
%% 4. 避免触发钩子，防止循环调用
%%
%% 设计原理：
%% - 使用emqx_retainer:store_retained/2直接存储到EMQX保留消息存储
%% - 比通过发布更高效，避免触发钩子和消息路由
%% - 直接操作EMQX内置的保留消息存储，确保完全兼容
%%
%% Java等价概念：
%% public int restoreRetainedMessages(List<RetainedMessageDoc> docs) {
%%     int count = 0;
%%     for (RetainedMessageDoc doc : docs) {
%%         try {
%%             // 构建保留消息对象
%%             RetainedMessage message = buildRetainedMessage(doc);
%%
%%             // 直接存储到保留消息存储，不触发发布流程
%%             retainerService.storeRetained(doc.getTopic(), message);
%%             count++;
%%         } catch (Exception e) {
%%             logger.error("Failed to restore retained message", e);
%%         }
%%     }
%%     return count;
%% }
restore_messages_to_emqx(Docs) ->
    lists:foldl(fun(Doc, Count) ->
        try
            %% 解析保留消息文档
            Topic = maps:get(<<"topic">>, Doc, <<>>),
            Payload = maps:get(<<"payload">>, Doc, <<>>),
            QoS = maps:get(<<"qos">>, Doc, 0),
            Properties = decode_properties(maps:get(<<"properties">>, Doc, #{})),

            %% 获取原始发布者ID，如果没有则使用mongodb_restore作为标识
            PublisherId = maps:get(<<"publisher_id">>, Doc, <<"mongodb_restore">>),

            %% 验证关键字段
            case {Topic, byte_size(Topic)} of
                {<<>>, _} ->
                    ?SLOG(warning, #{
                        msg => "skipping_retained_message_empty_topic",
                        doc => Doc
                    }),
                    Count;
                {_, 0} ->
                    ?SLOG(warning, #{
                        msg => "skipping_retained_message_empty_topic",
                        doc => Doc
                    }),
                    Count;
                {ValidTopic, _} ->
                    %% 构建临时消息用于系统消息检查
                    TempMessage = emqx_message:make(PublisherId, QoS, ValidTopic, Payload),
                    TempMessage1 = emqx_message:set_header(properties, Properties, TempMessage),

                    %% 修复：在恢复过程中也要过滤系统消息
                    case emqx_message:is_sys(TempMessage1) of
                        true ->
                            ?SLOG(debug, #{
                                msg => "skipping_sys_message_during_restoration",
                                topic => ValidTopic,
                                reason => "sys_messages_should_not_be_restored"
                            }),
                            Count;
                        false ->
                            %% 使用已构建的消息并设置保留标志
                            Message2 = emqx_message:set_flag(retain, true, TempMessage1),
                            %% 修复：标记为恢复的保留消息，避免触发消息持久化和主题过滤
                            Message3 = emqx_message:set_header(restored_retained_message, true, Message2),
                            Message4 = emqx_message:set_header(skip_persistence, true, Message3),

                            ?SLOG(debug, #{
                                msg => "restoring_retained_message_via_retainer_api",
                                topic => ValidTopic,
                                publisher_id => PublisherId,
                                payload_size => byte_size(Payload),
                                qos => QoS
                            }),

                            %% 修复：使用emqx:publish发布保留消息来恢复
                            %% 这样可以让EMQX的内置保留消息功能自然处理
                            try
                                case emqx:publish(Message4) of
                                    [] ->
                                        %% 没有订阅者，但保留消息已存储
                                        ?SLOG(debug, #{
                                            msg => "retained_message_restored_via_publish_success",
                                            topic => ValidTopic,
                                            result => "no_subscribers_but_retained"
                                        }),
                                        Count + 1;
                                    Results when is_list(Results) ->
                                        %% 有订阅者，消息已发布并保留
                                        ?SLOG(debug, #{
                                            msg => "retained_message_restored_via_publish_success",
                                            topic => ValidTopic,
                                            subscribers => length(Results)
                                        }),
                                        Count + 1;
                                    Other ->
                                        ?SLOG(debug, #{
                                            msg => "retained_message_restored_via_publish_other_result",
                                            topic => ValidTopic,
                                            result => Other
                                        }),
                                        Count + 1
                                end
                            catch
                                E2:R2:S2 ->
                                    ?SLOG(warning, #{
                                        msg => "failed_to_restore_retained_message_via_publish",
                                        topic => ValidTopic,
                                        error => E2,
                                        reason => R2,
                                        stacktrace => S2
                                    }),
                                    Count
                            end
                    end
            end
        catch
            E:R:S ->
                ?SLOG(error, #{
                    msg => "error_restoring_single_retained_message",
                    error => E,
                    reason => R,
                    stacktrace => S,
                    doc => Doc
                }),
                Count
        end
    end, 0, Docs).

%%--------------------------------------------------------------------
%% 辅助函数
%%--------------------------------------------------------------------

%% 注意：系统消息检查现在使用EMQX内置的emqx_message:is_sys/1函数
%% 该函数检查消息标志中的sys标记和$SYS主题前缀

%%--------------------------------------------------------------------
%% MQTT 5.0保留消息属性解析函数
%%--------------------------------------------------------------------

%% @doc 提取Message Expiry Interval属性
%% MQTT 5.0协议：指定消息在发布后的有效期（秒）
extract_message_expiry_interval(Properties) when is_map(Properties) ->
    case maps:get('Message-Expiry-Interval', Properties,
                  maps:get(<<"Message-Expiry-Interval">>, Properties,
                          maps:get(message_expiry_interval, Properties, undefined))) of
        Interval when is_integer(Interval), Interval > 0 -> Interval;
        _ -> undefined
    end;
extract_message_expiry_interval(_) -> undefined.

%% @doc 提取Payload Format Indicator属性
%% MQTT 5.0协议：指示载荷格式（0=字节，1=UTF-8字符串）
extract_payload_format_indicator(Properties) when is_map(Properties) ->
    case maps:get('Payload-Format-Indicator', Properties,
                  maps:get(<<"Payload-Format-Indicator">>, Properties,
                          maps:get(payload_format_indicator, Properties, 0))) of
        Indicator when Indicator =:= 0; Indicator =:= 1 -> Indicator;
        _ -> 0
    end;
extract_payload_format_indicator(_) -> 0.

%% @doc 提取Content Type属性
%% MQTT 5.0协议：描述载荷内容的MIME类型
extract_content_type(Properties) when is_map(Properties) ->
    case maps:get('Content-Type', Properties,
                  maps:get(<<"Content-Type">>, Properties,
                          maps:get(content_type, Properties, undefined))) of
        ContentType when is_binary(ContentType), byte_size(ContentType) > 0 -> ContentType;
        ContentType when is_list(ContentType), length(ContentType) > 0 -> list_to_binary(ContentType);
        _ -> null  % 使用null而不是空字符串，在MongoDB中存储为null
    end;
extract_content_type(_) -> null.

%% @doc 提取Response Topic属性
%% MQTT 5.0协议：指定响应消息的主题
extract_response_topic(Properties) when is_map(Properties) ->
    case maps:get('Response-Topic', Properties,
                  maps:get(<<"Response-Topic">>, Properties,
                          maps:get(response_topic, Properties, undefined))) of
        Topic when is_binary(Topic), byte_size(Topic) > 0 -> Topic;
        Topic when is_list(Topic), length(Topic) > 0 -> list_to_binary(Topic);
        _ -> null  % 使用null而不是空字符串，在MongoDB中存储为null
    end;
extract_response_topic(_) -> null.

%% @doc 提取Correlation Data属性
%% MQTT 5.0协议：用于请求/响应模式的关联数据
extract_correlation_data(Properties) when is_map(Properties) ->
    case maps:get('Correlation-Data', Properties,
                  maps:get(<<"Correlation-Data">>, Properties,
                          maps:get(correlation_data, Properties, undefined))) of
        Data when is_binary(Data), byte_size(Data) > 0 -> Data;
        Data when is_list(Data), length(Data) > 0 -> list_to_binary(Data);
        _ -> null  % 使用null而不是空字符串，在MongoDB中存储为null
    end;
extract_correlation_data(_) -> null.

%% @doc 提取User Properties属性
%% MQTT 5.0协议：用户自定义属性键值对
extract_user_properties(Properties) when is_map(Properties) ->
    case maps:get('User-Property', Properties,
                  maps:get(<<"User-Property">>, Properties,
                          maps:get(user_properties, Properties, #{}))) of
        UserProps when is_map(UserProps) -> UserProps;
        _ -> #{}
    end;
extract_user_properties(_) -> #{}.

%% @doc 计算保留消息过期时间
%% 优先使用消息自身的Message Expiry Interval，否则使用插件配置
calculate_retained_message_expiry_time(undefined) ->
    % 没有指定Message Expiry Interval，使用插件配置
    DefaultExpiry = application:get_env(emqx_plugin_mongodb, retained_expiry, 604800000), % 7天
    erlang:system_time(millisecond) + DefaultExpiry;
calculate_retained_message_expiry_time(MessageExpiryInterval) when is_integer(MessageExpiryInterval) ->
    % 使用消息指定的过期时间（转换为毫秒）
    erlang:system_time(millisecond) + (MessageExpiryInterval * 1000);
calculate_retained_message_expiry_time(_) ->
    % 无效值，使用插件配置
    DefaultExpiry = application:get_env(emqx_plugin_mongodb, retained_expiry, 604800000),
    erlang:system_time(millisecond) + DefaultExpiry.

%% @doc 检查保留消息是否过期
%% 考虑MQTT 5.0 Message Expiry Interval的动态过期
is_retained_message_expired(RetainedDoc) ->
    try
        Now = erlang:system_time(millisecond),

        % 检查存储的过期时间
        case maps:get(<<"expires_at">>, RetainedDoc, null) of
            null ->
                % 永不过期
                false;
            ExpiresAt when is_integer(ExpiresAt) ->
                % 检查是否已过期
                Now >= ExpiresAt;
            _ ->
                % 无效的过期时间，认为已过期
                true
        end
    catch
        _:_ ->
            % 异常时认为已过期
            true
    end.

%% @doc 更新保留消息的过期时间
%% 根据MQTT 5.0协议，在消息传输过程中更新过期时间
update_message_expiry_interval(RetainedDoc) ->
    try
        case maps:get(<<"message_expiry_interval">>, RetainedDoc, undefined) of
            undefined ->
                % 没有Message Expiry Interval，不需要更新
                RetainedDoc;
            OriginalInterval when is_integer(OriginalInterval) ->
                % 计算已经过去的时间
                PublishedAt = maps:get(<<"published_at">>, RetainedDoc, erlang:system_time(millisecond)),
                Now = erlang:system_time(millisecond),
                ElapsedSeconds = (Now - PublishedAt) div 1000,

                % 计算剩余的过期时间
                RemainingInterval = max(0, OriginalInterval - ElapsedSeconds),

                % 更新文档
                RetainedDoc#{
                    <<"message_expiry_interval">> => RemainingInterval,
                    <<"last_updated">> => Now
                };
            _ ->
                % 无效的过期时间，不更新
                RetainedDoc
        end
    catch
        _:_ ->
            % 异常时返回原文档
            RetainedDoc
    end.

%% @doc 集成到协调器
%% 这个函数将保留消息持久化模块注册到模块协调器中
%% 使模块能够参与系统级的协调和通信
%%
%% 功能说明：
%% 1. 向协调器注册模块信息和元数据
%% 2. 声明模块的功能特性和优先级
%% 3. 建立与其他模块的通信通道
%% 4. 启用协调器的监控和管理功能
%%
%% 返回值：
%% - ok: 集成成功
%% - {error, Reason}: 集成失败
%%
%% Java等价概念：
%% 类似于Spring的@Component注册
%% 或者OSGi Bundle的激活
%% 或者微服务的服务注册
integrate() ->
    try
        % 定义模块信息和元数据
        ModuleInfo = #{
            priority => medium,                    % 中等优先级
            description => <<"Retained message persistence module">>,
            features => [
                retained_message_persistence,      % 保留消息持久化功能
                message_restoration,               % 消息恢复功能
                expiry_cleanup,                    % 过期清理功能
                topic_filtering                    % 主题过滤功能
            ],
            dependencies => [
                emqx_plugin_mongodb_api,           % 依赖MongoDB API模块
                emqx_plugin_mongodb_backpressure   % 依赖背压模块
            ],
            version => <<"1.0.0">>,
            status => active
        },

        % 向协调器注册模块
        case emqx_plugin_mongodb_coordinator:register_module(?MODULE, ModuleInfo) of
            ok ->
                ?SLOG(info, #{
                    msg => "retained_persistence_module_integrated_successfully",
                    module => ?MODULE,
                    features => maps:get(features, ModuleInfo)
                }),
                ok;
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_integrate_retained_persistence_module",
                    module => ?MODULE,
                    reason => Reason
                }),
                {error, Reason}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "retained_persistence_integration_exception",
                module => ?MODULE,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {integration_exception, R}}
    end.


