%% @doc MQTT消息去重模块 - 防止消息重复投递的核心组件
%% 这个模块提供基于ETS表的全局消息ID去重机制，确保消息恢复时的幂等性
%%
%% 功能概述：
%% 1. 消息ID去重 - 使用ETS表跟踪已处理的消息ID
%% 2. 自动过期清理 - 定期清理过期的去重记录
%% 3. 内存管理 - 限制去重表的最大记录数
%% 4. 高性能查询 - 基于ETS的O(1)查询性能
%% 5. 并发安全 - 支持多进程并发访问
%%
%% 设计原则：
%% - 幂等性保证：确保相同消息ID不会被重复处理
%% - 性能优化：使用ETS表提供高性能的查询和插入
%% - 内存控制：自动清理过期记录，防止内存泄漏
%% - 容错设计：即使去重失败也不影响消息传递
%%
%% Java等价概念：
%% @Component
%% @Service
%% public class MessageDeduplicationService {
%%     private final ConcurrentHashMap<String, Long> processedMessages;
%%     private final ScheduledExecutorService cleanupExecutor;
%% }
-module(emqx_plugin_mongodb_message_dedup).

-include("emqx_plugin_mongodb.hrl").

%% API导出
-export([
    start/0,                    % 启动去重服务
    stop/0,                     % 停止去重服务
    is_duplicate/1,             % 检查消息是否重复
    mark_processed/1,           % 标记消息已处理
    mark_processed/2,           % 标记消息已处理（带TTL）
    cleanup_expired/0,          % 清理过期记录
    get_stats/0,                % 获取统计信息
    clear_all/0,                % 清空所有记录（测试用）
    integrate/0                 % 集成到协调器
]).

%% 内部函数导出
-export([
    cleanup_loop/0              % 清理循环进程
]).

%%--------------------------------------------------------------------
%% API函数
%%--------------------------------------------------------------------

%% @doc 集成到协调器
%% 将消息去重模块注册到协调器中，实现统一管理和协调
-spec integrate() -> ok | {error, term()}.
integrate() ->
    try
        ?SLOG(info, #{msg => "integrating_message_dedup_module"}),

        % 定义模块信息
        ModuleInfo = #{
            priority => high,                    % 高优先级，去重是核心功能
            description => <<"Message deduplication service">>,
            features => [
                message_deduplication,           % 消息去重
                idempotency_guarantee,          % 幂等性保证
                automatic_cleanup,              % 自动清理
                memory_management,              % 内存管理
                high_performance_lookup,        % 高性能查询
                concurrent_safe                 % 并发安全
            ],
            version => <<"1.0.0">>,
            status => active,
            resource_usage => #{
                memory => low,                  % 内存使用量低
                cpu => low,                     % CPU使用量低
                io => minimal                   % IO使用量极小
            },
            dependencies => [],                 % 无依赖
            coordination_events => [
                memory_pressure,                % 内存压力事件
                cleanup_request,                % 清理请求事件
                performance_adjustment          % 性能调整事件
            ]
        },

        % 向协调器注册模块
        case erlang:function_exported(emqx_plugin_mongodb_coordinator, register_module, 2) of
            true ->
                case emqx_plugin_mongodb_coordinator:register_module(?MODULE, ModuleInfo) of
                    ok ->
                        ?SLOG(info, #{
                            msg => "message_dedup_module_integrated_successfully",
                            module => ?MODULE,
                            features => maps:get(features, ModuleInfo)
                        }),
                        ok;
                    {error, Reason} ->
                        ?SLOG(error, #{
                            msg => "failed_to_integrate_message_dedup_module",
                            module => ?MODULE,
                            reason => Reason
                        }),
                        {error, Reason}
                end;
            false ->
                % 协调器不可用，记录信息但不影响功能
                ?SLOG(info, #{
                    msg => "coordinator_not_available_skipping_integration",
                    module => ?MODULE
                }),
                ok
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "message_dedup_integration_exception",
                module => ?MODULE,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {integration_exception, R}}
    end.

%% @doc 启动消息去重服务
%% 创建ETS表并启动清理进程，集成错误处理和监控
-spec start() -> ok | {error, term()}.
start() ->
    try
        % 创建ETS表用于存储已处理的消息ID
        % 表结构：{MessageId, ProcessedTime}
        case ets:info(?MESSAGE_DEDUP_TABLE) of
            undefined ->
                % 表不存在，创建新表
                ?MESSAGE_DEDUP_TABLE = ets:new(?MESSAGE_DEDUP_TABLE, [
                    named_table,        % 命名表
                    public,             % 公共访问
                    set,                % 集合类型（唯一键）
                    {read_concurrency, true},   % 优化并发读取
                    {write_concurrency, true}   % 优化并发写入
                ]),
                ?SLOG(info, #{
                    msg => "message_dedup_table_created",
                    table => ?MESSAGE_DEDUP_TABLE
                }),

                % 向资源管理器注册ETS表
                register_with_resource_manager();
            _ ->
                % 表已存在
                ?SLOG(info, #{
                    msg => "message_dedup_table_already_exists",
                    table => ?MESSAGE_DEDUP_TABLE
                })
        end,

        % 启动清理进程
        case whereis(message_dedup_cleanup) of
            undefined ->
                CleanupPid = spawn_link(?MODULE, cleanup_loop, []),
                register(message_dedup_cleanup, CleanupPid),
                ?SLOG(info, #{
                    msg => "message_dedup_cleanup_process_started",
                    pid => CleanupPid
                }),

                % 向监控模块注册清理进程
                register_with_monitor(CleanupPid);
            Pid ->
                ?SLOG(info, #{
                    msg => "message_dedup_cleanup_process_already_running",
                    pid => Pid
                })
        end,

        ok
    catch
        E:R:S ->
            % 使用错误处理模块记录错误
            handle_error(startup_error, #{
                module => ?MODULE,
                function => start,
                line => ?LINE
            }, #{
                error => E,
                reason => R,
                stacktrace => S
            }),

            ?SLOG(error, #{
                msg => "failed_to_start_message_dedup_service",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%% @doc 停止消息去重服务
-spec stop() -> ok.
stop() ->
    try
        % 停止清理进程
        case whereis(message_dedup_cleanup) of
            undefined ->
                ok;
            Pid ->
                unregister(message_dedup_cleanup),
                exit(Pid, normal),
                ?SLOG(info, #{
                    msg => "message_dedup_cleanup_process_stopped",
                    pid => Pid
                })
        end,

        % 删除ETS表
        case ets:info(?MESSAGE_DEDUP_TABLE) of
            undefined ->
                ok;
            _ ->
                ets:delete(?MESSAGE_DEDUP_TABLE),
                ?SLOG(info, #{
                    msg => "message_dedup_table_deleted",
                    table => ?MESSAGE_DEDUP_TABLE
                })
        end,

        ok
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_stopping_message_dedup_service",
                error => E,
                reason => R,
                stacktrace => S
            }),
            ok
    end.

%% @doc 检查消息是否为重复消息
%% 返回true表示重复，false表示首次处理
-spec is_duplicate(binary() | string()) -> boolean().
is_duplicate(MessageId) when is_binary(MessageId) ->
    try
        case ets:info(?MESSAGE_DEDUP_TABLE) of
            undefined ->
                % 表不存在，认为不重复
                false;
            _ ->
                case ets:lookup(?MESSAGE_DEDUP_TABLE, MessageId) of
                    [] ->
                        % 未找到记录，不重复
                        false;
                    [{MessageId, ProcessedTime}] ->
                        % 找到记录，检查是否过期
                        Now = erlang:system_time(millisecond),
                        case (Now - ProcessedTime) < ?MESSAGE_DEDUP_TTL of
                            true ->
                                % 未过期，是重复消息
                                ?SLOG(debug, #{
                                    msg => "duplicate_message_detected",
                                    message_id => MessageId,
                                    processed_time => ProcessedTime,
                                    age_ms => Now - ProcessedTime
                                }),
                                true;
                            false ->
                                % 已过期，删除记录并认为不重复
                                ets:delete(?MESSAGE_DEDUP_TABLE, MessageId),
                                false
                        end
                end
        end
    catch
        E:R:S ->
            handle_error(lookup_error, #{
                module => ?MODULE,
                function => is_duplicate,
                line => ?LINE
            }, #{
                message_id => MessageId,
                error => E,
                reason => R,
                stacktrace => S
            }),
            ?SLOG(error, #{
                msg => "error_checking_message_duplicate",
                message_id => MessageId,
                error => E,
                reason => R,
                stacktrace => S
            }),
            % 出错时认为不重复，避免阻塞消息处理
            false
    end;
is_duplicate(MessageId) when is_list(MessageId) ->
    is_duplicate(list_to_binary(MessageId));
is_duplicate(MessageId) ->
    ?SLOG(warning, #{
        msg => "invalid_message_id_type_for_dedup_check",
        message_id => MessageId,
        type => type_of(MessageId)
    }),
    false.

%% @doc 标记消息已处理（使用默认TTL）
-spec mark_processed(binary() | string()) -> ok.
mark_processed(MessageId) ->
    mark_processed(MessageId, ?MESSAGE_DEDUP_TTL).

%% @doc 标记消息已处理（指定TTL）
-spec mark_processed(binary() | string(), non_neg_integer()) -> ok.
mark_processed(MessageId, TTL) when is_binary(MessageId), is_integer(TTL), TTL > 0 ->
    try
        case ets:info(?MESSAGE_DEDUP_TABLE) of
            undefined ->
                ?SLOG(warning, #{
                    msg => "message_dedup_table_not_available",
                    message_id => MessageId
                });
            _ ->
                Now = erlang:system_time(millisecond),
                ExpiryTime = Now + TTL,
                
                % 检查表大小，如果超过限制则先清理
                TableSize = ets:info(?MESSAGE_DEDUP_TABLE, size),
                if TableSize > ?MESSAGE_DEDUP_MAX_RECORDS ->
                    spawn(fun() -> cleanup_expired() end);
                   true -> ok
                end,

                % 插入记录
                ets:insert(?MESSAGE_DEDUP_TABLE, {MessageId, Now}),
                ?SLOG(debug, #{
                    msg => "message_marked_as_processed",
                    message_id => MessageId,
                    processed_time => Now,
                    expiry_time => ExpiryTime,
                    table_size => TableSize
                })
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_marking_message_processed",
                message_id => MessageId,
                error => E,
                reason => R,
                stacktrace => S
            })
    end;
mark_processed(MessageId, TTL) when is_list(MessageId) ->
    mark_processed(list_to_binary(MessageId), TTL);
mark_processed(MessageId, TTL) ->
    ?SLOG(warning, #{
        msg => "invalid_parameters_for_mark_processed",
        message_id => MessageId,
        message_id_type => type_of(MessageId),
        ttl => TTL,
        ttl_type => type_of(TTL)
    }).

%% @doc 清理过期的去重记录
-spec cleanup_expired() -> {ok, non_neg_integer()}.
cleanup_expired() ->
    try
        case ets:info(?MESSAGE_DEDUP_TABLE) of
            undefined ->
                {ok, 0};
            _ ->
                Now = erlang:system_time(millisecond),
                ExpiredCount = cleanup_expired_records(Now, 0),
                
                ?SLOG(info, #{
                    msg => "message_dedup_cleanup_completed",
                    expired_count => ExpiredCount,
                    current_time => Now,
                    table_size => ets:info(?MESSAGE_DEDUP_TABLE, size)
                }),
                
                {ok, ExpiredCount}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_during_message_dedup_cleanup",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {ok, 0}
    end.

%% @doc 获取去重服务统计信息
-spec get_stats() -> map().
get_stats() ->
    try
        case ets:info(?MESSAGE_DEDUP_TABLE) of
            undefined ->
                #{
                    table_exists => false,
                    total_records => 0,
                    memory_usage => 0,
                    cleanup_process_alive => is_process_alive(whereis(message_dedup_cleanup))
                };
            _ ->
                TableSize = ets:info(?MESSAGE_DEDUP_TABLE, size),
                MemoryUsage = ets:info(?MESSAGE_DEDUP_TABLE, memory),
                
                #{
                    table_exists => true,
                    total_records => TableSize,
                    memory_usage => MemoryUsage,
                    memory_usage_words => MemoryUsage,
                    memory_usage_bytes => MemoryUsage * erlang:system_info(wordsize),
                    cleanup_process_alive => is_process_alive(whereis(message_dedup_cleanup)),
                    max_records => ?MESSAGE_DEDUP_MAX_RECORDS,
                    ttl_ms => ?MESSAGE_DEDUP_TTL,
                    cleanup_interval_ms => ?MESSAGE_DEDUP_CLEANUP_INTERVAL
                }
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_getting_message_dedup_stats",
                error => E,
                reason => R,
                stacktrace => S
            }),
            #{error => {E, R}}
    end.

%% @doc 清空所有去重记录（仅用于测试）
-spec clear_all() -> ok.
clear_all() ->
    try
        case ets:info(?MESSAGE_DEDUP_TABLE) of
            undefined ->
                ok;
            _ ->
                ets:delete_all_objects(?MESSAGE_DEDUP_TABLE),
                ?SLOG(warning, #{
                    msg => "all_message_dedup_records_cleared",
                    note => "this_should_only_be_used_for_testing"
                })
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_clearing_message_dedup_records",
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%%--------------------------------------------------------------------
%% 内部函数
%%--------------------------------------------------------------------

%% @doc 向资源管理器注册ETS表
%% 让资源管理器监控去重表的内存使用情况
-spec register_with_resource_manager() -> ok.
register_with_resource_manager() ->
    try
        case erlang:function_exported(emqx_plugin_mongodb_resource_manager, register_resource, 3) of
            true ->
                ResourceInfo = #{
                    type => ets_table,
                    name => ?MESSAGE_DEDUP_TABLE,
                    description => <<"Message deduplication ETS table">>,
                    memory_limit => ?MESSAGE_DEDUP_MAX_RECORDS * 100, % 估算内存限制
                    cleanup_function => fun() -> cleanup_expired() end
                },
                emqx_plugin_mongodb_resource_manager:register_resource(
                    message_dedup_table,
                    ResourceInfo,
                    self()
                ),
                ?SLOG(debug, #{
                    msg => "registered_dedup_table_with_resource_manager",
                    table => ?MESSAGE_DEDUP_TABLE
                });
            false ->
                ?SLOG(debug, #{
                    msg => "resource_manager_not_available",
                    note => "skipping_resource_registration"
                })
        end
    catch
        E:R:S ->
            ?SLOG(warning, #{
                msg => "failed_to_register_with_resource_manager",
                error => E,
                reason => R,
                stacktrace => S
            })
    end,
    ok.

%% @doc 向监控模块注册清理进程
%% 让监控模块跟踪清理进程的健康状态
-spec register_with_monitor(pid()) -> ok.
register_with_monitor(CleanupPid) ->
    try
        case erlang:function_exported(emqx_plugin_mongodb_monitor, register_process, 3) of
            true ->
                ProcessInfo = #{
                    type => cleanup_process,
                    name => message_dedup_cleanup,
                    description => <<"Message deduplication cleanup process">>,
                    critical => false,
                    restart_function => fun() -> restart_cleanup_process() end
                },
                emqx_plugin_mongodb_monitor:register_process(
                    message_dedup_cleanup,
                    ProcessInfo,
                    CleanupPid
                ),
                ?SLOG(debug, #{
                    msg => "registered_cleanup_process_with_monitor",
                    pid => CleanupPid
                });
            false ->
                ?SLOG(debug, #{
                    msg => "monitor_not_available",
                    note => "skipping_process_registration"
                })
        end
    catch
        E:R:S ->
            ?SLOG(warning, #{
                msg => "failed_to_register_with_monitor",
                error => E,
                reason => R,
                stacktrace => S
            })
    end,
    ok.

%% @doc 使用错误处理模块处理错误
%% 统一的错误处理和恢复机制
-spec handle_error(atom(), map(), map()) -> ok.
handle_error(ErrorType, Source, Details) ->
    try
        case erlang:function_exported(emqx_plugin_mongodb_error_handler, handle_error, 3) of
            true ->
                emqx_plugin_mongodb_error_handler:handle_error(ErrorType, Source, Details);
            false ->
                ?SLOG(debug, #{
                    msg => "error_handler_not_available",
                    error_type => ErrorType,
                    source => Source,
                    details => Details
                })
        end
    catch
        E:R:S ->
            ?SLOG(warning, #{
                msg => "failed_to_use_error_handler",
                error => E,
                reason => R,
                stacktrace => S,
                original_error => #{
                    type => ErrorType,
                    source => Source,
                    details => Details
                }
            })
    end,
    ok.

%% @doc 重启清理进程
%% 当监控模块检测到清理进程异常时调用
-spec restart_cleanup_process() -> ok | {error, term()}.
restart_cleanup_process() ->
    try
        % 停止现有的清理进程
        case whereis(message_dedup_cleanup) of
            undefined ->
                ok;
            Pid ->
                unregister(message_dedup_cleanup),
                exit(Pid, shutdown),
                ?SLOG(info, #{
                    msg => "stopped_existing_cleanup_process",
                    pid => Pid
                })
        end,

        % 启动新的清理进程
        CleanupPid = spawn_link(?MODULE, cleanup_loop, []),
        register(message_dedup_cleanup, CleanupPid),

        % 重新注册到监控模块
        register_with_monitor(CleanupPid),

        ?SLOG(info, #{
            msg => "restarted_cleanup_process",
            new_pid => CleanupPid
        }),
        ok
    catch
        E:R:S ->
            handle_error(cleanup_restart_error, #{
                module => ?MODULE,
                function => restart_cleanup_process,
                line => ?LINE
            }, #{
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%% @doc 清理循环进程
%% 定期清理过期的去重记录
cleanup_loop() ->
    try
        timer:sleep(?MESSAGE_DEDUP_CLEANUP_INTERVAL),
        cleanup_expired(),
        cleanup_loop()
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "message_dedup_cleanup_loop_error",
                error => E,
                reason => R,
                stacktrace => S
            }),
            % 等待一段时间后重新开始
            timer:sleep(60000),
            cleanup_loop()
    end.

%% @doc 清理过期记录的内部实现
cleanup_expired_records(Now, Count) ->
    case ets:first(?MESSAGE_DEDUP_TABLE) of
        '$end_of_table' ->
            Count;
        Key ->
            case ets:lookup(?MESSAGE_DEDUP_TABLE, Key) of
                [{Key, ProcessedTime}] ->
                    case (Now - ProcessedTime) >= ?MESSAGE_DEDUP_TTL of
                        true ->
                            ets:delete(?MESSAGE_DEDUP_TABLE, Key),
                            cleanup_expired_records(Now, Count + 1);
                        false ->
                            cleanup_expired_records_continue(Now, Count)
                    end;
                [] ->
                    cleanup_expired_records_continue(Now, Count)
            end
    end.

%% @doc 继续清理下一个记录
cleanup_expired_records_continue(Now, Count) ->
    case ets:next(?MESSAGE_DEDUP_TABLE, ets:first(?MESSAGE_DEDUP_TABLE)) of
        '$end_of_table' ->
            Count;
        NextKey ->
            case ets:lookup(?MESSAGE_DEDUP_TABLE, NextKey) of
                [{NextKey, ProcessedTime}] ->
                    case (Now - ProcessedTime) >= ?MESSAGE_DEDUP_TTL of
                        true ->
                            ets:delete(?MESSAGE_DEDUP_TABLE, NextKey),
                            cleanup_expired_records(Now, Count + 1);
                        false ->
                            cleanup_expired_records_continue(Now, Count)
                    end;
                [] ->
                    cleanup_expired_records_continue(Now, Count)
            end
    end.

%% @doc 获取变量类型（辅助函数）
type_of(X) when is_integer(X) -> integer;
type_of(X) when is_float(X) -> float;
type_of(X) when is_list(X) -> list;
type_of(X) when is_tuple(X) -> tuple;
type_of(X) when is_binary(X) -> binary;
type_of(X) when is_atom(X) -> atom;
type_of(X) when is_boolean(X) -> boolean;
type_of(X) when is_map(X) -> map;
type_of(X) when is_pid(X) -> pid;
type_of(X) when is_port(X) -> port;
type_of(X) when is_reference(X) -> reference;
type_of(X) when is_function(X) -> function;
type_of(_) -> unknown.
