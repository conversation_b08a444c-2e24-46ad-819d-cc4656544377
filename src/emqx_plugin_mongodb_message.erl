%% @doc MQTT消息持久化模块 - 企业级消息可靠性保障系统
%% 这个模块是MQTT消息持久化的核心组件，提供企业级的消息可靠性保障
%%
%% 功能概述：
%% 1. QoS消息持久化 - 确保QoS 1和QoS 2消息在系统故障时不丢失
%% 2. 消息确认处理 - 智能处理客户端ACK，及时清理已确认消息
%% 3. 未确认消息重发 - 客户端重连后自动重发未确认消息
%% 4. 消息过期清理 - 定时清理过期的未确认消息，防止存储膨胀
%% 5. 消息统计监控 - 提供详细的消息存储和处理统计信息
%% 6. 系统恢复支持 - EMQX重启后自动恢复未确认消息到内存
%% 7. 高性能批处理 - 支持批量消息处理，提高系统吞吐量
%% 8. 智能索引管理 - 自动创建和管理MongoDB索引，优化查询性能
%%
%% 架构设计：
%% - 事件驱动架构：通过EMQX钩子系统响应消息生命周期事件
%% - 异步处理模式：所有持久化操作都是异步的，不阻塞消息传递
%% - 分层存储策略：内存缓存 + MongoDB持久化的混合存储
%% - 智能清理机制：基于时间和空间的双重清理策略
%% - 容错设计：单点故障不影响整体消息传递功能
%%
%% Java等价概念：
%% 类似于Spring Boot中的消息持久化服务：
%% @Service
%% @Component
%% @Transactional
%% public class MessagePersistenceService {
%%     @Autowired private MongoTemplate mongoTemplate;
%%     @Autowired private MessageRepository messageRepository;
%%     @Autowired private RedisTemplate redisTemplate; // 缓存层
%%
%%     @EventListener
%%     @Async
%%     public void onMessagePublish(MessagePublishEvent event) {
%%         // 异步持久化QoS 1/2消息
%%         if (event.getMessage().getQos() > 0) {
%%             messageRepository.save(event.getMessage());
%%         }
%%     }
%%
%%     @EventListener
%%     @Async
%%     public void onMessageAcked(MessageAckedEvent event) {
%%         // 异步删除已确认消息
%%         messageRepository.deleteByMessageId(event.getMessageId());
%%     }
%%
%%     @Scheduled(fixedDelay = 300000) // 5分钟清理过期消息
%%     public void cleanExpiredMessages() {
%%         messageRepository.deleteExpiredMessages();
%%     }
%%
%%     @PostConstruct
%%     public void restoreUnackedMessages() {
%%         // 系统启动时恢复未确认消息
%%         List<Message> unackedMessages = messageRepository.findUnackedMessages();
%%         messageQueue.addAll(unackedMessages);
%%     }
%%
%%     @Retryable(maxAttempts = 3)
%%     public void resendUnackedMessages(String clientId) {
%%         // 重发未确认消息，支持重试
%%         List<Message> messages = messageRepository.findUnackedByClientId(clientId);
%%         messagePublisher.resend(messages);
%%     }
%% }
%%
%% 设计模式：
%% - 观察者模式：监听MQTT消息生命周期事件
%% - 策略模式：不同QoS级别使用不同的持久化策略
%% - 模板方法模式：统一的消息处理流程模板
%% - 工厂模式：消息对象的创建和转换
%% @end
-module(emqx_plugin_mongodb_message).

-include("emqx_plugin_mongodb.hrl").
-include_lib("emqx_utils/include/emqx_message.hrl").

%% ============================================================================
%% 生命周期管理API - 模块启动、配置和关闭管理
%% 这些函数管理消息持久化模块的完整生命周期
%% 类似于Java Spring Boot的生命周期管理：
%% @PostConstruct - 初始化资源和配置
%% @PreDestroy - 清理资源和保存状态
%% @ConfigurationProperties - 配置加载和验证
%% ============================================================================
-export([
    init/0,         % 初始化模块 - 类似于@PostConstruct，设置基础环境
    load/1,         % 加载配置 - 类似于@ConfigurationProperties，加载和验证配置
    unload/0,       % 卸载模块 - 类似于@PreDestroy，清理资源和保存状态
    integrate/0     % 集成钩子 - 类似于@EventListener注册，注册事件监听器
]).

%% ============================================================================
%% MQTT消息生命周期事件监听器 - 响应MQTT消息的各个生命周期阶段
%% 这些函数作为EMQX钩子回调，监听消息的发布、投递、确认和丢弃事件
%% 类似于Java Spring的事件监听器：
%% @EventListener
%% @Async  // 异步处理，不阻塞消息传递
%% public void onMessageEvent(MessageEvent event) { ... }
%% ============================================================================
-export([
    on_message_publish/1,       % 消息发布事件监听器
                               % 功能：当消息发布时触发，保存QoS 1/2消息到MongoDB
                               % Java等价：@EventListener(MessagePublishEvent.class)



    on_message_delivered/2,     % 消息投递事件监听器（基础版本）
                               % 功能：当消息成功投递给订阅者时触发
                               % Java等价：@EventListener(MessageDeliveredEvent.class)

    on_message_delivered/3,     % 消息投递事件监听器（扩展版本）
                               % 功能：提供更详细的投递信息，包含主题信息
                               % Java等价：@EventListener(MessageDeliveredEvent.class)

    on_message_acked/2,         % 消息确认事件监听器（基础版本）
                               % 功能：当客户端确认消息时触发，从MongoDB删除消息
                               % Java等价：@EventListener(MessageAckedEvent.class)

    on_message_acked/3,         % 消息确认事件监听器（扩展版本）
                               % 功能：提供更详细的确认信息，包含主题信息
                               % Java等价：@EventListener(MessageAckedEvent.class)

    on_message_dropped/3,       % 消息丢弃事件监听器（基础版本）
                               % 功能：当消息被丢弃时触发，记录丢弃原因和统计
                               % Java等价：@EventListener(MessageDroppedEvent.class)

    on_message_dropped/4        % 消息丢弃事件监听器（扩展版本）
                               % 功能：提供更详细的丢弃信息和环境上下文
                               % Java等价：@EventListener(MessageDroppedEvent.class)
]).

%% ============================================================================
%% 消息管理业务API - 核心业务逻辑和数据操作
%% 这些函数提供消息持久化的核心业务功能
%% 类似于Java Spring的Service层：
%% @Service
%% @Transactional
%% public class MessagePersistenceService {
%%     // 消息CRUD操作
%%     // 批量处理和优化
%%     // 统计和监控
%% }
%% ============================================================================
-export([
    save_message/1,             % 保存消息到MongoDB
                               % 功能：持久化QoS 1/2消息，确保消息不丢失
                               % Java等价：@Transactional public void saveMessage(Message message)

    clean_expired_messages/0,   % 清理过期消息
                               % 功能：定期清理过期的未确认消息，防止存储膨胀
                               % Java等价：@Scheduled(fixedDelay = 900000) public void cleanExpiredMessages()

    extract_message_expiry_interval/1, % 提取Message Expiry Interval属性
                                      % 功能：从MQTT 5.0属性中提取消息过期时间
                                      % Java等价：public Integer extractMessageExpiryInterval(Properties props)

    extract_topic_alias/1,            % 提取Topic Alias属性
                                     % 功能：从MQTT 5.0属性中提取主题别名
                                     % Java等价：public int extractTopicAlias(Properties props)

    calculate_message_expiry_time/2,  % 计算消息过期时间
                                     % 功能：根据MQTT协议计算消息的过期时间
                                     % Java等价：public long calculateMessageExpiryTime(Integer expiryInterval, Config config)

    find_unacked_messages/1,    % 查找客户端的未确认消息
                               % 功能：查询特定客户端的所有未确认消息
                               % Java等价：public List<Message> findUnackedMessages(String clientId)

    resend_unacked_messages/1,  % 重发客户端的未确认消息（基础版本）
                               % 功能：客户端重连后重发所有未确认消息
                               % Java等价：public void resendUnackedMessages(String clientId)

    resend_unacked_messages/2,  % 重发客户端的未确认消息（带选项）
                               % 功能：支持自定义重发选项，如批量大小、重试次数等
                               % Java等价：public void resendUnackedMessages(String clientId, ResendOptions options)

    handle_unacked_messages_after_restart/0, % 处理系统重启后的未确认消息
                                        % 功能：恢复未确认消息到EMQX消息队列
                                        % Java等价：@PostConstruct public void handleUnackedMessagesAfterRestart()

    restore_client_messages/1,          % 恢复客户端未确认消息
                                       % 功能：恢复指定客户端的所有未确认QoS 1/2消息
                                       % Java等价：public void restoreClientMessages(String clientId)

    delete_client_messages/1,           % 删除客户端的所有消息
                                       % 功能：删除指定客户端的所有持久化消息

    restore_unacked_messages/0,         % 恢复未确认消息（EMQX重启后）
                                       % 功能：EMQX重启后恢复所有未确认的QoS 1/2消息
                                       % Java等价：@PostConstruct public void restoreUnackedMessages()

    log_client_unacked_messages_info/2, % 记录客户端未确认消息信息
                                       % 功能：记录特定客户端的未确认消息详情
                                       % Java等价：public void logClientUnackedMessagesInfo(String clientId, List<Message> messages)

    get_message_stats/0,        % 获取消息存储统计信息
                               % 功能：提供消息数量、存储大小等统计数据
                               % Java等价：public MessageStats getMessageStats()

    find_messages_by_topic/1,   % 按主题查找消息
                               % 功能：查询特定主题的所有消息，支持调试和监控
                               % Java等价：public List<Message> findMessagesByTopic(String topic)

    get_client_message_count/1  % 获取客户端消息数量
                               % 功能：统计特定客户端的消息数量
                               % Java等价：public long getClientMessageCount(String clientId)
]).

%% ============================================================================
%% 内部辅助函数 - 模块内部使用的工具和配置函数
%% 这些函数提供模块内部的辅助功能，类似于Java的private方法
%% ============================================================================
-export([
    start_cleanup_timer/0,      % 启动清理定时器 - 类似于@Scheduled任务启动
    do_clean_expired_messages/0,% 执行过期消息清理 - 定时任务的具体实现
    get_message_collection/0,   % 获取消息集合名称 - 配置获取方法
    wait_for_resource/0,        % 等待MongoDB资源可用 - 资源就绪检查
    get_message_expiry/0,       % 获取消息过期时间配置 - 配置读取方法
    get_cleanup_interval/0,     % 获取清理间隔配置 - 配置读取方法
    get_cleanup_batch_size/0,   % 获取清理批次大小配置 - 配置读取方法
    register_hooks/0,           % 注册消息钩子 - 钩子管理
    unregister_hooks/0,         % 注销消息钩子 - 钩子管理
    ensure_message_collection_and_indexes/1 % 确保消息集合和索引存在 - 集合初始化
]).

%% ============================================================================
%% 配置常量定义 - 消息持久化的核心配置参数
%% 这些常量控制消息持久化的行为和性能特征
%% 类似于Java中的配置常量或@ConfigurationProperties
%% ============================================================================

%% 注意：MongoDB集合名称现在统一定义在 emqx_plugin_mongodb.hrl 中
%% 这样可以保持整个项目的命名一致性和集中管理

%% 默认消息过期时间：4小时（14400000毫秒）
%% 功能：控制未确认消息在MongoDB中的保存时间
%% 超过此时间的消息将被自动清理，防止存储无限增长
%% Java等价：@Value("${message.expiry.time:14400000}")
%% 或者：spring.message.expiry-time=14400000
-define(DEFAULT_MESSAGE_EXPIRY, 14400000).

%% 默认清理间隔：15分钟（900000毫秒）
%% 功能：控制过期消息清理任务的执行频率
%% 定时任务会按此间隔清理过期的未确认消息
%% Java等价：@Scheduled(fixedDelay = 900000)
%% 或者：spring.task.scheduling.pool.size=1
-define(DEFAULT_CLEANUP_INTERVAL, 900000).

%% 默认批次大小：500条消息
%% 功能：控制批量操作的大小，平衡性能和内存使用
%% 清理任务每次处理的消息数量，避免一次性处理过多数据
%% Java等价：@Value("${message.cleanup.batch-size:500}")
%% 或者：spring.data.mongodb.batch-size=500
-define(DEFAULT_CLEANUP_BATCH_SIZE, 500).

%% ============================================================================
%% 模块初始化函数 - 消息持久化模块的启动入口
%% ============================================================================

%% @doc 初始化消息持久化模块
%% 这个函数是模块的启动入口，负责初始化所有必要的资源和服务
%%
%% 功能说明：
%% 1. 检查消息持久化功能是否启用
%% 2. 等待MongoDB资源就绪
%% 3. 创建必要的集合和索引
%% 4. 启动过期消息清理定时器
%%
%% 返回值：
%% - ok: 初始化成功
%%
%% Java等价概念：
%% @PostConstruct
%% @ConditionalOnProperty(name = "message.persistence.enabled", havingValue = "true")
%% public void initializeMessagePersistence() {
%%     if (messagePersistenceEnabled) {
%%         waitForMongoDBReady();
%%         createCollectionsAndIndexes();
%%         startCleanupScheduler();
%%     }
%% }
%%
%% 设计特点：
%% - 条件初始化：只有在启用消息持久化时才执行初始化
%% - 资源等待：确保MongoDB资源可用后再进行后续操作
%% - 完整设置：一次性完成所有必要的初始化工作
init() ->
    %% 记录模块初始化开始的日志
    %% 这有助于系统启动过程的跟踪和调试
    ?SLOG(info, #{msg => "initializing_mongodb_message_module"}),

    %% 检查消息持久化功能是否启用
    %% 这是一个功能开关，允许运行时控制消息持久化
    %% 在Java中相当于：
    %% @ConditionalOnProperty(name = "message.persistence.enabled", havingValue = "true")
    Config = emqx_plugin_mongodb:read_config(),
    MessageConfig = maps:get(message_persistence, Config, #{}),

    case maps:get(enabled, MessageConfig, false) of
        true ->
            %% 消息持久化已启用，开始初始化所有组件
            ?SLOG(info, #{msg => "message_persistence_enabled", module => ?MODULE}),

            %% 等待MongoDB资源就绪
            %% 确保数据库连接可用后再进行后续操作
            %% 在Java中相当于：
            %% @Autowired private MongoHealthIndicator mongoHealth;
            %% while (!mongoHealth.isHealthy()) { Thread.sleep(1000); }
            wait_for_resource(),

            %% 创建MongoDB集合和索引
            %% 确保数据库结构正确设置
            %% 在Java中相当于：
            %% @PostConstruct
            %% public void createCollectionsAndIndexes() { ... }
            ensure_collections(),

            %% 启动过期消息清理定时器
            %% 定期清理过期的未确认消息
            %% 在Java中相当于：
            %% @Scheduled(fixedDelay = 900000)
            %% public void cleanExpiredMessages() { ... }
            start_cleanup_timer();
        false ->
            %% 消息持久化未启用，跳过初始化
            %% 记录信息日志，说明功能被禁用
            ?SLOG(info, #{msg => "message_persistence_disabled", module => ?MODULE})
    end,
    %% 无论是否启用，都返回ok，确保模块加载不会失败
    ok.

%% @doc 等待MongoDB资源就绪（无参数版本）
%% 这是wait_for_resource/1函数的便捷包装，使用默认重试次数
%%
%% 功能说明：
%% 调用wait_for_resource/1函数并传入默认的重试次数10
%%
%% Java等价概念：
%% 类似于方法重载或默认参数
%% public void waitForResource() { waitForResource(10); }
wait_for_resource() ->
    %% 调用带参数的wait_for_resource函数，使用默认重试次数10
    wait_for_resource(10).

%% @doc 等待MongoDB资源就绪（带重试次数）
%% 这个函数确保MongoDB资源在使用前处于可用状态
%%
%% 功能说明：
%% 1. 使用EMQX资源健康检查API检测MongoDB连接状态
%% 2. 如果资源不可用，等待1秒后重试
%% 3. 达到最大重试次数后放弃等待
%% 4. 记录详细的等待和重试日志
%%
%% 参数说明：
%% - Retries: 剩余重试次数，0表示不再重试
%%
%% 返回值：
%% - ok: 资源就绪或达到最大重试次数
%%
%% Java等价概念：
%% 类似于Spring Boot的健康检查等待
%% @Retryable(maxAttempts = 10, delay = 1000)
%% public void waitForMongoDBReady() {
%%     HealthIndicator health = mongoHealthIndicator.health();
%%     if (health.getStatus() != Status.UP) {
%%         throw new ResourceNotReadyException("MongoDB not ready");
%%     }
%% }
%%
%% 设计特点：
%% - 递归重试：使用尾递归实现重试逻辑
%% - 健康检查：使用EMQX标准的资源健康检查API
%% - 优雅降级：达到重试上限后不抛出异常，允许系统继续运行
wait_for_resource(0) ->
    %% 重试次数耗尽，记录警告日志但不抛出异常
    %% 这是一种优雅降级策略，避免因为MongoDB暂时不可用而阻止系统启动
    %% 在Java中相当于：
    %% logger.warn("MongoDB resource not ready after maximum retries, continuing anyway");
    ?SLOG(warning, #{msg => "mongodb_resource_not_ready_after_retries"}),
    ok;
wait_for_resource(Retries) ->
    %% 使用EMQX资源管理器的健康检查API
    %% 这比直接ping数据库更可靠，因为它检查整个资源栈的状态
    %% 在Java中相当于：
    %% HealthIndicator.Health health = mongoHealthIndicator.health();
    %% if (health.getStatus() == Status.UP) { ... }
    case emqx_resource:health_check(?PLUGIN_MONGODB_RESOURCE_ID) of
        ok ->
            %% 资源健康检查通过，MongoDB可用
            ?SLOG(info, #{msg => "mongodb_resource_ready"}),
            ok;
        {ok, _} ->
            %% 资源健康检查通过（带额外信息），MongoDB可用
            ?SLOG(info, #{msg => "mongodb_resource_ready"}),
            ok;
        _ ->
            %% 资源健康检查失败，等待后重试
            %% 记录当前重试状态，便于调试
            ?SLOG(warning, #{msg => "mongodb_resource_not_ready", retries_left => Retries}),

            %% 等待1秒后重试，避免过于频繁的检查
            %% 在Java中相当于：
            %% Thread.sleep(1000);
            timer:sleep(1000),

            %% 递归调用，减少重试次数
            wait_for_resource(Retries - 1)
    end.

%% 加载消息模块
load(Config) ->
    ?SLOG(info, #{msg => "loading_mongodb_message_module", config => Config}),
    % 检查消息持久化是否启用
    MessageConfig = maps:get(message_persistence, Config, #{}),
    MessagePersistenceEnabled = maps:get(enabled, MessageConfig, false),

    % 保存启用状态到应用环境变量中
    application:set_env(emqx_plugin_mongodb, message_persistence_enabled, MessagePersistenceEnabled),

    % 注册消息持久化钩子，使用高优先级确保在主题过滤之前执行
    try
        register_hooks(),
        ?SLOG(info, #{msg => "message_persistence_hooks_registered_with_high_priority"})
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "failed_to_register_message_persistence_hooks",
                error => E,
                reason => R,
                stacktrace => S
            })
    end,

    % 如果消息持久化启用，检查连接状态
    case MessagePersistenceEnabled of
        true ->
            ?SLOG(info, #{msg => "message_persistence_enabled_checking_connection"}),
            % 检查MongoDB连接是否已建立
            case check_mongodb_connection_ready() of
                true ->
                    ?SLOG(info, #{msg => "mongodb_connection_ready_initializing_message_persistence"}),
                    initialize_message_persistence();
                false ->
                    ?SLOG(info, #{msg => "mongodb_connection_not_ready_deferring_message_persistence_initialization"}),
                    % 启动后台任务等待连接建立
                    spawn_link(fun() -> wait_and_initialize_message_persistence() end),
                    ok
            end;
        false ->
            ?SLOG(info, #{msg => "message_persistence_disabled"})
    end,
    ok.

%% @doc 检查MongoDB连接是否已建立
-spec check_mongodb_connection_ready() -> boolean().
check_mongodb_connection_ready() ->
    try
        % 使用简单的资源健康检查而不是查询
        case emqx_resource:health_check(?PLUGIN_MONGODB_RESOURCE_ID) of
            ok ->
                true;
            {ok, _} ->
                true;
            _ ->
                false
        end
    catch
        _:_ ->
            false
    end.

%% @doc 等待MongoDB连接建立并初始化消息持久化
-spec wait_and_initialize_message_persistence() -> ok.
wait_and_initialize_message_persistence() ->
    wait_and_initialize_message_persistence(60). % 最多等待60次，每次2秒

%% @doc 等待循环
-spec wait_and_initialize_message_persistence(integer()) -> ok.
wait_and_initialize_message_persistence(0) ->
    ?SLOG(error, #{msg => "message_persistence_initialization_timeout"}),
    ok;
wait_and_initialize_message_persistence(Retries) ->
    timer:sleep(2000), % 每2秒检查一次
    case check_mongodb_connection_ready() of
        true ->
            ?SLOG(info, #{msg => "mongodb_connection_ready_initializing_message_persistence_delayed"}),
            initialize_message_persistence();
        false ->
            wait_and_initialize_message_persistence(Retries - 1)
    end.

%% @doc 初始化消息持久化
-spec initialize_message_persistence() -> ok.
initialize_message_persistence() ->
    try
        % 确保集合存在
        ensure_collections(),
        % 启动清理定时器
        start_cleanup_timer(),
        ?SLOG(info, #{msg => "message_persistence_initialized_successfully"}),
        ok
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "failed_to_initialize_message_persistence",
                error => E,
                reason => R,
                stacktrace => S
            }),
            ok
    end.

%% 卸载消息模块
unload() ->
    ?SLOG(info, #{msg => "unloading_mongodb_message_module"}),
    % 注销消息相关钩子
    unregister_hooks(),
    ok.

%% 确保消息相关的集合存在
ensure_collections() ->
    try
        % 获取集合名称
        MessageCollection = get_message_collection(),

        ?SLOG(info, #{
            msg => "ensuring_message_collections",
            message_collection => MessageCollection
        }),

        % 统一集合创建策略：主动创建集合和索引，与保留消息模块保持一致
        % 这样确保所有集合在插件启动时就存在，便于管理和调试
        ensure_message_collection_and_indexes(MessageCollection),

        ?SLOG(info, #{msg => "message_collections_ensured", collection => MessageCollection})
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_ensuring_collections",
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% 创建消息集合索引 - 使用统一命名规则
create_message_indexes_direct(Pid, Collection) ->
    % 生成集合缩写 emqx_mqtt_messages -> emm
    MessageAbbr = generate_collection_abbreviation(Collection),

    ?SLOG(info, #{
        msg => "creating_message_indexes",
        collection => Collection,
        collection_abbr => MessageAbbr
    }),

    % 创建消息ID索引
    IdIndex = #{
        <<"key">> => #{<<"message_id">> => 1},
        <<"name">> => <<MessageAbbr/binary, "_message_id_unique">>,
        <<"unique">> => true
    },

    % 创建客户端ID索引
    ClientIdIndex = #{
        <<"key">> => #{<<"client_id">> => 1},
        <<"name">> => <<MessageAbbr/binary, "_client_id_asc">>
    },

    % 创建主题索引
    TopicIndex = #{
        <<"key">> => #{<<"topic">> => 1},
        <<"name">> => <<MessageAbbr/binary, "_topic_asc">>
    },

    % 创建过期时间索引
    ExpiryIndex = #{
        <<"key">> => #{<<"expiry_time">> => 1},
        <<"name">> => <<MessageAbbr/binary, "_expiry_time_asc">>
    },

    % 创建复合索引 (客户端ID + QoS)
    ClientQoSIndex = #{
        <<"key">> => #{<<"client_id">> => 1, <<"qos">> => 1},
        <<"name">> => <<MessageAbbr/binary, "_client_id_qos_compound">>
    },

    % 创建QoS索引
    QoSIndex = #{
        <<"key">> => #{<<"qos">> => 1},
        <<"name">> => <<MessageAbbr/binary, "_qos_asc">>
    },

    % 创建时间戳索引
    TimestampIndex = #{
        <<"key">> => #{<<"timestamp">> => 1},
        <<"name">> => <<MessageAbbr/binary, "_timestamp_asc">>
    },

    % 创建所有索引
    Indexes = [IdIndex, ClientIdIndex, TopicIndex, ExpiryIndex, ClientQoSIndex, QoSIndex, TimestampIndex],

    lists:foreach(fun(Index) ->
        IndexName = maps:get(<<"name">>, Index),
        try
            Command = #{
                <<"createIndexes">> => Collection,
                <<"indexes">> => [Index]
            },
            {ok, _} = emqx_plugin_mongodb_api:command(Pid, Command),
            ?SLOG(info, #{
                msg => "message_index_created",
                collection => Collection,
                index_name => IndexName
            })
        catch
            E:R:S ->
                handle_message_index_error(Collection, IndexName, {E, R, S})
        end
    end, Indexes).

%% 获取MongoDB连接
get_mongodb_connection() ->
    % 修复：优先使用emqx_resource，避免persistent_term的问题
    try
        % 第一步：尝试使用emqx_resource获取连接
        case emqx_resource:get_instance(?PLUGIN_MONGODB_RESOURCE_ID) of
            % 处理新的返回格式：{ok, ResourceGroup, ResourceData}
            {ok, _ResourceGroup, #{state := #{topology_pid := Pid}}} when is_pid(Pid) ->
                % 成功获取连接，同时更新persistent_term缓存
                try
                    persistent_term:put(?PLUGIN_MONGODB_RESOURCE_ID, Pid)
                catch
                    _:_ -> ok  % 忽略persistent_term更新失败
                end,
                {ok, Pid};
            {ok, _ResourceGroup, #{state := State}} ->
                % 尝试从状态中获取其他可能的连接信息
                case maps:get(topology_pid, State, undefined) of
                    Pid when is_pid(Pid) ->
                        try
                            persistent_term:put(?PLUGIN_MONGODB_RESOURCE_ID, Pid)
                        catch
                            _:_ -> ok
                        end,
                        {ok, Pid};
                    _ ->
                        % 尝试从其他字段获取连接
                        case maps:get(pool, State, undefined) of
                            Pid when is_pid(Pid) ->
                                try
                                    persistent_term:put(?PLUGIN_MONGODB_RESOURCE_ID, Pid)
                                catch
                                    _:_ -> ok
                                end,
                                {ok, Pid};
                            _ ->
                                ?SLOG(warning, #{
                                    msg => "mongodb_resource_state_no_topology_pid",
                                    state => State
                                }),
                                {error, invalid_resource_state}
                        end
                end;
            % 处理旧的返回格式：{ok, ResourceData}（向后兼容）
            {ok, #{state := #{topology_pid := Pid}}} when is_pid(Pid) ->
                try
                    persistent_term:put(?PLUGIN_MONGODB_RESOURCE_ID, Pid)
                catch
                    _:_ -> ok
                end,
                {ok, Pid};
            {ok, #{state := State}} ->
                case maps:get(topology_pid, State, undefined) of
                    Pid when is_pid(Pid) ->
                        try
                            persistent_term:put(?PLUGIN_MONGODB_RESOURCE_ID, Pid)
                        catch
                            _:_ -> ok
                        end,
                        {ok, Pid};
                    _ ->
                        {error, invalid_resource_state}
                end;
            {error, not_found} ->
                % 资源未找到，尝试从persistent_term获取备用连接
                try
                    case persistent_term:get(?PLUGIN_MONGODB_RESOURCE_ID, undefined) of
                        Pid when is_pid(Pid) ->
                            % 验证进程是否还活着
                            case is_process_alive(Pid) of
                                true -> {ok, Pid};
                                false ->
                                    persistent_term:erase(?PLUGIN_MONGODB_RESOURCE_ID),
                                    {error, resource_not_found}
                            end;
                        _ ->
                            {error, resource_not_found}
                    end
                catch
                    error:badarg ->
                        {error, resource_not_found}
                end;
            Other ->
                ?SLOG(error, #{
                    msg => "mongodb_resource_get_instance_failed",
                    result => Other
                }),
                {error, resource_get_failed}
        end
    catch
        error:badarg ->
            ?SLOG(error, #{
                msg => "mongodb_resource_badarg_error",
                resource_id => ?PLUGIN_MONGODB_RESOURCE_ID
            }),
            {error, badarg};
        E:R:S ->
            ?SLOG(error, #{
                msg => "failed_to_get_mongodb_connection",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%% 注册消息钩子
register_hooks() ->
    % 确保模块已加载
    case code:ensure_loaded(?MODULE) of
        {module, ?MODULE} ->
            % 先注销可能存在的旧钩子，防止重复注册
            unregister_hooks(),

            % 注册消息发布钩子 - 用于消息持久化
            case erlang:function_exported(?MODULE, on_message_publish, 1) of
                true ->
                    % 使用更高的优先级，确保在主模块的主题过滤之前执行
                    % 主题过滤使用1000，消息持久化使用1100，确保先执行消息持久化
                    emqx_hooks:add('message.publish', {?MODULE, on_message_publish, []}, 1100),
                    ?SLOG(info, #{
                        msg => "message_publish_hook_registered",
                        module => ?MODULE,
                        priority => 1100
                    });
                false ->
                    ?SLOG(warning, #{msg => "on_message_publish_not_exported"})
            end,

            % 消息投递钩子 - 可选，用于追踪消息投递状态
            case erlang:function_exported(?MODULE, on_message_delivered, 2) of
                true ->
                    emqx_hooks:add('message.delivered', {?MODULE, on_message_delivered, []}, 500);
                false ->
                    ?SLOG(warning, #{msg => "on_message_delivered_not_exported"})
            end,

            % 消息确认钩子 - 用于删除已确认的消息
            case erlang:function_exported(?MODULE, on_message_acked, 2) of
                true ->
                    emqx_hooks:add('message.acked', {?MODULE, on_message_acked, []}, 500);
                false ->
                    ?SLOG(warning, #{msg => "on_message_acked_not_exported"})
            end,

            % 消息丢弃钩子 - 可用于记录被丢弃的消息
            % 尝试注册3参数版本（优先）
            case erlang:function_exported(?MODULE, on_message_dropped, 3) of
                true ->
                    emqx_hooks:add('message.dropped', {?MODULE, on_message_dropped, []}, 500);
                false ->
                    % 如果3参数版本不存在，尝试4参数版本
                    case erlang:function_exported(?MODULE, on_message_dropped, 4) of
                        true ->
                            emqx_hooks:add('message.dropped', {?MODULE, on_message_dropped, []}, 500);
                        false ->
                            ?SLOG(warning, #{msg => "on_message_dropped_not_exported"})
                    end
            end,

            ok;
        {error, Reason} ->
            ?SLOG(error, #{msg => "failed_to_load_module", module => ?MODULE, reason => Reason}),
            {error, module_load_failed}
    end.

%% 注销消息钩子
unregister_hooks() ->
    emqx_hooks:del('message.publish', {?MODULE, on_message_publish}),
    emqx_hooks:del('message.delivered', {?MODULE, on_message_delivered}),
    emqx_hooks:del('message.acked', {?MODULE, on_message_acked}),
    emqx_hooks:del('message.dropped', {?MODULE, on_message_dropped}),
    emqx_hooks:del('client.disconnected', {?MODULE, on_client_disconnected}),
    ok.

%% 消息发布钩子回调
on_message_publish(Message = #message{topic = <<"$SYS/", _/binary>>}) ->
    % 系统主题消息，直接返回，不进行处理
    ?SLOG(debug, #{msg => "sys_topic_message_skipped", topic => Message#message.topic}),
    {ok, Message};
on_message_publish(Message = #message{topic = <<"/emqx/status/", _/binary>>}) ->
    % 客户端状态消息，直接返回，不进行持久化
    % 这些是客户端的在线状态消息，不需要持久化
    {ok, Message};
on_message_publish(Message = #message{topic = <<"/emqx/will/", _/binary>>}) ->
    % 遗嘱消息主题，直接返回，不进行持久化
    % 遗嘱消息由专门的遗嘱消息模块处理
    {ok, Message};
on_message_publish(Message) ->
    % 检查是否为恢复的消息，如果是则跳过持久化
    case emqx_message:get_header(skip_persistence, Message, false) of
        true ->
            % 这是恢复的消息，跳过持久化避免重复处理
            ?SLOG(debug, #{
                msg => "skipping_recovered_message_persistence",
                topic => Message#message.topic,
                reason => "message_already_persisted_during_recovery"
            }),
            {ok, Message};
        false ->
            % 修复：检查是否为恢复的遗嘱消息，如果是则跳过持久化
            case emqx_message:get_header(restored_will_message, Message, false) of
                true ->
                    % 这是恢复的遗嘱消息，跳过持久化避免无限重试
                    ?SLOG(debug, #{
                        msg => "skipping_persistence_for_restored_will_message",
                        topic => Message#message.topic,
                        client_id => emqx_message:from(Message),
                        reason => "restored_will_message_should_not_trigger_persistence"
                    }),
                    {ok, Message};
                false ->
                    % 处理正常的客户端消息
                    handle_normal_message_publish(Message)
            end
    end.

%% @doc 处理正常消息发布（非恢复的遗嘱消息）
handle_normal_message_publish(Message = #message{qos = QoS}) ->
    Topic = Message#message.topic,
    MessageId = emqx_guid:to_hexstr(Message#message.id),
    ClientId = emqx_message:from(Message),

    % 创建唯一的调用标识符用于跟踪重复调用
    CallId = erlang:unique_integer([positive]),

    % 最简单的日志确认钩子被调用
    io:format("HOOK CALLED[~p]: Topic=~p, MsgId=~p, Client=~p~n", [CallId, Topic, MessageId, ClientId]),

    % 详细追踪消息来源，找出额外消息的产生原因
    MessageHeaders = emqx_message:get_headers(Message),
    {current_stacktrace, CallStack} = erlang:process_info(self(), current_stacktrace),

    % 添加调试日志确认钩子被调用
    ?SLOG(info, #{
        msg => "message_persistence_hook_source_analysis",
        call_id => CallId,
        topic => Topic,
        qos => QoS,
        client_id => ClientId,
        message_id => MessageId,
        module => ?MODULE,
        message_headers => MessageHeaders,
        message_flags => Message#message.flags,
        message_from => emqx_message:from(Message),
        message_timestamp => emqx_message:timestamp(Message),
        username => emqx_message:get_header(username, Message, undefined),
        peerhost => emqx_message:get_header(peerhost, Message, undefined),
        % 检查是否为内部消息的标识
        is_internal_message => maps:get(internal, MessageHeaders, false),
        is_system_message => maps:get(system, MessageHeaders, false),
        is_will_message => maps:get(will_message, MessageHeaders, false),
        is_retained_message => maps:get(retained, MessageHeaders, false),
        is_restored_message => maps:get(restored, MessageHeaders, false),
        skip_persistence => maps:get(skip_persistence, MessageHeaders, false),
        restored_will_message => maps:get(restored_will_message, MessageHeaders, false),
        % 调用栈前5层，用于追踪消息来源
        call_stack_top5 => lists:sublist(CallStack, 5),
        % 进程信息
        process_info => #{
            pid => self(),
            registered_name => erlang:process_info(self(), registered_name),
            initial_call => erlang:process_info(self(), initial_call)
        }
    }),
    % 获取配置并检查消息持久化是否启用
    Config = emqx_plugin_mongodb:read_config(),

    % 添加详细的调试日志
    ?SLOG(info, #{
        msg => "message_persistence_config_debug",
        config_result => Config,
        config_type => type_of(Config)
    }),

    MessageConfig = maps:get(message_persistence, Config, #{}),

    % 添加消息配置的调试日志
    ?SLOG(info, #{
        msg => "message_persistence_config_extracted",
        message_config => MessageConfig,
        enabled_value => maps:get(enabled, MessageConfig, false)
    }),

    case maps:get(enabled, MessageConfig, false) of
        false ->
            % 消息持久化未启用，直接返回
            ?SLOG(debug, #{
                msg => "message_persistence_disabled",
                topic => Message#message.topic,
                qos => QoS,
                client_id => emqx_message:from(Message)
            }),
            % 主题过滤现在通过独立钩子处理，不需要在这里调用
            {ok, Message};
        true ->

            %% 修复：严格按照MQTT协议和配置处理消息持久化
            %% 首先检查配置是否允许持久化该QoS级别的消息
            case should_persist_message(QoS, MessageConfig) of
                false ->
                    %% 配置不允许持久化该QoS级别的消息
                    ?SLOG(debug, #{
                        msg => "message_persistence_disabled_by_config",
                        client_id => emqx_message:from(Message),
                        topic => Message#message.topic,
                        qos => QoS,
                        reason => "not_in_retain_policy"
                    }),
                    {ok, Message};
                true ->
                    %% 配置允许持久化，根据QoS级别处理
                    case QoS of
                        0 ->
                            %% QoS 0消息：根据MQTT协议，QoS 0消息是"最多一次"投递
                            %% "发后即忘"语义，不需要确认，不应该持久化
                            %% 这是MQTT协议的基本要求，不可配置
                            ?SLOG(debug, #{
                                msg => "qos0_message_no_persistence_per_mqtt_protocol",
                                client_id => emqx_message:from(Message),
                                topic => Message#message.topic,
                                reason => "qos0_fire_and_forget_semantics"
                            }),
                            {ok, Message};
                        1 ->
                            %% QoS 1消息：需要持久化直到收到PUBACK确认
                            %% 同步保存确保消息在投递前已经持久化
                            try
                                save_message(Message),
                                ?SLOG(debug, #{
                                    msg => "qos1_message_saved_for_ack_tracking",
                                    client_id => emqx_message:from(Message),
                                    topic => Message#message.topic,
                                    message_id => emqx_guid:to_hexstr(Message#message.id)
                                }),
                                % 消息持久化完成后调用主题过滤
                                ?SLOG(info, #{
                                    msg => "qos1_message_saved_successfully",
                                    topic => Message#message.topic,
                                    client_id => emqx_message:from(Message)
                                }),
                                {ok, Message}
                            catch
                                E:R:S ->
                                    ?SLOG(error, #{
                                        msg => "failed_to_save_qos1_message_critical",
                                        error => E,
                                        reason => R,
                                        stack => S,
                                        client_id => emqx_message:from(Message),
                                        topic => Message#message.topic,
                                        message_id => emqx_guid:to_hexstr(Message#message.id),
                                        note => "qos1_reliability_may_be_compromised"
                                    }),
                                    %% 修复：根据MQTT协议，QoS 1消息必须确保可靠投递
                                    %% 如果持久化失败，应该考虑消息投递的可靠性
                                    %% 这里仍然允许消息继续传递，但记录严重错误
                                    %% 在生产环境中，可以考虑返回错误来阻止消息传递
                                    {ok, Message}
                            end;
                        2 ->
                            %% QoS 2消息：需要持久化直到完成PUBREC/PUBREL/PUBCOMP握手
                            %% 同步保存确保消息在投递前已经持久化
                            try
                                save_message(Message),
                                ?SLOG(debug, #{
                                    msg => "qos2_message_saved_for_handshake_tracking",
                                    client_id => emqx_message:from(Message),
                                    topic => Message#message.topic,
                                    message_id => emqx_guid:to_hexstr(Message#message.id)
                                }),
                                % 消息持久化完成后调用主题过滤
                                ?SLOG(info, #{
                                    msg => "qos2_message_saved_successfully",
                                    topic => Message#message.topic,
                                    client_id => emqx_message:from(Message)
                                }),
                                {ok, Message}
                            catch
                                E:R:S ->
                                    ?SLOG(error, #{
                                        msg => "failed_to_save_qos2_message_critical",
                                        error => E,
                                        reason => R,
                                        stack => S,
                                        client_id => emqx_message:from(Message),
                                        topic => Message#message.topic,
                                        message_id => emqx_guid:to_hexstr(Message#message.id),
                                        note => "qos2_reliability_may_be_compromised"
                                    }),
                                    %% 修复：根据MQTT协议，QoS 2消息必须确保可靠投递
                                    %% 如果持久化失败，应该考虑消息投递的可靠性
                                    {ok, Message}
                            end;
                        _ ->
                            %% 其他QoS级别（理论上不应该存在）
                            ?SLOG(warning, #{
                                msg => "unknown_qos_level",
                                qos => QoS,
                                client_id => emqx_message:from(Message),
                                topic => Message#message.topic
                            }),
                            {ok, Message}
                    end
            end;
        _ ->
            % 未知QoS级别
            ?SLOG(warning, #{
                msg => "unknown_qos_level_ignoring_message",
                client_id => emqx_message:from(Message),
                topic => Topic,
                qos => QoS
            }),
            {ok, Message}
    end.





%% 消息投递钩子回调 - 两个参数版本 (EMQX 5.x)
on_message_delivered(ClientInfo, Message) ->
    ?SLOG(debug, #{msg => "message_delivered_2args", client_id => maps:get(clientid, ClientInfo, undefined)}),
    ok.

%% 消息投递钩子回调 - 三个参数版本 (旧版EMQX)
on_message_delivered(ClientInfo, Topic, Message) ->
    ?SLOG(debug, #{msg => "message_delivered_3args", client_id => maps:get(clientid, ClientInfo, undefined), topic => Topic}),
    ok.

%% 消息确认钩子回调 - 两个参数版本 (EMQX 5.x)
on_message_acked(ClientInfo, Message) ->
    ?SLOG(debug, #{
        msg => "message_acked_2args",
        client_id => maps:get(clientid, ClientInfo, undefined),
        message_id => emqx_guid:to_hexstr(Message#message.id),
        qos => Message#message.qos,
        topic => Message#message.topic
    }),
    process_message_ack(ClientInfo, Message),
    ok.

%% 消息确认钩子回调 - 三个参数版本 (旧版EMQX)
on_message_acked(ClientInfo, Topic, Message) ->
    ?SLOG(debug, #{
        msg => "message_acked_3args",
        topic => Topic,
        client_id => maps:get(clientid, ClientInfo, undefined),
        message_id => emqx_guid:to_hexstr(Message#message.id),
        qos => Message#message.qos
    }),
    process_message_ack(ClientInfo, Message),
    ok.

%% @doc 处理消息确认的内部函数
%% 这个函数实现了MQTT协议的消息确认处理逻辑
%%
%% 功能说明：
%% 1. 只处理QoS 1和2的消息确认（QoS 0不需要确认）
%% 2. 消息确认后从emqx_mqtt_messages集合中删除对应记录
%% 3. 提供详细的日志记录和错误处理
%% 4. 确保客户端ID的一致性检查
%%
%% MQTT协议说明：
%% - QoS 0: 最多一次投递，无需确认，不会调用此函数
%% - QoS 1: 至少一次投递，需要PUBACK确认，确认后删除
%% - QoS 2: 恰好一次投递，需要完整握手，完成后删除
%%
%% 参数说明：
%% - ClientInfo: 客户端信息，包含clientid等
%% - Message: 被确认的消息对象
%%
%% Java等价概念：
%% @EventListener
%% public void processMessageAck(ClientInfo clientInfo, Message message) {
%%     if (!messagePersistenceEnabled) return;
%%
%%     if (message.getQos() == 1) {
%%         // QoS 1: PUBACK确认，删除消息
%%         messageRepository.deleteByClientIdAndMessageId(
%%             message.getFrom(), message.getId());
%%     } else if (message.getQos() == 2) {
%%         // QoS 2: PUBCOMP确认，删除消息
%%         messageRepository.deleteByClientIdAndMessageId(
%%             message.getFrom(), message.getId());
%%     }
%% }
process_message_ack(ClientInfo, Message) ->
    %% 检查消息持久化是否启用
    Config = emqx_plugin_mongodb:read_config(),
    MessageConfig = maps:get(message_persistence, Config, #{}),

    case maps:get(enabled, MessageConfig, false) of
        false ->
            %% 消息持久化未启用，不需要处理确认
            ?SLOG(debug, #{
                msg => "message_persistence_disabled_skipping_ack",
                client_id => maps:get(clientid, ClientInfo, <<>>)
            }),
            ok;
        true ->
            %% 消息持久化已启用，处理消息确认
            %% 只处理QoS 1和2的消息确认，QoS 0消息不需要确认
            case Message#message.qos of
                0 ->
                    %% QoS 0消息不需要确认，也不应该被持久化
                    %% 如果到达这里，可能是配置或逻辑错误
                    ?SLOG(debug, #{
                        msg => "qos0_message_ack_unexpected",
                        client_id => maps:get(clientid, ClientInfo, <<>>),
                        message_id => emqx_guid:to_hexstr(Message#message.id),
                        note => "qos0_messages_should_not_need_ack"
                    }),
                    ok;
                1 ->
                    %% QoS 1消息确认：收到PUBACK，可以删除持久化记录
                    handle_qos1_message_ack(ClientInfo, Message);
                2 ->
                    %% QoS 2消息确认：完成PUBREC/PUBREL/PUBCOMP握手，可以删除持久化记录
                    handle_qos2_message_ack(ClientInfo, Message);
                UnknownQoS ->
                    %% 未知的QoS值，记录警告
                    ?SLOG(warning, #{
                        msg => "unknown_qos_in_message_ack",
                        qos => UnknownQoS,
                        message_from_client_id => Message#message.from,
                        clientinfo_client_id => maps:get(clientid, ClientInfo, <<>>),
                        message_id => emqx_guid:to_hexstr(Message#message.id)
                    })
            end
    end.

%% @doc 处理QoS 1消息确认
%% QoS 1消息在收到PUBACK后可以从持久化存储中删除
handle_qos1_message_ack(ClientInfo, Message) ->
    try
        %% 提取消息信息
        ClientId = Message#message.from,
        MessageId = emqx_guid:to_hexstr(Message#message.id),
        ClientInfoId = maps:get(clientid, ClientInfo, <<>>),

        ?SLOG(debug, #{
            msg => "processing_qos1_message_ack",
            message_from_client_id => ClientId,
            clientinfo_client_id => ClientInfoId,
            client_id_match => (ClientId =:= ClientInfoId),
            message_id => MessageId,
            ack_type => "PUBACK"
        }),

        %% 获取集合名称并删除消息
        MessageCollection = get_message_collection(),
        delete_acked_message(ClientId, MessageId, MessageCollection)
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_processing_qos1_message_ack",
                error => E,
                reason => R,
                stacktrace => S,
                message_from_client_id => Message#message.from,
                clientinfo_client_id => maps:get(clientid, ClientInfo, <<>>),
                message_id => emqx_guid:to_hexstr(Message#message.id)
            })
    end.

%% @doc 处理QoS 2消息确认
%% QoS 2消息在完成完整的PUBREC/PUBREL/PUBCOMP握手后可以从持久化存储中删除
handle_qos2_message_ack(ClientInfo, Message) ->
    try
        %% 提取消息信息
        ClientId = Message#message.from,
        MessageId = emqx_guid:to_hexstr(Message#message.id),
        ClientInfoId = maps:get(clientid, ClientInfo, <<>>),

        ?SLOG(debug, #{
            msg => "processing_qos2_message_ack",
            message_from_client_id => ClientId,
            clientinfo_client_id => ClientInfoId,
            client_id_match => (ClientId =:= ClientInfoId),
            message_id => MessageId,
            ack_type => "PUBCOMP"
        }),

        %% 获取集合名称并删除消息
        MessageCollection = get_message_collection(),
        delete_acked_message(ClientId, MessageId, MessageCollection)
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_processing_qos2_message_ack",
                error => E,
                reason => R,
                stacktrace => S,
                message_from_client_id => Message#message.from,
                clientinfo_client_id => maps:get(clientid, ClientInfo, <<>>),
                message_id => emqx_guid:to_hexstr(Message#message.id)
            })
    end.

%% @doc 删除已确认的消息
%% 这个函数从emqx_mqtt_messages集合中删除已确认的消息记录
%%
%% 功能说明：
%% 1. 根据客户端ID和消息ID精确删除消息记录
%% 2. 提供多种删除方式的fallback机制
%% 3. 处理消息可能还在保存过程中的竞态条件
%% 4. 提供详细的删除结果日志
%%
%% 参数说明：
%% - ClientId: 消息发布者的客户端ID
%% - MessageId: 消息的唯一标识符（十六进制字符串）
%% - MessageCollection: MongoDB集合名称
%%
%% Java等价概念：
%% @Transactional
%% public void deleteAckedMessage(String clientId, String messageId, String collection) {
%%     try {
%%         Query query = Query.query(
%%             Criteria.where("client_id").is(clientId)
%%                    .and("message_id").is(messageId)
%%         );
%%         DeleteResult result = mongoTemplate.remove(query, collection);
%%
%%         if (result.getDeletedCount() == 0) {
%%             // 消息可能还在保存中，延迟重试
%%             scheduleRetryDelete(clientId, messageId, collection);
%%         }
%%     } catch (Exception e) {
%%         logger.error("Failed to delete acked message", e);
%%     }
%% }
delete_acked_message(ClientId, MessageId, MessageCollection) ->
    %% 首先尝试使用直接MongoDB驱动删除消息
    %% 这是最高效的删除方式
    try
        case get_mongodb_connection() of
            {ok, Pid} when is_pid(Pid) ->
                %% 构建删除过滤器 - 确保类型一致性
                %% 使用客户端ID和消息ID的组合来精确定位消息
                Filter = #{
                    <<"client_id">> => ensure_binary_string(ClientId),
                    <<"message_id">> => ensure_binary_string(MessageId)
                },

                ?SLOG(debug, #{
                    msg => "attempting_direct_message_deletion",
                    client_id => ClientId,
                    message_id => MessageId,
                    collection => MessageCollection
                }),

                %% 使用扩展API删除文档
                case emqx_plugin_mongodb_api:bulk_delete(Pid, MessageCollection, [Filter]) of
                    #{deleted_count := Count, errors := []} when Count > 0 ->
                        %% 删除成功
                        ?SLOG(debug, #{
                            msg => "message_deleted_after_ack_success",
                            client_id => ClientId,
                            message_id => MessageId,
                            deleted_count => Count,
                            method => "direct_api"
                        });
                    #{deleted_count := 0} ->
                        %% 没有找到要删除的消息
                        %% 这可能是因为消息还在保存过程中（竞态条件）
                        ?SLOG(info, #{
                            msg => "message_not_found_for_deletion_scheduling_retry",
                            client_id => ClientId,
                            message_id => MessageId,
                            reason => "possible_race_condition_with_save"
                        }),
                        %% 异步延迟重试删除，避免阻塞确认流程
                        spawn(fun() ->
                            timer:sleep(1000), %% 等待1秒让保存操作完成
                            retry_delete_message(ClientId, MessageId, MessageCollection, 3)
                        end);
                    #{errors := Errors} when length(Errors) > 0 ->
                        %% 删除过程中出现错误
                        ?SLOG(error, #{
                            msg => "failed_to_delete_message_with_errors",
                            client_id => ClientId,
                            message_id => MessageId,
                            errors => Errors,
                            method => "direct_api"
                        });
                    Other ->
                        %% 其他未预期的结果
                        ?SLOG(warning, #{
                            msg => "unexpected_message_delete_result",
                            client_id => ClientId,
                            message_id => MessageId,
                            result => Other,
                            method => "direct_api"
                        })
                end;
            _ ->
                %% 直接MongoDB连接不可用，使用emqx_resource作为fallback
                ?SLOG(debug, #{
                    msg => "direct_mongodb_connection_unavailable_using_fallback",
                    client_id => ClientId,
                    message_id => MessageId
                }),
                use_resource_fallback_delete(ClientId, MessageId, MessageCollection)
        end
    catch
        E1:R1:S1 ->
            %% 直接删除方式出现异常，记录错误并尝试fallback
            ?SLOG(error, #{
                msg => "error_in_direct_message_deletion",
                error => E1,
                reason => R1,
                stacktrace => S1,
                client_id => ClientId,
                message_id => MessageId
            }),
            %% 尝试使用emqx_resource作为备用方式
            use_resource_fallback_delete(ClientId, MessageId, MessageCollection)
    end.

%% @doc 检查消息是否存在于MongoDB中
%% 用于诊断删除失败的原因
check_message_exists(Pid, Collection, Filter) ->
    try
        case emqx_plugin_mongodb_api:find_one(Pid, Collection, Filter) of
            {ok, Doc} when is_map(Doc) ->
                true;
            {ok, undefined} ->
                false;
            {ok, null} ->
                false;
            _ ->
                false
        end
    catch
        _:_ ->
            false
    end.

%% @doc 使用emqx_resource作为fallback删除消息
%% 当直接MongoDB连接不可用时使用此方法
use_resource_fallback_delete(ClientId, MessageId, MessageCollection) ->
    try
        ?SLOG(debug, #{
            msg => "using_resource_fallback_for_message_deletion",
            client_id => ClientId,
            message_id => MessageId
        }),

        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                                {delete_message, MessageCollection, ClientId, MessageId}) of
            {ok, _} ->
                ?SLOG(debug, #{
                    msg => "message_deleted_after_ack_success",
                    client_id => ClientId,
                    message_id => MessageId,
                    method => "resource_fallback"
                });
            {async_return, ok} ->
                ?SLOG(debug, #{
                    msg => "message_deleted_after_ack_success",
                    client_id => ClientId,
                    message_id => MessageId,
                    method => "resource_fallback_async"
                });
            {async_return, {ok, _}} ->
                ?SLOG(debug, #{
                    msg => "message_deleted_after_ack_success",
                    client_id => ClientId,
                    message_id => MessageId,
                    method => "resource_fallback_async"
                });
            {async_return, {error, Reason}} ->
                ?SLOG(warning, #{
                    msg => "failed_to_delete_acked_message",
                    client_id => ClientId,
                    message_id => MessageId,
                    reason => Reason,
                    method => "resource_fallback_async"
                });
            {error, Reason} ->
                ?SLOG(warning, #{
                    msg => "failed_to_delete_acked_message",
                    client_id => ClientId,
                    message_id => MessageId,
                    reason => Reason,
                    method => "resource_fallback"
                })
        end
    catch
        E2:R2:S2 ->
            %% 所有删除方式都失败了
            ?SLOG(error, #{
                msg => "all_message_deletion_methods_failed",
                error => E2,
                reason => R2,
                stacktrace => S2,
                client_id => ClientId,
                message_id => MessageId,
                note => "message_may_remain_in_database"
            })
    end.

%% @doc 重试删除消息
%% 处理消息保存和删除之间的竞态条件
retry_delete_message(_ClientId, _MessageId, _MessageCollection, 0) ->
    %% 重试次数用完，记录警告并提供诊断信息
    ?SLOG(warning, #{
        msg => "message_deletion_retry_exhausted",
        client_id => _ClientId,
        message_id => _MessageId,
        collection => _MessageCollection,
        note => "message_may_remain_in_database_check_manually",
        diagnostic_query => #{
            <<"message_id">> => _MessageId,
            <<"client_id">> => _ClientId
        },
        suggested_action => "Check MongoDB connection, permissions, and manually verify if message exists"
    });
retry_delete_message(ClientId, MessageId, MessageCollection, RetriesLeft) ->
    ?SLOG(debug, #{
        msg => "retrying_message_deletion",
        client_id => ClientId,
        message_id => MessageId,
        retries_left => RetriesLeft
    }),

    try
        case get_mongodb_connection() of
            {ok, Pid} when is_pid(Pid) ->
                Filter = #{
                    <<"client_id">> => ensure_binary_string(ClientId),
                    <<"message_id">> => ensure_binary_string(MessageId)
                },

                case emqx_plugin_mongodb_api:bulk_delete(Pid, MessageCollection, [Filter]) of
                    #{deleted_count := Count, errors := []} when Count > 0 ->
                        ?SLOG(info, #{
                            msg => "message_deletion_retry_successful",
                            client_id => ClientId,
                            message_id => MessageId,
                            deleted_count => Count,
                            retries_used => 4 - RetriesLeft
                        });
                    #{deleted_count := 0} ->
                        %% 仍然没有找到消息，检查消息是否真的存在
                        case check_message_exists(Pid, MessageCollection, Filter) of
                            true ->
                                ?SLOG(warning, #{
                                    msg => "message_exists_but_delete_failed",
                                    client_id => ClientId,
                                    message_id => MessageId,
                                    retries_left => RetriesLeft - 1,
                                    note => "message_exists_in_database_but_deletion_returned_0"
                                }),
                                timer:sleep(2000),
                                retry_delete_message(ClientId, MessageId, MessageCollection, RetriesLeft - 1);
                            false ->
                                ?SLOG(info, #{
                                    msg => "message_not_found_deletion_successful",
                                    client_id => ClientId,
                                    message_id => MessageId,
                                    note => "message_was_already_deleted_or_never_existed"
                                })
                        end;
                    #{errors := Errors} when length(Errors) > 0 ->
                        ?SLOG(error, #{
                            msg => "message_deletion_retry_failed_with_errors",
                            client_id => ClientId,
                            message_id => MessageId,
                            errors => Errors,
                            retries_left => RetriesLeft - 1
                        }),
                        %% 出现错误，但还有重试机会
                        if RetriesLeft > 1 ->
                            timer:sleep(2000),
                            retry_delete_message(ClientId, MessageId, MessageCollection, RetriesLeft - 1);
                        true ->
                            ok %% 不再重试
                        end
                end;
            _ ->
                %% 连接不可用，尝试使用resource方式
                use_resource_fallback_delete(ClientId, MessageId, MessageCollection)
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_in_message_deletion_retry",
                error => E,
                reason => R,
                stacktrace => S,
                client_id => ClientId,
                message_id => MessageId,
                retries_left => RetriesLeft
            }),
            %% 出现异常，如果还有重试机会就继续
            if RetriesLeft > 1 ->
                timer:sleep(2000),
                retry_delete_message(ClientId, MessageId, MessageCollection, RetriesLeft - 1);
            true ->
                ok %% 不再重试
            end
    end.

%% 消息丢弃钩子回调 (3参数版本，用于兼容不同EMQX版本)
on_message_dropped(Message = #message{topic = <<"$SYS/", _/binary>>}, _Context, _Reason) ->
    % 忽略系统主题消息
    ok;
on_message_dropped(Message, Context, Reason) ->
    % 调用4参数版本
    on_message_dropped(Message, Reason, Context, #{}).

%% 消息丢弃钩子回调 (4参数版本)
on_message_dropped(Message = #message{topic = <<"$SYS/", _/binary>>}, _Reason, _Context, _Env) ->
    % 忽略系统主题消息
    ok;
on_message_dropped(Message, Reason, Context, _Env) ->
    % 检查消息持久化是否启用
    Config = emqx_plugin_mongodb:read_config(),
    MessageConfig = maps:get(message_persistence, Config, #{}),

    case maps:get(enabled, MessageConfig, false) of
        false ->
            % 消息持久化未启用，只记录基本日志
            ?SLOG(debug, #{
                msg => "message_dropped",
                client_id => maps:get(clientid, Context, undefined),
                topic => Message#message.topic,
                reason => Reason
            });
        true ->
            % 记录详细日志并可以执行其他处理
            try
                ?SLOG(debug, #{
                    msg => "message_dropped",
                    client_id => maps:get(clientid, Context, undefined),
                    topic => Message#message.topic,
                    reason => Reason,
                    qos => Message#message.qos,
                    message_id => emqx_guid:to_hexstr(Message#message.id)
                })
            catch
                E:R:S ->
                    ?SLOG(warning, #{
                        msg => "error_processing_dropped_message",
                        error => E,
                        reason => R,
                        stacktrace => S
                    })
            end
    end,
    ok.

%% @doc 保存消息到MongoDB
%% 这个函数只处理QoS 1和2的消息，因为只有这些消息需要确认跟踪
%%
%% 功能说明：
%% 1. QoS 1消息：保存直到收到PUBACK确认
%% 2. QoS 2消息：保存直到完成PUBREC/PUBREL/PUBCOMP握手
%% 3. QoS 0消息：不会到达这个函数，因为在上层已经被过滤
%%
%% 参数说明：
%% - Message: EMQX消息记录，包含所有消息信息
%%
%% Java等价概念：
%% @Transactional
%% public void saveMessageForAckTracking(Message message) {
%%     if (message.getQos() == 0) {
%%         // QoS 0消息不需要确认跟踪，直接返回
%%         return;
%%     }
%%     // 保存QoS 1/2消息到数据库，等待确认
%%     messageRepository.save(message);
%% }
save_message(Message = #message{qos = QoS, id = Id, from = ClientId, topic = Topic, payload = Payload, timestamp = Timestamp}) when QoS > 0 ->
    try
        % 获取配置
        Config = emqx_plugin_mongodb:read_config(),
        MessageConfig = maps:get(message_persistence, Config, #{}),

        % 检查是否需要持久化该QoS级别的消息
        case should_persist_message(QoS, MessageConfig) of
            false ->
                % 不需要持久化
                ok;
            true ->
                % 获取MQTT 5.0消息属性
                Props = emqx_message:get_header(properties, Message, #{}),

                % 解析MQTT 5.0消息属性
                MessageExpiryInterval = extract_message_expiry_interval(Props),
                TopicAlias = extract_topic_alias(Props),
                ResponseTopic = extract_response_topic(Props),
                CorrelationData = extract_correlation_data(Props),
                UserProperties = extract_user_properties(Props),
                ContentType = extract_content_type(Props),
                PayloadFormatIndicator = extract_payload_format_indicator(Props),
                SubscriptionIdentifier = extract_subscription_identifier(Props),

                % 计算消息过期时间（优先使用消息自身的过期设置）
                ExpiryTime = calculate_message_expiry_time(MessageExpiryInterval, MessageConfig),

                % 检查消息大小
                LargeThreshold = parse_size(maps:get(large_message_threshold, MessageConfig, <<"64KB">>)),
                PayloadSize = byte_size(Payload),

                % 处理大消息
                ProcessedPayload = case PayloadSize > LargeThreshold of
                    true ->
                        % 大消息处理
                        case maps:get(compress_payload, MessageConfig, false) of
                            true ->
                                % 压缩消息内容
                                zlib:compress(Payload);
                            false ->
                                % 大消息不压缩，根据配置可能会被截断
                                Payload
                        end;
                    false ->
                        % 普通大小消息
                        Payload
                end,

                % 检查Payload是否为JSON格式，如果是，则解析为MongoDB文档
                ParsedPayload = try_parse_json(ProcessedPayload),

                % 调试保存时的客户端ID
                BinaryClientId = ensure_binary_string(ClientId),
                MessageIdHex = emqx_guid:to_hexstr(Id),

                ?SLOG(debug, #{
                    msg => "debug_save_message_ids",
                    original_client_id => ClientId,
                    original_client_id_type => type_of(ClientId),
                    binary_client_id => BinaryClientId,
                    binary_client_id_type => type_of(BinaryClientId),
                    original_message_id => Id,
                    hex_message_id => MessageIdHex,
                    client_id_match => (ClientId =:= BinaryClientId)
                }),

                % 提取用户名和对等主机信息
                Username = emqx_message:get_header(username, Message, <<>>),
                Peerhost = emqx_message:get_header(peerhost, Message, undefined),

                % 检查并记录空字段情况
                case {Username, Peerhost} of
                    {<<>>, undefined} ->
                        ?SLOG(warning, #{
                            msg => "message_with_empty_username_and_peerhost",
                            client_id => BinaryClientId,
                            topic => Topic,
                            message_id => MessageIdHex,
                            message_headers => emqx_message:get_headers(Message),
                            message_from => emqx_message:from(Message),
                            reason => "both_username_and_peerhost_missing"
                        });
                    {<<>>, _} ->
                        ?SLOG(info, #{
                            msg => "message_with_empty_username",
                            client_id => BinaryClientId,
                            topic => Topic,
                            message_id => MessageIdHex,
                            peerhost => Peerhost,
                            reason => "username_missing_possibly_anonymous_client"
                        });
                    {_, undefined} ->
                        ?SLOG(info, #{
                            msg => "message_with_undefined_peerhost",
                            client_id => BinaryClientId,
                            topic => Topic,
                            message_id => MessageIdHex,
                            username => Username,
                            reason => "peerhost_missing_possibly_internal_message"
                        });
                    _ ->
                        ok  % 正常情况，不记录日志
                end,

                % 构建符合MQTT协议的消息文档
                MessageDoc = #{
                    <<"message_id">> => MessageIdHex,
                    <<"client_id">> => BinaryClientId,
                    <<"username">> => ensure_binary_string(Username),
                    <<"peerhost">> => ensure_binary_string(format_peerhost(Peerhost)),
                    <<"topic">> => ensure_binary_string(Topic),
                    <<"qos">> => QoS,
                    <<"payload">> => ParsedPayload,
                    <<"payload_size">> => PayloadSize,
                    <<"is_compressed">> => maps:get(compress_payload, MessageConfig, false) andalso PayloadSize > LargeThreshold,
                    <<"timestamp">> => Timestamp,
                    <<"expiry_time">> => ExpiryTime,
                    <<"node">> => atom_to_binary(node(), utf8),

                    % MQTT 5.0消息属性
                    <<"message_expiry_interval">> => MessageExpiryInterval,
                    <<"topic_alias">> => TopicAlias,
                    <<"response_topic">> => ResponseTopic,
                    <<"correlation_data">> => CorrelationData,
                    <<"user_properties">> => UserProperties,
                    <<"content_type">> => ContentType,
                    <<"payload_format_indicator">> => PayloadFormatIndicator,
                    <<"subscription_identifier">> => SubscriptionIdentifier
                },

                % 添加消息属性
                MessageDocWithProps = case maps:get(store_message_properties, MessageConfig, true) of
                    true ->
                        % 获取MQTT 5.0属性
                        Props = emqx_message:get_header(properties, Message, #{}),
                        MessageDoc#{<<"properties">> => maps:map(fun format_property/2, Props)};
                    false ->
                        MessageDoc
                end,

                % 获取集合名称
                MessageCollection = get_message_collection(),

                % 使用insert操作避免覆盖已存在的消息数据
                try
                    % 先检查消息是否已存在，避免覆盖已持久化的数据
                    MessageIdHex = emqx_guid:to_hexstr(Id),
                    % 获取MongoDB连接
                    case get_mongodb_connection() of
                        {ok, Connection} ->
                            case emqx_plugin_mongodb_api:find_one(
                                Connection,
                                MessageCollection,
                                #{<<"message_id">> => MessageIdHex}
                            ) of
                        {ok, undefined} ->
                            % 消息不存在，可以安全插入
                            case emqx_plugin_mongodb_api:insert(
                                Connection,
                                MessageCollection,
                                MessageDocWithProps
                            ) of
                                ok ->
                                    ?SLOG(info, #{
                                        msg => "message_saved_successfully",
                                        client_id => ClientId,
                                        topic => Topic,
                                        message_id => MessageIdHex,
                                        qos => QoS,
                                        collection => MessageCollection,
                                        method => "insert_new"
                                    });
                                {error, Reason} ->
                                    ?SLOG(error, #{
                                        msg => "message_save_failed",
                                        client_id => ClientId,
                                        topic => Topic,
                                        message_id => MessageIdHex,
                                        qos => QoS,
                                        reason => Reason,
                                        collection => MessageCollection,
                                        method => "insert_new"
                                    })
                            end;
                        {ok, _ExistingDoc} ->
                            % 消息已存在，不覆盖，保持数据一致性
                            ?SLOG(debug, #{
                                msg => "message_already_exists_skipping",
                                client_id => ClientId,
                                topic => Topic,
                                message_id => MessageIdHex,
                                qos => QoS,
                                collection => MessageCollection,
                                reason => "preserving_existing_data_integrity"
                            });
                        {error, FindReason} ->
                            ?SLOG(error, #{
                                msg => "message_existence_check_failed",
                                client_id => ClientId,
                                topic => Topic,
                                message_id => MessageIdHex,
                                qos => QoS,
                                reason => FindReason,
                                collection => MessageCollection
                            })
                            end;
                        {error, ConnectionReason} ->
                            ?SLOG(error, #{
                                msg => "mongodb_connection_failed_for_message_check",
                                client_id => ClientId,
                                topic => Topic,
                                message_id => MessageIdHex,
                                qos => QoS,
                                reason => ConnectionReason,
                                collection => MessageCollection
                            })
                    end
                catch
                    E1:R1:S1 ->
                        ?SLOG(error, #{
                            msg => "error_saving_message",
                            error => E1,
                            reason => R1,
                            stacktrace => S1,
                            client_id => ClientId,
                            topic => Topic
                        })
                end
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_processing_message",
                error => E,
                reason => R,
                stacktrace => S,
                message_id => emqx_guid:to_hexstr(Id),
                client_id => ClientId,
                topic => Topic
            })
    end.

%% 启动清理定时器
start_cleanup_timer() ->
    CleanupInterval = get_cleanup_interval(),
    ?SLOG(info, #{msg => "starting_message_cleanup_timer", interval => CleanupInterval}),
    % 使用独立进程处理定时清理，避免进程依赖问题
    spawn(fun() -> cleanup_timer_loop(CleanupInterval) end),
    ok.

%% 清理定时器循环
cleanup_timer_loop(Interval) ->
    timer:sleep(Interval),
    ?SLOG(info, #{msg => "cleaning_expired_messages"}),
    % 执行清理
    spawn(fun() -> do_clean_expired_messages() end),
    % 继续循环
    cleanup_timer_loop(Interval).

%% 清理过期消息（保留原函数用于手动调用）
clean_expired_messages() ->
    ?SLOG(info, #{msg => "manual_cleaning_expired_messages"}),
    spawn(fun() -> do_clean_expired_messages() end),
    ok.

%% 执行过期消息清理
do_clean_expired_messages() ->
    try
        % 获取集合名称
        MessageCollection = get_message_collection(),

        % 计算过期时间
        Now = erlang:system_time(millisecond),

        % 删除已过期的消息
        Filter = #{<<"expiry_time">> => #{<<"$lt">> => Now}},

        % 获取批次大小
        BatchSize = get_cleanup_batch_size(),

        % 执行删除 (注意：MongoDB delete操作的limit只能是0或1，不能用BatchSize)
        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                                {delete_many, MessageCollection, Filter, #{}}) of
            {ok, #{<<"deletedCount">> := DeletedCount}} ->
                ?SLOG(info, #{msg => "expired_messages_cleaned", count => DeletedCount});
            {error, Reason} ->
                ?SLOG(error, #{msg => "failed_to_clean_expired_messages", reason => Reason})
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_cleaning_expired_messages",
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% 获取消息集合名称
get_message_collection() ->
    % 从配置中获取集合名称
    Config = application:get_env(emqx_plugin_mongodb, config, #{}),
    MessageConfig = maps:get(message_persistence, Config, #{}),
    maps:get(collection, MessageConfig, ?DEFAULT_MESSAGE_COLLECTION).

%% 获取消息过期时间
get_message_expiry() ->
    Config = application:get_env(emqx_plugin_mongodb, config, #{}),
    MessageConfig = maps:get(message_persistence, Config, #{}),
    maps:get(message_expiry, MessageConfig, ?DEFAULT_MESSAGE_EXPIRY).

%% 获取清理间隔
get_cleanup_interval() ->
    Config = application:get_env(emqx_plugin_mongodb, config, #{}),
    MessageConfig = maps:get(message_persistence, Config, #{}),
    maps:get(cleanup_interval, MessageConfig, ?DEFAULT_CLEANUP_INTERVAL).

%% 获取清理批次大小
get_cleanup_batch_size() ->
    Config = application:get_env(emqx_plugin_mongodb, config, #{}),
    MessageConfig = maps:get(message_persistence, Config, #{}),
    maps:get(cleanup_batch_size, MessageConfig, ?DEFAULT_CLEANUP_BATCH_SIZE).

%% @doc 判断是否应该持久化该QoS级别的消息
%% 根据MQTT协议，只有QoS 1和2的消息需要持久化到emqx_mqtt_messages集合
%%
%% 功能说明：
%% 1. QoS 0消息：不需要持久化，因为是"最多一次"投递，无需确认跟踪
%% 2. QoS 1消息：需要持久化，直到收到PUBACK确认
%% 3. QoS 2消息：需要持久化，直到完成完整的握手过程
%%
%% 参数说明：
%% - QoS: 消息的QoS级别 (0, 1, 或 2)
%% - Config: 消息持久化配置
%%
%% 返回值：
%% - true: 需要持久化
%% - false: 不需要持久化
%%
%% Java等价概念：
%% public boolean shouldPersistMessage(int qos, MessageConfig config) {
%%     // 根据MQTT协议，只有QoS 1和2需要确认跟踪
%%     if (qos == 0) {
%%         return false; // QoS 0是fire-and-forget，不需要持久化
%%     }
%%
%%     List<String> retainPolicy = config.getRetainPolicy();
%%     String qosStr = "qos" + qos;
%%     return retainPolicy.contains(qosStr);
%% }
should_persist_message(QoS, Config) ->
    %% 首先检查QoS级别：QoS 0消息永远不需要持久化
    case QoS of
        0 ->
            %% QoS 0消息是"最多一次"投递，不需要确认，因此不需要持久化
            false;
        1 ->
            %% QoS 1消息需要PUBACK确认，检查配置是否启用QoS 1持久化
            try
                RetainPolicy = maps:get(retain_policy, Config, ["qos1", "qos2"]),
                lists:member("qos1", RetainPolicy)
            catch
                _:_ ->
                    %% 配置解析失败时，默认持久化QoS 1消息
                    true
            end;
        2 ->
            %% QoS 2消息需要完整握手，检查配置是否启用QoS 2持久化
            try
                RetainPolicy = maps:get(retain_policy, Config, ["qos1", "qos2"]),
                lists:member("qos2", RetainPolicy)
            catch
                _:_ ->
                    %% 配置解析失败时，默认持久化QoS 2消息
                    true
            end;
        _ ->
            %% 未知的QoS级别，为了安全起见不持久化
            ?SLOG(warning, #{
                msg => "unknown_qos_level_not_persisting",
                qos => QoS
            }),
            false
    end.

%% 格式化MQTT属性
format_property(K, V) when is_atom(K) ->
    format_property(atom_to_binary(K, utf8), V);
format_property(K, V) when is_atom(V) ->
    format_property(K, atom_to_binary(V, utf8));
format_property(K, V) when is_tuple(V) ->
    % 将元组转换为二进制列表表示
    format_property(K, list_to_binary(io_lib:format("~p", [V])));
format_property(K, V) when is_list(V) ->
    % 将列表转换为二进制
    case io_lib:printable_list(V) of
        true ->
            format_property(K, list_to_binary(V));
        false ->
            % 非可打印列表，转换为二进制表示
            format_property(K, list_to_binary(io_lib:format("~p", [V])))
    end;
format_property(_K, V) ->
    % 其他类型保持不变
    V.

%% 解析大小字符串，如"64KB"
parse_size(SizeStr) when is_binary(SizeStr) ->
    case re:run(SizeStr, "^(\\d+)([KMGT]?B)$", [{capture, all_but_first, list}]) of
        {match, [ValueStr, Unit]} ->
            Value = list_to_integer(ValueStr),
            case Unit of
                "B"  -> Value;
                "KB" -> Value * 1024;
                "MB" -> Value * 1024 * 1024;
                "GB" -> Value * 1024 * 1024 * 1024;
                "TB" -> Value * 1024 * 1024 * 1024 * 1024
            end;
        _ ->
            % 默认64KB
            64 * 1024
    end;
parse_size(Size) when is_integer(Size) ->
    Size;
parse_size(_) ->
    % 默认64KB
    64 * 1024.

%% 处理JSON字符串 - 保留原始格式
try_parse_json(Payload) when is_binary(Payload) ->
    % 只记录日志，不解析JSON
    ?SLOG(debug, #{
        msg => "keeping_original_message_payload",
        payload_size => byte_size(Payload),
        payload_sample => case byte_size(Payload) > 100 of
            true -> binary:part(Payload, 0, 100);
            false -> Payload
        end
    }),
    % 直接返回原始payload
    Payload;
try_parse_json(Payload) ->
    % 非二进制数据，保持原样
    Payload.

%% @doc 集成到协调器
integrate() ->
    ?SLOG(info, #{msg => "integrating_message_module"}),
    % 将此模块注册到协调器
    case erlang:function_exported(emqx_plugin_mongodb_coordinator, register_module, 2) of
        true ->
            emqx_plugin_mongodb_coordinator:register_module(?MODULE, #{
                priority => high,
                description => <<"Message persistence module">>,
                features => [message_persistence, qos_guarantee]
            });
        false ->
            ok
    end.

%% 查找客户端的未确认消息
find_unacked_messages(ClientId) ->
    try
        % 获取集合名称
        MessageCollection = get_message_collection(),

        % 查询客户端的未确认消息
        Filter = #{<<"client_id">> => ClientId},

        % 执行查询
        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                               {find, MessageCollection, Filter, #{}, 0, 1000}) of
            {ok, Messages} ->
                ?SLOG(debug, #{msg => "found_unacked_messages", client_id => ClientId, count => length(Messages)}),
                {ok, Messages};
            {async_return, ok} ->
                ?SLOG(debug, #{msg => "found_unacked_messages_async", client_id => ClientId}),
                {ok, []};
            {async_return, {ok, Messages}} ->
                ?SLOG(debug, #{msg => "found_unacked_messages_async", client_id => ClientId, count => length(Messages)}),
                {ok, Messages};
            {async_return, {error, Reason}} ->
                ?SLOG(error, #{msg => "failed_to_find_unacked_messages_async", client_id => ClientId, reason => Reason}),
                {error, Reason};
            {error, Reason} ->
                ?SLOG(error, #{msg => "failed_to_find_unacked_messages", client_id => ClientId, reason => Reason}),
                {error, Reason}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_finding_unacked_messages",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%% 重发客户端的未确认消息
resend_unacked_messages(ClientId) ->
    % 默认重发策略：立即重发，最多重试3次
    resend_unacked_messages(ClientId, #{
        max_retries => 3,
        retry_interval => 1000,  % 1秒
        batch_size => 50
    }).

%% 重发客户端的未确认消息，带配置选项
resend_unacked_messages(ClientId, Options) ->
    try
        % 获取未确认消息
        case find_unacked_messages(ClientId) of
            {ok, []} ->
                ?SLOG(debug, #{msg => "no_unacked_messages_to_resend", client_id => ClientId}),
                {ok, 0};
            {ok, Messages} ->
                % 获取配置选项
                MaxRetries = maps:get(max_retries, Options, 3),
                RetryInterval = maps:get(retry_interval, Options, 1000),
                BatchSize = maps:get(batch_size, Options, 50),

                % 分批处理
                BatchedMessages = batch_messages(Messages, BatchSize),

                % 处理每一批
                ResendCount = lists:foldl(
                    fun(Batch, Count) ->
                        Count + resend_batch(ClientId, Batch, MaxRetries, RetryInterval)
                    end, 0, BatchedMessages),

                ?SLOG(info, #{
                    msg => "unacked_messages_resent",
                    client_id => ClientId,
                    total => length(Messages),
                    resent => ResendCount
                }),
                {ok, ResendCount}
            ;
            {error, Reason} ->
                ?SLOG(error, #{msg => "failed_to_resend_unacked_messages", client_id => ClientId, reason => Reason}),
                {error, Reason}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_resending_unacked_messages",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%% @doc 处理系统重启后的未确认消息
%% 修复：采用方案A - 纯持久化方案
%%
%% 功能说明：
%% 1. 不直接重新发布消息，避免与EMQX内置功能冲突
%% 2. 记录未确认消息的信息供管理员参考
%% 3. 让EMQX的重连和重传机制自然处理这些消息
%%
%% 设计原理：
%% - 插件只负责持久化，不干扰EMQX的消息处理逻辑
%% - EMQX的内置QoS机制会在客户端重连时自动处理未确认消息
%% - 避免重复投递和与内置功能的冲突
%%
%% 修复说明：
%% - 原方案：插件直接重新发布消息 → 可能与EMQX内置功能冲突
%% - 新方案：插件只记录信息 → 让EMQX内置功能处理
%%
%% Java等价概念：
%% @PostConstruct
%% public void handleUnackedMessagesAfterRestart() {
%%     List<UnackedMessage> unackedMessages = messageRepository.findAllUnacked();
%%
%%     // 不直接重新发布，而是记录信息供管理员参考
%%     for (UnackedMessage msg : unackedMessages) {
%%         logger.warn("Found unacked message after restart: client={}, topic={}",
%%                    msg.getClientId(), msg.getTopic());
%%     }
%%
%%     // 让MQTT broker的重连机制自然处理这些消息
%% }
handle_unacked_messages_after_restart() ->
    %% 检查是否为全新安装，如果是则跳过数据恢复
    case emqx_plugin_mongodb_api:is_fresh_installation() of
        true ->
            ?SLOG(info, #{
                msg => "fresh_installation_detected_skipping_message_restoration",
                reason => "no_existing_message_data_to_restore"
            }),
            ok;
        false ->
            ?SLOG(info, #{
                msg => "restoring_unacked_messages_after_emqx_restart",
                approach => "restore_to_emqx_message_queue_per_mqtt_protocol"
            }),
            try
        %% 获取消息集合
        MessageCollection = get_message_collection(),

        %% 修复：查询需要恢复的未确认消息
        %% 只查询QoS 1/2的未确认消息且未过期的消息
        Now = erlang:system_time(millisecond),
        Filter = #{
            <<"qos">> => #{<<"$in">> => [1, 2]},  % 只恢复QoS 1/2消息
            <<"acked">> => false,  % 只恢复未确认消息
            <<"$or">> => [
                #{<<"expiry_time">> => #{<<"$gt">> => Now}}, %% 未过期
                #{<<"expiry_time">> => 0}                     %% 永不过期
            ]
        },

        ?SLOG(info, #{
            msg => "querying_unacked_messages_for_restoration",
            filter => Filter,
            current_time => Now
        }),

        %% 查询所有未确认的消息
        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                               {find, MessageCollection, Filter, #{}, 0, 10000}) of
            {ok, Messages} when is_list(Messages) ->
                MessageCount = length(Messages),
                ?SLOG(info, #{
                    msg => "found_unacked_messages_after_restart",
                    message_count => MessageCount
                }),

                case MessageCount of
                    0 ->
                        ?SLOG(info, #{
                            msg => "no_unacked_messages_found_after_restart"
                        });
                    _ ->
                        ?SLOG(info, #{
                            msg => "found_unacked_messages_restoring_to_emqx",
                            message_count => MessageCount,
                            approach => "restore_to_emqx_message_queue"
                        }),

                        %% 修复：实际恢复消息到EMQX消息队列
                        restore_unacked_messages_to_emqx(Messages)
                end,
                ok;
            {ok, []} ->
                ?SLOG(info, #{msg => "no_unacked_messages_found_for_restoration"}),
                ok;
            {async_return, ok} ->
                ?SLOG(debug, #{msg => "unacked_messages_query_async_ok"}),
                ok;
            {async_return, {ok, Messages}} when is_list(Messages) ->
                MessageCount = length(Messages),
                ?SLOG(info, #{
                    msg => "found_unacked_messages_after_restart_async",
                    message_count => MessageCount
                }),
                case MessageCount of
                    0 -> ok;
                    _ ->
                        restore_unacked_messages_to_emqx(Messages),
                        ok
                end;
            {async_return, {error, Reason}} ->
                ?SLOG(error, #{
                    msg => "error_querying_unacked_messages_for_restoration_async",
                    reason => Reason
                }),
                {error, Reason};
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "error_querying_unacked_messages_for_restoration",
                    reason => Reason
                }),
                {error, Reason}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_during_unacked_messages_restoration",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end
    end.

%% @doc 恢复未确认消息到EMQX
%% 修复：按照MQTT协议正确恢复未确认消息
%%
%% 功能说明：
%% 1. 将MongoDB中的未确认消息恢复到EMQX消息队列
%% 2. 只恢复QoS 1/2的未确认消息
%% 3. 设置DUP标志，符合MQTT协议要求
%% 4. 处理QoS 2消息的PUBREC/PUBREL状态
%%
%% MQTT协议要求：
%% - QoS 1: 至少投递一次，需要PUBACK确认
%% - QoS 2: 恰好投递一次，需要完整握手
%% - 重传消息必须设置DUP=1
restore_unacked_messages_to_emqx(Messages) ->
    ?SLOG(info, #{
        msg => "starting_unacked_message_restoration_to_emqx",
        message_count => length(Messages),
        approach => "mqtt_protocol_compliant_restoration"
    }),

    %% 按客户端ID分组消息
    ClientMessages = group_messages_by_client(Messages),

    %% 异步批量恢复消息，避免阻塞
    spawn(fun() ->
        try
            %% 批量处理消息恢复
            MaxConcurrent = 10, % 最多同时恢复10个客户端的消息
            restore_messages_in_batches(ClientMessages, MaxConcurrent),

            ?SLOG(info, #{
                msg => "unacked_message_restoration_completed",
                total_clients => maps:size(ClientMessages),
                total_messages => length(Messages)
            })
        catch
            E:R:S ->
                ?SLOG(error, #{
                    msg => "error_in_unacked_message_restoration",
                    error => E,
                    reason => R,
                    stacktrace => S
                })
        end
    end),
    ok.

%% @doc 批量恢复消息到EMQX
%% 修复：使用正确的批量处理策略
restore_messages_in_batches(ClientMessages, MaxConcurrent) ->
    ClientList = maps:to_list(ClientMessages),
    restore_messages_batch_loop(ClientList, MaxConcurrent).

restore_messages_batch_loop([], _MaxConcurrent) ->
    ?SLOG(info, #{msg => "all_client_messages_restoration_completed"}),
    ok;
restore_messages_batch_loop(ClientList, MaxConcurrent) ->
    {Batch, Rest} = case length(ClientList) =< MaxConcurrent of
        true -> {ClientList, []};
        false -> lists:split(MaxConcurrent, ClientList)
    end,

    ?SLOG(info, #{
        msg => "processing_message_restoration_batch",
        batch_size => length(Batch),
        remaining => length(Rest)
    }),

    %% 并发恢复这批客户端的消息
    Tasks = [spawn_monitor(fun() ->
        restore_client_messages_to_emqx(ClientId, Messages)
    end) || {ClientId, Messages} <- Batch],

    %% 等待所有任务完成
    wait_for_message_restoration_tasks(Tasks),

    %% 处理剩余客户端
    case Rest of
        [] -> ok;
        _ ->
            timer:sleep(1000), % 批次间间隔
            restore_messages_batch_loop(Rest, MaxConcurrent)
    end.

%% @doc 等待消息恢复任务完成
wait_for_message_restoration_tasks([]) ->
    ok;
wait_for_message_restoration_tasks([{Pid, Ref} | Rest]) ->
    receive
        {'DOWN', Ref, process, Pid, normal} ->
            wait_for_message_restoration_tasks(Rest);
        {'DOWN', Ref, process, Pid, Reason} ->
            ?SLOG(warning, #{
                msg => "message_restoration_task_failed",
                pid => Pid,
                reason => Reason
            }),
            wait_for_message_restoration_tasks(Rest)
    after 30000 -> % 30秒超时
        ?SLOG(error, #{
            msg => "message_restoration_task_timeout",
            pid => Pid
        }),
        exit(Pid, kill),
        wait_for_message_restoration_tasks(Rest)
    end.

%% @doc 恢复单个客户端的消息到EMQX
%% 修复：使用EMQX标准API恢复消息到消息队列
restore_client_messages_to_emqx(ClientId, Messages) ->
    try
        ?SLOG(info, #{
            msg => "restoring_client_messages_to_emqx",
            client_id => ClientId,
            message_count => length(Messages)
        }),

        %% 过滤和验证消息
        ValidMessages = lists:filter(fun(Msg) ->
            is_message_valid_for_restoration(Msg)
        end, Messages),

        case ValidMessages of
            [] ->
                ?SLOG(info, #{
                    msg => "no_valid_messages_to_restore",
                    client_id => ClientId
                }),
                {ok, 0};
            _ ->
                %% 恢复消息到EMQX消息队列
                case restore_messages_to_emqx_queue(ClientId, ValidMessages) of
                    ok ->
                        {ok, length(ValidMessages)};
                    {error, Reason} ->
                        {error, Reason};
                    _ ->
                        {ok, length(ValidMessages)}
                end
        end

    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_restoring_client_messages_to_emqx",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%% @doc 检查消息是否有效且可以恢复
is_message_valid_for_restoration(Message) ->
    try
        %% 检查必要字段
        Topic = maps:get(<<"topic">>, Message, undefined),
        ClientId = maps:get(<<"client_id">>, Message, undefined),
        QoS = maps:get(<<"qos">>, Message, 0),
        Acked = maps:get(<<"acked">>, Message, true),

        case {Topic, ClientId, QoS, Acked} of
            {undefined, _, _, _} ->
                ?SLOG(warning, #{
                    msg => "message_missing_topic",
                    message => Message
                }),
                false;
            {_, undefined, _, _} ->
                ?SLOG(warning, #{
                    msg => "message_missing_client_id",
                    message => Message
                }),
                false;
            {_, _, QoS, _} when QoS < 1 ->
                %% QoS 0消息不需要恢复
                ?SLOG(debug, #{
                    msg => "qos0_message_skipped",
                    client_id => ClientId,
                    topic => Topic
                }),
                false;
            {_, _, _, true} ->
                %% 已确认的消息不需要恢复
                ?SLOG(debug, #{
                    msg => "acked_message_skipped",
                    client_id => ClientId,
                    topic => Topic
                }),
                false;
            {_, _, QoS, false} when QoS >= 1 ->
                %% 检查消息是否已过期
                case is_message_expired(Message) of
                    true ->
                        ?SLOG(info, #{
                            msg => "message_expired_skipping",
                            client_id => ClientId,
                            topic => Topic
                        }),
                        false;
                    false ->
                        true
                end
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_validating_message",
                message => Message,
                error => E,
                reason => R,
                stacktrace => S
            }),
            false
    end.

%% @doc 检查消息是否已过期
is_message_expired(Message) ->
    ExpiryTime = maps:get(<<"expiry_time">>, Message, 0),
    case ExpiryTime of
        0 -> false; % 永不过期
        _ ->
            Now = erlang:system_time(millisecond),
            ExpiryTime =< Now
    end.

%% @doc 恢复消息到EMQX消息队列
%% 修复：使用EMQX标准API恢复消息状态
restore_messages_to_emqx_queue(ClientId, Messages) ->
    try
        ?SLOG(info, #{
            msg => "restoring_messages_to_emqx_queue",
            client_id => ClientId,
            message_count => length(Messages),
            approach => "use_emqx_message_queue_api"
        }),

        %% 分别处理QoS 1和QoS 2消息
        {QoS1Messages, QoS2Messages} = lists:partition(fun(Msg) ->
            maps:get(<<"qos">>, Msg, 0) =:= 1
        end, Messages),

        %% 恢复QoS 1消息
        QoS1Results = restore_qos1_messages_to_emqx(ClientId, QoS1Messages),

        %% 恢复QoS 2消息
        QoS2Results = restore_qos2_messages_to_emqx(ClientId, QoS2Messages),

        %% 统计结果
        TotalSuccess = QoS1Results + QoS2Results,
        TotalFailed = length(Messages) - TotalSuccess,

        ?SLOG(info, #{
            msg => "messages_restored_to_emqx_queue",
            client_id => ClientId,
            total_messages => length(Messages),
            qos1_messages => length(QoS1Messages),
            qos2_messages => length(QoS2Messages),
            success_count => TotalSuccess,
            failed_count => TotalFailed
        }),

        %% 注释掉更新恢复状态的操作，避免修改已存在的消息数据
        %% update_messages_restoration_status(ClientId, <<"restored">>),
        ?SLOG(debug, #{
            msg => "skipping_restoration_status_update",
            client_id => ClientId,
            reason => "avoid_modifying_existing_message_data"
        }),

        TotalSuccess

    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_restoring_messages_to_emqx_queue",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S
            }),
            %% 更新失败状态
            update_messages_restoration_status(ClientId, <<"failed">>),
            0
    end.

%% @doc 恢复QoS 1消息到EMQX
restore_qos1_messages_to_emqx(ClientId, QoS1Messages) ->
    try
        SuccessCount = lists:foldl(fun(Msg, Acc) ->
            case restore_single_qos1_message_to_emqx(ClientId, Msg) of
                ok -> Acc + 1;
                {error, _} -> Acc
            end
        end, 0, QoS1Messages),

        ?SLOG(info, #{
            msg => "qos1_messages_restoration_completed",
            client_id => ClientId,
            total_count => length(QoS1Messages),
            success_count => SuccessCount,
            failed_count => length(QoS1Messages) - SuccessCount
        }),

        SuccessCount
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_restoring_qos1_messages",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S
            }),
            0
    end.

%% @doc 恢复单个QoS 1消息到EMQX
restore_single_qos1_message_to_emqx(ClientId, MessageDoc) ->
    try
        %% 获取消息ID用于去重检查
        MessageId = maps:get(<<"message_id">>, MessageDoc, <<>>),

        %% 检查消息是否已经处理过（去重检查）
        case emqx_plugin_mongodb_message_dedup:is_duplicate(MessageId) of
            true ->
                %% 消息已处理过，跳过恢复
                ?SLOG(debug, #{
                    msg => "qos1_message_already_processed_skipping",
                    client_id => ClientId,
                    message_id => MessageId,
                    reason => "duplicate_message_detected"
                }),
                {error, duplicate_message};
            false ->
                %% 消息未处理过，继续恢复流程
                %% 从MongoDB文档重建EMQX消息
                case rebuild_emqx_message_from_doc(MessageDoc) of
                    {ok, Message} ->
                        %% 设置DUP标志（MQTT协议要求重传消息设置DUP=1）
                        MessageWithDup = emqx_message:set_flag(dup, true, Message),

                        %% 添加恢复标识，防止重复持久化
                        MessageWithHeaders = emqx_message:set_header(restart_recovery, true,
                            emqx_message:set_header(skip_persistence, true, MessageWithDup)),

                        %% 获取消息信息
                        Topic = maps:get(<<"topic">>, MessageDoc, <<>>),

                        ?SLOG(debug, #{
                            msg => "restoring_qos1_message_to_emqx_queue",
                            client_id => ClientId,
                            topic => Topic,
                            message_id => MessageId,
                            dup_flag => true
                        }),

                        %% 使用EMQX消息队列API恢复消息
                        %% 这里使用emqx:publish来重新投递消息到消息队列
                        case emqx:publish(MessageWithHeaders) of
                            ok ->
                                %% 消息发布成功，标记为已处理
                                emqx_plugin_mongodb_message_dedup:mark_processed(MessageId),
                                ?SLOG(debug, #{
                                    msg => "qos1_message_restored_successfully",
                                    client_id => ClientId,
                                    topic => Topic,
                                    message_id => MessageId
                                }),
                                ok;
                            {error, Reason} ->
                                %% 发布失败，不标记为已处理，允许重试
                                ?SLOG(error, #{
                                    msg => "failed_to_restore_qos1_message",
                                    client_id => ClientId,
                                    topic => Topic,
                                    message_id => MessageId,
                                    reason => Reason
                                }),
                                {error, Reason}
                        end;
                    {error, Reason} ->
                        ?SLOG(warning, #{
                            msg => "failed_to_rebuild_qos1_message_from_doc",
                            client_id => ClientId,
                            message_id => MessageId,
                            reason => Reason
                        }),
                        {error, Reason}
                end
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_restoring_single_qos1_message",
                client_id => ClientId,
                message_id => maps:get(<<"message_id">>, MessageDoc, <<>>),
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%% @doc 恢复QoS 2消息到EMQX
restore_qos2_messages_to_emqx(ClientId, QoS2Messages) ->
    try
        SuccessCount = lists:foldl(fun(Msg, Acc) ->
            case restore_single_qos2_message_to_emqx(ClientId, Msg) of
                ok -> Acc + 1;
                {error, _} -> Acc
            end
        end, 0, QoS2Messages),

        ?SLOG(info, #{
            msg => "qos2_messages_restoration_completed",
            client_id => ClientId,
            total_count => length(QoS2Messages),
            success_count => SuccessCount,
            failed_count => length(QoS2Messages) - SuccessCount
        }),

        SuccessCount
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_restoring_qos2_messages",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S
            }),
            0
    end.

%% @doc 恢复单个QoS 2消息到EMQX
restore_single_qos2_message_to_emqx(ClientId, MessageDoc) ->
    try
        %% 获取消息ID用于去重检查
        MessageId = maps:get(<<"message_id">>, MessageDoc, <<>>),

        %% 检查消息是否已经处理过（去重检查）
        case emqx_plugin_mongodb_message_dedup:is_duplicate(MessageId) of
            true ->
                %% 消息已处理过，跳过恢复
                ?SLOG(debug, #{
                    msg => "qos2_message_already_processed_skipping",
                    client_id => ClientId,
                    message_id => MessageId,
                    reason => "duplicate_message_detected"
                }),
                {error, duplicate_message};
            false ->
                %% 消息未处理过，继续恢复流程
                %% 从MongoDB文档重建EMQX消息
                case rebuild_emqx_message_from_doc(MessageDoc) of
                    {ok, Message} ->
                        %% 设置DUP标志（MQTT协议要求重传消息设置DUP=1）
                        MessageWithDup = emqx_message:set_flag(dup, true, Message),

                        %% 添加恢复标识，防止重复持久化
                        MessageWithHeaders = emqx_message:set_header(restart_recovery, true,
                            emqx_message:set_header(skip_persistence, true, MessageWithDup)),

                        %% 获取消息信息和QoS 2状态
                        Topic = maps:get(<<"topic">>, MessageDoc, <<>>),
                        QoS2State = maps:get(<<"qos2_state">>, MessageDoc, <<"publish">>),

                        ?SLOG(debug, #{
                            msg => "restoring_qos2_message_to_emqx_queue",
                            client_id => ClientId,
                            topic => Topic,
                            message_id => MessageId,
                            qos2_state => QoS2State,
                            dup_flag => true
                        }),

                %% 根据QoS 2状态恢复消息
                case QoS2State of
                    <<"publish">> ->
                        %% 重新发布消息，等待PUBREC
                        restore_qos2_publish_state(ClientId, MessageWithHeaders, MessageDoc);
                    <<"pubrec">> ->
                        %% 重新发送PUBREL，等待PUBCOMP
                        restore_qos2_pubrec_state(ClientId, MessageDoc);
                    _ ->
                        ?SLOG(warning, #{
                            msg => "unknown_qos2_state_defaulting_to_publish",
                            client_id => ClientId,
                            message_id => MessageId,
                            qos2_state => QoS2State
                        }),
                        restore_qos2_publish_state(ClientId, MessageWithHeaders, MessageDoc)
                        end;
                    {error, Reason} ->
                        ?SLOG(warning, #{
                            msg => "failed_to_rebuild_qos2_message_from_doc",
                            client_id => ClientId,
                            message_id => MessageId,
                            reason => Reason
                        }),
                        {error, Reason}
                end
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_restoring_single_qos2_message",
                client_id => ClientId,
                message_id => maps:get(<<"message_id">>, MessageDoc, <<>>),
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%% @doc 恢复QoS 2消息的PUBLISH状态
restore_qos2_publish_state(ClientId, Message, MessageDoc) ->
    try
        Topic = maps:get(<<"topic">>, MessageDoc, <<>>),
        MessageId = maps:get(<<"message_id">>, MessageDoc, <<>>),

        %% 使用EMQX消息队列API重新发布消息
        case emqx:publish(Message) of
            ok ->
                %% 消息发布成功，标记为已处理
                emqx_plugin_mongodb_message_dedup:mark_processed(MessageId),
                ?SLOG(debug, #{
                    msg => "qos2_message_publish_state_restored",
                    client_id => ClientId,
                    topic => Topic,
                    message_id => MessageId,
                    next_state => "waiting_for_pubrec"
                }),
                ok;
            {error, Reason} ->
                %% 发布失败，不标记为已处理，允许重试
                ?SLOG(error, #{
                    msg => "failed_to_restore_qos2_publish_state",
                    client_id => ClientId,
                    topic => Topic,
                    message_id => MessageId,
                    reason => Reason
                }),
                {error, Reason}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_restoring_qos2_publish_state",
                client_id => ClientId,
                message_id => maps:get(<<"message_id">>, MessageDoc, <<>>),
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%% @doc 恢复QoS 2消息的PUBREC状态
restore_qos2_pubrec_state(ClientId, MessageDoc) ->
    try
        Topic = maps:get(<<"topic">>, MessageDoc, <<>>),
        MessageId = maps:get(<<"message_id">>, MessageDoc, <<>>),

        %% QoS 2消息已经被接收，标记为已处理
        emqx_plugin_mongodb_message_dedup:mark_processed(MessageId),

        ?SLOG(info, #{
            msg => "qos2_message_pubrec_state_restored",
            client_id => ClientId,
            topic => Topic,
            message_id => MessageId,
            note => "message_already_received_waiting_for_pubcomp"
        }),

        %% QoS 2消息已经被接收，等待PUBCOMP
        %% 这种状态下不需要重新发布消息，只需要等待客户端重连后完成握手
        ok
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_restoring_qos2_pubrec_state",
                client_id => ClientId,
                message_id => maps:get(<<"message_id">>, MessageDoc, <<>>),
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%% @doc 恢复指定客户端的未确认消息
%% 修复：实现缺失的restore_client_messages/1函数
%%
%% 功能说明：
%% 1. 查询指定客户端的所有未确认消息
%% 2. 只恢复QoS 1/2的未确认消息
%% 3. 使用EMQX标准API恢复消息到消息队列
%% 4. 处理消息过期和验证
%%
%% 这个函数被会话持久化模块调用，用于恢复会话相关的未确认消息
restore_client_messages(ClientId) ->
    ?SLOG(info, #{
        msg => "restoring_client_messages",
        client_id => ClientId,
        approach => "query_and_restore_unacked_messages"
    }),
    try
        %% 获取集合名称
        MessageCollection = get_message_collection(),
        Now = erlang:system_time(millisecond),

        %% 查询该客户端的未确认消息
        Filter = #{
            <<"client_id">> => ClientId,
            <<"qos">> => #{<<"$in">> => [1, 2]},  % 只恢复QoS 1/2消息
            <<"acked">> => false,  % 只恢复未确认消息
            <<"$or">> => [
                #{<<"expiry_time">> => #{<<"$gt">> => Now}},  % 未过期的消息
                #{<<"expiry_time">> => 0}  % 永不过期的消息
            ]
        },

        ?SLOG(debug, #{
            msg => "querying_client_unacked_messages",
            client_id => ClientId,
            filter => Filter,
            current_time => Now
        }),

        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                               {find, MessageCollection, Filter, #{}, 0, 1000}) of
            {ok, Messages} when is_list(Messages) ->
                MessageCount = length(Messages),
                case MessageCount of
                    0 ->
                        ?SLOG(info, #{
                            msg => "no_unacked_messages_found_for_client",
                            client_id => ClientId
                        }),
                        {ok, 0};
                    _ ->
                        ?SLOG(info, #{
                            msg => "found_unacked_messages_for_client",
                            client_id => ClientId,
                            message_count => MessageCount
                        }),

                        %% 恢复消息到EMQX消息队列
                        restore_client_messages_to_emqx(ClientId, Messages)
                end;
            {async_return, ok} ->
                ?SLOG(debug, #{msg => "found_unacked_messages_async_ok", client_id => ClientId}),
                {ok, 0};
            {async_return, {ok, Messages}} when is_list(Messages) ->
                MessageCount = length(Messages),
                ?SLOG(info, #{
                    msg => "found_unacked_messages_async",
                    client_id => ClientId,
                    message_count => MessageCount
                }),
                case MessageCount of
                    0 -> {ok, 0};
                    _ -> restore_client_messages_to_emqx(ClientId, Messages)
                end;
            {async_return, {error, Reason}} ->
                ?SLOG(error, #{
                    msg => "failed_to_query_client_unacked_messages_async",
                    client_id => ClientId,
                    reason => Reason
                }),
                {error, Reason};
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_query_client_unacked_messages",
                    client_id => ClientId,
                    reason => Reason
                }),
                {error, Reason}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_restoring_client_messages",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%% @doc 更新消息恢复状态
update_messages_restoration_status(ClientId, Status) ->
    try
        MessageCollection = get_message_collection(),
        UpdateDoc = #{
            <<"$set">> => #{
                <<"restoration_status">> => Status,
                <<"restored_at">> => erlang:system_time(millisecond),
                <<"updated_at">> => erlang:system_time(millisecond)
            }
        },

        case emqx_plugin_mongodb_api:update_many(MessageCollection,
                                                #{<<"client_id">> => ClientId},
                                                UpdateDoc) of
            {ok, _} ->
                ?SLOG(debug, #{
                    msg => "messages_restoration_status_updated",
                    client_id => ClientId,
                    status => Status
                });
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_update_messages_restoration_status",
                    client_id => ClientId,
                    status => Status,
                    reason => Reason
                })
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_updating_messages_restoration_status",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% @doc 删除客户端的所有消息
delete_client_messages(ClientId) ->
    try
        ?SLOG(info, #{msg => "deleting_client_messages", client_id => ClientId}),

        Collection = get_message_collection(),
        Filter = #{<<"client_id">> => ClientId},

        case emqx_plugin_mongodb_api:delete_many(Collection, Filter) of
            {ok, Result} ->
                DeletedCount = maps:get(<<"deletedCount">>, Result, 0),
                ?SLOG(info, #{
                    msg => "client_messages_deleted",
                    client_id => ClientId,
                    deleted_count => DeletedCount
                }),
                ok;
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_delete_client_messages",
                    client_id => ClientId,
                    reason => Reason
                }),
                {error, Reason}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_deleting_client_messages",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, R}
    end.

%% @doc 记录客户端未确认消息信息
%% 修复：采用方案A - 纯持久化方案
%%
%% 功能说明：
%% 1. 记录特定客户端的未确认消息详细信息
%% 2. 不重新发布消息，避免与EMQX内置功能冲突
%% 3. 供管理员参考和故障排查使用
%%
%% 参数说明：
%% - ClientId: 客户端ID
%% - Messages: 该客户端的未确认消息列表
%%
%% Java等价概念：
%% public void logClientUnackedMessagesInfo(String clientId, List<UnackedMessage> messages) {
%%     logger.warn("Client {} has {} unacked messages after restart", clientId, messages.size());
%%     for (UnackedMessage msg : messages) {
%%         logger.warn("  - Message: topic={}, qos={}, created_at={}",
%%                    msg.getTopic(), msg.getQos(), msg.getCreatedAt());
%%     }
%% }
log_client_unacked_messages_info(ClientId, Messages) ->
    MessageCount = length(Messages),
    ?SLOG(warning, #{
        msg => "client_has_unacked_messages_after_restart",
        client_id => ClientId,
        message_count => MessageCount,
        note => "messages_will_be_handled_by_emqx_reconnection_mechanism"
    }),

    %% 记录每个消息的详细信息（限制数量避免日志过多）
    MaxLogMessages = 10,
    MessagesToLog = case MessageCount > MaxLogMessages of
        true -> lists:sublist(Messages, MaxLogMessages);
        false -> Messages
    end,

    lists:foreach(fun(MessageDoc) ->
        try
            Topic = maps:get(<<"topic">>, MessageDoc, <<"unknown">>),
            QoS = maps:get(<<"qos">>, MessageDoc, 0),
            CreatedAt = maps:get(<<"created_at">>, MessageDoc, 0),
            PayloadSize = case maps:get(<<"payload">>, MessageDoc, <<>>) of
                Payload when is_binary(Payload) -> byte_size(Payload);
                _ -> 0
            end,

            ?SLOG(warning, #{
                msg => "unacked_message_details_after_restart",
                client_id => ClientId,
                topic => Topic,
                qos => QoS,
                payload_size => PayloadSize,
                created_at => CreatedAt,
                note => "will_be_handled_by_emqx_when_client_reconnects"
            })
        catch
            E:R:S ->
                ?SLOG(error, #{
                    msg => "error_logging_unacked_message_details",
                    client_id => ClientId,
                    error => E,
                    reason => R,
                    stacktrace => S,
                    message_doc => MessageDoc
                })
        end
    end, MessagesToLog),

    %% 如果消息数量超过限制，记录省略信息
    case MessageCount > MaxLogMessages of
        true ->
            ?SLOG(warning, #{
                msg => "additional_unacked_messages_not_logged",
                client_id => ClientId,
                logged_count => MaxLogMessages,
                total_count => MessageCount,
                omitted_count => MessageCount - MaxLogMessages
            });
        false ->
            ok
    end.

%% 将消息列表分批
batch_messages(Messages, BatchSize) ->
    batch_messages(Messages, BatchSize, []).

batch_messages([], _BatchSize, Batches) ->
    lists:reverse(Batches);
batch_messages(Messages, BatchSize, Batches) ->
    {Batch, Rest} = case length(Messages) =< BatchSize of
        true -> {Messages, []};
        false -> lists:split(BatchSize, Messages)
    end,
    batch_messages(Rest, BatchSize, [Batch | Batches]).

%% 重发一批消息
resend_batch(ClientId, Batch, MaxRetries, RetryInterval) ->
    % 检查客户端是否在线
    case emqx_cm:lookup_channels(ClientId) of
        [] ->
            ?SLOG(debug, #{msg => "client_offline_skipping_resend", client_id => ClientId}),
            0;
        _ ->
            % 客户端在线，重发消息
            lists:foldl(
                fun(Msg, Count) ->
                    case resend_message(ClientId, Msg, MaxRetries, RetryInterval) of
                        ok -> Count + 1;
                        _ -> Count
                    end
                end, 0, Batch)
    end.

%% 重发单条消息
resend_message(ClientId, MessageDoc, MaxRetries, RetryInterval) ->
    try
        % 提取消息信息
        Topic = maps:get(<<"topic">>, MessageDoc, <<>>),
        QoS = maps:get(<<"qos">>, MessageDoc, 0),
        Payload = maps:get(<<"payload">>, MessageDoc, <<>>),
        IsCompressed = maps:get(<<"is_compressed">>, MessageDoc, false),

        % 解压缩payload（如果需要）
        DecompressedPayload = case IsCompressed of
            true -> zlib:uncompress(Payload);
            false -> Payload
        end,

        % 构建MQTT消息
        Message = emqx_message:make(
            ClientId,
            QoS,
            Topic,
            DecompressedPayload
        ),

        % 添加消息属性（如果有）
        MessageWithProps = case maps:get(<<"properties">>, MessageDoc, undefined) of
            undefined -> Message;
            Props when is_map(Props) ->
                % 转换属性
                ConvertedProps = maps:map(fun convert_property/2, Props),
                emqx_message:set_header(properties, ConvertedProps, Message)
        end,

        % 尝试重发消息
        resend_with_retry(ClientId, MessageWithProps, MaxRetries, RetryInterval)
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_resending_message",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%% 带重试的消息重发
resend_with_retry(_ClientId, _Message, 0, _Interval) ->
    {error, max_retries_reached};
resend_with_retry(ClientId, Message, Retries, Interval) ->
    try
        % 发布消息
        case emqx_broker:publish(Message) of
            ok ->
                ?SLOG(debug, #{
                    msg => "message_resent_successfully",
                    client_id => ClientId,
                    topic => Message#message.topic
                }),
                ok;
            [] ->
                % 空列表表示没有订阅者，但消息已发布
                ?SLOG(debug, #{
                    msg => "message_resent_no_subscribers",
                    client_id => ClientId,
                    topic => Message#message.topic
                }),
                ok;
            Results when is_list(Results) ->
                % 发布结果列表，表示消息已发布到订阅者
                ?SLOG(debug, #{
                    msg => "message_resent_with_results",
                    client_id => ClientId,
                    topic => Message#message.topic,
                    results_count => length(Results)
                }),
                ok;
            {error, Reason} ->
                ?SLOG(warning, #{
                    msg => "failed_to_resend_message",
                    client_id => ClientId,
                    topic => Message#message.topic,
                    reason => Reason,
                    retries_left => Retries - 1
                }),
                % 等待一段时间后重试
                timer:sleep(Interval),
                resend_with_retry(ClientId, Message, Retries - 1, Interval);
            Other ->
                % 处理其他可能的返回值
                ?SLOG(warning, #{
                    msg => "unexpected_resend_result",
                    client_id => ClientId,
                    topic => Message#message.topic,
                    result => Other
                }),
                % 假设消息已发布成功
                ok
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_in_message_resend",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S,
                retries_left => Retries - 1
            }),
            % 等待一段时间后重试
            timer:sleep(Interval),
            resend_with_retry(ClientId, Message, Retries - 1, Interval)
    end.

%% 转换消息属性
convert_property(_K, V) when is_binary(V) -> V;
convert_property(_K, V) when is_number(V) -> V;
convert_property(_K, V) when is_boolean(V) -> V;
convert_property(_K, V) when is_atom(V) -> V;
convert_property(_K, V) ->
    % 尝试解析复杂类型
    try
        binary_to_term(V)
    catch
        _:_ -> V
    end.

%% 客户端断开连接钩子回调
on_client_disconnected(ClientInfo, Reason, _Env) ->
    % 检查消息持久化是否启用
    Config = emqx_plugin_mongodb:read_config(),
    MessageConfig = maps:get(message_persistence, Config, #{}),

    case maps:get(enabled, MessageConfig, false) of
        false ->
            % 消息持久化未启用，只记录基本日志
            ?SLOG(debug, #{
                msg => "client_disconnected",
                client_id => maps:get(clientid, ClientInfo, undefined),
                reason => Reason
            });
        true ->
            % 记录客户端断开连接状态
            try
                ClientId = maps:get(clientid, ClientInfo, undefined),
                ?SLOG(debug, #{
                    msg => "client_disconnected",
                    client_id => ClientId,
                    reason => Reason
                }),

                % 获取配置
                Config = emqx_plugin_mongodb:read_config(),
                MessageConfig = maps:get(message_persistence, Config, #{}),

                % 检查是否需要标记客户端离线状态
                case maps:get(mark_offline_status, MessageConfig, true) of
                    true ->
                        % 获取集合名称
                        MessageCollection = get_message_collection(),

                        % 更新客户端的所有消息状态为"客户端离线"
                        Filter = #{<<"client_id">> => ClientId},
                        Update = #{
                            <<"$set">> => #{
                                <<"client_offline">> => true,
                                <<"disconnect_reason">> => format_reason(Reason),
                                <<"disconnected_at">> => erlang:system_time(millisecond)
                            }
                        },

                        % 执行更新
                        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                                               {update_many, MessageCollection, Filter, Update}) of
                            {ok, _} ->
                                ?SLOG(debug, #{msg => "marked_messages_as_client_offline", client_id => ClientId});
                            {error, UpdateReason} ->
                                ?SLOG(warning, #{
                                    msg => "failed_to_mark_messages_as_client_offline",
                                    client_id => ClientId,
                                    reason => UpdateReason
                                })
                        end;
                    false ->
                        ok
                end
            catch
                E:R:S ->
                    ?SLOG(error, #{
                        msg => "error_processing_client_disconnect",
                        error => E,
                        reason => R,
                        stacktrace => S
                    })
            end
    end,
    ok.

%% 格式化断开连接原因
format_reason(Reason) ->
    try
        case Reason of
            normal -> <<"normal">>;
            {shutdown, Reason2} when is_binary(Reason2) -> Reason2;
            {shutdown, Reason2} ->
                list_to_binary(io_lib:format("~p", [Reason2]));
            Reason when is_atom(Reason) ->
                atom_to_binary(Reason, utf8);
            Reason ->
                list_to_binary(io_lib:format("~p", [Reason]))
        end
    catch
        _:_ -> <<"unknown">>
    end.

%%--------------------------------------------------------------------
%% 消息统计和查询功能
%%--------------------------------------------------------------------

%% @doc 获取消息统计信息
get_message_stats() ->
    try
        MessageCollection = get_message_collection(),
        DataCollection = get_data_collection(),

        % 统计未确认消息数
        UnackedFilter = #{},
        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                               {count, MessageCollection, UnackedFilter}) of
            {ok, UnackedCount} ->
                % 统计历史消息数
                case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                                       {count, DataCollection, #{}}) of
                    {ok, TotalCount} ->
                        {ok, #{
                            unacked_messages => UnackedCount,
                            total_messages => TotalCount,
                            acked_messages => TotalCount - UnackedCount
                        }};
                    {error, Reason} ->
                        {error, {total_count_failed, Reason}}
                end;
            {error, Reason} ->
                {error, {unacked_count_failed, Reason}}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_getting_message_stats",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%% @doc 根据主题查找消息
find_messages_by_topic(Topic) ->
    try
        DataCollection = get_data_collection(),
        Filter = #{<<"topic">> => Topic},

        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                               {find, DataCollection, Filter, #{}, 0, 100}) of
            {ok, Messages} ->
                {ok, Messages};
            {async_return, {ok, Messages}} ->
                {ok, Messages};
            {async_return, {error, Reason}} ->
                {error, Reason};
            {error, Reason} ->
                {error, Reason}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_finding_messages_by_topic",
                error => E,
                reason => R,
                stacktrace => S,
                topic => Topic
            }),
            {error, {E, R}}
    end.

%% @doc 获取客户端消息数量
get_client_message_count(ClientId) ->
    try
        DataCollection = get_data_collection(),
        Filter = #{<<"clientid">> => ClientId},

        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                               {count, DataCollection, Filter}) of
            {ok, Count} ->
                {ok, Count};
            {error, Reason} ->
                {error, Reason}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_getting_client_message_count",
                error => E,
                reason => R,
                stacktrace => S,
                client_id => ClientId
            }),
            {error, {E, R}}
    end.

%% @doc 获取数据集合名称
get_data_collection() ->
    application:get_env(emqx_plugin_mongodb, data_collection, ?DEFAULT_DATA_COLLECTION).

%% @doc 生成集合名称缩写
generate_collection_abbreviation(Collection) when is_binary(Collection) ->
    % 将集合名称转换为缩写
    % emqx_mqtt_messages -> emm
    % emqx_messages -> em
    Parts = binary:split(Collection, <<"_">>, [global]),
    Abbreviation = lists:foldl(fun(Part, Acc) ->
        case Part of
            <<>> -> Acc;
            <<First:8, _/binary>> -> <<Acc/binary, First>>
        end
    end, <<>>, Parts),

    % 确保缩写不为空，至少有2个字符
    case byte_size(Abbreviation) of
        0 -> <<"idx">>;
        1 -> <<Abbreviation/binary, "x">>;
        _ -> Abbreviation
    end;
generate_collection_abbreviation(_) ->
    <<"idx">>.

%% @doc 处理消息索引创建错误
handle_message_index_error(Collection, IndexName, {E, R, S}) ->
    % 检查是否是索引已存在的错误
    case is_index_exists_error({error, R}) of
        true ->
            ?SLOG(info, #{
                msg => "message_index_already_exists",
                collection => Collection,
                index_name => IndexName
            });
        false ->
            ?SLOG(error, #{
                msg => "error_creating_message_index",
                collection => Collection,
                index_name => IndexName,
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% @doc 检查是否是索引已存在的错误
is_index_exists_error(Reason) ->
    case Reason of
        {error, {badmatch, {error, {error, {error, {op_msg_response, Response}}}}}} ->
            check_index_conflict_in_response(Response);
        {error, {error, {error, {op_msg_response, Response}}}} ->
            check_index_conflict_in_response(Response);
        {error, {error, {op_msg_response, Response}}} ->
            check_index_conflict_in_response(Response);
        {error, {op_msg_response, Response}} ->
            check_index_conflict_in_response(Response);
        _ ->
            false
    end.

%% @doc 检查MongoDB响应中是否包含索引冲突信息
check_index_conflict_in_response(Response) when is_map(Response) ->
    case maps:get(<<"codeName">>, Response, undefined) of
        <<"IndexOptionsConflict">> -> true;
        <<"IndexKeySpecsConflict">> -> true;
        _ ->
            case maps:get(<<"code">>, Response, undefined) of
                85 -> true;  % IndexOptionsConflict
                86 -> true;  % IndexKeySpecsConflict
                _ -> false
            end
    end;
check_index_conflict_in_response(_) ->
    false.

%% 确保值是MongoDB兼容的字符串类型
%% MongoDB会将Erlang binary转换为String，但查询时需要类型一致
ensure_binary_string(Value) when is_binary(Value) ->
    Value;
ensure_binary_string(Value) when is_list(Value) ->
    list_to_binary(Value);
ensure_binary_string(Value) when is_atom(Value) ->
    atom_to_binary(Value, utf8);
ensure_binary_string(Value) when is_integer(Value) ->
    integer_to_binary(Value);
ensure_binary_string(Value) when is_float(Value) ->
    float_to_binary(Value);
ensure_binary_string(Value) ->
    % 对于其他类型，尝试转换为字符串
    iolist_to_binary(io_lib:format("~p", [Value])).

%% 为了调试，添加一个函数来记录实际的查询条件
log_delete_query(ClientId, MessageId, Filter) ->
    ?SLOG(warning, #{
        msg => "delete_query_details",
        client_id => ClientId,
        message_id => MessageId,
        filter => Filter,
        client_id_type => type_of(ClientId),
        message_id_type => type_of(MessageId)
    }).

%% 获取值的类型信息
type_of(Value) when is_binary(Value) -> binary;
type_of(Value) when is_list(Value) -> list;
type_of(Value) when is_atom(Value) -> atom;
type_of(Value) when is_integer(Value) -> integer;
type_of(Value) when is_float(Value) -> float;
type_of(_) -> unknown.

%% 调试消息ID格式转换
debug_message_id_format(OriginalId, ConvertedId) ->
    ?SLOG(debug, #{
        msg => "message_id_format_debug",
        original_id => OriginalId,
        original_type => type_of(OriginalId),
        converted_id => ConvertedId,
        converted_type => type_of(ConvertedId),
        original_size => case is_binary(OriginalId) of
            true -> byte_size(OriginalId);
            false -> unknown
        end,
        converted_size => case is_binary(ConvertedId) of
            true -> byte_size(ConvertedId);
            false -> unknown
        end
    }).

%% 调试查询相关消息
debug_query_related_messages(Pid, Collection, ClientId, MessageId) ->
    try
        % 查询该客户端的所有消息
        ClientFilter = #{<<"client_id">> => ClientId},
        case emqx_plugin_mongodb_api:find(Pid, Collection, ClientFilter, #{}, #{limit => 10}) of
            {ok, ClientMessages} ->
                ?SLOG(warning, #{
                    msg => "debug_client_messages",
                    client_id => ClientId,
                    target_message_id => MessageId,
                    found_messages => length(ClientMessages),
                    message_ids => [maps:get(<<"message_id">>, Msg, <<"unknown">>) || Msg <- ClientMessages]
                });
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "debug_client_messages_failed",
                    client_id => ClientId,
                    reason => Reason
                })
        end,

        % 查询消息ID相似的消息
        MessageIdFilter = #{<<"message_id">> => #{<<"$regex">> => MessageId}},
        case emqx_plugin_mongodb_api:find(Pid, Collection, MessageIdFilter, #{}, #{limit => 5}) of
            {ok, SimilarMessages} ->
                % 详细分析找到的消息
                MessageDetails = lists:map(fun(Msg) ->
                    #{
                        message_id => maps:get(<<"message_id">>, Msg, <<"unknown">>),
                        client_id => maps:get(<<"client_id">>, Msg, <<"unknown">>),
                        client_id_type => type_of(maps:get(<<"client_id">>, Msg, <<"unknown">>)),
                        client_id_size => case maps:get(<<"client_id">>, Msg, <<"unknown">>) of
                            Val when is_binary(Val) -> byte_size(Val);
                            _ -> unknown
                        end
                    }
                end, SimilarMessages),

                ?SLOG(warning, #{
                    msg => "debug_similar_message_ids_detailed",
                    target_message_id => MessageId,
                    target_client_id => ClientId,
                    target_client_id_type => type_of(ClientId),
                    target_client_id_size => case is_binary(ClientId) of
                        true -> byte_size(ClientId);
                        false -> unknown
                    end,
                    found_messages => length(SimilarMessages),
                    message_details => MessageDetails
                });
            {error, Reason2} ->
                ?SLOG(error, #{
                    msg => "debug_similar_messages_failed",
                    target_message_id => MessageId,
                    reason => Reason2
                })
        end,

        % 查询最近的所有消息（不限制客户端ID）
        RecentFilter = #{},
        case emqx_plugin_mongodb_api:find(Pid, Collection, RecentFilter, #{}, #{limit => 10, sort => #{<<"timestamp">> => -1}}) of
            {ok, RecentMessages} ->
                RecentDetails = lists:map(fun(Msg) ->
                    #{
                        message_id => maps:get(<<"message_id">>, Msg, <<"unknown">>),
                        client_id => maps:get(<<"client_id">>, Msg, <<"unknown">>),
                        timestamp => maps:get(<<"timestamp">>, Msg, 0)
                    }
                end, RecentMessages),

                ?SLOG(warning, #{
                    msg => "debug_recent_all_messages",
                    target_client_id => ClientId,
                    target_message_id => MessageId,
                    found_messages => length(RecentMessages),
                    recent_messages => RecentDetails
                });
            {error, Reason3} ->
                ?SLOG(error, #{
                    msg => "debug_recent_messages_failed",
                    reason => Reason3
                })
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "debug_query_related_messages_exception",
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% 按客户端ID分组消息
%% @spec group_messages_by_client(Messages :: [map()]) -> [{binary(), [map()]}]
group_messages_by_client(Messages) ->
    GroupedMap = lists:foldl(fun(Message, Acc) ->
        ClientId = maps:get(<<"client_id">>, Message, <<>>),
        case maps:get(ClientId, Acc, undefined) of
            undefined ->
                maps:put(ClientId, [Message], Acc);
            ExistingMessages ->
                maps:put(ClientId, [Message | ExistingMessages], Acc)
        end
    end, #{}, Messages),

    % 转换为列表并反转消息顺序（保持原始顺序）
    lists:map(fun({ClientId, ClientMessages}) ->
        {ClientId, lists:reverse(ClientMessages)}
    end, maps:to_list(GroupedMap)).

%% @doc 为特定客户端恢复消息（兼容性函数）
%% 修复：这个函数现在是正确的消息恢复实现
%%
%% 功能说明：
%% - 批量处理消息恢复，提高效率
%% - 使用EMQX标准API恢复消息到消息队列
%% - 符合MQTT协议的消息恢复语义
restore_client_messages(ClientId, Messages) ->
    ?SLOG(info, #{
        msg => "restore_client_messages_batch_called",
        client_id => ClientId,
        message_count => length(Messages),
        approach => "mqtt_protocol_compliant_message_restoration"
    }),

    try
        % 检查客户端是否在线
        case emqx_cm:lookup_channels(ClientId) of
            [] ->
                % 客户端不在线，跳过恢复
                ?SLOG(debug, #{
                    msg => "client_offline_skipping_message_restoration",
                    client_id => ClientId
                }),
                ok;
            _Channels ->
                % 客户端在线，恢复消息
                RestoredCount = lists:foldl(fun(MessageDoc, Count) ->
                    case restore_single_message(ClientId, MessageDoc) of
                        ok -> Count + 1;
                        {error, _} -> Count
                    end
                end, 0, Messages),

                ?SLOG(info, #{
                    msg => "client_messages_restoration_completed",
                    client_id => ClientId,
                    total_messages => length(Messages),
                    restored_count => RestoredCount
                }),

                ok
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_restoring_client_messages",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%% @doc 恢复单个消息
%% 警告：这个函数在方案A中不应该被调用！
%%
%% 修复说明：
%% - 这个函数违反了方案A的设计原则（插件不应该直接重新发布消息）
%% - 保留此函数仅用于向后兼容，但会记录警告日志
%% - 在新的方案A中，应该让EMQX的重连机制处理未确认消息
%%
%% 如果这个函数被调用，说明代码中还有地方没有按照方案A修复
restore_single_message(ClientId, MessageDoc) ->
    %% 记录警告：这个函数不应该在方案A中被调用
    ?SLOG(warning, #{
        msg => "restore_single_message_called_violates_plan_a",
        client_id => ClientId,
        message_id => maps:get(<<"message_id">>, MessageDoc, <<>>),
        note => "this_function_should_not_be_called_in_pure_persistence_approach"
    }),

    try
        %% 从MongoDB文档重建EMQX消息
        case rebuild_emqx_message_from_doc(MessageDoc) of
            {ok, Message} ->
                %% 添加恢复标识，防止重复持久化
                MessageWithHeaders = emqx_message:set_header(restart_recovery, true,
                    emqx_message:set_header(skip_persistence, true, Message)),
                %% 重新发布消息（违反方案A原则）
                case emqx:publish(MessageWithHeaders) of
                    ok ->
                        ?SLOG(debug, #{
                            msg => "message_restored_successfully_but_violates_plan_a",
                            client_id => ClientId,
                            message_id => maps:get(<<"message_id">>, MessageDoc, <<>>),
                            topic => maps:get(<<"topic">>, MessageDoc, <<>>)
                        }),
                        ok;
                    {error, Reason} ->
                        ?SLOG(warning, #{
                            msg => "failed_to_republish_restored_message",
                            client_id => ClientId,
                            message_id => maps:get(<<"message_id">>, MessageDoc, <<>>),
                            reason => Reason
                        }),
                        {error, Reason}
                end;
            {error, Reason} ->
                ?SLOG(warning, #{
                    msg => "failed_to_rebuild_message_from_doc",
                    client_id => ClientId,
                    message_id => maps:get(<<"message_id">>, MessageDoc, <<>>),
                    reason => Reason
                }),
                {error, Reason}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_restoring_single_message",
                client_id => ClientId,
                message_id => maps:get(<<"message_id">>, MessageDoc, <<>>),
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%% @doc 格式化peerhost字段
%% 将peerhost转换为二进制字符串格式
format_peerhost(undefined) -> <<>>;
format_peerhost(Peerhost) when is_binary(Peerhost) -> Peerhost;
format_peerhost(Peerhost) when is_tuple(Peerhost) ->
    % IP地址元组格式，如 {192,168,1,1}
    case inet:ntoa(Peerhost) of
        {error, _} -> <<>>;
        IpStr -> list_to_binary(IpStr)
    end;
format_peerhost(Peerhost) when is_list(Peerhost) ->
    list_to_binary(Peerhost);
format_peerhost(_) -> <<>>.

%% 从MongoDB文档重建EMQX消息
%% @spec rebuild_emqx_message_from_doc(MessageDoc :: map()) -> {ok, emqx_types:message()} | {error, Reason}
rebuild_emqx_message_from_doc(MessageDoc) ->
    try
        ClientId = maps:get(<<"client_id">>, MessageDoc, <<>>),
        Topic = maps:get(<<"topic">>, MessageDoc, <<>>),
        Payload = maps:get(<<"payload">>, MessageDoc, <<>>),
        QoS = maps:get(<<"qos">>, MessageDoc, 0),
        Retain = maps:get(<<"retain">>, MessageDoc, false),

        % 重建消息
        Message = emqx_message:make(ClientId, QoS, Topic, Payload),
        Message1 = Message#message{flags = #{retain => Retain}},

        % 设置消息ID（如果有的话）
        Message2 = case maps:get(<<"message_id">>, MessageDoc, undefined) of
            undefined -> Message1;
            MessageIdHex when is_binary(MessageIdHex) ->
                try
                    MessageId = emqx_guid:from_hexstr(MessageIdHex),
                    Message1#message{id = MessageId}
                catch
                    _:_ -> Message1
                end
        end,

        % 修复：恢复消息的头部信息，包括username、peerhost等
        % 这些信息在消息持久化时被保存，恢复时需要重新设置
        Username = maps:get(<<"username">>, MessageDoc, <<>>),
        PeerhostBin = maps:get(<<"peerhost">>, MessageDoc, <<>>),
        Timestamp = maps:get(<<"timestamp">>, MessageDoc, erlang:system_time(millisecond)),

        % 设置消息头部信息
        Message3 = emqx_message:set_header(username, Username, Message2),
        Message4 = case PeerhostBin of
            <<>> -> Message3;
            _ ->
                % 尝试将二进制字符串转换回IP地址格式
                Peerhost = case inet:parse_address(binary_to_list(PeerhostBin)) of
                    {ok, IpTuple} -> IpTuple;
                    {error, _} -> PeerhostBin  % 如果解析失败，保持原格式
                end,
                emqx_message:set_header(peerhost, Peerhost, Message3)
        end,

        % 设置时间戳
        Message5 = Message4#message{timestamp = Timestamp},

        {ok, Message5}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_rebuilding_message_from_doc",
                message_doc => MessageDoc,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%% 统计恢复结果
%% @spec count_restore_results(Results :: [ok | {error, term()}]) -> {integer(), integer()}
count_restore_results(Results) ->
    lists:foldl(fun
        (ok, {SuccessCount, ErrorCount}) ->
            {SuccessCount + 1, ErrorCount};
        ({error, _}, {SuccessCount, ErrorCount}) ->
            {SuccessCount, ErrorCount + 1}
    end, {0, 0}, Results).

%%--------------------------------------------------------------------
%% MQTT 5.0消息属性解析函数
%%--------------------------------------------------------------------

%% @doc 提取Message Expiry Interval属性
%% MQTT 5.0协议：指定消息在发布后的有效期（秒）
extract_message_expiry_interval(Properties) when is_map(Properties) ->
    case maps:get('Message-Expiry-Interval', Properties,
                  maps:get(<<"Message-Expiry-Interval">>, Properties,
                          maps:get(message_expiry_interval, Properties, undefined))) of
        Interval when is_integer(Interval), Interval > 0 -> Interval;
        _ -> undefined
    end;
extract_message_expiry_interval(_) -> undefined.

%% @doc 提取Topic Alias属性
%% MQTT 5.0协议：主题别名，用于减少网络传输
extract_topic_alias(Properties) when is_map(Properties) ->
    case maps:get('Topic-Alias', Properties,
                  maps:get(<<"Topic-Alias">>, Properties,
                          maps:get(topic_alias, Properties, 0))) of
        Alias when is_integer(Alias), Alias > 0 -> Alias;
        _ -> 0
    end;
extract_topic_alias(_) -> 0.

%% @doc 提取Response Topic属性
%% MQTT 5.0协议：指定响应消息的主题
extract_response_topic(Properties) when is_map(Properties) ->
    case maps:get('Response-Topic', Properties,
                  maps:get(<<"Response-Topic">>, Properties,
                          maps:get(response_topic, Properties, <<>>))) of
        Topic when is_binary(Topic) -> Topic;
        Topic when is_list(Topic) -> list_to_binary(Topic);
        _ -> <<>>
    end;
extract_response_topic(_) -> <<>>.

%% @doc 提取Correlation Data属性
%% MQTT 5.0协议：用于请求/响应模式的关联数据
extract_correlation_data(Properties) when is_map(Properties) ->
    case maps:get('Correlation-Data', Properties,
                  maps:get(<<"Correlation-Data">>, Properties,
                          maps:get(correlation_data, Properties, <<>>))) of
        Data when is_binary(Data) -> Data;
        Data when is_list(Data) -> list_to_binary(Data);
        _ -> <<>>
    end;
extract_correlation_data(_) -> <<>>.

%% @doc 提取User Properties属性
%% MQTT 5.0协议：用户自定义属性键值对
extract_user_properties(Properties) when is_map(Properties) ->
    case maps:get('User-Property', Properties,
                  maps:get(<<"User-Property">>, Properties,
                          maps:get(user_properties, Properties, #{}))) of
        UserProps when is_map(UserProps) -> UserProps;
        _ -> #{}
    end;
extract_user_properties(_) -> #{}.

%% @doc 提取Content Type属性
%% MQTT 5.0协议：描述载荷内容的MIME类型
extract_content_type(Properties) when is_map(Properties) ->
    case maps:get('Content-Type', Properties,
                  maps:get(<<"Content-Type">>, Properties,
                          maps:get(content_type, Properties, <<>>))) of
        ContentType when is_binary(ContentType) -> ContentType;
        ContentType when is_list(ContentType) -> list_to_binary(ContentType);
        _ -> <<>>
    end;
extract_content_type(_) -> <<>>.

%% @doc 提取Payload Format Indicator属性
%% MQTT 5.0协议：指示载荷格式（0=字节，1=UTF-8字符串）
extract_payload_format_indicator(Properties) when is_map(Properties) ->
    case maps:get('Payload-Format-Indicator', Properties,
                  maps:get(<<"Payload-Format-Indicator">>, Properties,
                          maps:get(payload_format_indicator, Properties, 0))) of
        Indicator when Indicator =:= 0; Indicator =:= 1 -> Indicator;
        _ -> 0
    end;
extract_payload_format_indicator(_) -> 0.

%% @doc 提取Subscription Identifier属性
%% MQTT 5.0协议：订阅标识符，用于标识特定订阅
extract_subscription_identifier(Properties) when is_map(Properties) ->
    case maps:get('Subscription-Identifier', Properties,
                  maps:get(<<"Subscription-Identifier">>, Properties,
                          maps:get(subscription_identifier, Properties, 0))) of
        Identifier when is_integer(Identifier), Identifier > 0 -> Identifier;
        _ -> 0
    end;
extract_subscription_identifier(_) -> 0.

%% @doc 计算消息过期时间
%% 优先使用消息自身的Message Expiry Interval，否则使用插件配置
calculate_message_expiry_time(undefined, MessageConfig) ->
    % 没有指定Message Expiry Interval，使用插件配置
    ExpiryInterval = maps:get(message_expiry, MessageConfig, ?DEFAULT_MESSAGE_EXPIRY),
    case ExpiryInterval of
        0 -> 0; % 0表示不过期
        _ -> erlang:system_time(millisecond) + ExpiryInterval
    end;
calculate_message_expiry_time(MessageExpiryInterval, _MessageConfig) when is_integer(MessageExpiryInterval) ->
    % 使用消息指定的过期时间（转换为毫秒）
    erlang:system_time(millisecond) + (MessageExpiryInterval * 1000);
calculate_message_expiry_time(_, MessageConfig) ->
    % 无效值，使用插件配置
    ExpiryInterval = maps:get(message_expiry, MessageConfig, ?DEFAULT_MESSAGE_EXPIRY),
    case ExpiryInterval of
        0 -> 0;
        _ -> erlang:system_time(millisecond) + ExpiryInterval
    end.

%% @doc 恢复未确认消息（EMQX重启后）
%% 修复：实现缺失的关键函数，确保QoS 1/2消息的可靠投递
%%
%% 功能说明：
%% 1. 查询MongoDB中所有未过期的QoS 1/2消息
%% 2. 根据MQTT协议要求，重新投递这些消息
%% 3. 确保消息的可靠性和MQTT协议合规性
%%
%% MQTT协议要求：
%% - QoS 1消息：必须至少投递一次，直到收到PUBACK
%% - QoS 2消息：必须恰好投递一次，完成完整握手
%% - 异常关闭后重启：必须恢复并重新投递未确认消息
%%
%% 设计原则：
%% - 遵循MQTT协议的可靠性要求
%% - 只恢复真正需要重新投递的消息
%% - 避免重复投递已确认的消息
%%
%% Java等价概念：
%% @PostConstruct
%% @Transactional
%% public void restoreUnackedMessages() {
%%     List<UnackedMessage> messages = messageRepository.findUnackedMessages();
%%     for (UnackedMessage msg : messages) {
%%         if (msg.getQos() > 0 && !msg.isExpired()) {
%%             // 重新投递QoS 1/2消息
%%             mqttPublisher.republish(msg);
%%         }
%%     }
%% }
restore_unacked_messages() ->
    %% 检查是否为全新安装，如果是则跳过数据恢复
    case emqx_plugin_mongodb_api:is_fresh_installation() of
        true ->
            ?SLOG(info, #{
                msg => "fresh_installation_detected_skipping_message_restoration",
                reason => "no_existing_message_data_to_restore"
            }),
            ok;
        false ->
            try
                ?SLOG(info, #{
                    msg => "starting_unacked_messages_restoration_after_restart",
                    approach => "mqtt_protocol_compliant_restoration"
                }),

        %% 获取消息集合
        MessageCollection = get_message_collection(),

        %% 查询所有未过期的QoS 1/2消息
        Now = erlang:system_time(millisecond),
        Filter = #{
            <<"qos">> => #{<<"$in">> => [1, 2]}, %% 只查询QoS 1和2的消息
            <<"$or">> => [
                #{<<"expiry_time">> => #{<<"$gt">> => Now}}, %% 未过期
                #{<<"expiry_time">> => null}                  %% 永不过期
            ]
        },

        %% 使用分批加载避免内存溢出
        case restore_unacked_messages_in_batches(MessageCollection, Filter) of
            {ok, {TotalMessages, RestoredCount, ClientCount}} ->
                ?SLOG(info, #{
                    msg => "unacked_messages_restoration_completed",
                    total_messages => TotalMessages,
                    restored_messages => RestoredCount,
                    client_count => ClientCount,
                    approach => "batch_loading_to_prevent_memory_overflow"
                }),
                ok;
            {ok, []} ->
                ?SLOG(info, #{msg => "no_unacked_messages_found_for_restoration"}),
                ok;
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "error_querying_unacked_messages_for_restoration",
                    reason => Reason
                }),
                {error, Reason}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_restoring_unacked_messages_after_restart",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end
    end.

%% @doc 分批恢复未确认消息，避免内存溢出
%% 使用游标分批查询和处理消息，防止大量数据一次性加载到内存
restore_unacked_messages_in_batches(MessageCollection, Filter) ->
    try
        BatchSize = application:get_env(emqx_plugin_mongodb, message_restore_batch_size, 500),
        MaxBatches = application:get_env(emqx_plugin_mongodb, message_restore_max_batches, 100),

        ?SLOG(info, #{
            msg => "starting_batch_restoration_of_unacked_messages",
            batch_size => BatchSize,
            max_batches => MaxBatches
        }),

        %% 初始化统计
        TotalMessages = 0,
        RestoredCount = 0,
        ClientSet = sets:new(),

        %% 分批处理
        case process_message_batches(MessageCollection, Filter, BatchSize, MaxBatches,
                                   {TotalMessages, RestoredCount, ClientSet}) of
            {ok, {FinalTotal, FinalRestored, FinalClientSet}} ->
                ClientCount = sets:size(FinalClientSet),
                {ok, {FinalTotal, FinalRestored, ClientCount}};
            {error, Reason} ->
                {error, Reason}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_in_batch_restoration_of_unacked_messages",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%% @doc 处理消息批次
process_message_batches(Collection, Filter, BatchSize, MaxBatches, Stats) ->
    process_message_batches(Collection, Filter, BatchSize, MaxBatches, Stats, 0, null).

process_message_batches(_Collection, _Filter, _BatchSize, MaxBatches, Stats, BatchNum, _LastId)
    when BatchNum >= MaxBatches ->
    ?SLOG(warning, #{
        msg => "reached_max_batches_limit_for_message_restoration",
        max_batches => MaxBatches,
        batch_number => BatchNum
    }),
    {ok, Stats};

process_message_batches(Collection, Filter, BatchSize, MaxBatches,
                       {TotalMessages, RestoredCount, ClientSet}, BatchNum, LastId) ->
    try
        %% 构建分页查询条件
        PaginatedFilter = case LastId of
            null -> Filter;
            _ -> maps:put(<<"_id">>, #{<<"$gt">> => LastId}, Filter)
        end,

        %% 查询一批消息
        Options = #{
            limit => BatchSize,
            sort => #{<<"_id">> => 1}  % 按_id排序确保一致的分页
        },

        case emqx_plugin_mongodb_api:find(Collection, PaginatedFilter, Options) of
            {ok, Messages} when is_list(Messages), length(Messages) > 0 ->
                BatchCount = length(Messages),

                ?SLOG(debug, #{
                    msg => "processing_message_batch",
                    batch_number => BatchNum + 1,
                    batch_size => BatchCount,
                    total_processed => TotalMessages + BatchCount
                }),

                %% 处理这批消息
                {BatchRestored, UpdatedClientSet} = process_message_batch(Messages, ClientSet),

                %% 获取最后一个消息的ID用于下次分页
                LastMessage = lists:last(Messages),
                NewLastId = maps:get(<<"_id">>, LastMessage),

                %% 更新统计
                NewStats = {
                    TotalMessages + BatchCount,
                    RestoredCount + BatchRestored,
                    UpdatedClientSet
                },

                %% 处理下一批
                process_message_batches(Collection, Filter, BatchSize, MaxBatches,
                                      NewStats, BatchNum + 1, NewLastId);

            {ok, []} ->
                %% 没有更多消息
                ?SLOG(info, #{
                    msg => "completed_batch_restoration_no_more_messages",
                    total_batches => BatchNum,
                    total_messages => TotalMessages,
                    restored_messages => RestoredCount
                }),
                {ok, {TotalMessages, RestoredCount, ClientSet}};

            {ok, Messages} when is_list(Messages) ->
                %% 空批次，完成处理
                {ok, {TotalMessages, RestoredCount, ClientSet}};

            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "error_querying_message_batch",
                    batch_number => BatchNum + 1,
                    reason => Reason
                }),
                {error, Reason}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_processing_message_batch",
                batch_number => BatchNum + 1,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%% @doc 处理单个消息批次
process_message_batch(Messages, ClientSet) ->
    %% 按客户端ID分组消息
    GroupedMessages = group_messages_by_client(Messages),

    %% 处理每个客户端的消息
    {RestoredCount, UpdatedClientSet} = lists:foldl(
        fun({ClientId, ClientMessages}, {Count, CSet}) ->
            case restore_client_unacked_messages(ClientId, ClientMessages) of
                {ok, Restored} ->
                    {Count + Restored, sets:add_element(ClientId, CSet)};
                {error, _} ->
                    {Count, sets:add_element(ClientId, CSet)}
            end
        end,
        {0, ClientSet},
        GroupedMessages
    ),

    {RestoredCount, UpdatedClientSet}.

%% @doc 恢复客户端的未确认消息
%% 根据MQTT协议要求，重新投递客户端的QoS 1/2消息
restore_client_unacked_messages(ClientId, Messages) ->
    try
        %% 方案2：强制恢复机制 - 符合MQTT协议和业务逻辑
        %% 无论客户端是否在线，都强制恢复QoS=2未确认消息
        %% 这确保了MQTT协议的"Exactly Once"语义和业务连续性

        ?SLOG(info, #{
            msg => "force_restoring_unacked_messages_mqtt_protocol_compliant",
            client_id => ClientId,
            message_count => length(Messages),
            approach => "force_restore_regardless_of_client_status",
            reason => "mqtt_qos2_exactly_once_semantics"
        }),

        %% 逐个强制恢复消息
        RestoredCount = lists:foldl(fun(MessageDoc, Count) ->
            case restore_single_unacked_message(ClientId, MessageDoc) of
                ok -> Count + 1;
                {error, _} -> Count
            end
        end, 0, Messages),

        ?SLOG(info, #{
            msg => "client_unacked_messages_restoration_completed_force_restore",
            client_id => ClientId,
            total_messages => length(Messages),
            restored_count => RestoredCount,
            approach => "mqtt_protocol_compliant_force_restore"
        }),

        {ok, RestoredCount}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_restoring_client_unacked_messages",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%% @doc 恢复单个未确认消息
%% 根据MQTT协议重新投递单个QoS 1/2消息
restore_single_unacked_message(ClientId, MessageDoc) ->
    try
        %% 关键修复：检查是否为遗嘱消息，如果是则跳过恢复
        %% 遗嘱消息应该只由遗嘱消息模块处理，不应该被消息持久化模块恢复
        Topic = maps:get(<<"topic">>, MessageDoc, <<>>),
        case is_will_message_topic(Topic) of
            true ->
                ?SLOG(info, #{
                    msg => "skipping_will_message_restoration_in_message_persistence",
                    client_id => ClientId,
                    topic => Topic,
                    message_id => maps:get(<<"message_id">>, MessageDoc, <<>>),
                    reason => "will_messages_should_be_handled_by_will_persistence_module_only"
                }),
                ok; % 跳过遗嘱消息的恢复
            false ->
                %% 普通消息：继续正常的恢复流程
                restore_regular_unacked_message(ClientId, MessageDoc)
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_restoring_single_unacked_message",
                client_id => ClientId,
                message_id => maps:get(<<"message_id">>, MessageDoc, <<>>),
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%% @doc 检查主题是否为遗嘱消息主题
%% 根据测试脚本，遗嘱消息主题格式为：/emqx/will/messages/{client_id}
is_will_message_topic(Topic) when is_binary(Topic) ->
    case Topic of
        <<"/emqx/will/messages/", _/binary>> ->
            true;
        _ ->
            false
    end;
is_will_message_topic(_) ->
    false.

%% @doc 恢复普通的未确认消息（非遗嘱消息）
restore_regular_unacked_message(ClientId, MessageDoc) ->
    try
        %% 从MongoDB文档重建EMQX消息
        case rebuild_emqx_message_from_doc(MessageDoc) of
            {ok, Message} ->
                %% 设置DUP标志（MQTT协议要求重传消息设置DUP=1）
                MessageWithDup = emqx_message:set_flag(dup, true, Message),

                %% 添加恢复标识，防止重复处理
                MessageWithHeaders = emqx_message:set_header(restart_recovery, true,
                    emqx_message:set_header(skip_persistence, true, MessageWithDup)),

                %% 修复：使用更可靠的消息恢复策略
                %% 对于QoS 1/2消息，在EMQX重启场景下需要特殊处理
                QoS = maps:get(<<"qos">>, MessageDoc, 0),
                Topic = maps:get(<<"topic">>, MessageDoc, <<>>),
                MessageId = maps:get(<<"message_id">>, MessageDoc, <<>>),

                case QoS of
                    0 ->
                        %% QoS 0消息：直接发布，不需要特殊处理
                        publish_qos0_message(MessageWithHeaders, ClientId, MessageId, Topic);
                    1 ->
                        %% QoS 1消息：使用重试机制确保投递
                        publish_qos1_message_with_retry(MessageWithHeaders, ClientId, MessageId, Topic);
                    2 ->
                        %% QoS 2消息：使用状态管理确保恰好一次投递
                        publish_qos2_message_with_state_management(MessageWithHeaders, ClientId, MessageId, Topic);
                    _ ->
                        %% 未知QoS，按QoS 0处理
                        ?SLOG(warning, #{
                            msg => "unknown_qos_treating_as_qos0",
                            client_id => ClientId,
                            message_id => MessageId,
                            qos => QoS
                        }),
                        publish_qos0_message(MessageWithHeaders, ClientId, MessageId, Topic)
                end;
            {error, Reason} ->
                ?SLOG(warning, #{
                    msg => "failed_to_rebuild_unacked_message_from_doc",
                    client_id => ClientId,
                    message_id => maps:get(<<"message_id">>, MessageDoc, <<>>),
                    reason => Reason
                }),
                {error, Reason};
            [] ->
                ?SLOG(warning, #{
                    msg => "rebuild_message_returned_empty_list",
                    client_id => ClientId,
                    message_id => maps:get(<<"message_id">>, MessageDoc, <<>>),
                    message_doc => MessageDoc
                }),
                {error, empty_result};
            Other ->
                ?SLOG(warning, #{
                    msg => "rebuild_message_returned_unexpected_result",
                    client_id => ClientId,
                    message_id => maps:get(<<"message_id">>, MessageDoc, <<>>),
                    result => Other,
                    message_doc => MessageDoc
                }),
                {error, {unexpected_result, Other}}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_restoring_single_unacked_message",
                client_id => ClientId,
                message_id => maps:get(<<"message_id">>, MessageDoc, <<>>),
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%% @doc 发布QoS 0消息
publish_qos0_message(Message, ClientId, MessageId, Topic) ->
    %% 确保恢复的消息有skip_persistence标识，防止重复持久化
    MessageWithSkip = emqx_message:set_header(skip_persistence, true, Message),
    case emqx:publish(MessageWithSkip) of
        ok ->
            ?SLOG(debug, #{
                msg => "qos0_message_restored_successfully",
                client_id => ClientId,
                message_id => MessageId,
                topic => Topic
            }),
            ok;
        [] ->
            ?SLOG(debug, #{
                msg => "qos0_message_restored_no_subscribers",
                client_id => ClientId,
                message_id => MessageId,
                topic => Topic
            }),
            ok;
        {error, Reason} ->
            ?SLOG(warning, #{
                msg => "failed_to_restore_qos0_message",
                client_id => ClientId,
                message_id => MessageId,
                reason => Reason
            }),
            {error, Reason};
        Other ->
            ?SLOG(debug, #{
                msg => "qos0_message_restore_other_result",
                client_id => ClientId,
                message_id => MessageId,
                result => Other
            }),
            ok
    end.

%% @doc 发布QoS 1消息，带重试机制
publish_qos1_message_with_retry(Message, ClientId, MessageId, Topic) ->
    %% QoS 1消息的关键特性：至少投递一次
    %% 在EMQX重启场景下，如果没有订阅者，消息应该被保留或延迟投递
    %% 确保恢复的消息有skip_persistence标识，防止重复持久化
    MessageWithSkip = emqx_message:set_header(skip_persistence, true, Message),
    case emqx:publish(MessageWithSkip) of
        ok ->
            ?SLOG(debug, #{
                msg => "qos1_message_restored_successfully",
                client_id => ClientId,
                message_id => MessageId,
                topic => Topic,
                dup_flag => true
            }),
            ok;
        [] ->
            %% 关键修复：QoS 1消息没有订阅者时，延迟重试
            ?SLOG(info, #{
                msg => "qos1_message_no_subscribers_scheduling_retry",
                client_id => ClientId,
                message_id => MessageId,
                topic => Topic,
                note => "will_retry_after_clients_reconnect"
            }),

            %% 延迟重试，给客户端重连的时间
            spawn(fun() ->
                timer:sleep(5000), % 延迟5秒
                retry_qos1_message_publish(Message, ClientId, MessageId, Topic, 3)
            end),
            ok;
        {error, Reason} ->
            ?SLOG(warning, #{
                msg => "failed_to_restore_qos1_message",
                client_id => ClientId,
                message_id => MessageId,
                reason => Reason
            }),
            {error, Reason};
        Other ->
            ?SLOG(debug, #{
                msg => "qos1_message_restore_other_result",
                client_id => ClientId,
                message_id => MessageId,
                result => Other
            }),
            ok
    end.

%% @doc 发布QoS 2消息，带状态管理
publish_qos2_message_with_state_management(Message, ClientId, MessageId, Topic) ->
    %% QoS 2消息的关键特性：恰好投递一次
    %% 需要完整的PUBREC/PUBREL/PUBCOMP握手
    %% 确保恢复的消息有skip_persistence标识，防止重复持久化
    MessageWithSkip = emqx_message:set_header(skip_persistence, true, Message),
    case emqx:publish(MessageWithSkip) of
        ok ->
            ?SLOG(debug, #{
                msg => "qos2_message_restored_successfully",
                client_id => ClientId,
                message_id => MessageId,
                topic => Topic,
                dup_flag => true,
                handshake_state => "initiated"
            }),
            ok;
        [] ->
            %% 关键修复：QoS 2消息没有订阅者时，需要特殊处理
            %% QoS 2要求恰好一次投递，即使没有当前订阅者，也需要确保消息可用
            ?SLOG(info, #{
                msg => "qos2_message_no_subscribers_using_persistent_delivery",
                client_id => ClientId,
                message_id => MessageId,
                topic => Topic,
                note => "qos2_exactly_once_semantics_requires_persistent_availability"
            }),

            %% 对于QoS 2消息，使用持久化投递策略
            %% 将消息标记为保留消息，确保后续订阅者能收到
            MessageWithRetain = emqx_message:set_flag(retain, true, Message),
            case emqx:publish(MessageWithRetain) of
                ok ->
                    ?SLOG(info, #{
                        msg => "qos2_message_published_as_retained_for_exactly_once_semantics",
                        client_id => ClientId,
                        message_id => MessageId,
                        topic => Topic
                    }),
                    ok;
                [] ->
                    ?SLOG(info, #{
                        msg => "qos2_message_retained_no_current_subscribers",
                        client_id => ClientId,
                        message_id => MessageId,
                        topic => Topic,
                        note => "message_available_for_future_subscribers"
                    }),
                    ok;
                {error, RetainReason} ->
                    ?SLOG(error, #{
                        msg => "failed_to_publish_qos2_message_as_retained",
                        client_id => ClientId,
                        message_id => MessageId,
                        topic => Topic,
                        reason => RetainReason
                    }),
                    {error, RetainReason};
                RetainOther ->
                    ?SLOG(info, #{
                        msg => "qos2_message_retained_other_result",
                        client_id => ClientId,
                        message_id => MessageId,
                        topic => Topic,
                        result => RetainOther
                    }),
                    ok
            end;
        {error, Reason} ->
            ?SLOG(warning, #{
                msg => "failed_to_restore_qos2_message",
                client_id => ClientId,
                message_id => MessageId,
                reason => Reason
            }),
            {error, Reason};
        Other ->
            ?SLOG(debug, #{
                msg => "qos2_message_restore_other_result",
                client_id => ClientId,
                message_id => MessageId,
                result => Other
            }),
            ok
    end.

%% @doc 重试QoS 1消息发布
%% 修复：避免无限重试，设置合理的重试限制和删除机制
retry_qos1_message_publish(_Message, ClientId, MessageId, Topic, 0) ->
    ?SLOG(warning, #{
        msg => "qos1_message_retry_exhausted_deleting_message",
        client_id => ClientId,
        message_id => MessageId,
        topic => Topic,
        note => "message_deleted_to_prevent_infinite_retry"
    }),
    % 修复：删除消息以防止无限重试
    delete_message_by_id(MessageId);
retry_qos1_message_publish(Message, ClientId, MessageId, Topic, RetriesLeft) ->
    ?SLOG(debug, #{
        msg => "retrying_qos1_message_publish",
        client_id => ClientId,
        message_id => MessageId,
        topic => Topic,
        retries_left => RetriesLeft
    }),

    case emqx:publish(Message) of
        ok ->
            ?SLOG(info, #{
                msg => "qos1_message_retry_successful",
                client_id => ClientId,
                message_id => MessageId,
                topic => Topic,
                retries_used => 4 - RetriesLeft
            }),
            % 成功后删除消息
            delete_message_by_id(MessageId);
        [] ->
            %% 修复：没有订阅者时，减少重试次数并最终删除消息
            ?SLOG(debug, #{
                msg => "qos1_message_no_subscribers_during_retry",
                client_id => ClientId,
                message_id => MessageId,
                topic => Topic,
                retries_left => RetriesLeft - 1
            }),
            if RetriesLeft > 1 ->
                timer:sleep(5000), % 等待5秒
                retry_qos1_message_publish(Message, ClientId, MessageId, Topic, RetriesLeft - 1);
            true ->
                ?SLOG(warning, #{
                    msg => "qos1_message_no_subscribers_giving_up_deleting",
                    client_id => ClientId,
                    message_id => MessageId,
                    topic => Topic
                }),
                delete_message_by_id(MessageId)
            end;
        {error, Reason} ->
            ?SLOG(warning, #{
                msg => "qos1_message_retry_failed",
                client_id => ClientId,
                message_id => MessageId,
                topic => Topic,
                reason => Reason,
                retries_left => RetriesLeft - 1
            }),
            %% 出现错误，但还有重试机会
            if RetriesLeft > 1 ->
                timer:sleep(5000),
                retry_qos1_message_publish(Message, ClientId, MessageId, Topic, RetriesLeft - 1);
            true ->
                delete_message_by_id(MessageId) %% 删除消息避免继续重试
            end;
        Other ->
            ?SLOG(info, #{
                msg => "qos1_message_retry_other_result_assuming_success",
                client_id => ClientId,
                message_id => MessageId,
                topic => Topic,
                result => Other,
                retries_used => 4 - RetriesLeft
            }),
            % 假设成功，删除消息
            delete_message_by_id(MessageId)
    end.

%% @doc 根据消息ID删除消息
%% 简化版本的删除函数，用于清理重试失败的消息
-spec delete_message_by_id(binary()) -> ok.
delete_message_by_id(MessageId) ->
    try
        MessageCollection = get_message_collection(),
        Filter = #{<<"message_id">> => ensure_binary_string(MessageId)},

        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                                {delete_many, MessageCollection, Filter, #{}}) of
            {ok, #{<<"deletedCount">> := Count}} when Count > 0 ->
                ?SLOG(debug, #{
                    msg => "message_deleted_by_id",
                    message_id => MessageId,
                    deleted_count => Count
                });
            {ok, #{<<"deletedCount">> := 0}} ->
                ?SLOG(debug, #{
                    msg => "message_not_found_for_deletion",
                    message_id => MessageId
                });
            {error, Reason} ->
                ?SLOG(warning, #{
                    msg => "failed_to_delete_message_by_id",
                    message_id => MessageId,
                    reason => Reason
                })
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_deleting_message_by_id",
                message_id => MessageId,
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% @doc 确保消息集合和索引存在
ensure_message_collection_and_indexes(Collection) ->
    try
        % 创建索引
        Indexes = [
            % 消息ID索引 - 用于快速查找
            #{
                <<"key">> => #{<<"message_id">> => 1},
                <<"name">> => <<"emm_message_id_1_index">>,
                <<"unique">> => true
            },
            % 客户端ID索引 - 用于按客户端查询
            #{
                <<"key">> => #{<<"clientid">> => 1},
                <<"name">> => <<"emm_clientid_1_index">>
            },
            % 主题索引 - 用于按主题查询
            #{
                <<"key">> => #{<<"topic">> => 1},
                <<"name">> => <<"emm_topic_1_index">>
            },
            % 时间戳索引 - 用于时间范围查询
            #{
                <<"key">> => #{<<"timestamp">> => 1},
                <<"name">> => <<"emm_timestamp_1_index">>
            },
            % 过期时间索引 - 用于TTL清理
            #{
                <<"key">> => #{<<"expires_at">> => 1},
                <<"name">> => <<"emm_expires_at_1_index">>,
                <<"expireAfterSeconds">> => 0  % MongoDB TTL索引
            },
            % QoS索引 - 用于按QoS过滤
            #{
                <<"key">> => #{<<"qos">> => 1},
                <<"name">> => <<"emm_qos_1_index">>
            },
            % 复合索引：客户端ID+时间戳 - 用于高效的客户端消息查询
            #{
                <<"key">> => #{<<"clientid">> => 1, <<"timestamp">> => 1},
                <<"name">> => <<"emm_clientid_1_timestamp_1_index">>
            },
            % 复合索引：主题+时间戳 - 用于高效的主题消息查询
            #{
                <<"key">> => #{<<"topic">> => 1, <<"timestamp">> => 1},
                <<"name">> => <<"emm_topic_1_timestamp_1_index">>
            }
        ],

        % 逐个创建索引，带有存在性检查
        lists:foreach(fun(IndexSpec) ->
            IndexName = maps:get(<<"name">>, IndexSpec),
            case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID, {create_index, Collection, IndexSpec}) of
                {ok, _} ->
                    ?SLOG(debug, #{
                        msg => "message_index_created",
                        collection => Collection,
                        index_name => IndexName
                    });
                {error, {error, {error, {op_msg_response, #{<<"code">> := 85}}}}} ->
                    % IndexOptionsConflict - 索引已存在但名称不同，这是正常情况
                    ?SLOG(debug, #{
                        msg => "message_index_already_exists",
                        collection => Collection,
                        index_name => IndexName
                    });
                {error, Reason} ->
                    ?SLOG(warning, #{
                        msg => "message_index_creation_failed",
                        collection => Collection,
                        index_name => IndexName,
                        reason => Reason
                    })
            end
        end, Indexes),

        ?SLOG(info, #{
            msg => "message_collection_and_indexes_ensured",
            collection => Collection,
            index_count => length(Indexes)
        })
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_ensuring_message_collection_and_indexes",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {ensure_message_collection_failed, R}}
    end.
