%% @doc EMQX MongoDB插件主题过滤模块
%%
%% 这个模块专门处理主题过滤功能，将符合特定主题模式的消息保存到MongoDB的emqx_mqtt_data集合中。
%% 
%% 在Java中的等价概念：
%% @Service
%% @Component
%% public class TopicFilterService {
%%     @Autowired private MongoTemplate mongoTemplate;
%%     @Autowired private BackpressureManager backpressureManager;
%%     
%%     public void processTopicFilteredMessage(Message message) { ... }
%% }
%%
%% 主要职责：
%% 1. 主题模式匹配 - 检查消息主题是否符合配置的过滤条件
%% 2. 数据转换和存储 - 将匹配的消息转换为MongoDB文档并存储
%% 3. 背压管理 - 处理高负载情况下的流量控制
%% 4. 批处理优化 - 提供批量处理能力以提高性能
%% @end

-module(emqx_plugin_mongodb_topic_filter).

%% 包含头文件
-include("emqx_plugin_mongodb.hrl").

%% 导出函数 - 主要API
-export([
    init/0,                                 % 初始化模块
    load/1,                                 % 加载主题过滤配置
    unload/0,                               % 卸载模块
    process_message_for_topic_filtering/1,  % 处理消息的主题过滤
    check_topic_match/1,                    % 检查主题是否匹配
    store_filtered_message/2,               % 存储过滤后的消息
    integrate/0                             % 集成到协调器
]).

%% 导出函数 - 钩子回调
-export([
    on_message_publish/1                    % 消息发布钩子回调
]).

%% 导出函数 - 钩子管理
-export([
    register_hooks/0,                       % 注册钩子
    unregister_hooks/0                      % 注销钩子
]).

%% 导出函数 - 辅助功能
-export([
    extract_topic/1,                        % 提取消息主题
    determine_message_priority/1,           % 确定消息优先级
    eventmsg_publish/1,                     % 构造事件消息
    get_batch_params/2,                     % 获取批处理参数
    adjust_batch_size/1,                    % 调整批处理大小
    get_current_queue_length/0              % 获取当前队列长度
]).

%% ===================================================================
%% 初始化和配置函数
%% ===================================================================

%% @doc 初始化主题过滤模块
%% 这个函数负责初始化主题过滤所需的ETS表和缓存
%%
%% 功能说明：
%% 1. 创建主题过滤规则存储表
%% 2. 初始化主题匹配缓存
%% 3. 设置默认配置
%%
%% 返回值：
%% - ok: 初始化成功
%%
%% Java等价概念：
%% @PostConstruct
%% public void initTopicFilter() {
%%     createRuleStorage();
%%     initTopicMatchCache();
%%     setupDefaultConfig();
%% }
init() ->
    try
        % 创建主题过滤规则ETS表（如果不存在）
        case ets:info(?PLUGIN_MONGODB_TAB) of
            undefined ->
                ets:new(?PLUGIN_MONGODB_TAB, [
                    named_table, public, set, {keypos, 1}, {read_concurrency, true}
                ]);
            _ ->
                ok
        end,

        % 初始化主题匹配缓存
        init_topic_match_cache(),

        % 统一集合创建策略：主动创建集合和索引，与保留消息模块保持一致
        % 这样确保所有集合在插件启动时就存在，便于管理和调试
        ensure_topic_filter_collection_and_indexes(),

        ?SLOG(info, #{msg => "topic_filter_module_initialized_successfully"}),
        ok
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "topic_filter_module_initialization_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {init_failed, R}}
    end.

%% @doc 加载主题过滤配置
%% 这个函数负责解析和加载主题过滤规则到ETS表中
%%
%% 参数：
%% - Config: 包含主题配置的映射，格式为 #{topics := [TopicRule]}
%%
%% 返回值：
%% - ok: 加载成功
%% - {error, Reason}: 加载失败
%%
%% Java等价概念：
%% @ConfigurationProperties("topic.filter")
%% public void loadTopicFilterConfig(TopicFilterConfig config) {
%%     parseTopicRules(config.getTopics());
%%     storeRulesInCache(rules);
%% }
load(Config) ->
    try
        % 提取主题配置
        Topics = maps:get(topics, Config, []),

        case Topics of
            [] ->
                ?SLOG(info, #{msg => "no_topics_configured_skipping_topic_filter_load"}),
                ok;
            _ ->
                % 解析主题配置并存入ETS表
                topic_parse(Topics),

                % 保存配置到应用环境变量
                application:set_env(emqx_plugin_mongodb, topic_filter_enabled, true),
                application:set_env(emqx_plugin_mongodb, topic_filter_config, Topics),

                % 注册主题过滤钩子（不等待MongoDB资源，避免阻塞其他模块加载）
                register_hooks(),

                % 异步初始化MongoDB相关资源
                spawn(fun() ->
                    % 等待MongoDB资源就绪
                    case wait_for_mongodb_resource() of
                        ok ->
                            % 创建主题过滤相关的MongoDB索引
                            create_topic_filter_indexes();
                        {error, Reason} ->
                            ?SLOG(warning, #{
                                msg => "topic_filter_mongodb_resource_not_ready",
                                reason => Reason
                            })
                    end
                end),

                ?SLOG(info, #{
                    msg => "topic_filter_config_loaded_successfully",
                    topics_count => length(Topics)
                }),
                ok
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "topic_filter_config_loading_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {config_load_failed, R}}
    end.

%% ===================================================================
%% 钩子管理函数
%% ===================================================================

%% @doc 注册主题过滤钩子
register_hooks() ->
    ?SLOG(info, #{msg => "registering_topic_filter_hooks"}),

    % 先注销可能存在的旧钩子，防止重复注册
    unregister_hooks(),

    % 注册消息发布钩子 - 用于主题过滤
    % 优先级设置为900，确保在消息持久化(1100)和保留消息(1000)之后执行
    % 但在遗嘱消息(200)之前执行
    emqx_hooks:add('message.publish', {?MODULE, on_message_publish, []}, 900),

    ?SLOG(info, #{
        msg => "topic_filter_hooks_registered_successfully",
        module => ?MODULE,
        priority => 900
    }).

%% @doc 注销主题过滤钩子
unregister_hooks() ->
    ?SLOG(info, #{msg => "unregistering_topic_filter_hooks"}),
    emqx_hooks:del('message.publish', {?MODULE, on_message_publish}),
    ?SLOG(info, #{msg => "topic_filter_hooks_unregistered_successfully"}).

%% @doc 卸载主题过滤模块
unload() ->
    case application:get_env(emqx_plugin_mongodb, topic_filter_enabled, false) of
        true ->
            ?SLOG(info, #{msg => "unloading_topic_filter_module"}),

            % 注销钩子
            unregister_hooks(),

            % 清理应用环境变量（使用异步方式避免超时）
            spawn(fun() ->
                try
                    application:unset_env(emqx_plugin_mongodb, topic_filter_enabled),
                    application:unset_env(emqx_plugin_mongodb, topic_filter_config),
                    ?SLOG(info, #{msg => "topic_filter_env_vars_cleaned_async"})
                catch
                    E:R:S ->
                        ?SLOG(warning, #{
                            msg => "failed_to_clean_topic_filter_env_vars",
                            error => E,
                            reason => R,
                            stacktrace => S
                        })
                end
            end),

            ?SLOG(info, #{msg => "topic_filter_module_unloaded_successfully"});
        false ->
            ?SLOG(info, #{msg => "topic_filter_not_enabled_skipping_unload"})
    end.

%% ===================================================================
%% 钩子回调函数
%% ===================================================================

%% @doc 消息发布钩子回调函数
%% 处理主题过滤逻辑
on_message_publish(Message = #message{topic = Topic}) ->
    % 创建唯一的调用标识符用于跟踪重复调用
    CallId = erlang:unique_integer([positive]),
    MessageId = emqx_guid:to_hexstr(Message#message.id),
    ClientId = emqx_message:from(Message),

    % 添加调试日志
    io:format("TOPIC_FILTER_HOOK[~p]: Topic=~p, MsgId=~p, Client=~p~n", [CallId, Topic, MessageId, ClientId]),
    ?SLOG(info, #{
        msg => "topic_filter_hook_called",
        call_id => CallId,
        topic => Topic,
        client_id => ClientId,
        message_id => MessageId,
        module => ?MODULE
    }),

    % 检查是否为恢复的消息，如果是则跳过主题过滤
    case emqx_message:get_header(skip_persistence, Message, false) of
        true ->
            % 这是恢复的消息，跳过主题过滤避免重复处理
            ?SLOG(debug, #{
                msg => "skipping_recovered_message_topic_filtering",
                topic => Topic,
                reason => "message_already_filtered_during_recovery"
            }),
            {ok, Message};
        false ->
            % 检查是否为系统主题，如果是则跳过
            case emqx_topic:match(Topic, <<"$SYS/#">>) of
                true ->
                    % 系统主题消息，直接返回，不进行主题过滤
                    ?SLOG(debug, #{msg => "sys_topic_message_skipped_in_topic_filter", topic => Topic}),
                    {ok, Message};
                false ->
                    % 检查主题过滤是否启用
                    case application:get_env(emqx_plugin_mongodb, topic_filter_enabled, false) of
                        false ->
                            % 主题过滤未启用，直接返回
                            ?SLOG(debug, #{msg => "topic_filter_disabled", topic => Topic}),
                            {ok, Message};
                        true ->
                            % 主题过滤已启用，处理消息
                            try
                                ?SLOG(info, #{
                                    msg => "topic_filter_processing_message",
                                    topic => Topic,
                                    client_id => emqx_message:from(Message)
                                }),

                        % 调用主题过滤处理函数
                        process_message_for_topic_filtering(Message),

                        % 返回消息，允许继续传递给其他钩子
                        {ok, Message}
                    catch
                        E:R:S ->
                            ?SLOG(error, #{
                                msg => "topic_filter_processing_error",
                                error => E,
                                reason => R,
                                stacktrace => S,
                                topic => Topic,
                                client_id => emqx_message:from(Message)
                            }),
                            % 即使出错也返回消息，不阻断其他钩子
                            {ok, Message}
                    end
                    end
            end
    end.

%% ===================================================================
%% 主要API函数
%% ===================================================================

%% @doc 处理消息的主题过滤
%% 这是主要的入口函数，由消息持久化模块调用
%% @param Message EMQX消息记录
%% @return ok | {error, Reason}
process_message_for_topic_filtering(Message) ->
    Topic = extract_topic(Message),

    % 添加调试日志确认函数被调用
    ?SLOG(info, #{
        msg => "process_message_for_topic_filtering_called",
        topic => Topic,
        client_id => emqx_message:from(Message)
    }),

    % 检查主题匹配
    case check_topic_match(Message) of
        {true, Querys} when Querys =/= [] ->
            % 主题匹配，进行背压检查和处理
            ?SLOG(info, #{
                msg => "topic_filter_match_found",
                topic => Topic,
                matching_rules => length(Querys),
                client_id => emqx_message:from(Message)
            }),
            try
                % 确定消息优先级
                Priority = determine_message_priority(Topic),
                case emqx_plugin_mongodb_backpressure:check_pressure(Topic, Priority) of
                    {reject, _Actions} ->
                        % 背压拒绝，记录日志
                        ?SLOG(warning, #{
                            msg => "topic_filter_message_rejected_due_to_backpressure",
                            topic => Topic,
                            priority => Priority
                        }),
                        ok; % 不处理消息，但不返回错误
                    {allow, Actions} ->
                        % 背压允许，处理消息
                        store_filtered_message(Message, Querys, Actions)
                end
            catch
                E:R:S ->
                    % 背压检查失败，记录错误但继续处理
                    ?SLOG(warning, #{
                        msg => "topic_filter_backpressure_check_failed",
                        error => E,
                        reason => R,
                        stacktrace => S,
                        topic => Topic
                    }),
                    % 继续处理消息（无背压控制）
                    store_filtered_message(Message, Querys, [])
            end;
        false ->
            % 消息不匹配任何已配置的主题过滤器
            ?SLOG(info, #{
                msg => "topic_filter_no_match",
                topic => Topic,
                client_id => emqx_message:from(Message)
            }),
            ok
    end.

%% @doc 检查主题是否匹配配置的过滤器
%% @param Message EMQX消息记录
%% @return {true, Querys} | false
check_topic_match(Message) ->
    % 这个函数从主模块移植过来
    select(Message).

%% @doc 存储过滤后的消息
%% @param Message EMQX消息记录
%% @param Querys 查询列表
%% @param Actions 背压动作列表（可选）
%% @return ok | {error, Reason}
store_filtered_message(Message, Querys) ->
    store_filtered_message(Message, Querys, []).

store_filtered_message(Message, Querys, Actions) ->
    Topic = extract_topic(Message),
    
    try
        % 获取批处理参数
        {BatchSize, _BatchTimeout} = get_batch_params(Topic, Actions),
        
        % 使用背压调整的批处理大小
        adjust_batch_size(BatchSize),
        
        % 记录处理开始时间
        StartTime = erlang:system_time(millisecond),
        
        % 处理消息 (添加错误处理)
        Result = try
            query(eventmsg_publish(Message), Querys)
        catch
            Class:Reason:Stacktrace ->
                ?SLOG(error, #{
                    msg => "topic_filter_query_execution_failed",
                    error => Class,
                    reason => Reason,
                    stacktrace => Stacktrace,
                    topic => Topic
                }),
                {error, {query_error, Reason}}
        end,
        
        % 计算处理时间
        ProcessingTime = erlang:system_time(millisecond) - StartTime,
        
        % 更新背压指标（安全处理）
        try
            QueueLength = get_current_queue_length(),
            % 根据查询结果确定成功/失败
            IsSuccess = case Result of
                {error, _} -> false;
                _ -> true
            end,
            emqx_plugin_mongodb_backpressure:update_metrics(QueueLength, ProcessingTime, IsSuccess)
        catch
            _:_ -> ok
        end,
        
        % 返回结果
        case Result of
            {error, ErrorReason} -> {error, ErrorReason};
            _ -> ok
        end

    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "topic_filter_store_message_failed",
                error => E,
                reason => R,
                stacktrace => S,
                topic => Topic
            }),
            {error, {store_failed, R}}
    end.

%% @doc 提取消息主题
%% @param Message EMQX消息记录
%% @return binary() 主题
extract_topic(Message) ->
    Message#message.topic.

%% @doc 确定消息优先级
%% @param Topic 消息主题
%% @return atom() 优先级
determine_message_priority(Topic) ->
    % 这个函数从主模块移植过来
    % 根据主题确定优先级
    case Topic of
        <<"/edge/", _/binary>> -> high;
        <<"/system/", _/binary>> -> medium;
        _ -> low
    end.

%% ===================================================================
%% 从主模块移植的函数（需要完整实现）
%% ===================================================================

%% 这些函数需要从主模块移植过来，暂时提供占位符实现

select(Message) ->
    % 从主模块移植的 select/1 函数
    % 提取主题
    Topic = extract_topic(Message),

    % 预先检查系统消息
    case emqx_message:is_sys(Message) of
        true ->
            % 系统消息不处理
            false;
        false ->
            try
                % 使用ets:match_object获取所有规则，然后过滤
                AllRules = ets:match_object(?PLUGIN_MONGODB_TAB, {'_', '_', '_'}),

                % 查找匹配的规则
                MatchingRules = find_matching_rules(Topic, AllRules),

                case MatchingRules of
                    [] -> false;
                    _ -> {true, MatchingRules}
                end
            catch
                E:R:S ->
                    ?SLOG(error, #{
                        msg => "error_in_topic_filter_select",
                        error => E,
                        reason => R,
                        stack => S,
                        topic => Topic
                    }),
                    false
            end
    end.

%% @doc 查找匹配的规则
find_matching_rules(Topic, AllRules) ->
    % 过滤匹配的规则
    MatchedRules = lists:filter(fun({_Name, Filter, _Collection}) ->
        try
            emqx_topic:match(Topic, Filter)
        catch
            _:_ -> false
        end
    end, AllRules),

    % 转换为2元组格式 {Name, Collection}，去掉Filter字段
    % 这样connector模块就能正确处理
    lists:map(fun({Name, _Filter, Collection}) ->
        {Name, Collection}
    end, MatchedRules).

query(EvtMsg, Querys) ->
    % 从主模块移植的 query/2 函数
    ?SLOG(debug, #{
        msg => "topic_filter_mongodb_query_started",
        querys => Querys,
        evt_msg_summary => maps:get(id, EvtMsg, <<>>)
    }),

    % 使用MongoDB资源执行查询
    try
        Result = emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID, {Querys, EvtMsg}),

        ?SLOG(debug, #{
            msg => "topic_filter_mongodb_query_result",
            result => Result,
            querys => Querys
        }),

        case Result of
            ok -> ok;
            {ok, _} -> ok;
            {async_return, ok} -> ok;
            {async_return, {ok, _}} -> ok;
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "topic_filter_mongodb_query_failed",
                    error_reason => Reason,
                    evt_msg_summary => maps:get(id, EvtMsg, <<"unknown_id">>),
                    querys_count => length(Querys)
                }),
                {error, Reason};
            Other ->
                ?SLOG(error, #{
                    msg => "topic_filter_unexpected_mongodb_result",
                    result => Other,
                    evt_msg_summary => maps:get(id, EvtMsg, <<"unknown_id">>)
                }),
                {error, unexpected_return}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "topic_filter_mongodb_query_exception",
                error => E,
                reason => R,
                stacktrace => S,
                evt_msg_summary => maps:get(id, EvtMsg, <<"unknown_id">>)
            }),
            {error, {query_exception, R}}
    end.

eventmsg_publish(Message) ->
    % 从主模块移植的 eventmsg_publish/1 函数
    % 支持不同的消息格式
    case Message of
        #message{
            id = Id,
            from = ClientId,
            qos = QoS,
            flags = Flags,
            topic = Topic,
            payload = Payload,
            timestamp = Timestamp
        } ->
            % EMQX消息记录格式
            ParsedPayload = try_parse_json_payload(Payload),

            % 提取用户名和对等主机信息
            Username = emqx_message:get_header(username, Message, undefined),
            Peerhost = emqx_message:get_header(peerhost, Message, undefined),

            % 详细追踪消息来源，找出额外消息的产生原因
            MessageHeaders = emqx_message:get_headers(Message),
            {current_stacktrace, CallStack} = erlang:process_info(self(), current_stacktrace),

            ?SLOG(info, #{
                msg => "topic_filter_message_source_analysis",
                client_id => ClientId,
                topic => Topic,
                message_id => emqx_guid:to_hexstr(Id),
                username => Username,
                peerhost => Peerhost,
                qos => QoS,
                message_headers => MessageHeaders,
                message_flags => Flags,
                message_from => emqx_message:from(Message),
                message_timestamp => emqx_message:timestamp(Message),
                % 检查是否为内部消息的标识
                is_internal_message => maps:get(internal, MessageHeaders, false),
                is_system_message => maps:get(system, MessageHeaders, false),
                is_will_message => maps:get(will_message, MessageHeaders, false),
                is_retained_message => maps:get(retained, MessageHeaders, false),
                is_restored_message => maps:get(restored, MessageHeaders, false),
                skip_persistence => maps:get(skip_persistence, MessageHeaders, false),
                % 调用栈前5层，用于追踪消息来源
                call_stack_top5 => lists:sublist(CallStack, 5),
                % 进程信息
                process_info => #{
                    pid => self(),
                    registered_name => erlang:process_info(self(), registered_name),
                    initial_call => erlang:process_info(self(), initial_call)
                }
            }),

            with_basic_columns(
                'message.publish',
                #{
                    id => emqx_guid:to_hexstr(Id),
                    clientid => ClientId,
                    username => Username,
                    payload => ParsedPayload,
                    payload_format => get_payload_format(ParsedPayload),
                    peerhost => ntoa(Peerhost),
                    topic => Topic,
                    qos => QoS,
                    flags => format_flags(Flags),
                    pub_props => printable_maps(emqx_message:get_header(properties, Message, #{})),
                    publish_received_at => Timestamp
                }
            );
        _ ->
            % 通用格式处理
            Topic = extract_topic(Message),
            {Id, ClientId, QoS, Payload, Timestamp} = extract_message_fields(Message),
            ParsedPayload = try_parse_json_payload(Payload),

            % 修复：从消息中提取username、peerhost等字段
            Username = case Message of
                #message{} -> emqx_message:get_header(username, Message, undefined);
                _ -> undefined
            end,
            Peerhost = case Message of
                #message{} -> emqx_message:get_header(peerhost, Message, undefined);
                _ -> undefined
            end,
            Flags = case Message of
                #message{flags = F} -> F;
                _ -> #{}
            end,
            Properties = case Message of
                #message{} -> emqx_message:get_header(properties, Message, #{});
                _ -> #{}
            end,

            % 详细记录消息来源和字段信息，用于排查问题（通用格式）
            ?SLOG(info, #{
                msg => "topic_filter_processing_generic_message_detailed_debug",
                client_id => ClientId,
                topic => Topic,
                message_id => Id,
                username => Username,
                peerhost => Peerhost,
                qos => QoS,
                message_type => "generic_format",
                message_record => Message,
                call_stack => erlang:process_info(self(), current_stacktrace)
            }),

            with_basic_columns(
                'message.publish',
                #{
                    id => Id,
                    clientid => ClientId,
                    username => Username,
                    topic => Topic,
                    payload => ParsedPayload,
                    payload_format => get_payload_format(ParsedPayload),
                    peerhost => ntoa(Peerhost),
                    qos => QoS,
                    flags => format_flags(Flags),
                    pub_props => printable_maps(Properties),
                    publish_received_at => Timestamp
                }
            )
    end.

%% @doc 获取批处理参数
%% 从自适应批处理模块获取特定主题的批处理参数
get_batch_params(Topic, _Actions) ->
    try
        % 尝试从自适应批处理模块获取参数
        case whereis(emqx_plugin_mongodb_adaptive_batch) of
            undefined ->
                % 自适应批处理模块未启动，使用默认值
                {1000, 5000};
            _Pid ->
                % 调用自适应批处理模块获取参数
                emqx_plugin_mongodb_adaptive_batch:get_batch_params(Topic)
        end
    catch
        E:R:S ->
            ?SLOG(warning, #{
                msg => "failed_to_get_batch_params",
                topic => Topic,
                error => E,
                reason => R,
                stacktrace => S
            }),
            % 出错时使用默认值
            {1000, 5000}
    end.

%% @doc 调整批处理大小
%% 通过自适应批处理模块调整批处理大小
adjust_batch_size(BatchSize) ->
    try
        % 计算调整比例
        DefaultSize = 1000,
        Ratio = case DefaultSize of
            0 -> 1.0;
            _ -> BatchSize / DefaultSize
        end,

        % 尝试调用自适应批处理模块
        case whereis(emqx_plugin_mongodb_adaptive_batch) of
            undefined ->
                % 自适应批处理模块未启动
                ?SLOG(debug, #{
                    msg => "adaptive_batch_module_not_available",
                    batch_size => BatchSize
                }),
                ok;
            _Pid ->
                % 调用自适应批处理模块调整大小
                emqx_plugin_mongodb_adaptive_batch:adjust_batch_size(Ratio),
                ?SLOG(debug, #{
                    msg => "batch_size_adjusted",
                    batch_size => BatchSize,
                    ratio => Ratio
                })
        end
    catch
        E:R:S ->
            ?SLOG(warning, #{
                msg => "failed_to_adjust_batch_size",
                batch_size => BatchSize,
                error => E,
                reason => R,
                stacktrace => S
            })
    end,
    ok.

%% @doc 获取当前队列长度
%% 从自适应批处理模块获取当前消息队列长度
get_current_queue_length() ->
    try
        % 尝试从自适应批处理模块获取队列长度
        case whereis(emqx_plugin_mongodb_adaptive_batch) of
            undefined ->
                % 自适应批处理模块未启动，返回0
                0;
            _Pid ->
                % 调用自适应批处理模块获取队列长度
                emqx_plugin_mongodb_adaptive_batch:get_queue_length()
        end
    catch
        E:R:S ->
            ?SLOG(warning, #{
                msg => "failed_to_get_queue_length",
                error => E,
                reason => R,
                stacktrace => S
            }),
            % 出错时返回0
            0
    end.

%% @doc 集成到协调器
%% 这个函数将主题过滤模块注册到模块协调器中
%% 使模块能够参与系统级的协调和通信
%%
%% 功能说明：
%% 1. 向协调器注册模块信息和元数据
%% 2. 声明模块的功能特性和优先级
%% 3. 建立与其他模块的通信通道
%% 4. 启用协调器的监控和管理功能
%%
%% 返回值：
%% - ok: 集成成功
%% - {error, Reason}: 集成失败
%%
%% Java等价概念：
%% 类似于Spring的@Component注册
%% 或者OSGi Bundle的激活
%% 或者微服务的服务注册
integrate() ->
    try
        % 定义模块信息和元数据
        ModuleInfo = #{
            priority => medium,                    % 中等优先级
            description => <<"Topic filtering and message routing module">>,
            features => [
                topic_matching,                    % 主题匹配功能
                message_filtering,                 % 消息过滤功能
                data_storage,                      % 数据存储功能
                backpressure_support              % 背压支持
            ],
            dependencies => [
                emqx_plugin_mongodb_backpressure,  % 依赖背压模块
                emqx_plugin_mongodb_api            % 依赖MongoDB API模块
            ],
            version => <<"1.0.0">>,
            status => active
        },

        % 向协调器注册模块
        case emqx_plugin_mongodb_coordinator:register_module(?MODULE, ModuleInfo) of
            ok ->
                ?SLOG(info, #{
                    msg => "topic_filter_module_integrated_successfully",
                    module => ?MODULE,
                    features => maps:get(features, ModuleInfo)
                }),
                ok;
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_integrate_topic_filter_module",
                    module => ?MODULE,
                    reason => Reason
                }),
                {error, Reason}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "topic_filter_integration_exception",
                module => ?MODULE,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {integration_exception, R}}
    end.

%% ===================================================================
%% 辅助函数（从主模块移植）
%% ===================================================================

%% @doc 尝试解析JSON载荷
try_parse_json_payload(Payload) when is_binary(Payload) ->
    % 直接返回原始payload，不解析JSON
    Payload;
try_parse_json_payload(Payload) ->
    % 非二进制数据，保持原样
    Payload.

%% @doc 获取载荷格式
get_payload_format(Payload) when is_binary(Payload) ->
    <<"binary">>;
get_payload_format(_) ->
    <<"other">>.

%% @doc 添加基本列
with_basic_columns(EventType, Data) ->
    Data#{
        event => EventType,
        node => node(),
        timestamp => erlang:system_time(millisecond)
    }.

%% @doc 格式化标志
format_flags(Flags) when is_map(Flags) ->
    Flags;
format_flags(_) ->
    #{}.

%% @doc 可打印的映射
printable_maps(Map) when is_map(Map) ->
    Map;
printable_maps(_) ->
    #{}.

%% @doc 网络地址转换
ntoa(undefined) -> undefined;
ntoa({A, B, C, D}) ->
    list_to_binary(io_lib:format("~w.~w.~w.~w", [A, B, C, D]));
ntoa(Other) -> Other.

%% @doc 提取消息字段
extract_message_fields(Message) ->
    % 尝试从不同格式的消息中提取字段
    try
        case Message of
            #message{id = Id, from = ClientId, qos = QoS, payload = Payload, timestamp = Timestamp} ->
                {emqx_guid:to_hexstr(Id), ClientId, QoS, Payload, Timestamp};
            _ ->
                % 默认值
                {<<"unknown">>, <<"unknown">>, 0, <<>>, erlang:system_time(millisecond)}
        end
    catch
        _:_ ->
            {<<"unknown">>, <<"unknown">>, 0, <<>>, erlang:system_time(millisecond)}
    end.

%% ===================================================================
%% 主题配置解析函数（从主模块移植）
%% ===================================================================

%% @doc 解析主题配置并存入ETS表
%% 从主模块移植的 topic_parse/1 函数
topic_parse([]) ->
    ok;
topic_parse([#{filter := Filter, name := Name, collection := Collection} | T]) ->
    % 将规则名称转换为字符串 (如果它是原子的话)
    NameStr =
        case is_atom(Name) of
            true -> atom_to_list(Name);
            false -> Name
        end,

    % 将集合名称转换为二进制
    CollectionBin =
        case is_binary(Collection) of
            true -> Collection;
            false when is_list(Collection) -> list_to_binary(Collection);
            false when is_atom(Collection) -> atom_to_binary(Collection, utf8);
            false -> <<"emqx_mqtt_data">>
        end,

    % 构造要插入ETS的元组
    Item = {NameStr, Filter, CollectionBin},
    % 将规则插入到ETS表中
    ets:insert(?PLUGIN_MONGODB_TAB, Item),
    % 递归处理列表的剩余部分
    topic_parse(T);
% 处理列表中可能存在的无效项
topic_parse([InvalidItem | T]) ->
    ?SLOG(warning, #{msg => "invalid_item_in_topics_config_skipping", item => InvalidItem}),
    topic_parse(T).

%% @doc 初始化主题匹配缓存
%% 从主模块移植的 init_topic_match_cache/0 函数
init_topic_match_cache() ->
    % 主缓存表
    case ets:info(?TOPIC_MATCH_CACHE_TAB) of
        undefined ->
            ets:new(?TOPIC_MATCH_CACHE_TAB, [
                named_table, public, set, {read_concurrency, true}
            ]);
        _ -> ok
    end,

    % 最近访问表，用于LRU策略
    case ets:info(?TOPIC_MATCH_CACHE_RECENT_TAB) of
        undefined ->
            ets:new(?TOPIC_MATCH_CACHE_RECENT_TAB, [
                named_table, public, ordered_set, {write_concurrency, true}
            ]);
        _ -> ok
    end.

%% ===================================================================
%% MongoDB资源管理函数
%% ===================================================================

%% @doc 等待MongoDB资源就绪
wait_for_mongodb_resource() ->
    wait_for_mongodb_resource(30, 1000).

wait_for_mongodb_resource(0, _Interval) ->
    ?SLOG(error, #{msg => "mongodb_resource_not_ready_timeout"}),
    {error, mongodb_resource_timeout};
wait_for_mongodb_resource(Retries, Interval) ->
    case emqx_resource:health_check(?PLUGIN_MONGODB_RESOURCE_ID) of
        ok ->
            ?SLOG(info, #{msg => "mongodb_resource_ready_for_topic_filter"}),
            ok;
        {ok, _} ->
            ?SLOG(info, #{msg => "mongodb_resource_ready_for_topic_filter"}),
            ok;
        _ ->
            ?SLOG(debug, #{msg => "waiting_for_mongodb_resource", retries_left => Retries}),
            timer:sleep(Interval),
            wait_for_mongodb_resource(Retries - 1, Interval)
    end.

%% @doc 创建主题过滤相关的MongoDB索引
create_topic_filter_indexes() ->
    try
        % 获取主题过滤配置
        Topics = application:get_env(emqx_plugin_mongodb, topic_filter_config, []),

        % 为每个配置的主题创建索引
        lists:foreach(fun(TopicConfig) ->
            Collection = maps:get(collection, TopicConfig, ?DEFAULT_DATA_COLLECTION),
            create_indexes_for_collection(Collection)
        end, Topics),

        ?SLOG(info, #{msg => "topic_filter_indexes_created_successfully"}),
        ok
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "failed_to_create_topic_filter_indexes",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {index_creation_failed, R}}
    end.

%% @doc 为指定集合创建索引
create_indexes_for_collection(Collection) ->
    % 主题过滤数据索引 - 使用统一的命名规则
    TopicFilterIndexes = [
        % 主题索引
        {#{<<"topic">> => 1}, #{name => <<"tdf_topic_1_index">>}},
        % 客户端ID索引
        {#{<<"clientid">> => 1}, #{name => <<"tdf_clientid_1_index">>}},
        % 时间戳索引（用于数据清理）
        {#{<<"timestamp">> => 1}, #{name => <<"tdf_timestamp_1_index">>}},
        % 复合索引：主题+时间戳
        {#{<<"topic">> => 1, <<"timestamp">> => -1}, #{name => <<"tdf_topic_1_timestamp_-1_index">>}},
        % 复合索引：客户端ID+时间戳
        {#{<<"clientid">> => 1, <<"timestamp">> => -1}, #{name => <<"tdf_clientid_1_timestamp_-1_index">>}}
    ],

    % 创建索引，使用emqx_resource:query方式与其他模块保持一致
    lists:foreach(fun({IndexSpec, IndexOptions}) ->
        IndexName = maps:get(name, IndexOptions),
        % 构建正确的索引文档格式
        IndexDoc = #{
            <<"key">> => IndexSpec,
            <<"name">> => IndexName
        },
        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                               {create_index, Collection, IndexDoc}) of
            {ok, _} ->
                ?SLOG(info, #{
                    msg => "topic_filter_index_created",
                    collection => Collection,
                    index_name => IndexName
                });
            {async_return, ok} ->
                ?SLOG(info, #{
                    msg => "topic_filter_index_created_async",
                    collection => Collection,
                    index_name => IndexName
                });
            {async_return, {ok, _}} ->
                ?SLOG(info, #{
                    msg => "topic_filter_index_created_async",
                    collection => Collection,
                    index_name => IndexName
                });
            {error, {error, {error, {op_msg_response, #{<<"code">> := 85}}}}} ->
                % IndexOptionsConflict - 索引已存在但名称不同，这是正常情况
                ?SLOG(debug, #{
                    msg => "topic_filter_index_already_exists_different_name",
                    collection => Collection,
                    index_name => IndexName
                });
            {error, Reason} ->
                ?SLOG(warning, #{
                    msg => "topic_filter_index_creation_failed",
                    collection => Collection,
                    index_name => IndexName,
                    reason => Reason
                });
            {async_return, {error, {error, {error, {op_msg_response, #{<<"code">> := 85}}}}}} ->
                % IndexOptionsConflict - 索引已存在但名称不同，这是正常情况
                ?SLOG(debug, #{
                    msg => "topic_filter_index_already_exists_different_name_async",
                    collection => Collection,
                    index_name => IndexName
                });
            {async_return, {error, Reason}} ->
                ?SLOG(warning, #{
                    msg => "topic_filter_index_creation_failed_async",
                    collection => Collection,
                    index_name => IndexName,
                    reason => Reason
                })
        end
    end, TopicFilterIndexes).

%% @doc 确保主题过滤集合和索引存在
ensure_topic_filter_collection_and_indexes() ->
    try
        % 获取集合名称
        Collection = ?DEFAULT_DATA_COLLECTION,

        ?SLOG(info, #{
            msg => "ensuring_topic_filter_collection_and_indexes",
            collection => Collection
        }),

        % 创建索引 - 使用与现有索引兼容的名称和字段
        Indexes = [
            % 主题索引 - 用于快速查找（与现有索引保持一致）
            #{
                <<"key">> => #{<<"topic">> => 1},
                <<"name">> => <<"tdf_topic_1_index">>
            },
            % 客户端ID索引 - 用于按客户端查询（与现有索引保持一致）
            #{
                <<"key">> => #{<<"clientid">> => 1},
                <<"name">> => <<"tdf_clientid_1_index">>
            },
            % 时间戳索引 - 用于时间范围查询（与现有索引保持一致）
            #{
                <<"key">> => #{<<"timestamp">> => 1},
                <<"name">> => <<"tdf_timestamp_1_index">>
            },
            % 复合索引：主题+时间戳 - 用于高效的主题时间范围查询（与现有索引保持一致）
            #{
                <<"key">> => #{<<"topic">> => 1, <<"timestamp">> => -1},
                <<"name">> => <<"tdf_topic_1_timestamp_-1_index">>
            },
            % 复合索引：客户端ID+时间戳 - 用于高效的客户端时间范围查询（与现有索引保持一致）
            #{
                <<"key">> => #{<<"clientid">> => 1, <<"timestamp">> => -1},
                <<"name">> => <<"tdf_clientid_1_timestamp_-1_index">>
            }
        ],

        % 逐个创建索引，带有存在性检查
        lists:foreach(fun(IndexSpec) ->
            IndexName = maps:get(<<"name">>, IndexSpec),
            case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID, {create_index, Collection, IndexSpec}) of
                {ok, _} ->
                    ?SLOG(debug, #{
                        msg => "topic_filter_index_created",
                        collection => Collection,
                        index_name => IndexName
                    });
                {error, {error, {error, {op_msg_response, #{<<"code">> := 85}}}}} ->
                    % IndexOptionsConflict - 索引已存在但名称不同，这是正常情况
                    ?SLOG(debug, #{
                        msg => "topic_filter_index_already_exists",
                        collection => Collection,
                        index_name => IndexName
                    });
                {error, Reason} ->
                    ?SLOG(warning, #{
                        msg => "topic_filter_index_creation_failed",
                        collection => Collection,
                        index_name => IndexName,
                        reason => Reason
                    })
            end
        end, Indexes),

        ?SLOG(info, #{
            msg => "topic_filter_collection_and_indexes_ensured",
            collection => Collection,
            index_count => length(Indexes)
        })
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_ensuring_topic_filter_collection_and_indexes",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {ensure_topic_filter_collection_failed, R}}
    end.


