%%%-------------------------------------------------------------------
%%% @doc
%%% MongoDB插件的异常处理增强模块
%%% 这个模块是整个MongoDB插件系统的错误处理中心，提供企业级的错误管理能力
%%%
%%% 功能概述：
%%% 1. 错误分类与隔离 - 智能分类各种错误类型，实现错误隔离和分级处理
%%% 2. 自动恢复策略 - 根据错误类型和严重程度自动执行恢复操作
%%% 3. 故障注入测试 - 支持混沌工程，主动注入故障测试系统稳定性
%%% 4. 错误统计分析 - 收集和分析错误模式，提供系统健康度评估
%%% 5. 上下文感知处理 - 根据错误发生的上下文环境选择最佳处理策略
%%%
%%% 架构设计：
%%% - 采用中央化错误处理模式，统一管理所有模块的错误
%%% - 使用分层错误分类系统，支持细粒度的错误处理
%%% - 实现自适应恢复机制，根据历史数据优化恢复策略
%%% - 提供故障注入能力，支持混沌工程和弹性测试
%%%
%%% Java等价概念：
%%% 类似于Spring的@ExceptionHandler全局异常处理器
%%% 或者Netflix Hystrix的错误处理和熔断机制
%%% 或者企业级的异常管理框架（如Sentry、Rollbar）
%%% 或者混沌工程工具（如Chaos Monkey、Gremlin）
%%%
%%% 设计模式：
%%% - 策略模式（Strategy Pattern）：不同错误类型使用不同的处理策略
%%% - 观察者模式（Observer Pattern）：错误事件的通知和处理
%%% - 责任链模式（Chain of Responsibility）：错误处理的分级传递
%%% - 状态模式（State Pattern）：错误恢复过程的状态管理
%%% @end
%%%-------------------------------------------------------------------
-module(emqx_plugin_mongodb_error_handler).

%% 实现gen_server行为，提供状态管理和并发控制
%% 类似于Java中实现Runnable接口或继承Thread类
-behaviour(gen_server).

%% API - 公共接口函数
%% 这些函数提供错误处理的主要功能，类似于Java中的public方法
-export([
    start_link/1,           % 启动错误处理服务，类似于Java的服务启动方法
    handle_error/3,         % 处理错误，类似于异常处理器的handle方法
    classify_error/1,       % 分类错误，类似于异常分类器
    inject_fault/3,         % 注入故障，类似于混沌工程的故障注入
    get_error_stats/0,      % 获取错误统计，类似于监控指标查询
    get_error_stats/1,      % 获取特定类型错误统计
    reset_error_stats/0,    % 重置错误统计，类似于计数器重置
    enable_fault_injection/1, % 启用故障注入，类似于测试模式开关
    disable_fault_injection/0, % 禁用故障注入
    register_error_handler/3, % 注册错误处理器
    integrate/0             % 集成到系统中，类似于模块注册方法
]).

%% gen_server callbacks
-export([
    init/1,
    handle_call/3,
    handle_cast/2,
    handle_info/2,
    terminate/2,
    code_change/3
]).

%% 内部导出函数
-export([
    recovery_worker/0,
    fault_injection_worker/0
]).

-include("emqx_plugin_mongodb.hrl").

%% 错误记录数据结构
%% 这个记录结构用于存储每个错误事件的完整信息
%% 类似于Java中的异常对象或错误日志实体
%%
%% 在Java中相当于：
%% public class ErrorRecord {
%%     private String id;
%%     private long timestamp;
%%     private ErrorType errorType;
%%     private Severity severity;
%%     private String source;
%%     private Map<String, Object> details;
%%     private Map<String, Object> context;
%%     private int recoveryAttempts;
%%     private boolean recovered;
%%     private Long recoveryTime;
%% }
-record(error_record, {
    id,                     % 错误唯一标识符，类似于异常ID或事务ID
    timestamp,              % 错误发生的时间戳，用于时间序列分析
    error_type,             % 错误类型分类，如连接错误、超时错误等
    severity,               % 错误严重程度，如CRITICAL、MAJOR、MINOR
    source,                 % 错误来源信息（模块名、函数名等），便于定位问题
    details = #{},          % 错误详细信息映射，包含具体的错误数据
    context = #{},          % 错误发生时的上下文环境，如系统状态、配置等
    recovery_attempts = 0,  % 已尝试的恢复次数，用于避免无限重试
    recovered = false,      % 错误是否已成功恢复的标志
    recovery_time           % 错误恢复完成的时间戳，用于计算恢复时长
}).

%% 故障注入配置数据结构
%% 这个记录结构用于配置混沌工程的故障注入参数
%% 类似于Java中的测试配置类或混沌工程配置
%%
%% 在Java中相当于：
%% public class FaultInjectionConfig {
%%     private boolean enabled;
%%     private List<ErrorType> errorTypes;
%%     private double probability;
%%     private List<String> targetModules;
%%     private long duration;
%%     private long startTime;
%%     private long endTime;
%% }
-record(fault_injection, {
    enabled = false,        % 故障注入开关，控制是否启用混沌测试
    error_types = [],       % 要注入的错误类型列表，如[timeout, connection_error]
    probability = 0.0,      % 故障注入概率（0.0-1.0），控制注入频率
    target_modules = [],    % 目标模块列表，指定在哪些模块中注入故障
    duration = 0,           % 故障注入持续时间（毫秒），0表示无限期
    start_time = 0,         % 故障注入开始时间戳，用于定时注入
    end_time = 0            % 故障注入结束时间戳，用于自动停止
}).

%%%===================================================================
%%% API - 公共接口函数
%%% 这些函数提供错误处理的主要功能，供其他模块调用
%%%===================================================================

%% @doc 启动异常处理服务
%% 这个函数启动错误处理器的gen_server进程，开始提供错误处理服务
%%
%% 功能说明：
%% 1. 创建一个本地注册的gen_server进程
%% 2. 初始化错误处理器的内部状态和配置
%% 3. 启动错误恢复和故障注入工作进程
%% 4. 开始监听错误事件和处理请求
%%
%% 参数说明：
%% - Options: 错误处理器配置选项映射
%%   格式示例：#{
%%     recovery_enabled => true,
%%     fault_injection_enabled => false,
%%     max_recovery_attempts => 3
%%   }
%%
%% 返回值：
%% - {ok, Pid}: 启动成功，返回进程ID
%% - {error, Reason}: 启动失败，返回错误原因
%%
%% Java等价概念：
%% 类似于Spring的@Service注解的Bean启动
%% 或者异常处理器的初始化
%%
%% 示例：
%% Options = #{recovery_enabled => true, max_recovery_attempts => 5},
%% {ok, Pid} = emqx_plugin_mongodb_error_handler:start_link(Options)
start_link(Options) ->
    %% 启动gen_server进程
    %% {local, ?MODULE} 表示本地注册，进程名为模块名
    %% ?MODULE 是当前模块名的宏
    %% [Options] 是传递给init/1回调的参数列表
    %% 在Java中相当于：
    %% @Service
    %% @PostConstruct
    %% public void startErrorHandler(Map<String, Object> options) { ... }
    gen_server:start_link({local, ?MODULE}, ?MODULE, [Options], []).

%% @doc 处理错误
%% 这个函数接收错误事件并异步处理，是错误处理的核心入口点
%%
%% 功能说明：
%% 1. 接收来自各个模块的错误报告
%% 2. 异步处理错误，不阻塞调用者
%% 3. 触发错误分类、记录和恢复流程
%% 4. 更新错误统计和监控指标
%%
%% 参数说明：
%% - ErrorType: 错误类型（原子类型），如timeout、connection_error等
%% - Source: 错误来源信息，包含模块名、函数名等上下文
%% - Details: 错误详细信息映射，包含具体的错误数据
%%   格式示例：#{
%%     reason => connection_timeout,
%%     duration => 5000,
%%     retry_count => 3
%%   }
%%
%% 返回值：
%% - ok: 错误处理请求已发送（异步操作）
%%
%% Java等价概念：
%% 类似于Spring的@ExceptionHandler注解方法
%% 或者全局异常处理器的handleException方法
%%
%% 示例：
%% emqx_plugin_mongodb_error_handler:handle_error(
%%     timeout,
%%     #{module => emqx_plugin_mongodb_connector, function => query},
%%     #{reason => connection_timeout, duration => 5000}
%% )
handle_error(ErrorType, Source, Details) ->
    %% 异步发送错误处理请求到gen_server
    %% 使用gen_server:cast确保不阻塞错误报告者
    %% 在Java中相当于：
    %% @Async
    %% public void handleError(ErrorType errorType, Source source, Map<String, Object> details) {
    %%     errorProcessingService.processError(errorType, source, details);
    %% }
    gen_server:cast(?MODULE, {handle_error, ErrorType, Source, Details}).

%% @doc 分类错误
%% 这个函数根据错误的特征将其分类为不同的类型和严重程度
%%
%% 功能说明：
%% 1. 分析错误的模式和特征
%% 2. 将错误归类到预定义的错误类型
%% 3. 评估错误的严重程度级别
%% 4. 为后续的错误处理策略提供依据
%%
%% 参数说明：
%% - Error: 错误元组，通常格式为{error, Reason}
%%
%% 返回值：
%% - {ErrorType, Severity}: 错误类型和严重程度的元组
%%
%% 错误类型分类：
%% - TIMEOUT: 超时错误，通常是网络或操作超时
%% - CONNECTION: 连接错误，数据库连接失败
%% - AUTHENTICATION: 认证错误，用户名密码错误
%% - NETWORK: 网络错误，网络不可达或中断
%% - QUERY: 查询错误，SQL语法错误或数据问题
%% - SYSTEM: 系统错误，系统资源不足或内部错误
%% - UNKNOWN: 未知错误，无法分类的错误
%%
%% 严重程度级别：
%% - CRITICAL: 严重错误，影响系统核心功能
%% - MAJOR: 重要错误，影响主要功能
%% - MINOR: 轻微错误，影响较小
%%
%% Java等价概念：
%% 类似于异常分类器或错误代码映射
%% 或者日志级别的分类逻辑
%%
%% 示例：
%% {ErrorType, Severity} = classify_error({error, timeout}),
%% % 返回 {timeout, major}
classify_error({error, timeout}) ->
    %% 超时错误：通常是网络或操作超时，严重程度为重要
    %% 在Java中相当于：
    %% if (error instanceof TimeoutException) {
    %%     return new ErrorClassification(ErrorType.TIMEOUT, Severity.MAJOR);
    %% }
    {?ERROR_TYPE_TIMEOUT, ?SEVERITY_MAJOR};
classify_error({error, {connection_error, _}}) ->
    %% 连接错误：数据库连接失败，严重程度为严重
    %% 这类错误会影响系统的核心功能
    {?ERROR_TYPE_CONNECTION, ?SEVERITY_CRITICAL};
classify_error({error, {auth_error, _}}) ->
    %% 认证错误：用户名密码错误或权限不足，严重程度为严重
    %% 这类错误表明配置或安全问题
    {?ERROR_TYPE_AUTHENTICATION, ?SEVERITY_CRITICAL};
classify_error({error, {network_error, _}}) ->
    %% 网络错误：网络不可达或中断，严重程度为重要
    %% 这类错误通常是临时性的
    {?ERROR_TYPE_NETWORK, ?SEVERITY_MAJOR};
classify_error({error, {query_error, _}}) ->
    %% 查询错误：SQL语法错误或数据问题，严重程度为重要
    %% 这类错误通常是业务逻辑问题
    {?ERROR_TYPE_QUERY, ?SEVERITY_MAJOR};
classify_error({error, {system_error, _}}) ->
    %% 系统错误：系统资源不足或内部错误，严重程度为严重
    %% 这类错误表明系统级问题
    {?ERROR_TYPE_SYSTEM, ?SEVERITY_CRITICAL};
classify_error(_) ->
    %% 未知错误：无法分类的错误，严重程度为轻微
    %% 这是一种防御性分类，避免遗漏
    {?ERROR_TYPE_UNKNOWN, ?SEVERITY_MINOR}.

%% @doc 注入故障
%% 这个函数用于混沌工程，主动向系统注入故障以测试系统的弹性
%%
%% 功能说明：
%% 1. 配置故障注入的参数和策略
%% 2. 在指定的模块中按概率注入故障
%% 3. 控制故障注入的持续时间
%% 4. 支持多种错误类型的组合注入
%%
%% 参数说明：
%% - ErrorTypes: 要注入的错误类型列表，如[timeout, connection_error]
%% - Probability: 故障注入概率（0.0-1.0），控制注入频率
%% - Duration: 故障注入持续时间（毫秒），0表示无限期
%%
%% 返回值：
%% - ok: 故障注入配置成功
%% - {error, Reason}: 配置失败，返回错误原因
%%
%% Java等价概念：
%% 类似于Chaos Monkey的故障注入
%% 或者测试框架中的异常模拟
%%
%% 示例：
%% inject_fault([timeout, connection_error], 0.1, 60000)
%% % 以10%的概率注入超时和连接错误，持续1分钟
inject_fault(ErrorTypes, Probability, Duration) ->
    %% 同步调用gen_server配置故障注入
    %% 在Java中相当于：
    %% @Test
    %% public void testWithFaultInjection() {
    %%     chaosMonkey.injectFaults(Arrays.asList(TIMEOUT, CONNECTION_ERROR), 0.1, 60000);
    %% }
    gen_server:call(?MODULE, {inject_fault, ErrorTypes, Probability, Duration}).

%% @doc 获取错误统计
get_error_stats() ->
    gen_server:call(?MODULE, get_error_stats).

%% @doc 获取特定类型的错误统计
get_error_stats(ErrorType) ->
    gen_server:call(?MODULE, {get_error_stats, ErrorType}).

%% @doc 重置错误统计
reset_error_stats() ->
    gen_server:call(?MODULE, reset_error_stats).

%% @doc 启用故障注入
enable_fault_injection(Config) ->
    gen_server:call(?MODULE, {enable_fault_injection, Config}).

%% @doc 禁用故障注入
disable_fault_injection() ->
    gen_server:call(?MODULE, disable_fault_injection).

%% @doc 注册错误处理器
%% 注册一个特定类型错误的处理器函数
%% @param ErrorType 错误类型
%% @param Handler 处理器函数
%% @param Options 处理器选项
register_error_handler(ErrorType, Handler, Options) ->
    try
        % 将错误处理器存储到专用的ETS表中（确保表存在）
        case ets:info(?ERROR_HANDLER_REGISTRY) of
            undefined ->
                % ETS表还未创建，先创建表
                ets:new(?ERROR_HANDLER_REGISTRY, [named_table, public, {write_concurrency, true}]);
            _ ->
                ok
        end,
        HandlerInfo = #{
            handler => Handler,
            options => Options,
            registered_at => erlang:system_time(millisecond)
        },
        ets:insert(?ERROR_HANDLER_REGISTRY, {ErrorType, HandlerInfo}),
        ?SLOG(info, #{
            msg => "error_handler_registered",
            error_type => ErrorType,
            options => Options
        }),
        ok
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "failed_to_register_error_handler",
                error_type => ErrorType,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%% @doc 集成到协调器
integrate() ->
    ?SLOG(info, #{msg => "integrating_error_handler_module"}),
    % 将此模块注册到协调器
    case erlang:function_exported(emqx_plugin_mongodb_coordinator, register_module, 2) of
        true ->
            emqx_plugin_mongodb_coordinator:register_module(?MODULE, #{
                priority => high,
                description => <<"Error handling module">>,
                features => [error_classification, auto_recovery, fault_tolerance]
            });
        false ->
            ok
    end.

%%%===================================================================
%%% gen_server callbacks
%%%===================================================================

%% @doc 初始化回调
init([Options]) ->
    % 创建ETS表（检查是否已存在）
    case ets:info(?ERROR_REGISTRY) of
        undefined ->
            ets:new(?ERROR_REGISTRY, [named_table, public, {write_concurrency, true}]);
        _ ->
            ok
    end,
    case ets:info(?ERROR_HANDLER_REGISTRY) of
        undefined ->
            ets:new(?ERROR_HANDLER_REGISTRY, [named_table, public, {write_concurrency, true}]);
        _ ->
            ok
    end,
    case ets:info(?ERROR_STATS) of
        undefined ->
            ets:new(?ERROR_STATS, [named_table, public, {write_concurrency, true}]);
        _ ->
            ok
    end,

    % 初始化错误统计
    init_error_stats(),

    % 启动恢复工作进程
    spawn_link(?MODULE, recovery_worker, []),

    % 启动故障注入工作进程
    spawn_link(?MODULE, fault_injection_worker, []),

    % 初始化故障注入配置
    FaultInjection = #fault_injection{},

    % 存储故障注入配置
    persistent_term:put(mongodb_fault_injection, FaultInjection),

    {ok, #{
        options => Options,
        recovery_interval => maps:get(recovery_interval, Options, ?RECOVERY_INTERVAL),
        fault_injection => FaultInjection
    }}.

%% @doc 处理同步调用
handle_call(get_error_stats, _From, State) ->
    % 获取所有错误统计
    Stats = get_all_error_stats(),
    {reply, {ok, Stats}, State};

handle_call({get_error_stats, ErrorType}, _From, State) ->
    % 获取特定类型的错误统计
    Stats = get_error_stats_by_type(ErrorType),
    {reply, {ok, Stats}, State};

handle_call(reset_error_stats, _From, State) ->
    % 重置错误统计
    reset_all_error_stats(),
    {reply, ok, State};

handle_call({inject_fault, ErrorTypes, Probability, Duration}, _From, State) ->
    % 注入故障
    FaultInjection = #fault_injection{
        enabled = true,
        error_types = ErrorTypes,
        probability = Probability,
        duration = Duration,
        start_time = erlang:system_time(millisecond),
        end_time = erlang:system_time(millisecond) + Duration
    },

    % 更新状态
    NewState = State#{fault_injection => FaultInjection},

    % 存储故障注入配置
    persistent_term:put(mongodb_fault_injection, FaultInjection),

    {reply, ok, NewState};

handle_call({enable_fault_injection, Config}, _From, State) ->
    % 获取当前故障注入配置
    FaultInjection = maps:get(fault_injection, State),

    % 更新配置
    NewFaultInjection = FaultInjection#fault_injection{
        enabled = true,
        error_types = maps:get(error_types, Config, FaultInjection#fault_injection.error_types),
        probability = maps:get(probability, Config, FaultInjection#fault_injection.probability),
        target_modules = maps:get(target_modules, Config, FaultInjection#fault_injection.target_modules),
        duration = maps:get(duration, Config, FaultInjection#fault_injection.duration),
        start_time = erlang:system_time(millisecond),
        end_time = erlang:system_time(millisecond) + maps:get(duration, Config, FaultInjection#fault_injection.duration)
    },

    % 更新状态
    NewState = State#{fault_injection => NewFaultInjection},

    % 存储故障注入配置
    persistent_term:put(mongodb_fault_injection, NewFaultInjection),

    {reply, ok, NewState};

handle_call(disable_fault_injection, _From, State) ->
    % 获取当前故障注入配置
    FaultInjection = maps:get(fault_injection, State),

    % 禁用故障注入
    NewFaultInjection = FaultInjection#fault_injection{
        enabled = false
    },

    % 更新状态
    NewState = State#{fault_injection => NewFaultInjection},

    % 存储故障注入配置
    persistent_term:put(mongodb_fault_injection, NewFaultInjection),

    {reply, ok, NewState};

handle_call(stop, _From, State) ->
    {stop, normal, ok, State};

handle_call(_Request, _From, State) ->
    {reply, {error, unknown_call}, State}.

%% @doc 处理异步调用
handle_cast({handle_error, ErrorType, Source, Details}, State) ->
    % 分类错误
    {ClassifiedType, Severity} = classify_error({error, ErrorType}),

    % 创建错误记录
    ErrorId = generate_error_id(),
    ErrorRecord = #error_record{
        id = ErrorId,
        timestamp = erlang:system_time(millisecond),
        error_type = ClassifiedType,
        severity = Severity,
        source = Source,
        details = Details,
        context = get_error_context()
    },

    % 存储错误记录
    ets:insert(?ERROR_REGISTRY, {ErrorId, ErrorRecord}),

    % 更新错误统计
    update_error_stats(ClassifiedType, Severity),

    % 对严重错误进行紧急处理
    case Severity of
        ?SEVERITY_CRITICAL ->
            % 立即尝试恢复
            spawn(fun() -> attempt_recovery(ErrorId, ErrorRecord) end);
        _ ->
            % 其他错误通过定期恢复进程处理
            ok
    end,

    {noreply, State};

handle_cast(_Msg, State) ->
    {noreply, State}.

%% @doc 处理消息
handle_info(_Info, State) ->
    {noreply, State}.

%% @doc 终止回调
terminate(_Reason, _State) ->
    % 删除ETS表
    catch ets:delete(?ERROR_REGISTRY),
    catch ets:delete(?ERROR_STATS),
    ok.

%% @doc 代码更新回调
code_change(_OldVsn, State, _Extra) ->
    {ok, State}.

%%%===================================================================
%%% 内部函数
%%%===================================================================

%% @doc 初始化错误统计
init_error_stats() ->
    % 初始化各种错误类型的统计
    ErrorTypes = [
        ?ERROR_TYPE_CONNECTION,
        ?ERROR_TYPE_AUTHENTICATION,
        ?ERROR_TYPE_TIMEOUT,
        ?ERROR_TYPE_QUERY,
        ?ERROR_TYPE_NETWORK,
        ?ERROR_TYPE_SYSTEM,
        ?ERROR_TYPE_UNKNOWN
    ],

    % 初始化严重程度统计
    Severities = [
        ?SEVERITY_CRITICAL,
        ?SEVERITY_MAJOR,
        ?SEVERITY_MINOR,
        ?SEVERITY_WARNING,
        ?SEVERITY_INFO
    ],

    % 初始化错误类型统计
    [ets:insert(?ERROR_STATS, {{error_type, Type}, 0}) || Type <- ErrorTypes],

    % 初始化严重程度统计
    [ets:insert(?ERROR_STATS, {{severity, Severity}, 0}) || Severity <- Severities],

    % 初始化总错误计数
    ets:insert(?ERROR_STATS, {total_errors, 0}),

    % 初始化恢复统计
    ets:insert(?ERROR_STATS, {recovery_attempts, 0}),
    ets:insert(?ERROR_STATS, {successful_recoveries, 0}).

%% @doc 更新错误统计
update_error_stats(ErrorType, Severity) ->
    % 防御性检查：确保ETS表存在
    case ets:info(?ERROR_STATS) of
        undefined ->
            % ETS表不存在，尝试重新创建
            try
                ets:new(?ERROR_STATS, [named_table, public, {write_concurrency, true}]),
                init_error_stats()
            catch
                _:_ -> ok % 可能已经创建，忽略错误
            end;
        _ -> ok
    end,

    % 使用try-catch保护ETS操作
    try
    % 更新错误类型计数
    ets:update_counter(?ERROR_STATS, {error_type, ErrorType}, 1),

    % 更新严重程度计数
    ets:update_counter(?ERROR_STATS, {severity, Severity}, 1),

    % 更新总错误计数
        ets:update_counter(?ERROR_STATS, total_errors, 1)
    catch
        _:_ ->
            % ETS操作失败，尝试重新初始化
            try
                init_error_stats(),
                % 重试更新
                ets:update_counter(?ERROR_STATS, {error_type, ErrorType}, 1),
                ets:update_counter(?ERROR_STATS, {severity, Severity}, 1),
                ets:update_counter(?ERROR_STATS, total_errors, 1)
            catch
                _:_ -> ok % 忽略错误，避免崩溃
            end
    end.

%% @doc 获取所有错误统计
get_all_error_stats() ->
    % 防御性检查：确保ETS表存在
    case ets:info(?ERROR_STATS) of
        undefined ->
            % ETS表不存在，返回空统计
            #{
                by_type => #{},
                by_severity => #{},
                total => 0,
                recovery => #{
                    attempts => 0,
                    successful => 0,
                    success_rate => 0.0
                }
            };
        _ ->
            % 使用try-catch保护ETS操作
            try
    % 获取错误类型统计
    ErrorTypes = [
        ?ERROR_TYPE_CONNECTION,
        ?ERROR_TYPE_AUTHENTICATION,
        ?ERROR_TYPE_TIMEOUT,
        ?ERROR_TYPE_QUERY,
        ?ERROR_TYPE_NETWORK,
        ?ERROR_TYPE_SYSTEM,
        ?ERROR_TYPE_UNKNOWN
    ],
                TypeStats = get_type_stats(ErrorTypes, []),

    % 获取严重程度统计
    Severities = [
        ?SEVERITY_CRITICAL,
        ?SEVERITY_MAJOR,
        ?SEVERITY_MINOR,
        ?SEVERITY_WARNING,
        ?SEVERITY_INFO
    ],
                SeverityStats = get_severity_stats(Severities, []),

    % 获取总错误计数
                TotalErrors =
                    try
                        ets:lookup_element(?ERROR_STATS, total_errors, 2)
                    catch
                        _:_ -> 0
                    end,

    % 获取恢复统计
                RecoveryAttempts =
                    try
                        ets:lookup_element(?ERROR_STATS, recovery_attempts, 2)
                    catch
                        _:_ -> 0
                    end,
                SuccessfulRecoveries =
                    try
                        ets:lookup_element(?ERROR_STATS, successful_recoveries, 2)
                    catch
                        _:_ -> 0
                    end,

                % 安全计算成功率，避免除零
                SuccessRate =
                    if
                        RecoveryAttempts =:= 0 -> 0.0;
                        true -> SuccessfulRecoveries / RecoveryAttempts
                    end,

    % 返回统计数据
    #{
        by_type => maps:from_list(TypeStats),
        by_severity => maps:from_list(SeverityStats),
        total => TotalErrors,
        recovery => #{
            attempts => RecoveryAttempts,
            successful => SuccessfulRecoveries,
                        success_rate => SuccessRate
                    }
                }
            catch
                _:_ ->
                    % 发生错误，返回空统计
                    #{
                        by_type => #{},
                        by_severity => #{},
                        total => 0,
                        recovery => #{
                            attempts => 0,
                            successful => 0,
                            success_rate => 0.0
                        }
                    }
            end
    end.

%% @doc 辅助函数：安全获取错误类型统计
get_type_stats([], Acc) ->
    Acc;
get_type_stats([Type|Rest], Acc) ->
    Count =
        try
            ets:lookup_element(?ERROR_STATS, {error_type, Type}, 2)
        catch
            _:_ -> 0
        end,
    get_type_stats(Rest, [{Type, Count}|Acc]).

%% @doc 辅助函数：安全获取严重程度统计
get_severity_stats([], Acc) ->
    Acc;
get_severity_stats([Severity|Rest], Acc) ->
    Count =
        try
            ets:lookup_element(?ERROR_STATS, {severity, Severity}, 2)
        catch
            _:_ -> 0
        end,
    get_severity_stats(Rest, [{Severity, Count}|Acc]).

%% @doc 获取特定类型的错误统计
get_error_stats_by_type(ErrorType) ->
    % 获取错误类型计数
    Count = ets:lookup_element(?ERROR_STATS, {error_type, ErrorType}, 2),

    % 获取该类型的所有错误记录
    Errors = ets:foldl(
        fun({_, Error}, Acc) when is_record(Error, error_record) andalso
                                  Error#error_record.error_type =:= ErrorType ->
            [Error | Acc];
           (_, Acc) ->
            Acc
        end,
        [],
        ?ERROR_REGISTRY
    ),

    % 按时间排序
    SortedErrors = lists:sort(
        fun(A, B) -> A#error_record.timestamp >= B#error_record.timestamp end,
        Errors
    ),

    % 返回统计数据
    #{
        count => Count,
        recent_errors => lists:sublist(SortedErrors, 10)
    }.

%% @doc 重置所有错误统计
reset_all_error_stats() ->
    % 删除所有统计
    ets:delete_all_objects(?ERROR_STATS),

    % 重新初始化
    init_error_stats().

%% @doc 生成错误ID
generate_error_id() ->
    erlang:unique_integer([positive]).

%% @doc 获取错误上下文
get_error_context() ->
    % 获取进程信息
    ProcInfo = process_info(self(), [registered_name, current_function, memory]),

    % 获取系统信息
    SysInfo = #{
        time => calendar:local_time(),
        node => node(),
        memory_usage => erlang:memory(total) / (1024 * 1024) % MB
    },

    % 返回上下文
    #{
        process => ProcInfo,
        system => SysInfo
    }.

%% @doc 恢复工作进程
recovery_worker() ->
    % 定期检查并尝试恢复未解决的错误
    recovery_loop().

%% @doc 恢复循环
recovery_loop() ->
    try
        % 检查ETS表是否存在
        case ets:info(?ERROR_REGISTRY) of
            undefined ->
                % ETS表不存在，可能插件正在停止，退出循环
                ?SLOG(info, #{
                    msg => "error_registry_table_not_exists_stopping_recovery_loop",
                    table => ?ERROR_REGISTRY
                }),
                ok;
            _ ->
                % ETS表存在，继续恢复流程
                try
                    % 查找需要恢复的错误
                    ets:foldl(
                        fun({ErrorId, Error}, _) ->
                            % 检查是否是错误记录（而不是错误处理器）
                            case is_record(Error, error_record) of
                                true ->
                                    % 检查是否需要恢复
                                    case Error#error_record.recovered of
                                        false ->
                                            % 尝试恢复
                                            attempt_recovery(ErrorId, Error);
                                        true ->
                                            % 已恢复，不做任何操作
                                            ok
                                    end;
                                false ->
                                    % 不是错误记录，跳过
                                    ok
                            end
                        end,
                        ok,
                        ?ERROR_REGISTRY
                    ),

                    % 等待下一个恢复周期
                    timer:sleep(?RECOVERY_INTERVAL),

                    % 继续循环
                    recovery_loop()
                catch
                    error:badarg ->
                        % ETS表在操作过程中被删除，停止循环
                        ?SLOG(info, #{
                            msg => "error_registry_table_deleted_during_operation_stopping_recovery_loop",
                            table => ?ERROR_REGISTRY
                        }),
                        ok;
                    E:R:S ->
                        ?SLOG(error, #{
                            msg => "error_in_recovery_loop",
                            error => E,
                            reason => R,
                            stacktrace => S
                        }),
                        % 等待一段时间后重试
                        timer:sleep(?RECOVERY_INTERVAL),
                        recovery_loop()
                end
        end
    catch
        E2:R2:S2 ->
            ?SLOG(error, #{
                msg => "critical_error_in_recovery_loop",
                error => E2,
                reason => R2,
                stacktrace => S2
            }),
            % 停止恢复循环
            ok
    end.

%% @doc 尝试恢复错误
attempt_recovery(ErrorId, Error) ->
    % 防御性检查：确保ETS表存在
    case ets:info(?ERROR_STATS) of
        undefined ->
            % ETS表不存在，尝试重新创建
            try
                ets:new(?ERROR_STATS, [named_table, public, {write_concurrency, true}]),
                init_error_stats()
            catch
                _:_ -> ok % 可能已经创建，忽略错误
            end;
        _ -> ok
    end,

    % 增加恢复尝试计数
    try
        ets:update_counter(?ERROR_STATS, recovery_attempts, 1)
    catch
        _:_ -> ok % 忽略错误，继续尝试恢复
    end,

    % 根据错误类型选择恢复策略
    % 添加超时保护
    Result =
        try
            % 设置超时时间为5秒，防止恢复操作阻塞
            {TRef, MonRef} = setup_timeout(5000),

            RecoveryResult =
                try
                    case Error#error_record.error_type of
        ?ERROR_TYPE_CONNECTION ->
            recover_connection_error(Error);
        ?ERROR_TYPE_AUTHENTICATION ->
            recover_authentication_error(Error);
        ?ERROR_TYPE_TIMEOUT ->
            recover_timeout_error(Error);
        ?ERROR_TYPE_QUERY ->
            recover_query_error(Error);
        ?ERROR_TYPE_NETWORK ->
            recover_network_error(Error);
        ?ERROR_TYPE_SYSTEM ->
            recover_system_error(Error);
        _ ->
            {error, unknown_error_type}
                    end
                after
                    % 清理超时设置
                    cleanup_timeout(TRef, MonRef)
                end,

            RecoveryResult
        catch
            timeout ->
                {error, recovery_timeout};
            Class:Reason:_Stack ->
                {error, {recovery_failed, {Class, Reason}}}
    end,

    % 处理恢复结果
    case Result of
        ok ->
            % 恢复成功
            try
                ets:update_counter(?ERROR_STATS, successful_recoveries, 1)
            catch
                _:_ -> ok % 忽略错误
            end,

            % 更新错误记录
            NewError = Error#error_record{
                recovered = true,
                recovery_time = erlang:system_time(millisecond),
                recovery_attempts = Error#error_record.recovery_attempts + 1
            },

            try
                ets:insert(?ERROR_REGISTRY, {ErrorId, NewError})
            catch
                _:_ -> ok % 忽略错误
            end;
        {error, _} ->
            % 恢复失败，更新尝试次数
            NewError = Error#error_record{
                recovery_attempts = Error#error_record.recovery_attempts + 1
            },

            try
            ets:insert(?ERROR_REGISTRY, {ErrorId, NewError})
            catch
                _:_ -> ok % 忽略错误
            end
    end.

%% @doc 设置超时保护
setup_timeout(Timeout) ->
    Self = self(),
    TRef = erlang:start_timer(Timeout, Self, recovery_timeout),
    % 使用监控进程而不是直接接收消息，避免消息丢失
    MonPid = spawn(fun() ->
        receive
            {timeout, TRef, _} ->
                Self ! timeout,
                exit(normal);
            {cancel, _Ref} ->
                exit(normal)
        end
    end),
    MonRef = erlang:monitor(process, MonPid),
    {TRef, MonRef}.

%% @doc 清理超时设置
cleanup_timeout(TRef, MonRef) ->
    % 取消定时器
    erlang:cancel_timer(TRef),
    % 接收可能已经发送的超时消息
    receive
        {timeout, TRef, _} -> ok
    after 0 -> ok
    end,
    % 清理监控
    erlang:demonitor(MonRef, [flush]),
    ok.

%% @doc 恢复连接错误
recover_connection_error(Error) ->
    % 尝试重新连接
    try
        % 调用连接器模块的重连函数，添加超时保护
        {TRef, MonRef} = setup_timeout(3000),
        try
        emqx_plugin_mongodb_connector:reconnect(),
        ok
        after
            cleanup_timeout(TRef, MonRef)
        end
    catch
        timeout ->
            {error, reconnect_timeout};
        _:Reason ->
            {error, Reason}
    end.

%% @doc 恢复认证错误
recover_authentication_error(_Error) ->
    % 认证错误通常需要人工干预，但可以尝试重新加载凭证
    {error, requires_manual_intervention}.

%% @doc 恢复超时错误
recover_timeout_error(_Error) ->
    % 超时错误通常是临时的，可以通过刷新拓扑来解决
    try
        emqx_plugin_mongodb_connector:refresh_topology(),
        ok
    catch
        _:Reason ->
            {error, Reason}
    end.

%% @doc 恢复查询错误
recover_query_error(_Error) ->
    % 查询错误可能是临时的，客户端可以重试
    {ok, retry_recommended}.

%% @doc 恢复网络错误
recover_network_error(_Error) ->
    % 网络错误通常需要重新连接
    try
        emqx_plugin_mongodb_connector:reconnect(),
        ok
    catch
        _:Reason ->
            {error, Reason}
    end.

%% @doc 恢复系统错误
recover_system_error(_Error) ->
    % 系统错误通常需要重启服务
    {error, requires_service_restart}.

%% @doc 故障注入工作进程
fault_injection_worker() ->
    % 定期执行故障注入测试
    fault_injection_loop().

%% @doc 故障注入循环
fault_injection_loop() ->
    % 检查是否启用故障注入
    case persistent_term:get(mongodb_fault_injection, #fault_injection{}) of
        #fault_injection{enabled = true} = FI ->
            % 检查是否在有效期内
            Now = erlang:system_time(millisecond),
            if
                Now < FI#fault_injection.end_time ->
                    % 执行故障注入
                    inject_random_fault(FI);
                true ->
                    % 超过有效期，禁用故障注入
                    NewFI = FI#fault_injection{enabled = false},
                    persistent_term:put(mongodb_fault_injection, NewFI)
            end;
        _ ->
            % 未启用故障注入
            ok
    end,

    % 等待下一个故障注入周期
    timer:sleep(?FAULT_INJECTION_INTERVAL),

    % 继续循环
    fault_injection_loop().

%% @doc 注入随机故障
inject_random_fault(FI) ->
    % 根据概率决定是否注入故障
    case rand:uniform() < FI#fault_injection.probability of
        true ->
            % 选择一个错误类型
            ErrorTypes = FI#fault_injection.error_types,
            ErrorType = lists:nth(rand:uniform(length(ErrorTypes)), ErrorTypes),

            % 注入故障
            inject_specific_fault(ErrorType);
        false ->
            % 不注入故障
            ok
    end.

%% @doc 注入特定类型的故障
inject_specific_fault(?ERROR_TYPE_CONNECTION) ->
    % 模拟连接错误
    handle_error({connection_error, simulated}, ?MODULE, #{
        reason => "Simulated connection error for testing",
        injected => true
    });
inject_specific_fault(?ERROR_TYPE_TIMEOUT) ->
    % 模拟超时错误
    handle_error(timeout, ?MODULE, #{
        reason => "Simulated timeout error for testing",
        injected => true
    });
inject_specific_fault(?ERROR_TYPE_QUERY) ->
    % 模拟查询错误
    handle_error({query_error, simulated}, ?MODULE, #{
        reason => "Simulated query error for testing",
        injected => true
    });
inject_specific_fault(?ERROR_TYPE_NETWORK) ->
    % 模拟网络错误
    handle_error({network_error, simulated}, ?MODULE, #{
        reason => "Simulated network error for testing",
        injected => true
    });
inject_specific_fault(_) ->
    % 默认模拟系统错误
    handle_error({system_error, simulated}, ?MODULE, #{
        reason => "Simulated system error for testing",
        injected => true
    }).
