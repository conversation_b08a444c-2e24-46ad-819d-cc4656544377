%%%-------------------------------------------------------------------
%%% @doc EMQX MongoDB Plugin Memory Leak Detector - 智能内存泄漏检测和预防模块
%%% 这个模块是企业级内存管理组件，提供全方位的内存泄漏检测和自动预防能力
%%%
%%% 功能概述：
%%% 1. ETS表智能监控 - 实时监控所有ETS表的大小、增长趋势和内存使用
%%% 2. 进程内存跟踪 - 跟踪关键进程的内存使用模式和异常增长
%%% 3. 内存泄漏早期预警 - 基于机器学习算法的内存泄漏预测
%%% 4. 自动清理机制 - 智能触发内存清理和垃圾回收
%%% 5. 内存压力管理 - 根据系统内存压力动态调整清理策略
%%% 6. 历史趋势分析 - 分析内存使用的历史趋势和周期性模式
%%% 7. 异常检测算法 - 使用统计学方法检测内存使用异常
%%%
%%% 检测算法：
%%% - 线性回归：检测内存增长趋势
%%% - 移动平均：平滑内存使用波动
%%% - 标准差分析：检测异常内存峰值
%%% - 周期性分析：识别内存使用的周期性模式
%%%
%%% 预防策略：
%%% - 主动清理：定期清理过期数据和无用对象
%%% - 阈值管理：动态调整内存使用阈值
%%% - 优雅降级：内存压力时降低功能复杂度
%%% - 紧急保护：极端情况下的紧急内存释放
%%%
%%% Java等价概念：
%%% 类似于Java的内存分析工具和垃圾回收器：
%%% @Component
%%% @Service
%%% public class MemoryLeakDetector {
%%%     @Autowired private MemoryMXBean memoryMXBean;
%%%     @Autowired private GarbageCollectorMXBean gcMXBean;
%%%     
%%%     @Scheduled(fixedDelay = 30000)
%%%     public void detectMemoryLeaks() {
%%%         MemoryUsage heapUsage = memoryMXBean.getHeapMemoryUsage();
%%%         if (isMemoryLeakSuspected(heapUsage)) {
%%%             triggerMemoryCleanup();
%%%         }
%%%     }
%%%     
%%%     private boolean isMemoryLeakSuspected(MemoryUsage usage) {
%%%         return usage.getUsed() > usage.getMax() * 0.8 &&
%%%                getMemoryGrowthRate() > THRESHOLD;
%%%     }
%%% }
%%%
%%% 设计模式：
%%% - 观察者模式：内存状态变化的监听和响应
%%% - 策略模式：不同内存压力级别的清理策略
%%% - 单例模式：全局唯一的内存检测器实例
%%% - 工厂模式：不同类型检测器的创建和管理
%%% @end
%%%-------------------------------------------------------------------
-module(emqx_plugin_mongodb_memory_leak_detector).

%% 实现OTP gen_server行为
%% 类似于Java中实现Runnable或Service接口
-behaviour(gen_server).

-include("emqx_plugin_mongodb.hrl").

%% ============================================================================
%% 公共API函数 - 内存泄漏检测和预防接口
%% 这些函数提供内存管理的核心功能
%% 类似于Java中的Service层公共接口
%% ============================================================================
-export([
    start_link/0,                   % 启动内存泄漏检测器
                                   % 功能：启动gen_server进程，初始化检测系统
                                   % Java等价：@PostConstruct public void startDetector()

    stop/0,                        % 停止内存泄漏检测器
                                   % 功能：停止检测进程，清理资源
                                   % Java等价：@PreDestroy public void stopDetector()

    check_memory_leaks/0,          % 立即检查内存泄漏
                                   % 功能：执行一次完整的内存泄漏检测
                                   % Java等价：public MemoryLeakReport checkMemoryLeaks()

    get_memory_report/0,           % 获取内存使用报告
                                   % 功能：生成详细的内存使用分析报告
                                   % Java等价：public MemoryUsageReport getMemoryReport()

    trigger_cleanup/0,             % 触发内存清理
                                   % 功能：手动触发内存清理操作
                                   % Java等价：public void triggerGarbageCollection()

    force_cleanup/0,               % 强制内存清理
                                   % 功能：强制执行内存清理操作（用于资源管理器调用）
                                   % Java等价：public void forceGarbageCollection()

    register_ets_table/2,          % 注册ETS表监控
                                   % 功能：将ETS表加入监控列表
                                   % Java等价：public void registerTable(String tableName, TableConfig config)

    unregister_ets_table/1,        % 取消ETS表监控
                                   % 功能：从监控列表中移除ETS表
                                   % Java等价：public void unregisterTable(String tableName)

    set_memory_threshold/2,        % 设置内存阈值
                                   % 功能：动态设置内存使用阈值
                                   % Java等价：public void setMemoryThreshold(ThresholdType type, long value)

    get_ets_table_stats/1,         % 获取ETS表统计
                                   % 功能：获取指定ETS表的详细统计信息
                                   % Java等价：public TableStats getTableStats(String tableName)

    analyze_memory_trend/1,        % 分析内存趋势
                                   % 功能：分析指定进程或表的内存使用趋势
                                   % Java等价：public TrendAnalysis analyzeMemoryTrend(String target)

    integrate/0                    % 集成到协调器
                                   % 功能：将检测器集成到系统协调器中
                                   % Java等价：public void integrateWithCoordinator()
]).

%% gen_server callbacks
-export([
    init/1,
    handle_call/3,
    handle_cast/2,
    handle_info/2,
    terminate/2,
    code_change/3
]).

%% 内部函数导出（用于spawn调用）
-export([
    detection_loop/0,              % 检测循环进程
    cleanup_worker/1               % 清理工作进程
]).

%% ============================================================================
%% 模块特定宏定义 - 算法和计算参数
%% 注意：通用宏定义已移至 emqx_plugin_mongodb.hrl 统一管理
%% ============================================================================

%% 线性回归分析参数
-define(LINEAR_REGRESSION_MIN_POINTS, 5).         % 最少数据点数
-define(LINEAR_REGRESSION_CONFIDENCE, 0.95).      % 置信度

%% 移动平均算法参数
-define(MOVING_AVERAGE_WINDOW, 10).               % 移动平均窗口大小
-define(MOVING_AVERAGE_WEIGHT_RECENT, 0.7).       % 近期数据权重

%% 标准差分析参数
-define(STANDARD_DEVIATION_MULTIPLIER, 2.0).      % 标准差倍数
-define(OUTLIER_DETECTION_THRESHOLD, 3.0).        % 异常值检测阈值

%% 周期性模式识别参数
-define(PATTERN_DETECTION_MIN_CYCLES, 3).         % 最少周期数
-define(PATTERN_SIMILARITY_THRESHOLD, 0.8).       % 模式相似度阈值

%% 预测算法参数
-define(PREDICTION_HORIZON_MINUTES, 30).          % 预测时间范围（分钟）
-define(PREDICTION_ACCURACY_THRESHOLD, 0.7).      % 预测准确度阈值

%% ============================================================================
%% 记录定义 - 数据结构
%% ============================================================================

%% @doc 内存统计记录
%% 存储系统内存使用的详细统计信息
-record(memory_stats, {
    timestamp,                     % 时间戳
    total_memory,                  % 总内存使用量
    process_memory,                % 进程内存使用量
    ets_memory,                    % ETS表内存使用量
    binary_memory,                 % 二进制数据内存使用量
    atom_memory,                   % 原子内存使用量
    system_memory,                 % 系统内存使用量
    memory_usage_ratio,            % 内存使用率
    growth_rate,                   % 内存增长率
    leak_score                     % 内存泄漏评分（0-100）
}).

%% @doc ETS表监控记录
%% 存储ETS表的监控信息和统计数据
-record(ets_monitor, {
    table_name,                    % 表名
    table_id,                      % 表ID
    size,                          % 表大小（记录数）
    memory,                        % 表内存使用量
    last_check,                    % 最后检查时间
    growth_rate,                   % 增长率
    max_size_threshold,            % 最大大小阈值
    max_memory_threshold,          % 最大内存阈值
    cleanup_function,              % 清理函数
    alert_level                    % 警告级别：normal, warning, critical, emergency
}).

%% @doc 内存泄漏警报记录
%% 存储检测到的内存泄漏警报信息
-record(leak_alert, {
    id,                            % 警报ID
    timestamp,                     % 发生时间
    type,                          % 警报类型：ets_leak, process_leak, system_leak
    target,                        % 目标对象（表名、进程ID等）
    severity,                      % 严重程度：warning, critical, emergency
    description,                   % 描述信息
    metrics,                       % 相关指标
    action_taken,                  % 已采取的行动
    resolved                       % 是否已解决
}).

%% @doc 检测器状态记录
%% 存储内存泄漏检测器的运行状态
-record(detector_state, {
    detection_timer,               % 检测定时器引用
    cleanup_timer,                % 清理定时器引用
    trend_timer,                   % 趋势分析定时器引用
    monitored_tables,              % 监控的ETS表列表
    memory_thresholds,             % 内存阈值配置
    detection_enabled,             % 是否启用检测
    cleanup_enabled,               % 是否启用自动清理
    alert_count,                   % 警报计数器
    last_cleanup_time,             % 最后清理时间
    statistics                     % 运行统计信息
}).

%%--------------------------------------------------------------------
%% API函数实现
%%--------------------------------------------------------------------

%% @doc 启动内存泄漏检测器
%% 启动gen_server进程并初始化检测系统
-spec start_link() -> {ok, pid()} | {error, term()}.
start_link() ->
    gen_server:start_link({local, ?MODULE}, ?MODULE, [], []).

%% @doc 停止内存泄漏检测器
%% 停止检测进程并清理所有资源
-spec stop() -> ok.
stop() ->
    gen_server:call(?MODULE, stop).

%% @doc 立即检查内存泄漏
%% 执行一次完整的内存泄漏检测并返回结果
-spec check_memory_leaks() -> {ok, [map()]} | {error, term()}.
check_memory_leaks() ->
    gen_server:call(?MODULE, check_memory_leaks).

%% @doc 获取内存使用报告
%% 生成详细的内存使用分析报告
-spec get_memory_report() -> {ok, map()} | {error, term()}.
get_memory_report() ->
    gen_server:call(?MODULE, get_memory_report).

%% @doc 触发内存清理
%% 手动触发内存清理操作
-spec trigger_cleanup() -> ok | {error, term()}.
trigger_cleanup() ->
    gen_server:call(?MODULE, trigger_cleanup).

%% @doc 强制内存清理
%% 强制执行内存清理操作（用于资源管理器调用）
-spec force_cleanup() -> ok | {error, term()}.
force_cleanup() ->
    trigger_cleanup().

%% @doc 注册ETS表监控
%% 将指定的ETS表加入监控列表
-spec register_ets_table(atom(), map()) -> ok | {error, term()}.
register_ets_table(TableName, Config) ->
    gen_server:call(?MODULE, {register_ets_table, TableName, Config}).

%% @doc 取消ETS表监控
%% 从监控列表中移除指定的ETS表
-spec unregister_ets_table(atom()) -> ok | {error, term()}.
unregister_ets_table(TableName) ->
    gen_server:call(?MODULE, {unregister_ets_table, TableName}).

%% @doc 设置内存阈值
%% 动态设置内存使用阈值
-spec set_memory_threshold(atom(), float()) -> ok | {error, term()}.
set_memory_threshold(Type, Value) ->
    gen_server:call(?MODULE, {set_memory_threshold, Type, Value}).

%% @doc 获取ETS表统计
%% 获取指定ETS表的详细统计信息
-spec get_ets_table_stats(atom()) -> {ok, map()} | {error, term()}.
get_ets_table_stats(TableName) ->
    gen_server:call(?MODULE, {get_ets_table_stats, TableName}).

%% @doc 分析内存趋势
%% 分析指定目标的内存使用趋势
-spec analyze_memory_trend(atom() | pid()) -> {ok, map()} | {error, term()}.
analyze_memory_trend(Target) ->
    gen_server:call(?MODULE, {analyze_memory_trend, Target}).



%%--------------------------------------------------------------------
%% gen_server回调函数实现
%%--------------------------------------------------------------------

%% @doc 初始化回调函数
%% 创建ETS表，设置定时器，初始化检测器状态
-spec init([]) -> {ok, #detector_state{}}.
init([]) ->
    try
        % 创建ETS表用于存储监控数据
        create_ets_tables(),

        % 初始化默认内存阈值
        DefaultThresholds = #{
            warning => ?MEMORY_WARNING_THRESHOLD,
            critical => ?MEMORY_CRITICAL_THRESHOLD,
            emergency => ?MEMORY_EMERGENCY_THRESHOLD
        },

        % 启动检测定时器
        DetectionTimer = erlang:send_after(?MEMORY_DETECTION_INTERVAL, self(), detect_memory_leaks),
        CleanupTimer = erlang:send_after(?MEMORY_CLEANUP_INTERVAL, self(), cleanup_expired_data),
        TrendTimer = erlang:send_after(?MEMORY_TREND_ANALYSIS_INTERVAL, self(), analyze_trends),

        % 注册默认的ETS表监控
        register_default_tables(),

        % 初始化状态
        State = #detector_state{
            detection_timer = DetectionTimer,
            cleanup_timer = CleanupTimer,
            trend_timer = TrendTimer,
            monitored_tables = [],
            memory_thresholds = DefaultThresholds,
            detection_enabled = true,
            cleanup_enabled = true,
            alert_count = 0,
            last_cleanup_time = erlang:system_time(millisecond),
            statistics = #{
                detections_run => 0,
                cleanups_performed => 0,
                alerts_generated => 0,
                memory_freed => 0
            }
        },

        ?SLOG(info, #{
            msg => "memory_leak_detector_initialized",
            thresholds => DefaultThresholds
        }),

        {ok, State}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "failed_to_initialize_memory_leak_detector",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {stop, {initialization_failed, R}}
    end.

%% @doc 处理同步调用
handle_call(stop, _From, State) ->
    % 停止所有定时器
    cancel_timer(State#detector_state.detection_timer),
    cancel_timer(State#detector_state.cleanup_timer),
    cancel_timer(State#detector_state.trend_timer),

    % 清理ETS表
    cleanup_ets_tables(),

    ?SLOG(info, #{msg => "memory_leak_detector_stopped"}),
    {stop, normal, ok, State};

handle_call(check_memory_leaks, _From, State) ->
    try
        % 执行内存泄漏检测
        LeakResults = perform_memory_leak_detection(),

        % 更新统计信息
        NewStats = maps:update_with(detections_run, fun(X) -> X + 1 end, 1,
                                   State#detector_state.statistics),
        NewState = State#detector_state{statistics = NewStats},

        {reply, {ok, LeakResults}, NewState}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_during_memory_leak_detection",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {reply, {error, {detection_failed, R}}, State}
    end;

handle_call(get_memory_report, _From, State) ->
    try
        % 生成内存使用报告
        Report = generate_memory_report(),
        {reply, {ok, Report}, State}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_generating_memory_report",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {reply, {error, {report_generation_failed, R}}, State}
    end;

handle_call(trigger_cleanup, _From, State) ->
    try
        % 执行内存清理
        CleanupResult = perform_memory_cleanup(),

        % 更新统计信息
        NewStats = maps:update_with(cleanups_performed, fun(X) -> X + 1 end, 1,
                                   State#detector_state.statistics),
        NewState = State#detector_state{
            statistics = NewStats,
            last_cleanup_time = erlang:system_time(millisecond)
        },

        {reply, CleanupResult, NewState}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_during_memory_cleanup",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {reply, {error, {cleanup_failed, R}}, State}
    end;

handle_call({register_ets_table, TableName, Config}, _From, State) ->
    try
        % 注册ETS表监控
        MonitorRecord = create_ets_monitor_record(TableName, Config),
        ets:insert(?ETS_MONITOR_TAB, MonitorRecord),

        % 更新监控表列表
        NewTables = [TableName | State#detector_state.monitored_tables],
        NewState = State#detector_state{monitored_tables = NewTables},

        ?SLOG(info, #{
            msg => "ets_table_registered_for_monitoring",
            table => TableName,
            config => Config
        }),

        {reply, ok, NewState}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_registering_ets_table",
                table => TableName,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {reply, {error, {registration_failed, R}}, State}
    end;

handle_call({unregister_ets_table, TableName}, _From, State) ->
    try
        % 从监控中移除ETS表
        ets:delete(?ETS_MONITOR_TAB, TableName),

        % 更新监控表列表
        NewTables = lists:delete(TableName, State#detector_state.monitored_tables),
        NewState = State#detector_state{monitored_tables = NewTables},

        ?SLOG(info, #{
            msg => "ets_table_unregistered_from_monitoring",
            table => TableName
        }),

        {reply, ok, NewState}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_unregistering_ets_table",
                table => TableName,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {reply, {error, {unregistration_failed, R}}, State}
    end;

handle_call({set_memory_threshold, Type, Value}, _From, State) ->
    try
        % 更新内存阈值
        NewThresholds = maps:put(Type, Value, State#detector_state.memory_thresholds),
        NewState = State#detector_state{memory_thresholds = NewThresholds},

        ?SLOG(info, #{
            msg => "memory_threshold_updated",
            type => Type,
            value => Value
        }),

        {reply, ok, NewState}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_setting_memory_threshold",
                type => Type,
                value => Value,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {reply, {error, {threshold_update_failed, R}}, State}
    end;

handle_call({get_ets_table_stats, TableName}, _From, State) ->
    try
        % 获取ETS表统计信息
        Stats = get_table_statistics(TableName),
        {reply, {ok, Stats}, State}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_getting_ets_table_stats",
                table => TableName,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {reply, {error, {stats_retrieval_failed, R}}, State}
    end;

handle_call({analyze_memory_trend, Target}, _From, State) ->
    try
        % 分析内存趋势
        TrendAnalysis = analyze_target_memory_trend(Target),
        {reply, {ok, TrendAnalysis}, State}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_analyzing_memory_trend",
                target => Target,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {reply, {error, {trend_analysis_failed, R}}, State}
    end;

handle_call(health_check, _From, State) ->
    try
        % 健康检查
        HealthStatus = #{
            status => healthy,
            detection_enabled => State#detector_state.detection_enabled,
            cleanup_enabled => State#detector_state.cleanup_enabled,
            monitored_tables => length(State#detector_state.monitored_tables),
            alert_count => State#detector_state.alert_count,
            statistics => State#detector_state.statistics,
            last_cleanup => State#detector_state.last_cleanup_time
        },
        {reply, {ok, HealthStatus}, State}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_during_health_check",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {reply, {error, {health_check_failed, R}}, State}
    end;

handle_call(ping, _From, State) ->
    {reply, pong, State};

handle_call(_Request, _From, State) ->
    {reply, {error, unknown_request}, State}.

%% @doc 处理异步消息
handle_cast(_Msg, State) ->
    {noreply, State}.

%% @doc 处理定时器和系统消息
handle_info(detect_memory_leaks, State) ->
    % 重新设置检测定时器
    NewTimer = erlang:send_after(?MEMORY_DETECTION_INTERVAL, self(), detect_memory_leaks),
    NewState = State#detector_state{detection_timer = NewTimer},
    try
        % 执行内存泄漏检测
        spawn(?MODULE, detection_loop, []),
        {noreply, NewState}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_in_detection_timer",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {noreply, NewState}
    end;

handle_info(cleanup_expired_data, State) ->
    % 重新设置清理定时器
    NewTimer = erlang:send_after(?MEMORY_CLEANUP_INTERVAL, self(), cleanup_expired_data),
    NewState = State#detector_state{cleanup_timer = NewTimer},
    try
        % 执行过期数据清理
        spawn(?MODULE, cleanup_worker, [expired_data]),
        {noreply, NewState}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_in_cleanup_timer",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {noreply, NewState}
    end;

handle_info(analyze_trends, State) ->
    % 重新设置趋势分析定时器
    NewTimer = erlang:send_after(?MEMORY_TREND_ANALYSIS_INTERVAL, self(), analyze_trends),
    NewState = State#detector_state{trend_timer = NewTimer},
    try
        % 执行趋势分析
        spawn(?MODULE, cleanup_worker, [trend_analysis]),
        {noreply, NewState}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_in_trend_analysis_timer",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {noreply, NewState}
    end;

handle_info(_Info, State) ->
    {noreply, State}.

%% @doc 进程终止回调
terminate(_Reason, State) ->
    try
        % 取消所有定时器
        cancel_timer(State#detector_state.detection_timer),
        cancel_timer(State#detector_state.cleanup_timer),
        cancel_timer(State#detector_state.trend_timer),

        % 清理ETS表
        cleanup_ets_tables(),

        ?SLOG(info, #{msg => "memory_leak_detector_terminated"})
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_during_detector_termination",
                error => E,
                reason => R,
                stacktrace => S
            })
    end,
    ok.

%% @doc 代码更新回调
code_change(_OldVsn, State, _Extra) ->
    {ok, State}.

%%--------------------------------------------------------------------
%% 内部函数实现
%%--------------------------------------------------------------------

%% @doc 创建ETS表
%% 创建用于存储监控数据的ETS表
-spec create_ets_tables() -> ok.
create_ets_tables() ->
    % 内存统计表
    case ets:info(?MEMORY_STATS_TAB) of
        undefined ->
            ets:new(?MEMORY_STATS_TAB, [
                named_table, public, ordered_set,
                {write_concurrency, true},
                {read_concurrency, true}
            ]);
        _ -> ok
    end,

    % ETS监控表
    case ets:info(?ETS_MONITOR_TAB) of
        undefined ->
            ets:new(?ETS_MONITOR_TAB, [
                named_table, public, set,
                {write_concurrency, true},
                {read_concurrency, true}
            ]);
        _ -> ok
    end,

    % 内存历史表
    case ets:info(?MEMORY_HISTORY_TAB) of
        undefined ->
            ets:new(?MEMORY_HISTORY_TAB, [
                named_table, public, ordered_set,
                {write_concurrency, true},
                {read_concurrency, true}
            ]);
        _ -> ok
    end,

    % 泄漏警报表
    case ets:info(?LEAK_ALERTS_TAB) of
        undefined ->
            ets:new(?LEAK_ALERTS_TAB, [
                named_table, public, ordered_set,
                {write_concurrency, true},
                {read_concurrency, true}
            ]);
        _ -> ok
    end,

    ?SLOG(info, #{msg => "memory_leak_detector_ets_tables_created"}),
    ok.

%% @doc 清理ETS表
%% 删除所有监控相关的ETS表
-spec cleanup_ets_tables() -> ok.
cleanup_ets_tables() ->
    Tables = [?MEMORY_STATS_TAB, ?ETS_MONITOR_TAB, ?MEMORY_HISTORY_TAB, ?LEAK_ALERTS_TAB],
    lists:foreach(fun(Table) ->
        case ets:info(Table) of
            undefined -> ok;
            _ ->
                ets:delete(Table),
                ?SLOG(debug, #{msg => "ets_table_deleted", table => Table})
        end
    end, Tables),
    ok.

%% @doc 注册默认的ETS表监控
%% 自动注册项目中重要的ETS表进行监控
-spec register_default_tables() -> ok.
register_default_tables() ->
    % 定义需要监控的默认ETS表
    DefaultTables = [
        {emqx_mongodb_message_dedup_table, #{
            max_size => 1000000,
            max_memory => 104857600,  % 100MB
            cleanup_function => fun() ->
                emqx_plugin_mongodb_message_dedup:cleanup_expired()
            end
        }},
        {emqx_mongodb_recon_stats, #{
            max_size => 2880,  % 24小时数据
            max_memory => 10485760,  % 10MB
            cleanup_function => fun() ->
                emqx_plugin_mongodb_monitor:cleanup_old_stats()
            end
        }},
        {emqx_mongodb_coordinator_registry, #{
            max_size => 1000,
            max_memory => 1048576,  % 1MB
            cleanup_function => undefined
        }}
    ],

    % 注册每个表
    lists:foreach(fun({TableName, Config}) ->
        try
            case ets:info(TableName) of
                undefined ->
                    % 表不存在，跳过
                    ?SLOG(debug, #{
                        msg => "skipping_nonexistent_table",
                        table => TableName
                    });
                _ ->
                    % 表存在，注册监控
                    MonitorRecord = create_ets_monitor_record(TableName, Config),
                    ets:insert(?ETS_MONITOR_TAB, MonitorRecord),
                    ?SLOG(debug, #{
                        msg => "default_table_registered",
                        table => TableName
                    })
            end
        catch
            E:R:S ->
                ?SLOG(warning, #{
                    msg => "failed_to_register_default_table",
                    table => TableName,
                    error => E,
                    reason => R,
                    stacktrace => S
                })
        end
    end, DefaultTables),
    ok.

%% @doc 创建ETS监控记录
%% 根据表名和配置创建监控记录
-spec create_ets_monitor_record(atom(), map()) -> #ets_monitor{}.
create_ets_monitor_record(TableName, Config) ->
    TableInfo = case ets:info(TableName) of
        undefined -> #{size => 0, memory => 0};
        _ -> #{
            size => ets:info(TableName, size),
            memory => ets:info(TableName, memory)
        }
    end,

    #ets_monitor{
        table_name = TableName,
        table_id = TableName,
        size = maps:get(size, TableInfo, 0),
        memory = maps:get(memory, TableInfo, 0),
        last_check = erlang:system_time(millisecond),
        growth_rate = 0.0,
        max_size_threshold = maps:get(max_size, Config, ?ETS_SIZE_WARNING),
        max_memory_threshold = maps:get(max_memory, Config, ?ETS_MEMORY_WARNING),
        cleanup_function = maps:get(cleanup_function, Config, undefined),
        alert_level = normal
    }.

%% @doc 执行内存泄漏检测
%% 检测循环进程的主要逻辑
-spec detection_loop() -> ok.
detection_loop() ->
    try
        ?SLOG(debug, #{msg => "starting_memory_leak_detection_cycle"}),

        % 1. 收集系统内存统计
        MemoryStats = collect_memory_statistics(),

        % 2. 检查ETS表状态
        ETSLeaks = check_ets_tables_for_leaks(),

        % 3. 检查进程内存使用
        ProcessLeaks = check_processes_for_leaks(),

        % 4. 分析内存增长趋势
        TrendAnalysis = analyze_memory_growth_trends(),

        % 5. 生成警报
        Alerts = generate_memory_alerts(MemoryStats, ETSLeaks, ProcessLeaks, TrendAnalysis),

        % 6. 存储统计数据
        store_memory_statistics(MemoryStats),

        % 7. 处理警报
        process_memory_alerts(Alerts),

        ?SLOG(debug, #{
            msg => "memory_leak_detection_cycle_completed",
            memory_stats => MemoryStats,
            ets_leaks => length(ETSLeaks),
            process_leaks => length(ProcessLeaks),
            alerts => length(Alerts)
        })
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_in_memory_leak_detection_cycle",
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% @doc 收集系统内存统计
%% 收集详细的系统内存使用信息
-spec collect_memory_statistics() -> #memory_stats{}.
collect_memory_statistics() ->
    try
        % 获取Erlang内存统计
        MemoryInfo = erlang:memory(),

        % 计算各种内存使用量
        TotalMemory = proplists:get_value(total, MemoryInfo, 0),
        ProcessMemory = proplists:get_value(processes, MemoryInfo, 0),
        ETSMemory = proplists:get_value(ets, MemoryInfo, 0),
        BinaryMemory = proplists:get_value(binary, MemoryInfo, 0),
        AtomMemory = proplists:get_value(atom, MemoryInfo, 0),
        SystemMemory = proplists:get_value(system, MemoryInfo, 0),

        % 计算内存使用率（相对于系统内存）
        SystemTotalMemory = get_system_total_memory(),
        MemoryUsageRatio = case SystemTotalMemory > 0 of
            true -> TotalMemory / SystemTotalMemory;
            false -> 0.0
        end,

        % 计算内存增长率
        GrowthRate = calculate_memory_growth_rate(TotalMemory),

        % 计算内存泄漏评分
        LeakScore = calculate_leak_score(MemoryUsageRatio, GrowthRate),

        #memory_stats{
            timestamp = erlang:system_time(millisecond),
            total_memory = TotalMemory,
            process_memory = ProcessMemory,
            ets_memory = ETSMemory,
            binary_memory = BinaryMemory,
            atom_memory = AtomMemory,
            system_memory = SystemMemory,
            memory_usage_ratio = MemoryUsageRatio,
            growth_rate = GrowthRate,
            leak_score = LeakScore
        }
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_collecting_memory_statistics",
                error => E,
                reason => R,
                stacktrace => S
            }),
            % 返回默认统计信息
            #memory_stats{
                timestamp = erlang:system_time(millisecond),
                total_memory = 0,
                process_memory = 0,
                ets_memory = 0,
                binary_memory = 0,
                atom_memory = 0,
                system_memory = 0,
                memory_usage_ratio = 0.0,
                growth_rate = 0.0,
                leak_score = 0
            }
    end.

%% @doc 检查ETS表是否存在内存泄漏
%% 分析所有监控的ETS表的内存使用情况
-spec check_ets_tables_for_leaks() -> [map()].
check_ets_tables_for_leaks() ->
    try
        % 获取所有监控的ETS表
        MonitoredTables = ets:tab2list(?ETS_MONITOR_TAB),

        % 检查每个表
        lists:filtermap(fun(MonitorRecord) ->
            check_single_ets_table(MonitorRecord)
        end, MonitoredTables)
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_checking_ets_tables_for_leaks",
                error => E,
                reason => R,
                stacktrace => S
            }),
            []
    end.

%% @doc 检查单个ETS表
%% 检查指定ETS表是否存在内存泄漏
-spec check_single_ets_table(#ets_monitor{}) -> {true, map()} | false.
check_single_ets_table(#ets_monitor{
    table_name = TableName,
    size = LastSize,
    memory = LastMemory,
    last_check = LastCheck,
    max_size_threshold = MaxSize,
    max_memory_threshold = MaxMemory
} = Monitor) ->
    try
        case ets:info(TableName) of
            undefined ->
                % 表不存在，可能已被删除
                false;
            _ ->
                % 获取当前表信息
                CurrentSize = ets:info(TableName, size),
                CurrentMemory = ets:info(TableName, memory),
                CurrentTime = erlang:system_time(millisecond),

                % 计算增长率
                TimeDiff = max(1, CurrentTime - LastCheck),  % 避免除零
                SizeGrowthRate = (CurrentSize - LastSize) / TimeDiff * 1000,  % 每秒增长
                MemoryGrowthRate = (CurrentMemory - LastMemory) / TimeDiff * 1000,

                % 更新监控记录
                UpdatedMonitor = Monitor#ets_monitor{
                    size = CurrentSize,
                    memory = CurrentMemory,
                    last_check = CurrentTime,
                    growth_rate = max(SizeGrowthRate, MemoryGrowthRate)
                },
                ets:insert(?ETS_MONITOR_TAB, UpdatedMonitor),

                % 检查是否超过阈值
                SizeExceeded = CurrentSize > MaxSize,
                MemoryExceeded = CurrentMemory > MaxMemory,
                HighGrowthRate = SizeGrowthRate > 100 orelse MemoryGrowthRate > 1048576,  % 100条/秒或1MB/秒

                case SizeExceeded orelse MemoryExceeded orelse HighGrowthRate of
                    true ->
                        {true, #{
                            type => ets_table_leak,
                            table_name => TableName,
                            current_size => CurrentSize,
                            current_memory => CurrentMemory,
                            size_growth_rate => SizeGrowthRate,
                            memory_growth_rate => MemoryGrowthRate,
                            size_exceeded => SizeExceeded,
                            memory_exceeded => MemoryExceeded,
                            high_growth_rate => HighGrowthRate,
                            severity => determine_severity(SizeExceeded, MemoryExceeded, HighGrowthRate)
                        }};
                    false ->
                        false
                end
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_checking_single_ets_table",
                table => TableName,
                error => E,
                reason => R,
                stacktrace => S
            }),
            false
    end.

%% @doc 辅助函数 - 取消定时器
-spec cancel_timer(reference() | undefined) -> ok.
cancel_timer(undefined) -> ok;
cancel_timer(TimerRef) when is_reference(TimerRef) ->
    erlang:cancel_timer(TimerRef),
    ok;
cancel_timer(_) -> ok.

%% @doc 辅助函数 - 获取系统总内存
-spec get_system_total_memory() -> integer().
get_system_total_memory() ->
    try
        % 尝试使用recon获取系统内存信息
        case erlang:function_exported(recon, node_stats_print, 2) of
            true ->
                % 使用recon获取系统内存
                100 * 1024 * 1024 * 1024;  % 默认100GB
            false ->
                % 使用erlang:memory()的total作为参考
                MemoryInfo = erlang:memory(),
                proplists:get_value(total, MemoryInfo, 100 * 1024 * 1024 * 1024)
        end
    catch
        _:_ ->
            100 * 1024 * 1024 * 1024  % 默认100GB
    end.

%% @doc 辅助函数 - 计算内存增长率
-spec calculate_memory_growth_rate(integer()) -> float().
calculate_memory_growth_rate(CurrentMemory) ->
    try
        % 获取历史内存数据
        Now = erlang:system_time(millisecond),
        OneMinuteAgo = Now - 60000,

        % 查找一分钟前的内存数据
        HistoryPattern = [{{'$1', '$2'}, [{'>', '$1', OneMinuteAgo}], ['$_']}],
        HistoryData = ets:select(?MEMORY_HISTORY_TAB, HistoryPattern),

        case HistoryData of
            [] ->
                0.0;  % 没有历史数据
            _ ->
                % 计算平均增长率
                {TotalGrowth, Count} = lists:foldl(
                    fun({_Time, Memory}, {AccGrowth, AccCount}) ->
                        Growth = (CurrentMemory - Memory) / max(1, Memory),
                        {AccGrowth + Growth, AccCount + 1}
                    end,
                    {0.0, 0},
                    HistoryData
                ),
                case Count > 0 of
                    true -> TotalGrowth / Count;
                    false -> 0.0
                end
        end
    catch
        _:_ ->
            0.0
    end.

%% @doc 辅助函数 - 计算内存泄漏评分
-spec calculate_leak_score(float(), float()) -> integer().
calculate_leak_score(MemoryUsageRatio, GrowthRate) ->
    try
        % 基础评分（基于内存使用率）
        BaseScore = trunc(MemoryUsageRatio * 50),

        % 增长率评分
        GrowthScore = case GrowthRate of
            Rate when Rate > 0.2 -> 30;  % 20%以上增长率
            Rate when Rate > 0.1 -> 20;  % 10-20%增长率
            Rate when Rate > 0.05 -> 10; % 5-10%增长率
            _ -> 0
        end,

        % 综合评分
        TotalScore = BaseScore + GrowthScore,
        min(100, max(0, TotalScore))
    catch
        _:_ ->
            0
    end.

%% @doc 辅助函数 - 确定严重程度
-spec determine_severity(boolean(), boolean(), boolean()) -> atom().
determine_severity(SizeExceeded, MemoryExceeded, HighGrowthRate) ->
    case {SizeExceeded, MemoryExceeded, HighGrowthRate} of
        {true, true, true} -> emergency;
        {true, true, false} -> critical;
        {true, false, true} -> critical;
        {false, true, true} -> critical;
        {true, false, false} -> warning;
        {false, true, false} -> warning;
        {false, false, true} -> warning;
        _ -> normal
    end.

%% @doc 检查进程内存泄漏（简化实现）
-spec check_processes_for_leaks() -> [map()].
check_processes_for_leaks() ->
    try
        % 获取内存使用最多的前20个进程
        TopProcesses = case erlang:function_exported(recon, proc_count, 2) of
            true ->
                recon:proc_count(memory, 20);
            false ->
                []
        end,

        % 检查每个进程
        lists:filtermap(fun({Pid, Memory, _}) ->
            case Memory > 10 * 1024 * 1024 of  % 10MB阈值
                true ->
                    {true, #{
                        type => process_memory_leak,
                        pid => Pid,
                        memory => Memory,
                        severity => case Memory > 50 * 1024 * 1024 of
                            true -> critical;
                            false -> warning
                        end
                    }};
                false ->
                    false
            end
        end, TopProcesses)
    catch
        _:_ ->
            []
    end.

%% @doc 分析内存增长趋势 - 高级时间序列分析
-spec analyze_memory_growth_trends() -> map().
analyze_memory_growth_trends() ->
    try
        ?SLOG(debug, #{msg => "starting_advanced_memory_trend_analysis"}),

        % 1. 获取历史内存数据
        HistoryData = get_memory_history_data(),

        % 2. 时间序列分析
        TimeSeriesAnalysis = perform_time_series_analysis(HistoryData),

        % 3. 趋势检测
        TrendDetection = detect_memory_trends(HistoryData),

        % 4. 异常检测
        AnomalyDetection = detect_memory_anomalies(HistoryData),

        % 5. 预测建模
        PredictionModel = build_memory_prediction_model(HistoryData),

        % 6. 季节性分析
        SeasonalityAnalysis = analyze_memory_seasonality(HistoryData),

        % 7. 综合评估
        ComprehensiveAssessment = assess_memory_health(
            TimeSeriesAnalysis, TrendDetection, AnomalyDetection,
            PredictionModel, SeasonalityAnalysis
        ),

        #{
            timestamp => erlang:system_time(millisecond),
            time_series => TimeSeriesAnalysis,
            trend => TrendDetection,
            anomalies => AnomalyDetection,
            prediction => PredictionModel,
            seasonality => SeasonalityAnalysis,
            assessment => ComprehensiveAssessment
        }
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "memory_trend_analysis_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            #{
                trend => unknown,
                prediction => uncertain,
                confidence => 0.0,
                error => R
            }
    end.

%% ============================================================================
%% 高级内存分析算法支持函数
%% ============================================================================

%% @doc 获取内存历史数据
get_memory_history_data() ->
    try
        % 从ETS表中获取历史数据
        case ets:info(?MEMORY_HISTORY_TAB, size) of
            undefined -> [];
            0 -> [];
            _ ->
                % 获取最近的历史记录
                AllRecords = ets:tab2list(?MEMORY_HISTORY_TAB),

                % 按时间戳排序
                SortedRecords = lists:sort(
                    fun(#{timestamp := T1}, #{timestamp := T2}) -> T1 =< T2 end,
                    AllRecords
                ),

                % 只保留最近的数据点（例如最近1小时的数据）
                Now = erlang:system_time(millisecond),
                OneHourAgo = Now - (60 * 60 * 1000),

                lists:filter(
                    fun(#{timestamp := Timestamp}) -> Timestamp >= OneHourAgo end,
                    SortedRecords
                )
        end
    catch
        _:_ -> []
    end.

%% @doc 执行时间序列分析
perform_time_series_analysis(HistoryData) ->
    try
        case length(HistoryData) of
            Len when Len < 3 ->
                #{
                    status => insufficient_data,
                    data_points => Len,
                    analysis => not_available
                };
            Len ->
                % 提取内存使用率时间序列
                MemoryUsageTimeSeries = extract_memory_usage_series(HistoryData),

                % 计算统计指标
                Statistics = calculate_time_series_statistics(MemoryUsageTimeSeries),

                % 计算变化率
                ChangeRates = calculate_change_rates(MemoryUsageTimeSeries),

                % 计算移动平均
                MovingAverages = calculate_moving_averages(MemoryUsageTimeSeries),

                #{
                    status => success,
                    data_points => Len,
                    statistics => Statistics,
                    change_rates => ChangeRates,
                    moving_averages => MovingAverages,
                    raw_series => MemoryUsageTimeSeries
                }
        end
    catch
        _:_ ->
            #{status => analysis_failed}
    end.

%% @doc 检测内存趋势
detect_memory_trends(HistoryData) ->
    try
        case length(HistoryData) of
            Len when Len < 5 ->
                #{trend => insufficient_data, confidence => 0.0};
            _ ->
                % 提取内存使用率序列
                MemoryUsages = [
                    maps:get(memory_usage_ratio, Record, 0.0) || Record <- HistoryData
                ],

                % 线性回归分析趋势
                TrendAnalysis = perform_linear_regression(MemoryUsages),

                % 分类趋势
                TrendType = classify_trend(TrendAnalysis),

                % 计算趋势强度
                TrendStrength = calculate_trend_strength(TrendAnalysis),

                % 计算置信度
                Confidence = calculate_trend_confidence(TrendAnalysis, MemoryUsages),

                #{
                    trend => TrendType,
                    strength => TrendStrength,
                    confidence => Confidence,
                    slope => maps:get(slope, TrendAnalysis, 0.0),
                    r_squared => maps:get(r_squared, TrendAnalysis, 0.0)
                }
        end
    catch
        _:_ ->
            #{trend => analysis_failed, confidence => 0.0}
    end.

%% @doc 检测内存异常
detect_memory_anomalies(HistoryData) ->
    try
        case length(HistoryData) of
            Len when Len < 10 ->
                #{anomalies => [], count => 0, status => insufficient_data};
            _ ->
                % 提取内存使用率序列
                MemoryUsages = [
                    maps:get(memory_usage_ratio, Record, 0.0) || Record <- HistoryData
                ],

                % 计算统计基线
                Mean = lists:sum(MemoryUsages) / length(MemoryUsages),
                Variance = calculate_variance(MemoryUsages, Mean),
                StdDev = math:sqrt(Variance),

                % 使用3-sigma规则检测异常
                Threshold = 3.0,
                UpperBound = Mean + Threshold * StdDev,
                LowerBound = Mean - Threshold * StdDev,

                % 检测异常点
                AnomaliesWithIndex = lists:foldl(
                    fun({Index, Usage}, Acc) ->
                        if
                            Usage > UpperBound ->
                                [#{
                                    type => high_anomaly,
                                    index => Index,
                                    value => Usage,
                                    threshold => UpperBound,
                                    deviation => Usage - Mean,
                                    severity => calculate_anomaly_severity(Usage, Mean, StdDev)
                                } | Acc];
                            Usage < LowerBound ->
                                [#{
                                    type => low_anomaly,
                                    index => Index,
                                    value => Usage,
                                    threshold => LowerBound,
                                    deviation => Usage - Mean,
                                    severity => calculate_anomaly_severity(Usage, Mean, StdDev)
                                } | Acc];
                            true ->
                                Acc
                        end
                    end,
                    [],
                    lists:zip(lists:seq(1, length(MemoryUsages)), MemoryUsages)
                ),

                #{
                    anomalies => lists:reverse(AnomaliesWithIndex),
                    count => length(AnomaliesWithIndex),
                    status => success,
                    baseline => #{mean => Mean, std_dev => StdDev},
                    thresholds => #{upper => UpperBound, lower => LowerBound}
                }
        end
    catch
        _:_ ->
            #{anomalies => [], count => 0, status => detection_failed}
    end.

%% @doc 构建内存预测模型
build_memory_prediction_model(HistoryData) ->
    try
        case length(HistoryData) of
            Len when Len < 10 ->
                #{prediction => unavailable, reason => insufficient_data};
            _ ->
                % 提取内存使用率序列
                MemoryUsages = [
                    maps:get(memory_usage_ratio, Record, 0.0) || Record <- HistoryData
                ],

                % 使用简单的线性预测模型
                LinearModel = build_linear_prediction_model(MemoryUsages),

                % 使用移动平均预测模型
                MovingAvgModel = build_moving_average_model(MemoryUsages),

                % 使用指数平滑预测模型
                ExponentialModel = build_exponential_smoothing_model(MemoryUsages),

                % 选择最佳模型
                BestModel = select_best_prediction_model([
                    LinearModel, MovingAvgModel, ExponentialModel
                ]),

                % 生成预测
                Predictions = generate_predictions(BestModel, 5), % 预测未来5个时间点

                #{
                    model_type => maps:get(type, BestModel),
                    accuracy => maps:get(accuracy, BestModel, 0.0),
                    predictions => Predictions,
                    confidence_intervals => calculate_prediction_confidence(BestModel, Predictions)
                }
        end
    catch
        _:_ ->
            #{prediction => failed, reason => model_build_error}
    end.

%% @doc 分析内存季节性
analyze_memory_seasonality(HistoryData) ->
    try
        case length(HistoryData) of
            Len when Len < 20 ->
                #{seasonality => none, reason => insufficient_data};
            _ ->
                % 提取时间戳和内存使用率
                TimeMemoryPairs = [
                    {maps:get(timestamp, Record), maps:get(memory_usage_ratio, Record, 0.0)}
                    || Record <- HistoryData
                ],

                % 分析小时级别的季节性
                HourlyPattern = analyze_hourly_pattern(TimeMemoryPairs),

                % 分析分钟级别的季节性（短期模式）
                MinutelyPattern = analyze_minutely_pattern(TimeMemoryPairs),

                % 检测周期性
                Periodicity = detect_periodicity(TimeMemoryPairs),

                #{
                    hourly_pattern => HourlyPattern,
                    minutely_pattern => MinutelyPattern,
                    periodicity => Periodicity,
                    has_seasonality => has_significant_seasonality(HourlyPattern, MinutelyPattern)
                }
        end
    catch
        _:_ ->
            #{seasonality => analysis_failed}
    end.

%% @doc 综合评估内存健康状况
assess_memory_health(_TimeSeriesAnalysis, TrendDetection, AnomalyDetection,
                    PredictionModel, _SeasonalityAnalysis) ->
    try
        % 基于趋势的评分
        TrendScore = case maps:get(trend, TrendDetection, stable) of
            increasing ->
                Strength = maps:get(strength, TrendDetection, 0.0),
                max(0.0, 1.0 - Strength * 2.0);  % 增长趋势降低评分
            decreasing -> 1.0;  % 下降趋势是好的
            stable -> 0.8;      % 稳定趋势较好
            _ -> 0.5
        end,

        % 基于异常的评分
        AnomalyScore = case maps:get(count, AnomalyDetection, 0) of
            0 -> 1.0;
            Count when Count =< 2 -> 0.8;
            Count when Count =< 5 -> 0.6;
            _ -> 0.3
        end,

        % 基于预测的评分
        PredictionScore = case maps:get(predictions, PredictionModel, []) of
            [] -> 0.5;  % 无预测数据
            Predictions ->
                % 检查预测是否显示内存使用率会超过阈值
                MaxPredicted = lists:max(Predictions),
                if
                    MaxPredicted > 0.9 -> 0.2;  % 预测会超过90%
                    MaxPredicted > 0.8 -> 0.5;  % 预测会超过80%
                    MaxPredicted > 0.7 -> 0.7;  % 预测会超过70%
                    true -> 1.0
                end
        end,

        % 综合健康评分
        OverallScore = (TrendScore * 0.4 + AnomalyScore * 0.3 + PredictionScore * 0.3),

        % 健康等级分类
        HealthLevel = if
            OverallScore >= 0.8 -> excellent;
            OverallScore >= 0.6 -> good;
            OverallScore >= 0.4 -> fair;
            OverallScore >= 0.2 -> poor;
            true -> critical
        end,

        % 生成建议
        Recommendations = generate_memory_health_recommendations(
            HealthLevel, TrendDetection, AnomalyDetection, PredictionModel
        ),

        #{
            overall_score => OverallScore,
            health_level => HealthLevel,
            component_scores => #{
                trend => TrendScore,
                anomaly => AnomalyScore,
                prediction => PredictionScore
            },
            recommendations => Recommendations
        }
    catch
        _:_ ->
            #{
                overall_score => 0.5,
                health_level => unknown,
                error => assessment_failed
            }
    end.

%% ============================================================================
%% 数学计算和统计分析辅助函数
%% ============================================================================

%% @doc 提取内存使用率时间序列
extract_memory_usage_series(HistoryData) ->
    [maps:get(memory_usage_ratio, Record, 0.0) || Record <- HistoryData].

%% @doc 计算时间序列统计指标
calculate_time_series_statistics(TimeSeries) ->
    Len = length(TimeSeries),
    Sum = lists:sum(TimeSeries),
    Mean = Sum / Len,

    Variance = calculate_variance(TimeSeries, Mean),
    StdDev = math:sqrt(Variance),

    SortedSeries = lists:sort(TimeSeries),
    Min = lists:min(TimeSeries),
    Max = lists:max(TimeSeries),
    Median = calculate_median(SortedSeries),

    #{
        count => Len,
        mean => Mean,
        variance => Variance,
        std_dev => StdDev,
        min => Min,
        max => Max,
        median => Median,
        range => Max - Min
    }.

%% @doc 计算方差
calculate_variance(Values, Mean) ->
    SumSquaredDiffs = lists:sum([
        math:pow(Value - Mean, 2) || Value <- Values
    ]),
    SumSquaredDiffs / length(Values).

%% @doc 计算中位数
calculate_median(SortedList) ->
    Len = length(SortedList),
    if
        Len rem 2 =:= 1 ->
            lists:nth((Len + 1) div 2, SortedList);
        true ->
            Mid1 = lists:nth(Len div 2, SortedList),
            Mid2 = lists:nth(Len div 2 + 1, SortedList),
            (Mid1 + Mid2) / 2
    end.

%% @doc 计算变化率
calculate_change_rates(TimeSeries) ->
    case length(TimeSeries) of
        Len when Len < 2 -> [];
        _ ->
            lists:zipwith(
                fun(Current, Previous) ->
                    if
                        Previous =:= 0.0 -> 0.0;
                        true -> (Current - Previous) / Previous
                    end
                end,
                tl(TimeSeries),
                TimeSeries
            )
    end.

%% @doc 计算移动平均
calculate_moving_averages(TimeSeries) ->
    WindowSize = min(5, length(TimeSeries) div 2),
    case WindowSize of
        Size when Size < 2 -> [];
        _ ->
            calculate_moving_average_with_window(TimeSeries, WindowSize)
    end.

%% @doc 计算指定窗口大小的移动平均
calculate_moving_average_with_window(TimeSeries, WindowSize) ->
    calculate_moving_average_helper(TimeSeries, WindowSize, []).

calculate_moving_average_helper(TimeSeries, WindowSize, Acc) ->
    case length(TimeSeries) of
        Len when Len < WindowSize -> lists:reverse(Acc);
        _ ->
            Window = lists:sublist(TimeSeries, WindowSize),
            Average = lists:sum(Window) / WindowSize,
            calculate_moving_average_helper(
                tl(TimeSeries), WindowSize, [Average | Acc]
            )
    end.

%% @doc 执行线性回归分析
perform_linear_regression(Values) ->
    N = length(Values),
    X = lists:seq(1, N),  % 时间索引
    Y = Values,           % 内存使用率值

    % 计算回归系数
    SumX = lists:sum(X),
    SumY = lists:sum(Y),
    SumXY = lists:sum(lists:zipwith(fun(Xi, Yi) -> Xi * Yi end, X, Y)),
    SumX2 = lists:sum([Xi * Xi || Xi <- X]),

    % 计算斜率和截距
    Slope = (N * SumXY - SumX * SumY) / (N * SumX2 - SumX * SumX),
    Intercept = (SumY - Slope * SumX) / N,

    % 计算R²
    MeanY = SumY / N,
    SSTotal = lists:sum([(Yi - MeanY) * (Yi - MeanY) || Yi <- Y]),
    SSResidual = lists:sum([
        begin
            Predicted = Slope * Xi + Intercept,
            (Yi - Predicted) * (Yi - Predicted)
        end || {Xi, Yi} <- lists:zip(X, Y)
    ]),

    RSquared = if
        SSTotal =:= 0.0 -> 1.0;
        true -> 1.0 - SSResidual / SSTotal
    end,

    #{
        slope => Slope,
        intercept => Intercept,
        r_squared => RSquared,
        n => N
    }.

%% @doc 分类趋势类型
classify_trend(#{slope := Slope}) ->
    if
        Slope > 0.001 -> increasing;
        Slope < -0.001 -> decreasing;
        true -> stable
    end.

%% @doc 计算趋势强度
calculate_trend_strength(#{slope := Slope}) ->
    abs(Slope).

%% @doc 计算趋势置信度
calculate_trend_confidence(#{r_squared := RSquared}, _Values) ->
    RSquared.

%% @doc 计算异常严重程度
calculate_anomaly_severity(Value, Mean, StdDev) ->
    Deviation = abs(Value - Mean),
    SigmaLevel = Deviation / StdDev,
    if
        SigmaLevel > 4.0 -> extreme;
        SigmaLevel > 3.5 -> severe;
        SigmaLevel > 3.0 -> moderate;
        true -> mild
    end.

%% ============================================================================
%% 预测模型相关函数
%% ============================================================================

%% @doc 构建线性预测模型
build_linear_prediction_model(Values) ->
    RegressionResult = perform_linear_regression(Values),
    Accuracy = maps:get(r_squared, RegressionResult, 0.0),

    #{
        type => linear,
        parameters => RegressionResult,
        accuracy => Accuracy
    }.

%% @doc 构建移动平均模型
build_moving_average_model(Values) ->
    WindowSize = min(5, length(Values) div 3),
    case WindowSize of
        Size when Size < 2 ->
            #{type => moving_average, accuracy => 0.0, error => insufficient_data};
        _ ->
            % 计算最近窗口的平均值作为预测基础
            RecentWindow = lists:sublist(lists:reverse(Values), WindowSize),
            Average = lists:sum(RecentWindow) / WindowSize,

            % 计算模型准确性（基于历史预测误差）
            Accuracy = calculate_moving_average_accuracy(Values, WindowSize),

            #{
                type => moving_average,
                window_size => WindowSize,
                current_average => Average,
                accuracy => Accuracy
            }
    end.

%% @doc 构建指数平滑模型
build_exponential_smoothing_model(Values) ->
    case length(Values) of
        Len when Len < 3 ->
            #{type => exponential_smoothing, accuracy => 0.0, error => insufficient_data};
        _ ->
            % 使用简单指数平滑
            Alpha = 0.3,  % 平滑参数

            % 计算指数平滑值
            [FirstValue | RestValues] = Values,
            {FinalSmoothed, _} = lists:foldl(
                fun(Value, {PrevSmoothed, _}) ->
                    NewSmoothed = Alpha * Value + (1 - Alpha) * PrevSmoothed,
                    {NewSmoothed, Value}
                end,
                {FirstValue, FirstValue},
                RestValues
            ),

            % 计算准确性
            Accuracy = calculate_exponential_smoothing_accuracy(Values, Alpha),

            #{
                type => exponential_smoothing,
                alpha => Alpha,
                current_smoothed => FinalSmoothed,
                accuracy => Accuracy
            }
    end.

%% @doc 选择最佳预测模型
select_best_prediction_model(Models) ->
    ValidModels = [M || M <- Models, maps:get(accuracy, M, 0.0) > 0.0],
    case ValidModels of
        [] -> #{type => none, accuracy => 0.0};
        _ ->
            lists:foldl(
                fun(Model, BestModel) ->
                    case maps:get(accuracy, Model, 0.0) > maps:get(accuracy, BestModel, 0.0) of
                        true -> Model;
                        false -> BestModel
                    end
                end,
                hd(ValidModels),
                tl(ValidModels)
            )
    end.

%% @doc 生成预测值
generate_predictions(#{type := linear, parameters := Params}, Steps) ->
    #{slope := Slope, intercept := Intercept, n := N} = Params,
    [Slope * (N + Step) + Intercept || Step <- lists:seq(1, Steps)];

generate_predictions(#{type := moving_average, current_average := Average}, Steps) ->
    [Average || _ <- lists:seq(1, Steps)];

generate_predictions(#{type := exponential_smoothing, current_smoothed := Smoothed}, Steps) ->
    [Smoothed || _ <- lists:seq(1, Steps)];

generate_predictions(_, _) ->
    [].

%% @doc 计算预测置信区间
calculate_prediction_confidence(#{type := linear, accuracy := Accuracy}, Predictions) ->
    ConfidenceLevel = Accuracy,
    ErrorMargin = (1.0 - Accuracy) * 0.1,  % 基于准确性计算误差范围

    [#{
        prediction => Pred,
        lower_bound => max(0.0, Pred - ErrorMargin),
        upper_bound => min(1.0, Pred + ErrorMargin),
        confidence => ConfidenceLevel
    } || Pred <- Predictions];

calculate_prediction_confidence(#{accuracy := Accuracy}, Predictions) ->
    ConfidenceLevel = Accuracy,
    ErrorMargin = (1.0 - Accuracy) * 0.05,

    [#{
        prediction => Pred,
        lower_bound => max(0.0, Pred - ErrorMargin),
        upper_bound => min(1.0, Pred + ErrorMargin),
        confidence => ConfidenceLevel
    } || Pred <- Predictions].

%% @doc 计算移动平均模型准确性
calculate_moving_average_accuracy(Values, WindowSize) ->
    case length(Values) of
        Len when Len =< WindowSize -> 0.0;
        Len ->
            % 使用后半部分数据测试准确性
            TestSize = min(10, Len - WindowSize),
            TestData = lists:sublist(Values, Len - TestSize + 1, TestSize),

            % 计算预测误差
            {_, TotalError} = lists:foldl(
                fun(ActualValue, {Index, ErrorAcc}) ->
                    TrainingData = lists:sublist(Values, Index - WindowSize, WindowSize),
                    PredictedValue = lists:sum(TrainingData) / WindowSize,
                    Error = abs(ActualValue - PredictedValue),
                    {Index + 1, ErrorAcc + Error}
                end,
                {Len - TestSize + WindowSize, 0.0},
                TestData
            ),

            AvgError = TotalError / TestSize,
            max(0.0, 1.0 - AvgError * 10.0)  % 转换为准确性分数
    end.

%% @doc 计算指数平滑模型准确性
calculate_exponential_smoothing_accuracy(Values, Alpha) ->
    case length(Values) of
        Len when Len < 3 -> 0.0;
        Len ->
            % 使用前80%的数据训练，后20%测试
            TrainSize = round(Len * 0.8),
            TrainData = lists:sublist(Values, TrainSize),
            TestData = lists:sublist(Values, TrainSize + 1, Len - TrainSize),

            % 训练模型
            [FirstValue | RestTrainData] = TrainData,
            {FinalSmoothed, _} = lists:foldl(
                fun(Value, {PrevSmoothed, _}) ->
                    NewSmoothed = Alpha * Value + (1 - Alpha) * PrevSmoothed,
                    {NewSmoothed, Value}
                end,
                {FirstValue, FirstValue},
                RestTrainData
            ),

            % 测试准确性
            TotalError = lists:sum([
                abs(TestValue - FinalSmoothed) || TestValue <- TestData
            ]),

            AvgError = TotalError / length(TestData),
            max(0.0, 1.0 - AvgError * 10.0)
    end.

%% ============================================================================
%% 季节性分析相关函数
%% ============================================================================

%% @doc 分析小时级别的模式
analyze_hourly_pattern(TimeMemoryPairs) ->
    try
        % 按小时分组
        HourlyGroups = lists:foldl(
            fun({Timestamp, MemoryUsage}, Acc) ->
                {{_, _, _}, {Hour, _, _}} = calendar:gregorian_seconds_to_datetime(
                    Timestamp div 1000
                ),
                Group = maps:get(Hour, Acc, []),
                maps:put(Hour, [MemoryUsage | Group], Acc)
            end,
            #{},
            TimeMemoryPairs
        ),

        % 计算每小时的平均内存使用率
        HourlyAverages = maps:map(
            fun(_Hour, UsageList) ->
                lists:sum(UsageList) / length(UsageList)
            end,
            HourlyGroups
        ),

        % 计算变异系数
        AllAverages = maps:values(HourlyAverages),
        Mean = lists:sum(AllAverages) / length(AllAverages),
        Variance = calculate_variance(AllAverages, Mean),
        CoefficientOfVariation = if
            Mean =:= 0.0 -> 0.0;
            true -> math:sqrt(Variance) / Mean
        end,

        #{
            hourly_averages => HourlyAverages,
            coefficient_of_variation => CoefficientOfVariation,
            peak_hour => find_peak_hour(HourlyAverages),
            low_hour => find_low_hour(HourlyAverages)
        }
    catch
        _:_ ->
            #{error => hourly_analysis_failed}
    end.

%% @doc 分析分钟级别的模式
analyze_minutely_pattern(TimeMemoryPairs) ->
    try
        % 按分钟分组（只看分钟部分，忽略小时）
        MinutelyGroups = lists:foldl(
            fun({Timestamp, MemoryUsage}, Acc) ->
                {{_, _, _}, {_, Minute, _}} = calendar:gregorian_seconds_to_datetime(
                    Timestamp div 1000
                ),
                Group = maps:get(Minute, Acc, []),
                maps:put(Minute, [MemoryUsage | Group], Acc)
            end,
            #{},
            TimeMemoryPairs
        ),

        % 计算每分钟的平均内存使用率
        MinutelyAverages = maps:map(
            fun(_Minute, UsageList) ->
                lists:sum(UsageList) / length(UsageList)
            end,
            MinutelyGroups
        ),

        % 计算变异系数
        AllAverages = maps:values(MinutelyAverages),
        case length(AllAverages) of
            0 -> #{error => no_data};
            _ ->
                Mean = lists:sum(AllAverages) / length(AllAverages),
                Variance = calculate_variance(AllAverages, Mean),
                CoefficientOfVariation = if
                    Mean =:= 0.0 -> 0.0;
                    true -> math:sqrt(Variance) / Mean
                end,

                #{
                    minutely_averages => MinutelyAverages,
                    coefficient_of_variation => CoefficientOfVariation
                }
        end
    catch
        _:_ ->
            #{error => minutely_analysis_failed}
    end.

%% @doc 检测周期性
detect_periodicity(TimeMemoryPairs) ->
    try
        % 简化的周期性检测
        % 计算相邻数据点的时间间隔
        TimeIntervals = calculate_time_intervals(TimeMemoryPairs),

        % 检查是否有规律的时间间隔
        case TimeIntervals of
            [] -> #{periodicity => none};
            _ ->
                % 计算最常见的时间间隔
                IntervalFrequency = lists:foldl(
                    fun(Interval, Acc) ->
                        maps:update_with(Interval, fun(Count) -> Count + 1 end, 1, Acc)
                    end,
                    #{},
                    TimeIntervals
                ),

                % 找到最频繁的间隔
                {MostCommonInterval, Frequency} = maps:fold(
                    fun(Interval, Count, {BestInterval, BestCount}) ->
                        if
                            Count > BestCount -> {Interval, Count};
                            true -> {BestInterval, BestCount}
                        end
                    end,
                    {0, 0},
                    IntervalFrequency
                ),

                % 判断是否有显著的周期性
                TotalIntervals = length(TimeIntervals),
                PeriodicityStrength = Frequency / TotalIntervals,

                #{
                    periodicity => if
                        PeriodicityStrength > 0.7 -> strong;
                        PeriodicityStrength > 0.5 -> moderate;
                        PeriodicityStrength > 0.3 -> weak;
                        true -> none
                    end,
                    common_interval => MostCommonInterval,
                    strength => PeriodicityStrength
                }
        end
    catch
        _:_ ->
            #{periodicity => detection_failed}
    end.

%% @doc 判断是否有显著的季节性
has_significant_seasonality(HourlyPattern, MinutelyPattern) ->
    HourlyCOV = maps:get(coefficient_of_variation, HourlyPattern, 0.0),
    MinutelyCOV = maps:get(coefficient_of_variation, MinutelyPattern, 0.0),

    % 如果变异系数较高，说明有季节性模式
    HourlyCOV > 0.2 orelse MinutelyCOV > 0.15.

%% @doc 生成内存健康建议
generate_memory_health_recommendations(HealthLevel, TrendDetection, AnomalyDetection, PredictionModel) ->
    BaseRecommendations = case HealthLevel of
        excellent ->
            ["内存使用状况良好，继续保持当前的监控策略"];
        good ->
            ["内存使用基本正常，建议定期检查内存使用趋势"];
        fair ->
            ["内存使用存在一些问题，建议加强监控和优化"];
        poor ->
            ["内存使用状况较差，需要立即采取优化措施"];
        critical ->
            ["内存使用状况危急，需要紧急处理"]
    end,

    % 基于趋势的建议
    TrendRecommendations = case maps:get(trend, TrendDetection, stable) of
        increasing ->
            ["检测到内存使用呈上升趋势，建议检查是否存在内存泄漏",
             "考虑增加内存清理频率或优化内存使用"];
        decreasing ->
            ["内存使用呈下降趋势，这是一个好现象"];
        stable ->
            ["内存使用趋势稳定"]
    end,

    % 基于异常的建议
    AnomalyRecommendations = case maps:get(count, AnomalyDetection, 0) of
        0 -> [];
        Count when Count =< 2 ->
            ["检测到少量内存使用异常，建议关注"];
        _ ->
            ["检测到多个内存使用异常，建议深入调查原因",
             "考虑检查是否有异常的内存分配模式"]
    end,

    % 基于预测的建议
    PredictionRecommendations = case maps:get(predictions, PredictionModel, []) of
        [] -> [];
        Predictions ->
            MaxPredicted = lists:max(Predictions),
            if
                MaxPredicted > 0.9 ->
                    ["预测显示内存使用率将超过90%，建议立即采取预防措施"];
                MaxPredicted > 0.8 ->
                    ["预测显示内存使用率将超过80%，建议准备扩容或优化"];
                true -> []
            end
    end,

    BaseRecommendations ++ TrendRecommendations ++ AnomalyRecommendations ++ PredictionRecommendations.

%% ============================================================================
%% 辅助函数
%% ============================================================================

%% @doc 找到峰值小时
find_peak_hour(HourlyAverages) ->
    case maps:size(HourlyAverages) of
        0 -> undefined;
        _ ->
            maps:fold(
                fun(Hour, Average, {BestHour, BestAverage}) ->
                    if
                        Average > BestAverage -> {Hour, Average};
                        true -> {BestHour, BestAverage}
                    end
                end,
                {0, 0.0},
                HourlyAverages
            )
    end.

%% @doc 找到低谷小时
find_low_hour(HourlyAverages) ->
    case maps:size(HourlyAverages) of
        0 -> undefined;
        _ ->
            maps:fold(
                fun(Hour, Average, {BestHour, BestAverage}) ->
                    if
                        Average < BestAverage -> {Hour, Average};
                        true -> {BestHour, BestAverage}
                    end
                end,
                {0, 1.0},
                HourlyAverages
            )
    end.

%% @doc 计算时间间隔
calculate_time_intervals(TimeMemoryPairs) ->
    Times = [Time || {Time, _} <- TimeMemoryPairs],
    SortedTimes = lists:sort(Times),
    case length(SortedTimes) of
        Len when Len < 2 -> [];
        _ ->
            lists:zipwith(
                fun(Current, Previous) -> Current - Previous end,
                tl(SortedTimes),
                SortedTimes
            )
    end.

%% @doc 生成内存警报
-spec generate_memory_alerts(#memory_stats{}, [map()], [map()], map()) -> [map()].
generate_memory_alerts(MemoryStats, ETSLeaks, ProcessLeaks, _TrendAnalysis) ->
    Alerts = [],

    % 检查系统内存使用率
    SystemAlerts = case MemoryStats#memory_stats.memory_usage_ratio of
        Ratio when Ratio > ?MEMORY_EMERGENCY_THRESHOLD ->
            [#{
                type => system_memory_critical,
                severity => emergency,
                memory_ratio => Ratio,
                description => <<"System memory usage critical">>
            }];
        Ratio when Ratio > ?MEMORY_CRITICAL_THRESHOLD ->
            [#{
                type => system_memory_high,
                severity => critical,
                memory_ratio => Ratio,
                description => <<"System memory usage high">>
            }];
        Ratio when Ratio > ?MEMORY_WARNING_THRESHOLD ->
            [#{
                type => system_memory_warning,
                severity => warning,
                memory_ratio => Ratio,
                description => <<"System memory usage warning">>
            }];
        _ ->
            []
    end,

    % 合并所有警报
    Alerts ++ SystemAlerts ++ ETSLeaks ++ ProcessLeaks.

%% @doc 存储内存统计数据
-spec store_memory_statistics(#memory_stats{}) -> ok.
store_memory_statistics(Stats) ->
    try
        % 存储到统计表
        ets:insert(?MEMORY_STATS_TAB, {Stats#memory_stats.timestamp, Stats}),

        % 存储到历史表（简化格式）
        ets:insert(?MEMORY_HISTORY_TAB, {
            Stats#memory_stats.timestamp,
            Stats#memory_stats.total_memory
        }),

        % 清理过期数据
        cleanup_old_memory_data(),
        ok
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_storing_memory_statistics",
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% @doc 处理内存警报
-spec process_memory_alerts([map()]) -> ok.
process_memory_alerts([]) ->
    ok;
process_memory_alerts(Alerts) ->
    try
        % 存储警报
        lists:foreach(fun(Alert) ->
            AlertId = erlang:unique_integer([positive]),
            AlertRecord = #leak_alert{
                id = AlertId,
                timestamp = erlang:system_time(millisecond),
                type = maps:get(type, Alert, unknown),
                target = maps:get(table_name, Alert, maps:get(pid, Alert, unknown)),
                severity = maps:get(severity, Alert, warning),
                description = maps:get(description, Alert, <<"Memory leak detected">>),
                metrics = Alert,
                action_taken = none,
                resolved = false
            },
            ets:insert(?LEAK_ALERTS_TAB, {AlertId, AlertRecord})
        end, Alerts),

        % 记录警报日志
        ?SLOG(warning, #{
            msg => "memory_leak_alerts_generated",
            alert_count => length(Alerts),
            alerts => Alerts
        }),

        ok
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_processing_memory_alerts",
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% @doc 清理过期的内存数据
-spec cleanup_old_memory_data() -> ok.
cleanup_old_memory_data() ->
    try
        Now = erlang:system_time(millisecond),
        CutoffTime = Now - (?MEMORY_HISTORY_RETENTION_HOURS * 3600 * 1000),

        % 清理统计表
        ets:select_delete(?MEMORY_STATS_TAB, [
            {{'$1', '_'}, [{'<', '$1', CutoffTime}], [true]}
        ]),

        % 清理历史表
        ets:select_delete(?MEMORY_HISTORY_TAB, [
            {{'$1', '_'}, [{'<', '$1', CutoffTime}], [true]}
        ]),

        % 清理已解决的警报
        ets:select_delete(?LEAK_ALERTS_TAB, [
            {{'$1', #leak_alert{resolved = true, timestamp = '$2'}},
             [{'<', '$2', CutoffTime}], [true]}
        ]),

        ok
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_cleaning_old_memory_data",
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% @doc 执行内存清理
-spec perform_memory_cleanup() -> ok | {error, term()}.
perform_memory_cleanup() ->
    try
        ?SLOG(info, #{msg => "starting_memory_cleanup"}),

        % 1. 强制垃圾回收
        erlang:garbage_collect(),

        % 2. 清理ETS表
        cleanup_ets_tables_data(),

        % 3. 清理过期数据
        cleanup_old_memory_data(),

        ?SLOG(info, #{msg => "memory_cleanup_completed"}),
        ok
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_during_memory_cleanup",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {cleanup_failed, R}}
    end.

%% @doc 清理ETS表数据
-spec cleanup_ets_tables_data() -> ok.
cleanup_ets_tables_data() ->
    try
        % 获取所有监控的ETS表
        MonitoredTables = ets:tab2list(?ETS_MONITOR_TAB),

        % 对每个表执行清理
        lists:foreach(fun(#ets_monitor{
            table_name = TableName,
            cleanup_function = CleanupFun
        }) ->
            case CleanupFun of
                undefined ->
                    ok;  % 没有清理函数
                Fun when is_function(Fun, 0) ->
                    try
                        Fun(),
                        ?SLOG(debug, #{
                            msg => "ets_table_cleanup_executed",
                            table => TableName
                        })
                    catch
                        E:R:S ->
                            ?SLOG(warning, #{
                                msg => "ets_table_cleanup_failed",
                                table => TableName,
                                error => E,
                                reason => R,
                                stacktrace => S
                            })
                    end;
                _ ->
                    ok
            end
        end, MonitoredTables),

        ok
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_cleaning_ets_tables_data",
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% @doc 清理工作进程
-spec cleanup_worker(atom()) -> ok.
cleanup_worker(expired_data) ->
    cleanup_old_memory_data();
cleanup_worker(trend_analysis) ->
    % 执行趋势分析
    try
        ?SLOG(debug, #{msg => "performing_trend_analysis"}),
        % 这里可以添加更复杂的趋势分析逻辑
        ok
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_in_trend_analysis",
                error => E,
                reason => R,
                stacktrace => S
            })
    end;
cleanup_worker(_) ->
    ok.

%% @doc 执行内存泄漏检测（同步版本）
-spec perform_memory_leak_detection() -> [map()].
perform_memory_leak_detection() ->
    try
        % 收集系统内存统计
        MemoryStats = collect_memory_statistics(),

        % 检查ETS表状态
        ETSLeaks = check_ets_tables_for_leaks(),

        % 检查进程内存使用
        ProcessLeaks = check_processes_for_leaks(),

        % 分析内存增长趋势
        TrendAnalysis = analyze_memory_growth_trends(),

        % 生成警报
        Alerts = generate_memory_alerts(MemoryStats, ETSLeaks, ProcessLeaks, TrendAnalysis),

        % 存储统计数据
        store_memory_statistics(MemoryStats),

        % 处理警报
        process_memory_alerts(Alerts),

        Alerts
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_in_memory_leak_detection",
                error => E,
                reason => R,
                stacktrace => S
            }),
            []
    end.

%% @doc 生成内存使用报告
-spec generate_memory_report() -> map().
generate_memory_report() ->
    try
        % 获取当前内存统计
        CurrentStats = collect_memory_statistics(),

        % 获取ETS表统计
        ETSStats = get_all_ets_statistics(),

        % 获取最近的警报
        RecentAlerts = get_recent_alerts(),

        #{
            timestamp => erlang:system_time(millisecond),
            current_memory => #{
                total => CurrentStats#memory_stats.total_memory,
                usage_ratio => CurrentStats#memory_stats.memory_usage_ratio,
                growth_rate => CurrentStats#memory_stats.growth_rate,
                leak_score => CurrentStats#memory_stats.leak_score
            },
            ets_tables => ETSStats,
            recent_alerts => RecentAlerts,
            system_health => determine_system_health(CurrentStats)
        }
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_generating_memory_report",
                error => E,
                reason => R,
                stacktrace => S
            }),
            #{
                error => <<"Failed to generate memory report">>,
                timestamp => erlang:system_time(millisecond)
            }
    end.

%% @doc 获取所有ETS表统计
-spec get_all_ets_statistics() -> [map()].
get_all_ets_statistics() ->
    try
        MonitoredTables = ets:tab2list(?ETS_MONITOR_TAB),
        lists:map(fun(#ets_monitor{
            table_name = TableName,
            size = Size,
            memory = Memory,
            growth_rate = GrowthRate,
            alert_level = AlertLevel
        }) ->
            #{
                table_name => TableName,
                size => Size,
                memory => Memory,
                growth_rate => GrowthRate,
                alert_level => AlertLevel
            }
        end, MonitoredTables)
    catch
        _:_ ->
            []
    end.

%% @doc 获取最近的警报
-spec get_recent_alerts() -> [map()].
get_recent_alerts() ->
    try
        Now = erlang:system_time(millisecond),
        OneHourAgo = Now - 3600000,

        RecentAlerts = ets:select(?LEAK_ALERTS_TAB, [
            {{'$1', #leak_alert{timestamp = '$2'} = '$3'},
             [{'>', '$2', OneHourAgo}], ['$3']}
        ]),

        lists:map(fun(#leak_alert{
            type = Type,
            target = Target,
            severity = Severity,
            timestamp = Timestamp,
            description = Description
        }) ->
            #{
                type => Type,
                target => Target,
                severity => Severity,
                timestamp => Timestamp,
                description => Description
            }
        end, RecentAlerts)
    catch
        _:_ ->
            []
    end.

%% @doc 确定系统健康状态
-spec determine_system_health(#memory_stats{}) -> atom().
determine_system_health(#memory_stats{
    memory_usage_ratio = Ratio,
    growth_rate = GrowthRate,
    leak_score = LeakScore
}) ->
    case {Ratio > ?MEMORY_CRITICAL_THRESHOLD,
          GrowthRate > ?MEMORY_GROWTH_RATE_CRITICAL,
          LeakScore > 70} of
        {true, _, _} -> critical;
        {_, true, _} -> critical;
        {_, _, true} -> warning;
        {false, false, false} when Ratio > ?MEMORY_WARNING_THRESHOLD -> warning;
        _ -> healthy
    end.

%% @doc 获取表统计信息
-spec get_table_statistics(atom()) -> map().
get_table_statistics(TableName) ->
    try
        case ets:lookup(?ETS_MONITOR_TAB, TableName) of
            [#ets_monitor{} = Monitor] ->
                #{
                    table_name => Monitor#ets_monitor.table_name,
                    size => Monitor#ets_monitor.size,
                    memory => Monitor#ets_monitor.memory,
                    growth_rate => Monitor#ets_monitor.growth_rate,
                    alert_level => Monitor#ets_monitor.alert_level,
                    last_check => Monitor#ets_monitor.last_check
                };
            [] ->
                #{error => <<"Table not monitored">>}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_getting_table_statistics",
                table => TableName,
                error => E,
                reason => R,
                stacktrace => S
            }),
            #{error => <<"Failed to get table statistics">>}
    end.

%% @doc 分析目标内存趋势
-spec analyze_target_memory_trend(atom() | pid()) -> map().
analyze_target_memory_trend(_Target) ->
    % 简化实现，返回基本趋势信息
    #{
        trend => stable,
        confidence => 0.8,
        prediction => normal,
        recommendation => <<"Continue monitoring">>
    }.

%%--------------------------------------------------------------------
%% 共用模块集成函数
%%--------------------------------------------------------------------

%% @doc 集成到协调器
%% 将内存泄漏检测器注册到协调器中，实现统一管理和协调
-spec integrate() -> ok | {error, term()}.
integrate() ->
    try
        ?SLOG(info, #{msg => "integrating_memory_leak_detector_module"}),

        % 定义模块信息
        ModuleInfo = #{
            priority => critical,                % 关键优先级，内存管理是核心功能
            description => <<"Memory leak detection and prevention service">>,
            features => [
                memory_leak_detection,           % 内存泄漏检测
                ets_table_monitoring,           % ETS表监控
                process_memory_tracking,        % 进程内存跟踪
                automatic_cleanup,              % 自动清理
                trend_analysis,                 % 趋势分析
                early_warning_system,           % 早期预警系统
                memory_pressure_management,     % 内存压力管理
                garbage_collection_trigger,     % 垃圾回收触发
                performance_optimization        % 性能优化
            ],
            version => <<"1.0.0">>,
            status => active,
            resource_usage => #{
                memory => medium,               % 内存使用量中等
                cpu => low,                     % CPU使用量低
                io => minimal                   % IO使用量极小
            },
            dependencies => [],                 % 无依赖
            coordination_events => [
                memory_pressure,                % 内存压力事件
                cleanup_request,                % 清理请求事件
                performance_adjustment,         % 性能调整事件
                leak_detection_alert,           % 泄漏检测警报
                threshold_exceeded,             % 阈值超出事件
                emergency_cleanup               % 紧急清理事件
            ]
        },

        % 向协调器注册模块
        case erlang:function_exported(emqx_plugin_mongodb_coordinator, register_module, 2) of
            true ->
                case emqx_plugin_mongodb_coordinator:register_module(?MODULE, ModuleInfo) of
                    ok ->
                        ?SLOG(info, #{
                            msg => "memory_leak_detector_integrated_successfully",
                            module => ?MODULE,
                            features => maps:get(features, ModuleInfo)
                        }),

                        % 集成到其他共用模块
                        integrate_with_shared_modules(),
                        ok;
                    {error, Reason} ->
                        ?SLOG(error, #{
                            msg => "failed_to_integrate_memory_leak_detector",
                            module => ?MODULE,
                            reason => Reason
                        }),
                        {error, Reason}
                end;
            false ->
                % 协调器不可用，记录信息但不影响功能
                ?SLOG(info, #{
                    msg => "coordinator_not_available_skipping_integration",
                    module => ?MODULE
                }),

                % 仍然尝试集成到其他共用模块
                integrate_with_shared_modules(),
                ok
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "memory_leak_detector_integration_exception",
                module => ?MODULE,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {integration_exception, R}}
    end.

%% @doc 集成到共用模块
%% 将内存泄漏检测器集成到错误处理、资源管理和监控模块
-spec integrate_with_shared_modules() -> ok.
integrate_with_shared_modules() ->
    try
        % 集成到错误处理模块
        register_with_error_handler(),

        % 集成到资源管理模块
        register_with_resource_manager(),

        % 集成到监控模块
        register_with_monitor(),

        ?SLOG(info, #{
            msg => "memory_leak_detector_shared_modules_integration_completed",
            module => ?MODULE
        }),
        ok
    catch
        E:R:S ->
            ?SLOG(warning, #{
                msg => "error_integrating_with_shared_modules",
                module => ?MODULE,
                error => E,
                reason => R,
                stacktrace => S
            }),
            ok  % 不影响主要功能
    end.

%% @doc 向错误处理模块注册
%% 让错误处理模块了解内存泄漏检测器的错误类型
-spec register_with_error_handler() -> ok.
register_with_error_handler() ->
    try
        case erlang:function_exported(emqx_plugin_mongodb_error_handler, register_error_source, 2) of
            true ->
                ErrorTypes = [
                    memory_leak_detected,           % 内存泄漏检测到
                    ets_table_overflow,            % ETS表溢出
                    process_memory_excessive,      % 进程内存过量
                    cleanup_failure,               % 清理失败
                    threshold_exceeded,            % 阈值超出
                    detection_error,               % 检测错误
                    trend_analysis_failure         % 趋势分析失败
                ],
                emqx_plugin_mongodb_error_handler:register_error_source(?MODULE, ErrorTypes),
                ?SLOG(debug, #{
                    msg => "registered_with_error_handler",
                    module => ?MODULE,
                    error_types => ErrorTypes
                });
            false ->
                ?SLOG(debug, #{
                    msg => "error_handler_not_available",
                    note => "skipping_error_source_registration"
                })
        end
    catch
        E:R:S ->
            ?SLOG(warning, #{
                msg => "failed_to_register_with_error_handler",
                error => E,
                reason => R,
                stacktrace => S
            })
    end,
    ok.

%% @doc 向资源管理模块注册
%% 让资源管理器监控内存泄漏检测器的资源使用
-spec register_with_resource_manager() -> ok.
register_with_resource_manager() ->
    try
        case erlang:function_exported(emqx_plugin_mongodb_resource_manager, register_resource, 3) of
            true ->
                ResourceInfo = #{
                    type => memory_detector,
                    name => memory_leak_detector,
                    description => <<"Memory leak detection and prevention service">>,
                    memory_limit => 50 * 1024 * 1024,  % 50MB限制
                    cpu_limit => 5,                     % 5% CPU限制
                    cleanup_function => fun() -> force_cleanup() end,
                    restart_function => fun() -> restart_detector() end
                },
                emqx_plugin_mongodb_resource_manager:register_resource(
                    memory_leak_detector,
                    ResourceInfo,
                    self()
                ),
                ?SLOG(debug, #{
                    msg => "registered_with_resource_manager",
                    module => ?MODULE
                });
            false ->
                ?SLOG(debug, #{
                    msg => "resource_manager_not_available",
                    note => "skipping_resource_registration"
                })
        end
    catch
        E:R:S ->
            ?SLOG(warning, #{
                msg => "failed_to_register_with_resource_manager",
                error => E,
                reason => R,
                stacktrace => S
            })
    end,
    ok.

%% @doc 向监控模块注册
%% 让监控模块跟踪内存泄漏检测器的健康状态
-spec register_with_monitor() -> ok.
register_with_monitor() ->
    try
        case erlang:function_exported(emqx_plugin_mongodb_monitor, register_process, 3) of
            true ->
                ProcessInfo = #{
                    type => memory_detector,
                    name => memory_leak_detector,
                    description => <<"Memory leak detection and prevention service">>,
                    critical => true,                   % 关键进程
                    restart_function => fun() -> restart_detector() end,
                    health_check_function => fun() -> health_check() end
                },
                emqx_plugin_mongodb_monitor:register_process(
                    memory_leak_detector,
                    ProcessInfo,
                    self()
                ),
                ?SLOG(debug, #{
                    msg => "registered_with_monitor",
                    module => ?MODULE
                });
            false ->
                ?SLOG(debug, #{
                    msg => "monitor_not_available",
                    note => "skipping_process_registration"
                })
        end
    catch
        E:R:S ->
            ?SLOG(warning, #{
                msg => "failed_to_register_with_monitor",
                error => E,
                reason => R,
                stacktrace => S
            })
    end,
    ok.

%% @doc 使用错误处理模块处理错误
%% 统一的错误处理和恢复机制
-spec handle_error(atom(), map(), map()) -> ok.
handle_error(ErrorType, Source, Details) ->
    try
        case erlang:function_exported(emqx_plugin_mongodb_error_handler, handle_error, 3) of
            true ->
                emqx_plugin_mongodb_error_handler:handle_error(ErrorType, Source, Details);
            false ->
                ?SLOG(debug, #{
                    msg => "error_handler_not_available",
                    error_type => ErrorType,
                    source => Source,
                    details => Details
                })
        end
    catch
        E:R:S ->
            ?SLOG(warning, #{
                msg => "failed_to_use_error_handler",
                error => E,
                reason => R,
                stacktrace => S,
                original_error => #{
                    type => ErrorType,
                    source => Source,
                    details => Details
                }
            })
    end,
    ok.

%% @doc 重启检测器
%% 当监控模块检测到检测器异常时调用
-spec restart_detector() -> ok | {error, term()}.
restart_detector() ->
    try
        ?SLOG(info, #{
            msg => "restarting_memory_leak_detector",
            module => ?MODULE
        }),

        % 停止当前检测器
        case whereis(?MODULE) of
            undefined ->
                ok;
            Pid ->
                exit(Pid, shutdown),
                timer:sleep(1000)  % 等待进程完全停止
        end,

        % 启动新的检测器
        case start_link() of
            {ok, NewPid} ->
                ?SLOG(info, #{
                    msg => "memory_leak_detector_restarted_successfully",
                    new_pid => NewPid
                }),
                ok;
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_restart_memory_leak_detector",
                    reason => Reason
                }),
                {error, Reason}
        end
    catch
        E:R:S ->
            handle_error(restart_error, #{
                module => ?MODULE,
                function => restart_detector,
                line => ?LINE
            }, #{
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%% @doc 健康检查
%% 检查内存泄漏检测器的健康状态
-spec health_check() -> ok | {error, term()}.
health_check() ->
    try
        case whereis(?MODULE) of
            undefined ->
                {error, process_not_running};
            Pid when is_pid(Pid) ->
                case is_process_alive(Pid) of
                    true ->
                        % 检查进程是否响应
                        case gen_server:call(?MODULE, ping, 5000) of
                            pong ->
                                ok;
                            _ ->
                                {error, process_not_responding}
                        end;
                    false ->
                        {error, process_dead}
                end
        end
    catch
        E:R:S ->
            ?SLOG(warning, #{
                msg => "health_check_error",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {health_check_failed, R}}
    end.
