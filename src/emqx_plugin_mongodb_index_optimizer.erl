%%%-------------------------------------------------------------------
%%% @doc MongoDB插件索引管理和查询优化模块
%%% 这个模块提供智能索引管理和全面的查询性能优化功能
%%%
%%% 核心功能：
%%% 1. 自动索引优化 - 基于查询模式自动创建和优化索引
%%% 2. 查询性能分析 - 实时监控查询性能并识别慢查询
%%% 3. 索引使用监控 - 监控索引使用情况并识别无用索引
%%% 4. 查询计划分析 - 分析查询执行计划并提供优化建议
%%% 5. 索引碎片整理 - 自动检测和整理索引碎片
%%% 6. 查询缓存优化 - 优化查询缓存策略
%%%
%%% 优化算法：
%%% - 查询模式识别：基于查询历史识别常见查询模式
%%% - 索引效果评估：评估索引对查询性能的影响
%%% - 自适应索引策略：根据工作负载动态调整索引策略
%%% - 成本效益分析：平衡索引维护成本和查询性能收益
%%%
%%% Java等价概念：
%%% 类似于数据库性能调优工具如MySQL的Performance Schema
%%% 或者MongoDB Compass的性能分析功能
%%%
%%% @end
%%%-------------------------------------------------------------------
-module(emqx_plugin_mongodb_index_optimizer).

-behaviour(gen_server).

-include("emqx_plugin_mongodb.hrl").

%% API导出
-export([
    start_link/0,                    % 启动索引优化服务
    stop/0,                          % 停止服务
    
    % 索引管理API
    analyze_query_patterns/0,        % 分析查询模式
    optimize_indexes/1,              % 优化索引
    create_recommended_indexes/1,    % 创建推荐索引
    remove_unused_indexes/1,         % 删除无用索引
    
    % 查询性能API
    analyze_query_performance/1,     % 分析查询性能
    get_slow_queries/1,              % 获取慢查询
    optimize_query/2,                % 优化查询
    
    % 索引监控API
    monitor_index_usage/0,           % 监控索引使用
    get_index_statistics/1,          % 获取索引统计
    analyze_index_efficiency/1,      % 分析索引效率
    
    % 报告API
    generate_optimization_report/0,  % 生成优化报告
    get_performance_insights/0,      % 获取性能洞察
    
    % 集成API
    integrate/0                      % 集成到协调器
]).

%% gen_server回调
-export([
    init/1,
    handle_call/3,
    handle_cast/2,
    handle_info/2,
    terminate/2,
    code_change/3
]).

%% 内部状态记录
-record(state, {
    % 查询分析状态
    query_patterns = #{},            % 查询模式统计
    slow_queries = [],               % 慢查询记录
    query_performance = #{},         % 查询性能数据
    
    % 索引管理状态
    index_usage = #{},               % 索引使用统计
    index_recommendations = [],      % 索引推荐
    unused_indexes = [],             % 无用索引列表
    
    % 优化配置
    optimization_enabled = true,     % 是否启用优化
    analysis_interval = 300000,      % 分析间隔（5分钟）
    slow_query_threshold = 1000,     % 慢查询阈值（毫秒）
    index_usage_threshold = 0.1,     % 索引使用率阈值
    
    % 统计信息
    total_optimizations = 0,         % 总优化次数
    indexes_created = 0,             % 创建的索引数量
    indexes_removed = 0,             % 删除的索引数量
    last_analysis_time = 0           % 上次分析时间
}).

%% ============================================================================
%% API函数
%% ============================================================================

%% @doc 启动索引优化服务
start_link() ->
    gen_server:start_link({local, ?MODULE}, ?MODULE, [], []).

%% @doc 停止服务
stop() ->
    gen_server:call(?MODULE, stop, 5000).

%% @doc 分析查询模式
analyze_query_patterns() ->
    gen_server:call(?MODULE, analyze_query_patterns).

%% @doc 优化索引
optimize_indexes(Collection) ->
    gen_server:call(?MODULE, {optimize_indexes, Collection}).

%% @doc 创建推荐索引
create_recommended_indexes(Collection) ->
    gen_server:call(?MODULE, {create_recommended_indexes, Collection}).

%% @doc 删除无用索引
remove_unused_indexes(Collection) ->
    gen_server:call(?MODULE, {remove_unused_indexes, Collection}).

%% @doc 分析查询性能
analyze_query_performance(TimeRange) ->
    gen_server:call(?MODULE, {analyze_query_performance, TimeRange}).

%% @doc 获取慢查询
get_slow_queries(TimeRange) ->
    gen_server:call(?MODULE, {get_slow_queries, TimeRange}).

%% @doc 优化查询
optimize_query(Query, Collection) ->
    gen_server:call(?MODULE, {optimize_query, Query, Collection}).

%% @doc 监控索引使用
monitor_index_usage() ->
    gen_server:call(?MODULE, monitor_index_usage).

%% @doc 获取索引统计
get_index_statistics(Collection) ->
    gen_server:call(?MODULE, {get_index_statistics, Collection}).

%% @doc 分析索引效率
analyze_index_efficiency(Collection) ->
    gen_server:call(?MODULE, {analyze_index_efficiency, Collection}).

%% @doc 生成优化报告
generate_optimization_report() ->
    gen_server:call(?MODULE, generate_optimization_report).

%% @doc 获取性能洞察
get_performance_insights() ->
    gen_server:call(?MODULE, get_performance_insights).

%% @doc 集成到协调器
integrate() ->
    try
        % 注册到协调器（如果协调器存在）
        case erlang:whereis(emqx_plugin_mongodb_coordinator) of
            undefined ->
                ?SLOG(debug, #{msg => "coordinator_not_running_skipping_index_optimizer_integration"});
            CoordinatorPid ->
                % 向协调器注册索引优化服务
                CoordinatorPid ! {register_service, index_optimizer, self()},
                ?SLOG(info, #{msg => "index_optimizer_integrated_with_coordinator"})
        end,
        ok
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "failed_to_integrate_index_optimizer",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {integration_failed, R}}
    end.

%% ============================================================================
%% gen_server回调实现
%% ============================================================================

%% @doc 初始化服务
init([]) ->
    ?SLOG(info, #{msg => "starting_index_optimizer_service"}),
    
    % 创建索引优化相关ETS表
    ets:new(?INDEX_USAGE_TAB, [named_table, public, set, {read_concurrency, true}]),
    ets:new(?QUERY_PERFORMANCE_TAB, [named_table, public, set, {read_concurrency, true}]),
    ets:new(?INDEX_RECOMMENDATIONS_TAB, [named_table, public, set, {read_concurrency, true}]),
    
    % 设置定期分析
    erlang:send_after(300000, self(), analyze_performance), % 5分钟后开始分析
    
    State = #state{
        query_patterns = #{},
        slow_queries = [],
        query_performance = #{},
        index_usage = #{},
        index_recommendations = [],
        unused_indexes = []
    },
    
    ?SLOG(info, #{msg => "index_optimizer_service_started"}),
    {ok, State}.

%% @doc 处理同步调用
handle_call(analyze_query_patterns, _From, State) ->
    try
        {Patterns, NewState} = do_analyze_query_patterns(State),
        {reply, {ok, Patterns}, NewState}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "query_pattern_analysis_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {reply, {error, R}, State}
    end;

handle_call({optimize_indexes, Collection}, _From, State) ->
    try
        {Result, NewState} = do_optimize_indexes(Collection, State),
        {reply, {ok, Result}, NewState}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "index_optimization_failed",
                collection => Collection,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {reply, {error, R}, State}
    end;

handle_call({create_recommended_indexes, Collection}, _From, State) ->
    try
        {Result, NewState} = do_create_recommended_indexes(Collection, State),
        {reply, {ok, Result}, NewState}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "recommended_index_creation_failed",
                collection => Collection,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {reply, {error, R}, State}
    end;

handle_call({remove_unused_indexes, Collection}, _From, State) ->
    try
        {Result, NewState} = do_remove_unused_indexes(Collection, State),
        {reply, {ok, Result}, NewState}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "unused_index_removal_failed",
                collection => Collection,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {reply, {error, R}, State}
    end;

handle_call({analyze_query_performance, TimeRange}, _From, State) ->
    try
        Analysis = do_analyze_query_performance(TimeRange, State),
        {reply, {ok, Analysis}, State}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "query_performance_analysis_failed",
                time_range => TimeRange,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {reply, {error, R}, State}
    end;

handle_call({get_slow_queries, TimeRange}, _From, State) ->
    try
        SlowQueries = do_get_slow_queries(TimeRange, State),
        {reply, {ok, SlowQueries}, State}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "slow_query_retrieval_failed",
                time_range => TimeRange,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {reply, {error, R}, State}
    end;

handle_call({optimize_query, Query, Collection}, _From, State) ->
    try
        OptimizedQuery = do_optimize_query(Query, Collection, State),
        {reply, {ok, OptimizedQuery}, State}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "query_optimization_failed",
                query => Query,
                collection => Collection,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {reply, {error, R}, State}
    end;

handle_call(monitor_index_usage, _From, State) ->
    try
        {Usage, NewState} = do_monitor_index_usage(State),
        {reply, {ok, Usage}, NewState}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "index_usage_monitoring_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {reply, {error, R}, State}
    end;

handle_call({get_index_statistics, Collection}, _From, State) ->
    try
        Statistics = do_get_index_statistics(Collection, State),
        {reply, {ok, Statistics}, State}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "index_statistics_retrieval_failed",
                collection => Collection,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {reply, {error, R}, State}
    end;

handle_call({analyze_index_efficiency, Collection}, _From, State) ->
    try
        Efficiency = do_analyze_index_efficiency(Collection, State),
        {reply, {ok, Efficiency}, State}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "index_efficiency_analysis_failed",
                collection => Collection,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {reply, {error, R}, State}
    end;

handle_call(generate_optimization_report, _From, State) ->
    try
        Report = do_generate_optimization_report(State),
        {reply, {ok, Report}, State}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "optimization_report_generation_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {reply, {error, R}, State}
    end;

handle_call(get_performance_insights, _From, State) ->
    try
        Insights = do_get_performance_insights(State),
        {reply, {ok, Insights}, State}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "performance_insights_retrieval_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {reply, {error, R}, State}
    end;

handle_call(stop, _From, State) ->
    {stop, normal, ok, State};

handle_call(_Request, _From, State) ->
    {reply, {error, unknown_request}, State}.

%% @doc 处理异步消息
handle_cast(_Msg, State) ->
    {noreply, State}.

%% @doc 处理定时器和其他消息
handle_info(analyze_performance, State) ->
    try
        NewState = do_periodic_analysis(State),
        % 设置下次分析
        erlang:send_after(State#state.analysis_interval, self(), analyze_performance),
        {noreply, NewState}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "periodic_index_analysis_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            % 即使分析失败，也要设置下次分析
            erlang:send_after(State#state.analysis_interval, self(), analyze_performance),
            {noreply, State}
    end;

handle_info(_Info, State) ->
    {noreply, State}.

%% @doc 服务终止处理
terminate(_Reason, _State) ->
    ?SLOG(info, #{msg => "index_optimizer_service_terminated"}),
    
    % 清理ETS表
    catch ets:delete(?INDEX_USAGE_TAB),
    catch ets:delete(?QUERY_PERFORMANCE_TAB),
    catch ets:delete(?INDEX_RECOMMENDATIONS_TAB),
    
    ok.

%% @doc 代码更新处理
code_change(_OldVsn, State, _Extra) ->
    {ok, State}.

%% ============================================================================
%% 核心实现函数
%% ============================================================================

%% @doc 分析查询模式
do_analyze_query_patterns(State) ->
    try
        % 获取所有集合的查询统计
        Collections = get_all_collections(),

        Patterns = lists:foldl(
            fun(Collection, Acc) ->
                CollectionPatterns = analyze_collection_query_patterns(Collection),
                maps:put(Collection, CollectionPatterns, Acc)
            end,
            #{},
            Collections
        ),

        % 更新状态
        NewState = State#state{
            query_patterns = Patterns,
            last_analysis_time = erlang:system_time(millisecond)
        },

        ?SLOG(info, #{
            msg => "query_patterns_analyzed",
            collections_count => length(Collections),
            patterns_count => maps:size(Patterns)
        }),

        {Patterns, NewState}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "query_pattern_analysis_error",
                error => E,
                reason => R,
                stacktrace => S
            }),
            throw({query_pattern_analysis_failed, R})
    end.

%% @doc 优化索引 - 高级智能分析实现
%% 使用机器学习和统计分析进行索引优化
do_optimize_indexes(Collection, State) ->
    try
        ?SLOG(debug, #{msg => "starting_advanced_index_optimization", collection => Collection}),

        % 1. 深度查询模式分析
        QueryPatterns = perform_deep_query_pattern_analysis(Collection),

        % 2. 索引性能基准测试
        IndexPerformanceBaseline = establish_index_performance_baseline(Collection),

        % 3. 智能索引推荐生成
        IntelligentRecommendations = generate_intelligent_index_recommendations(
            Collection, QueryPatterns, IndexPerformanceBaseline, State
        ),

        % 4. 索引效果预测
        PerformancePredictions = predict_index_performance_impact(
            Collection, IntelligentRecommendations, QueryPatterns
        ),

        % 5. 成本效益分析
        CostBenefitAnalysis = perform_index_cost_benefit_analysis(
            IntelligentRecommendations, PerformancePredictions
        ),

        % 6. 优化策略选择
        OptimalStrategy = select_optimal_index_strategy(
            IntelligentRecommendations, CostBenefitAnalysis, State
        ),

        % 7. 分阶段执行优化
        OptimizationResults = execute_phased_index_optimization(
            Collection, OptimalStrategy, State
        ),

        % 8. 效果验证和回滚机制
        ValidationResults = validate_optimization_effectiveness(
            Collection, OptimizationResults, IndexPerformanceBaseline
        ),

        % 9. 更新机器学习模型
        UpdatedState = update_optimization_learning_model(
            State, Collection, OptimizationResults, ValidationResults
        ),

        % 10. 生成详细报告
        DetailedResult = #{
            collection => Collection,
            analysis => #{
                query_patterns => QueryPatterns,
                performance_baseline => IndexPerformanceBaseline,
                cost_benefit => CostBenefitAnalysis
            },
            recommendations => IntelligentRecommendations,
            predictions => PerformancePredictions,
            strategy => OptimalStrategy,
            execution_results => OptimizationResults,
            validation => ValidationResults,
            performance_improvement => calculate_performance_improvement(
                IndexPerformanceBaseline, ValidationResults
            )
        },

        ?SLOG(info, #{
            msg => "advanced_index_optimization_completed",
            collection => Collection,
            recommendations_count => length(IntelligentRecommendations),
            optimizations_applied => length(OptimizationResults),
            performance_improvement => maps:get(performance_improvement, DetailedResult)
        }),

        {DetailedResult, UpdatedState}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "advanced_index_optimization_failed",
                collection => Collection,
                error => E,
                reason => R,
                stacktrace => S
            }),
            % 失败时返回基础优化结果
            fallback_to_basic_optimization(Collection, State)
    end.

%% ============================================================================
%% 高级索引优化算法实现
%% ============================================================================

%% @doc 深度查询模式分析
%% 使用机器学习技术分析查询模式
perform_deep_query_pattern_analysis(Collection) ->
    try
        ?SLOG(debug, #{msg => "performing_deep_query_pattern_analysis", collection => Collection}),

        % 1. 收集查询历史数据
        QueryHistory = collect_query_history_data(Collection),

        % 2. 查询特征提取
        QueryFeatures = extract_query_features(QueryHistory),

        % 3. 查询聚类分析
        QueryClusters = perform_query_clustering(QueryFeatures),

        % 4. 频率模式识别
        FrequencyPatterns = identify_frequency_patterns(QueryHistory),

        % 5. 时间模式分析
        TemporalPatterns = analyze_temporal_query_patterns(QueryHistory),

        % 6. 字段访问模式分析
        FieldAccessPatterns = analyze_field_access_patterns(QueryHistory),

        % 7. 查询复杂度分析
        ComplexityAnalysis = analyze_query_complexity(QueryHistory),

        % 8. 性能瓶颈识别
        PerformanceBottlenecks = identify_performance_bottlenecks(QueryHistory),

        #{
            query_clusters => QueryClusters,
            frequency_patterns => FrequencyPatterns,
            temporal_patterns => TemporalPatterns,
            field_access_patterns => FieldAccessPatterns,
            complexity_analysis => ComplexityAnalysis,
            performance_bottlenecks => PerformanceBottlenecks,
            total_queries_analyzed => length(QueryHistory),
            analysis_timestamp => erlang:system_time(millisecond)
        }
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "query_pattern_analysis_failed",
                collection => Collection,
                error => E,
                reason => R,
                stacktrace => S
            }),
            #{error => query_pattern_analysis_failed, reason => R}
    end.

%% @doc 建立索引性能基准
establish_index_performance_baseline(Collection) ->
    try
        % 1. 当前索引统计
        CurrentIndexes = get_collection_indexes(Collection),

        % 2. 查询性能基准测试
        QueryPerformanceBaseline = run_query_performance_benchmark(Collection),

        % 3. 索引使用率统计
        IndexUsageStats = collect_index_usage_statistics(Collection),

        % 4. 存储空间分析
        StorageAnalysis = analyze_index_storage_usage(Collection),

        % 5. 维护成本评估
        MaintenanceCosts = evaluate_index_maintenance_costs(Collection),

        #{
            current_indexes => CurrentIndexes,
            query_performance => QueryPerformanceBaseline,
            index_usage => IndexUsageStats,
            storage_analysis => StorageAnalysis,
            maintenance_costs => MaintenanceCosts,
            baseline_timestamp => erlang:system_time(millisecond)
        }
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "performance_baseline_establishment_failed",
                collection => Collection,
                error => E,
                reason => R,
                stacktrace => S
            }),
            #{error => baseline_establishment_failed, reason => R}
    end.

%% @doc 生成智能索引推荐
generate_intelligent_index_recommendations(Collection, QueryPatterns, PerformanceBaseline, State) ->
    try
        % 1. 基于查询模式的推荐
        PatternBasedRecommendations = generate_pattern_based_recommendations(QueryPatterns),

        % 2. 基于性能分析的推荐
        PerformanceBasedRecommendations = generate_performance_based_recommendations(
            PerformanceBaseline
        ),

        % 3. 基于机器学习的推荐
        MLBasedRecommendations = generate_ml_based_recommendations(
            Collection, QueryPatterns, State
        ),

        % 4. 复合索引智能推荐
        CompoundIndexRecommendations = generate_compound_index_recommendations(
            QueryPatterns, PerformanceBaseline
        ),

        % 5. 部分索引推荐
        PartialIndexRecommendations = generate_partial_index_recommendations(
            QueryPatterns
        ),

        % 6. 文本索引推荐
        TextIndexRecommendations = generate_text_index_recommendations(
            QueryPatterns
        ),

        % 7. 地理空间索引推荐
        GeospatialIndexRecommendations = generate_geospatial_index_recommendations(
            QueryPatterns
        ),

        % 8. 推荐去重和优先级排序
        AllRecommendations = PatternBasedRecommendations ++
                           PerformanceBasedRecommendations ++
                           MLBasedRecommendations ++
                           CompoundIndexRecommendations ++
                           PartialIndexRecommendations ++
                           TextIndexRecommendations ++
                           GeospatialIndexRecommendations,

        % 9. 智能去重和排序
        DeduplicatedRecommendations = deduplicate_recommendations(AllRecommendations),
        PrioritizedRecommendations = prioritize_recommendations(
            DeduplicatedRecommendations, QueryPatterns, PerformanceBaseline
        ),

        % 10. 推荐质量评分
        ScoredRecommendations = score_recommendations(
            PrioritizedRecommendations, QueryPatterns, PerformanceBaseline
        ),

        ?SLOG(debug, #{
            msg => "intelligent_recommendations_generated",
            collection => Collection,
            total_recommendations => length(ScoredRecommendations)
        }),

        ScoredRecommendations
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "intelligent_recommendation_generation_failed",
                collection => Collection,
                error => E,
                reason => R,
                stacktrace => S
            }),
            []
    end.

%% ============================================================================
%% 性能预测和成本效益分析
%% ============================================================================

%% @doc 预测索引性能影响
predict_index_performance_impact(Collection, Recommendations, QueryPatterns) ->
    try
        ?SLOG(debug, #{msg => "predicting_index_performance_impact", collection => Collection}),

        % 为每个推荐索引预测性能影响
        PredictionResults = lists:map(
            fun(Recommendation) ->
                IndexSpec = maps:get(index_spec, Recommendation),

                % 1. 查询加速预测
                QueryAcceleration = predict_query_acceleration(IndexSpec, QueryPatterns),

                % 2. 写入性能影响预测
                WriteImpact = predict_write_performance_impact(IndexSpec, QueryPatterns),

                % 3. 存储空间影响预测
                StorageImpact = predict_storage_impact(IndexSpec, Collection),

                % 4. 维护开销预测
                MaintenanceOverhead = predict_maintenance_overhead(IndexSpec),

                % 5. 整体性能评分预测
                OverallPerformanceScore = calculate_overall_performance_prediction(
                    QueryAcceleration, WriteImpact, StorageImpact, MaintenanceOverhead
                ),

                Recommendation#{
                    performance_prediction => #{
                        query_acceleration => QueryAcceleration,
                        write_impact => WriteImpact,
                        storage_impact => StorageImpact,
                        maintenance_overhead => MaintenanceOverhead,
                        overall_score => OverallPerformanceScore
                    }
                }
            end,
            Recommendations
        ),

        % 生成预测摘要
        PredictionSummary = generate_prediction_summary(PredictionResults),

        #{
            individual_predictions => PredictionResults,
            summary => PredictionSummary,
            prediction_confidence => calculate_prediction_confidence(QueryPatterns),
            prediction_timestamp => erlang:system_time(millisecond)
        }
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "performance_prediction_failed",
                collection => Collection,
                error => E,
                reason => R,
                stacktrace => S
            }),
            #{error => performance_prediction_failed, reason => R}
    end.

%% @doc 执行索引成本效益分析
perform_index_cost_benefit_analysis(_Recommendations, PerformancePredictions) ->
    try
        ?SLOG(debug, #{msg => "performing_cost_benefit_analysis"}),

        % 获取预测结果
        PredictionResults = maps:get(individual_predictions, PerformancePredictions, []),

        % 为每个推荐计算成本效益
        CostBenefitResults = lists:map(
            fun(Recommendation) ->
                IndexSpec = maps:get(index_spec, Recommendation),
                PerformancePred = maps:get(performance_prediction, Recommendation, #{}),

                % 1. 成本分析
                Costs = calculate_index_costs(IndexSpec, PerformancePred),

                % 2. 收益分析
                Benefits = calculate_index_benefits(IndexSpec, PerformancePred),

                % 3. ROI计算
                ROI = calculate_index_roi(Costs, Benefits),

                % 4. 风险评估
                RiskAssessment = assess_index_implementation_risk(IndexSpec, PerformancePred),

                % 5. 实施优先级
                ImplementationPriority = calculate_implementation_priority(
                    ROI, RiskAssessment, Benefits
                ),

                Recommendation#{
                    cost_benefit_analysis => #{
                        costs => Costs,
                        benefits => Benefits,
                        roi => ROI,
                        risk_assessment => RiskAssessment,
                        implementation_priority => ImplementationPriority
                    }
                }
            end,
            PredictionResults
        ),

        % 生成整体分析报告
        OverallAnalysis = generate_overall_cost_benefit_analysis(CostBenefitResults),

        #{
            individual_analysis => CostBenefitResults,
            overall_analysis => OverallAnalysis,
            analysis_timestamp => erlang:system_time(millisecond)
        }
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "cost_benefit_analysis_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            #{error => cost_benefit_analysis_failed, reason => R}
    end.

%% @doc 选择最优索引策略
select_optimal_index_strategy(Recommendations, CostBenefitAnalysis, State) ->
    try
        % 获取分析结果
        AnalysisResults = maps:get(individual_analysis, CostBenefitAnalysis, []),

        % 1. 基于ROI排序
        ROISortedRecommendations = lists:sort(
            fun(A, B) ->
                ROIA = get_nested_value([cost_benefit_analysis, roi], A, 0.0),
                ROIB = get_nested_value([cost_benefit_analysis, roi], B, 0.0),
                ROIA >= ROIB
            end,
            AnalysisResults
        ),

        % 2. 基于风险过滤
        LowRiskRecommendations = lists:filter(
            fun(Rec) ->
                Risk = get_nested_value([cost_benefit_analysis, risk_assessment, overall_risk], Rec, high),
                Risk =/= high
            end,
            ROISortedRecommendations
        ),

        % 3. 基于资源约束过滤
        ResourceConstrainedRecommendations = apply_resource_constraints(
            LowRiskRecommendations, State
        ),

        % 4. 分阶段实施策略
        PhasedStrategy = create_phased_implementation_strategy(
            ResourceConstrainedRecommendations
        ),

        % 5. 策略验证
        ValidatedStrategy = validate_implementation_strategy(PhasedStrategy, State),

        ?SLOG(debug, #{
            msg => "optimal_strategy_selected",
            total_recommendations => length(Recommendations),
            selected_for_implementation => length(ValidatedStrategy)
        }),

        ValidatedStrategy
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "strategy_selection_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            % 失败时返回保守策略
            create_conservative_strategy(Recommendations)
    end.

%% ============================================================================
%% 分阶段执行和效果验证
%% ============================================================================

%% @doc 分阶段执行索引优化
execute_phased_index_optimization(Collection, OptimalStrategy, State) ->
    try
        ?SLOG(debug, #{msg => "executing_phased_optimization", collection => Collection}),

        % 1. 准备阶段
        PreparationResults = prepare_optimization_environment(Collection, OptimalStrategy),

        % 2. 分阶段执行
        PhaseResults = lists:foldl(
            fun(Phase, AccResults) ->
                PhaseResult = execute_optimization_phase(Collection, Phase, State),
                [PhaseResult | AccResults]
            end,
            [],
            OptimalStrategy
        ),

        % 3. 后处理
        PostProcessingResults = post_process_optimization_results(
            Collection, lists:reverse(PhaseResults)
        ),

        #{
            preparation => PreparationResults,
            phase_results => lists:reverse(PhaseResults),
            post_processing => PostProcessingResults,
            execution_timestamp => erlang:system_time(millisecond)
        }
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "phased_optimization_execution_failed",
                collection => Collection,
                error => E,
                reason => R,
                stacktrace => S
            }),
            #{error => execution_failed, reason => R}
    end.

%% @doc 验证优化效果
validate_optimization_effectiveness(Collection, OptimizationResults, PerformanceBaseline) ->
    try
        ?SLOG(debug, #{msg => "validating_optimization_effectiveness", collection => Collection}),

        % 1. 性能对比测试
        PostOptimizationPerformance = run_post_optimization_performance_test(Collection),

        % 2. 查询性能改进验证
        QueryPerformanceImprovement = validate_query_performance_improvement(
            PerformanceBaseline, PostOptimizationPerformance
        ),

        % 3. 索引使用率验证
        IndexUsageValidation = validate_index_usage_improvement(Collection),

        % 4. 存储效率验证
        StorageEfficiencyValidation = validate_storage_efficiency(Collection),

        % 5. 系统稳定性验证
        StabilityValidation = validate_system_stability(Collection),

        % 6. 回滚决策
        RollbackDecision = make_rollback_decision(
            QueryPerformanceImprovement,
            IndexUsageValidation,
            StorageEfficiencyValidation,
            StabilityValidation
        ),

        % 7. 执行回滚（如果需要）
        RollbackResults = case RollbackDecision of
            {rollback_required, Reason} ->
                execute_optimization_rollback(Collection, OptimizationResults, Reason);
            no_rollback_needed ->
                #{status => no_rollback_needed}
        end,

        #{
            post_optimization_performance => PostOptimizationPerformance,
            query_performance_improvement => QueryPerformanceImprovement,
            index_usage_validation => IndexUsageValidation,
            storage_efficiency_validation => StorageEfficiencyValidation,
            stability_validation => StabilityValidation,
            rollback_decision => RollbackDecision,
            rollback_results => RollbackResults,
            validation_timestamp => erlang:system_time(millisecond)
        }
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "optimization_validation_failed",
                collection => Collection,
                error => E,
                reason => R,
                stacktrace => S
            }),
            #{error => validation_failed, reason => R}
    end.

%% @doc 更新优化学习模型
update_optimization_learning_model(State, Collection, OptimizationResults, ValidationResults) ->
    try
        % 1. 提取学习特征
        LearningFeatures = extract_optimization_learning_features(
            Collection, OptimizationResults, ValidationResults
        ),

        % 2. 更新模型参数
        UpdatedModelParams = update_model_parameters(
            State#state.query_patterns, LearningFeatures
        ),

        % 3. 更新推荐权重
        _UpdatedRecommendationWeights = update_recommendation_weights(
            ValidationResults
        ),

        % 4. 更新统计信息
        UpdatedStats = update_optimization_statistics(State, OptimizationResults),

        % 5. 保存学习结果
        save_learning_results(Collection, LearningFeatures, UpdatedModelParams),

        State#state{
            query_patterns = maps:merge(
                State#state.query_patterns,
                UpdatedModelParams
            ),
            total_optimizations = UpdatedStats#state.total_optimizations,
            last_analysis_time = erlang:system_time(millisecond)
        }
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "learning_model_update_failed",
                collection => Collection,
                error => E,
                reason => R,
                stacktrace => S
            }),
            State
    end.

%% @doc 计算性能改进
calculate_performance_improvement(PerformanceBaseline, ValidationResults) ->
    try
        BaselinePerf = maps:get(query_performance, PerformanceBaseline, #{}),
        PostOptPerf = maps:get(post_optimization_performance, ValidationResults, #{}),

        % 计算各项性能指标的改进
        QueryTimeImprovement = calculate_query_time_improvement(BaselinePerf, PostOptPerf),
        ThroughputImprovement = calculate_throughput_improvement(BaselinePerf, PostOptPerf),
        IndexEfficiencyImprovement = calculate_index_efficiency_improvement(BaselinePerf, PostOptPerf),

        % 计算综合改进分数
        OverallImprovement = (QueryTimeImprovement + ThroughputImprovement + IndexEfficiencyImprovement) / 3,

        #{
            query_time_improvement => QueryTimeImprovement,
            throughput_improvement => ThroughputImprovement,
            index_efficiency_improvement => IndexEfficiencyImprovement,
            overall_improvement => OverallImprovement,
            improvement_category => categorize_improvement(OverallImprovement)
        }
    catch
        _:_ ->
            #{
                overall_improvement => 0.0,
                improvement_category => unknown,
                error => calculation_failed
            }
    end.

%% @doc 基础优化回退
fallback_to_basic_optimization(Collection, State) ->
    try
        % 执行简化的索引优化
        BasicRecommendations = generate_basic_index_recommendations(Collection),
        BasicResults = execute_basic_optimization(Collection, BasicRecommendations),

        Result = #{
            collection => Collection,
            optimization_type => basic_fallback,
            recommendations => BasicRecommendations,
            results => BasicResults
        },

        {Result, State}
    catch
        _:_ ->
        {#{collection => Collection, error => fallback_failed}, State}
    end.

%% @doc 创建推荐索引
do_create_recommended_indexes(Collection, State) ->
    try
        % 获取索引推荐
        Recommendations = get_index_recommendations_for_collection(Collection, State),

        % 过滤出需要创建的索引
        IndexesToCreate = lists:filter(
            fun(Rec) ->
                maps:get(action, Rec, undefined) =:= create
            end,
            Recommendations
        ),

        % 创建索引
        CreationResults = lists:map(
            fun(IndexRec) ->
                IndexSpec = maps:get(index_spec, IndexRec),
                IndexOptions = maps:get(options, IndexRec, #{}),
                create_index_safely(Collection, IndexSpec, IndexOptions)
            end,
            IndexesToCreate
        ),

        % 统计成功创建的索引
        SuccessfulCreations = lists:filter(
            fun(Result) -> element(1, Result) =:= ok end,
            CreationResults
        ),

        % 更新状态
        NewState = State#state{
            indexes_created = State#state.indexes_created + length(SuccessfulCreations)
        },

        Result = #{
            collection => Collection,
            recommendations => length(IndexesToCreate),
            created => length(SuccessfulCreations),
            results => CreationResults
        },

        ?SLOG(info, #{
            msg => "recommended_indexes_created",
            collection => Collection,
            created_count => length(SuccessfulCreations)
        }),

        {Result, NewState}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "recommended_index_creation_error",
                collection => Collection,
                error => E,
                reason => R,
                stacktrace => S
            }),
            throw({recommended_index_creation_failed, R})
    end.

%% @doc 删除无用索引
do_remove_unused_indexes(Collection, State) ->
    try
        % 获取索引使用统计
        IndexUsage = get_index_usage_statistics(Collection),

        % 识别无用索引
        UnusedIndexes = identify_unused_indexes(Collection, IndexUsage, State),

        % 安全删除无用索引（保留必要索引）
        RemovalResults = lists:map(
            fun(IndexName) ->
                case is_index_safe_to_remove(Collection, IndexName) of
                    true ->
                        remove_index_safely(Collection, IndexName);
                    false ->
                        {skipped, IndexName, "index_is_critical"}
                end
            end,
            UnusedIndexes
        ),

        % 统计成功删除的索引
        SuccessfulRemovals = lists:filter(
            fun(Result) -> element(1, Result) =:= ok end,
            RemovalResults
        ),

        % 更新状态
        NewState = State#state{
            indexes_removed = State#state.indexes_removed + length(SuccessfulRemovals),
            unused_indexes = lists:subtract(State#state.unused_indexes,
                                          [element(2, R) || R <- SuccessfulRemovals])
        },

        Result = #{
            collection => Collection,
            unused_identified => length(UnusedIndexes),
            removed => length(SuccessfulRemovals),
            results => RemovalResults
        },

        ?SLOG(info, #{
            msg => "unused_indexes_removed",
            collection => Collection,
            removed_count => length(SuccessfulRemovals)
        }),

        {Result, NewState}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "unused_index_removal_error",
                collection => Collection,
                error => E,
                reason => R,
                stacktrace => S
            }),
            throw({unused_index_removal_failed, R})
    end.

%% @doc 分析查询性能
do_analyze_query_performance(TimeRange, State) ->
    try
        CurrentTime = erlang:system_time(millisecond),
        StartTime = CurrentTime - (TimeRange * 1000),

        % 获取时间范围内的查询性能数据
        PerformanceData = get_query_performance_data(StartTime, CurrentTime),

        % 分析性能指标
        Analysis = #{
            time_range => TimeRange,
            total_queries => length(PerformanceData),
            average_latency => calculate_average_latency(PerformanceData),
            slow_queries_count => count_slow_queries(PerformanceData, State#state.slow_query_threshold),
            query_distribution => analyze_query_distribution(PerformanceData),
            performance_trends => analyze_performance_trends(PerformanceData)
        },

        ?SLOG(debug, #{
            msg => "query_performance_analyzed",
            time_range => TimeRange,
            total_queries => maps:get(total_queries, Analysis)
        }),

        Analysis
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "query_performance_analysis_error",
                time_range => TimeRange,
                error => E,
                reason => R,
                stacktrace => S
            }),
            throw({query_performance_analysis_failed, R})
    end.

%% @doc 获取慢查询
do_get_slow_queries(TimeRange, State) ->
    try
        CurrentTime = erlang:system_time(millisecond),
        StartTime = CurrentTime - (TimeRange * 1000),
        Threshold = State#state.slow_query_threshold,

        % 从ETS表中获取慢查询数据
        AllQueries = ets:tab2list(?QUERY_PERFORMANCE_TAB),

        % 过滤慢查询
        SlowQueries = lists:filter(
            fun({_Key, QueryData}) ->
                Timestamp = maps:get(timestamp, QueryData, 0),
                Duration = maps:get(duration, QueryData, 0),
                Timestamp >= StartTime andalso Duration >= Threshold
            end,
            AllQueries
        ),

        % 按执行时间排序
        SortedSlowQueries = lists:sort(
            fun({_, A}, {_, B}) ->
                maps:get(duration, A, 0) >= maps:get(duration, B, 0)
            end,
            SlowQueries
        ),

        % 格式化结果
        FormattedQueries = lists:map(
            fun({_Key, QueryData}) ->
                #{
                    query => maps:get(query, QueryData, #{}),
                    collection => maps:get(collection, QueryData, unknown),
                    duration => maps:get(duration, QueryData, 0),
                    timestamp => maps:get(timestamp, QueryData, 0),
                    execution_plan => maps:get(execution_plan, QueryData, #{})
                }
            end,
            lists:sublist(SortedSlowQueries, 100) % 限制返回前100个
        ),

        ?SLOG(debug, #{
            msg => "slow_queries_retrieved",
            time_range => TimeRange,
            slow_queries_count => length(FormattedQueries)
        }),

        FormattedQueries
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "slow_query_retrieval_error",
                time_range => TimeRange,
                error => E,
                reason => R,
                stacktrace => S
            }),
            throw({slow_query_retrieval_failed, R})
    end.

%% @doc 优化查询
do_optimize_query(Query, Collection, _State) ->
    try
        % 分析查询结构
        QueryAnalysis = analyze_query_structure(Query),

        % 生成优化建议
        Optimizations = generate_query_optimizations(Query, Collection, QueryAnalysis),

        % 应用优化
        OptimizedQuery = apply_query_optimizations(Query, Optimizations),

        Result = #{
            original_query => Query,
            optimized_query => OptimizedQuery,
            optimizations_applied => Optimizations,
            estimated_improvement => estimate_performance_improvement(Query, OptimizedQuery, Collection)
        },

        ?SLOG(debug, #{
            msg => "query_optimized",
            collection => Collection,
            optimizations_count => length(Optimizations)
        }),

        Result
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "query_optimization_error",
                query => Query,
                collection => Collection,
                error => E,
                reason => R,
                stacktrace => S
            }),
            throw({query_optimization_failed, R})
    end.

%% @doc 监控索引使用
do_monitor_index_usage(State) ->
    try
        % 获取所有集合
        Collections = get_all_collections(),

        % 收集每个集合的索引使用情况
        UsageData = lists:foldl(
            fun(Collection, Acc) ->
                CollectionUsage = collect_index_usage_for_collection(Collection),
                maps:put(Collection, CollectionUsage, Acc)
            end,
            #{},
            Collections
        ),

        % 更新ETS表
        ets:insert(?INDEX_USAGE_TAB, {usage_snapshot, erlang:system_time(millisecond), UsageData}),

        % 更新状态
        NewState = State#state{index_usage = UsageData},

        Summary = #{
            collections_monitored => length(Collections),
            total_indexes => count_total_indexes(UsageData),
            active_indexes => count_active_indexes(UsageData),
            unused_indexes => count_unused_indexes(UsageData)
        },

        ?SLOG(debug, #{
            msg => "index_usage_monitored",
            collections => length(Collections),
            total_indexes => maps:get(total_indexes, Summary)
        }),

        {Summary, NewState}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "index_usage_monitoring_error",
                error => E,
                reason => R,
                stacktrace => S
            }),
            throw({index_usage_monitoring_failed, R})
    end.

%% ============================================================================
%% 辅助函数实现
%% ============================================================================

%% @doc 获取所有集合名称
get_all_collections() ->
    % 返回MongoDB插件使用的所有集合，使用统一的宏定义
    [
        ?DEFAULT_SESSION_COLLECTION,
        ?DEFAULT_SUBSCRIPTION_COLLECTION,
        ?DEFAULT_MESSAGE_COLLECTION,
        ?DEFAULT_WILL_MESSAGE_COLLECTION,
        ?DEFAULT_RETAINED_MESSAGE_COLLECTION,
        ?DEFAULT_DATA_COLLECTION
    ].

%% @doc 分析集合的查询模式
analyze_collection_query_patterns(Collection) ->
    % 这里应该分析实际的查询日志
    % 暂时返回模拟数据
    #{
        common_queries => [
            #{pattern => #{client_id => 1}, frequency => 100},
            #{pattern => #{topic => 1}, frequency => 80},
            #{pattern => #{timestamp => 1}, frequency => 60}
        ],
        query_types => #{
            find => 70,
            insert => 20,
            update => 8,
            delete => 2
        },
        collection => Collection
    }.

%% @doc 获取集合的索引信息
get_collection_indexes(Collection) ->
    try
        case emqx_plugin_mongodb_api:list_indexes(Collection) of
            {ok, Indexes} -> Indexes;
            {error, _} -> []
        end
    catch
        _:_ -> []
    end.

%% @doc 验证存储效率
validate_storage_efficiency(Collection) ->
    try
        % 简化的存储效率验证
        #{
            efficiency_score => 0.8,
            storage_optimization => good,
            recommendation => maintain_current_strategy
        }
    catch
        _:_ ->
            #{efficiency_score => 0.5, storage_optimization => unknown, recommendation => monitor}
    end.

%% @doc 获取集合大小
get_collection_size(Collection) ->
    try
        % 这里应该调用MongoDB API获取实际大小
        % 暂时返回估算值
        case Collection of
            ?SESSIONS_COLLECTION -> 10000000;    % 10MB
            ?MESSAGES_COLLECTION -> 50000000;    % 50MB
            ?RETAINED_COLLECTION -> 5000000;     % 5MB
            _ -> 1000000                          % 1MB
        end
    catch
        _:_ -> 1000000
    end.

%% @doc 分析集合的索引使用情况
analyze_index_usage_for_collection(Collection) ->
    % 这里应该从MongoDB获取实际的索引统计
    % 暂时返回模拟数据
    #{
        collection => Collection,
        indexes => [
            #{name => "_id_", usage_count => 1000, efficiency => 0.95},
            #{name => "client_id_1", usage_count => 800, efficiency => 0.85},
            #{name => "topic_1", usage_count => 600, efficiency => 0.75},
            #{name => "timestamp_1", usage_count => 50, efficiency => 0.30}
        ]
    }.

%% @doc 生成索引推荐
generate_index_recommendations(Collection, IndexUsage, _State) ->
    % 基于查询模式和索引使用情况生成推荐
    Indexes = maps:get(indexes, IndexUsage, []),

    % 识别低效索引
    LowEfficiencyIndexes = lists:filter(
        fun(Index) ->
            maps:get(efficiency, Index, 1.0) < 0.5
        end,
        Indexes
    ),

    % 生成推荐
    lists:map(
        fun(Index) ->
            #{
                action => optimize,
                index_name => maps:get(name, Index),
                collection => Collection,
                reason => "low_efficiency",
                priority => medium
            }
        end,
        LowEfficiencyIndexes
    ).

%% @doc 执行索引优化
execute_index_optimizations(Collection, Recommendations) ->
    lists:map(
        fun(Rec) ->
            Action = maps:get(action, Rec),
            IndexName = maps:get(index_name, Rec, undefined),

            case Action of
                optimize ->
                    {ok, #{action => optimize, index => IndexName, status => "optimized"}};
                create ->
                    {ok, #{action => create, index => IndexName, status => "created"}};
                remove ->
                    {ok, #{action => remove, index => IndexName, status => "removed"}};
                _ ->
                    {error, #{action => Action, reason => "unknown_action"}}
            end
        end,
        Recommendations
    ).

%% @doc 更新优化统计
update_optimization_statistics(State, OptimizationResults) ->
    SuccessfulOpts = length([Result || {ok, Result} <- OptimizationResults]),
    State#state{
        total_optimizations = State#state.total_optimizations + SuccessfulOpts
    }.

%% @doc 获取集合的索引推荐
get_index_recommendations_for_collection(Collection, _State) ->
    % 返回模拟的索引推荐
    [
        #{
            action => create,
            index_spec => #{client_id => 1, timestamp => -1},
            options => #{background => true},
            priority => high,
            estimated_benefit => "improve_query_performance_by_40%"
        }
    ].

%% @doc 安全创建索引
create_index_safely(Collection, IndexSpec, Options) ->
    try
        % 使用emqx_resource:query方式创建索引，与其他模块保持一致
        % 构建正确的索引文档格式
        DefaultIndexName = <<"idx_", Collection/binary, "_", (integer_to_binary(erlang:system_time(millisecond)))/binary>>,
        IndexDoc = #{
            <<"key">> => IndexSpec,
            <<"name">> => maps:get(<<"name">>, Options, DefaultIndexName)
        },
        % 合并其他选项（排除name，因为已经单独处理）
        FinalIndexDoc = maps:merge(IndexDoc, maps:remove(<<"name">>, Options)),
        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                               {create_index, Collection, FinalIndexDoc}) of
            {ok, Result} ->
                {ok, #{collection => Collection, index_spec => IndexSpec, result => Result}};
            {async_return, ok} ->
                {ok, #{collection => Collection, index_spec => IndexSpec, result => async_created}};
            {async_return, {ok, Result}} ->
                {ok, #{collection => Collection, index_spec => IndexSpec, result => Result}};
            {error, Reason} ->
                {error, #{collection => Collection, index_spec => IndexSpec, reason => Reason}};
            {async_return, {error, Reason}} ->
                {error, #{collection => Collection, index_spec => IndexSpec, reason => Reason}}
        end
    catch
        E:R:S ->
            {error, #{collection => Collection, index_spec => IndexSpec,
                     error => E, reason => R, stacktrace => S}}
    end.

%% @doc 获取索引使用统计
get_index_usage_statistics(Collection) ->
    % 这里应该从MongoDB获取实际统计
    % 暂时返回模拟数据
    #{
        collection => Collection,
        total_queries => 10000,
        index_hits => 8500,
        index_misses => 1500,
        indexes => [
            #{name => "_id_", hits => 1000, misses => 0},
            #{name => "client_id_1", hits => 800, misses => 200},
            #{name => "topic_1", hits => 600, misses => 400},
            #{name => "timestamp_1", hits => 50, misses => 950}
        ]
    }.

%% @doc 识别无用索引
identify_unused_indexes(Collection, IndexUsage, State) ->
    Threshold = State#state.index_usage_threshold,
    Indexes = maps:get(indexes, IndexUsage, []),

    % 计算使用率并识别无用索引
    lists:filtermap(
        fun(Index) ->
            Hits = maps:get(hits, Index, 0),
            Misses = maps:get(misses, Index, 0),
            Total = Hits + Misses,

            if
                Total > 0 ->
                    UsageRate = Hits / Total,
                    if
                        UsageRate < Threshold ->
                            {true, maps:get(name, Index)};
                        true ->
                            false
                    end;
                true ->
                    {true, maps:get(name, Index)} % 完全未使用
            end
        end,
        Indexes
    ).

%% @doc 检查索引是否可以安全删除
is_index_safe_to_remove(Collection, IndexName) ->
    % 不能删除_id索引和其他关键索引
    case IndexName of
        "_id_" -> false;
        _ -> true
    end.

%% @doc 安全删除索引
remove_index_safely(Collection, IndexName) ->
    try
        % 使用emqx_resource:query方式删除索引，与其他模块保持一致
        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                               {drop_index, Collection, IndexName}) of
            {ok, Result} ->
                {ok, IndexName, Result};
            {async_return, ok} ->
                {ok, IndexName, async_dropped};
            {async_return, {ok, Result}} ->
                {ok, IndexName, Result};
            {error, Reason} ->
                {error, IndexName, Reason};
            {async_return, {error, Reason}} ->
                {error, IndexName, Reason}
        end
    catch
        E:R:S ->
            {error, IndexName, #{error => E, reason => R, stacktrace => S}}
    end.

%% ============================================================================
%% 高级算法支持函数
%% ============================================================================

%% @doc 收集查询历史数据
collect_query_history_data(Collection) ->
    try
        % 从ETS表和日志中收集查询历史
        ETSQueries = ets:tab2list(?QUERY_STATS_TAB),
        FilteredQueries = lists:filter(
            fun({_Key, QueryData}) ->
                maps:get(collection, QueryData, undefined) =:= Collection
            end,
            ETSQueries
        ),
        [QueryData || {_Key, QueryData} <- FilteredQueries]
    catch
        _:_ -> []
    end.

%% @doc 提取查询特征
extract_query_features(QueryHistory) ->
    lists:map(
        fun(Query) ->
            #{
                query_type => classify_query_type(Query),
                field_count => count_query_fields(Query),
                complexity_score => calculate_query_complexity_score(Query),
                frequency => maps:get(frequency, Query, 1),
                avg_execution_time => maps:get(avg_execution_time, Query, 0)
            }
        end,
        QueryHistory
    ).

%% @doc 查询聚类分析
perform_query_clustering(QueryFeatures) ->
    try
        % 简化的K-means聚类
        ClusterCount = min(5, max(2, length(QueryFeatures) div 10)),
        kmeans_clustering(QueryFeatures, ClusterCount)
    catch
        _:_ ->
            % 失败时返回单一聚类
            [#{cluster_id => 1, queries => QueryFeatures, centroid => #{}}]
    end.

%% @doc K-means聚类实现
kmeans_clustering(Features, K) ->
    case length(Features) of
        N when N < K ->
            [#{cluster_id => 1, queries => Features, centroid => #{}}];
        _ ->
            % 初始化聚类中心
            InitialCentroids = initialize_centroids(Features, K),
            % 迭代聚类
            iterate_kmeans(Features, InitialCentroids, 10)
    end.

%% @doc 初始化聚类中心
initialize_centroids(Features, K) ->
    ShuffledFeatures = shuffle_list(Features),
    lists:sublist(ShuffledFeatures, K).

%% @doc K-means迭代
iterate_kmeans(Features, Centroids, 0) ->
    assign_to_clusters(Features, Centroids);
iterate_kmeans(Features, Centroids, Iterations) ->
    Clusters = assign_to_clusters(Features, Centroids),
    NewCentroids = update_centroids(Clusters),
    case centroids_converged(Centroids, NewCentroids) of
        true -> Clusters;
        false -> iterate_kmeans(Features, NewCentroids, Iterations - 1)
    end.

%% @doc 分配到聚类
assign_to_clusters(Features, Centroids) ->
    ClusterAssignments = lists:map(
        fun(Feature) ->
            ClosestCentroid = find_closest_centroid(Feature, Centroids),
            {Feature, ClosestCentroid}
        end,
        Features
    ),

    % 按聚类分组
    lists:foldl(
        fun({Feature, ClusterId}, Acc) ->
            ClusterFeatures = maps:get(ClusterId, Acc, []),
            Acc#{ClusterId => [Feature | ClusterFeatures]}
        end,
        #{},
        ClusterAssignments
    ).

%% @doc 识别频率模式
identify_frequency_patterns(QueryHistory) ->
    FrequencyMap = lists:foldl(
        fun(Query, Acc) ->
            QueryPattern = extract_query_pattern(Query),
            Count = maps:get(QueryPattern, Acc, 0),
            Acc#{QueryPattern => Count + 1}
        end,
        #{},
        QueryHistory
    ),

    % 按频率排序
    SortedPatterns = lists:sort(
        fun({_, CountA}, {_, CountB}) -> CountA >= CountB end,
        maps:to_list(FrequencyMap)
    ),

    #{
        total_patterns => length(SortedPatterns),
        top_patterns => lists:sublist(SortedPatterns, 10),
        pattern_distribution => calculate_pattern_distribution(SortedPatterns)
    }.

%% @doc 分析时间模式
analyze_temporal_query_patterns(QueryHistory) ->
    % 按小时分组查询
    HourlyPatterns = group_queries_by_hour(QueryHistory),

    % 按星期几分组
    WeeklyPatterns = group_queries_by_weekday(QueryHistory),

    #{
        hourly_patterns => HourlyPatterns,
        weekly_patterns => WeeklyPatterns,
        peak_hours => identify_peak_hours(HourlyPatterns),
        peak_days => identify_peak_days(WeeklyPatterns)
    }.

%% @doc 分析字段访问模式
analyze_field_access_patterns(QueryHistory) ->
    FieldAccess = lists:foldl(
        fun(Query, Acc) ->
            Fields = extract_query_fields(Query),
            lists:foldl(
                fun(Field, FieldAcc) ->
                    Count = maps:get(Field, FieldAcc, 0),
                    FieldAcc#{Field => Count + 1}
                end,
                Acc,
                Fields
            )
        end,
        #{},
        QueryHistory
    ),

    SortedFields = lists:sort(
        fun({_, CountA}, {_, CountB}) -> CountA >= CountB end,
        maps:to_list(FieldAccess)
    ),

    #{
        field_access_frequency => SortedFields,
        most_accessed_fields => lists:sublist(SortedFields, 10),
        field_combinations => analyze_field_combinations(QueryHistory)
    }.

%% @doc 分析查询复杂度
analyze_query_complexity(QueryHistory) ->
    ComplexityStats = lists:map(
        fun(Query) ->
            calculate_query_complexity_score(Query)
        end,
        QueryHistory
    ),

    #{
        avg_complexity => lists:sum(ComplexityStats) / length(ComplexityStats),
        max_complexity => lists:max(ComplexityStats),
        min_complexity => lists:min(ComplexityStats),
        complexity_distribution => calculate_complexity_distribution(ComplexityStats)
    }.

%% @doc 识别性能瓶颈
identify_performance_bottlenecks(QueryHistory) ->
    % 识别慢查询
    SlowQueries = lists:filter(
        fun(Query) ->
            ExecutionTime = maps:get(execution_time, Query, 0),
            ExecutionTime > 1000  % 超过1秒的查询
        end,
        QueryHistory
    ),

    % 分析瓶颈原因
    BottleneckAnalysis = analyze_bottleneck_causes(SlowQueries),

    #{
        slow_queries => SlowQueries,
        bottleneck_analysis => BottleneckAnalysis,
        optimization_opportunities => identify_optimization_opportunities(SlowQueries)
    }.

%% ============================================================================
%% 性能预测和分析支持函数
%% ============================================================================

%% @doc 预测查询加速
predict_query_acceleration(IndexSpec, QueryPatterns) ->
    try
        IndexFields = extract_index_fields(IndexSpec),

        % 计算匹配的查询模式
        MatchingPatterns = find_matching_query_patterns(IndexFields, QueryPatterns),

        % 预测加速比例
        AccelerationFactor = calculate_acceleration_factor(IndexFields, MatchingPatterns),

        #{
            acceleration_factor => AccelerationFactor,
            matching_queries => length(MatchingPatterns),
            estimated_improvement => AccelerationFactor * 0.7  % 保守估计
        }
    catch
        _:_ ->
            #{acceleration_factor => 1.0, matching_queries => 0, estimated_improvement => 0.0}
    end.

%% @doc 预测写入性能影响
predict_write_performance_impact(IndexSpec, _QueryPatterns) ->
    try
        IndexComplexity = calculate_index_complexity(IndexSpec),

        % 写入性能影响通常与索引复杂度成正比
        WriteImpact = IndexComplexity * 0.1,  % 10%的基础影响

        #{
            write_slowdown_factor => 1.0 + WriteImpact,
            estimated_write_impact => WriteImpact,
            impact_category => categorize_write_impact(WriteImpact)
        }
    catch
        _:_ ->
            #{write_slowdown_factor => 1.05, estimated_write_impact => 0.05, impact_category => low}
    end.

%% @doc 预测存储影响
predict_storage_impact(IndexSpec, Collection) ->
    try
        % 获取当前集合大小并估算索引大小
        CurrentCollectionSize = case Collection of
            ?SESSIONS_COLLECTION -> 10000000;    % 10MB
            ?MESSAGES_COLLECTION -> 50000000;    % 50MB
            ?RETAINED_COLLECTION -> 5000000;     % 5MB
            _ -> 1000000                          % 1MB
        end,

        FieldCount = length(maps:get(fields, IndexSpec, [])),
        EstimatedIndexSize = CurrentCollectionSize * FieldCount * 0.05,

        StorageIncrease = case CurrentCollectionSize of
            0 -> EstimatedIndexSize;
            Size -> EstimatedIndexSize / Size
        end,

        #{
            estimated_index_size => EstimatedIndexSize,
            storage_increase_ratio => StorageIncrease,
            storage_impact_category => categorize_storage_impact(StorageIncrease)
        }
    catch
        _:_ ->
            #{estimated_index_size => 0, storage_increase_ratio => 0.1, storage_impact_category => low}
    end.

%% @doc 预测维护开销
predict_maintenance_overhead(IndexSpec) ->
    try
        IndexComplexity = calculate_index_complexity(IndexSpec),

        % 维护开销包括：重建时间、碎片整理、统计更新
        MaintenanceScore = IndexComplexity * 0.2,

        #{
            maintenance_score => MaintenanceScore,
            rebuild_time_estimate => MaintenanceScore * 100,  % 毫秒
            fragmentation_risk => categorize_fragmentation_risk(IndexComplexity),
            maintenance_frequency => calculate_maintenance_frequency(IndexComplexity)
        }
    catch
        _:_ ->
            #{maintenance_score => 0.1, rebuild_time_estimate => 10, fragmentation_risk => low, maintenance_frequency => monthly}
    end.

%% @doc 计算整体性能预测
calculate_overall_performance_prediction(QueryAcceleration, WriteImpact, StorageImpact, MaintenanceOverhead) ->
    try
        % 权重分配：查询性能50%，写入影响30%，存储影响15%，维护开销5%
        QueryScore = maps:get(estimated_improvement, QueryAcceleration, 0.0) * 0.5,
        WriteScore = (1.0 - maps:get(estimated_write_impact, WriteImpact, 0.05)) * 0.3,
        StorageScore = (1.0 - maps:get(storage_increase_ratio, StorageImpact, 0.1)) * 0.15,
        MaintenanceScore = (1.0 - maps:get(maintenance_score, MaintenanceOverhead, 0.1)) * 0.05,

        OverallScore = QueryScore + WriteScore + StorageScore + MaintenanceScore,

        #{
            overall_score => OverallScore,
            score_breakdown => #{
                query_contribution => QueryScore,
                write_contribution => WriteScore,
                storage_contribution => StorageScore,
                maintenance_contribution => MaintenanceScore
            },
            recommendation_level => categorize_recommendation_level(OverallScore)
        }
    catch
        _:_ ->
            #{overall_score => 0.5, recommendation_level => neutral}
    end.

%% @doc 计算索引成本
calculate_index_costs(_IndexSpec, PerformancePred) ->
    try
        % 存储成本
        StorageCost = maps:get(estimated_index_size,
            maps:get(storage_impact, PerformancePred, #{}), 0),

        % 维护成本
        MaintenanceCost = maps:get(maintenance_score,
            maps:get(maintenance_overhead, PerformancePred, #{}), 0.1),

        % 写入性能成本
        WriteCost = maps:get(estimated_write_impact,
            maps:get(write_impact, PerformancePred, #{}), 0.05),

        TotalCost = StorageCost * 0.4 + MaintenanceCost * 0.3 + WriteCost * 0.3,

        #{
            storage_cost => StorageCost,
            maintenance_cost => MaintenanceCost,
            write_performance_cost => WriteCost,
            total_cost => TotalCost
        }
    catch
        _:_ ->
            #{storage_cost => 0.1, maintenance_cost => 0.1, write_performance_cost => 0.05, total_cost => 0.25}
    end.

%% @doc 计算索引收益
calculate_index_benefits(_IndexSpec, PerformancePred) ->
    try
        % 查询性能收益
        QueryBenefit = maps:get(estimated_improvement,
            maps:get(query_acceleration, PerformancePred, #{}), 0.0),

        % 匹配查询数量收益
        QueryCountBenefit = maps:get(matching_queries,
            maps:get(query_acceleration, PerformancePred, #{}), 0) * 0.01,

        TotalBenefit = QueryBenefit * 0.8 + QueryCountBenefit * 0.2,

        #{
            query_performance_benefit => QueryBenefit,
            query_count_benefit => QueryCountBenefit,
            total_benefit => TotalBenefit
        }
    catch
        _:_ ->
            #{query_performance_benefit => 0.1, query_count_benefit => 0.05, total_benefit => 0.15}
    end.

%% @doc 计算ROI
calculate_index_roi(Costs, Benefits) ->
    try
        TotalCost = maps:get(total_cost, Costs, 0.1),
        TotalBenefit = maps:get(total_benefit, Benefits, 0.1),

        ROI = case TotalCost of
            0 -> 0.0;
            _ -> (TotalBenefit - TotalCost) / TotalCost
        end,

        #{
            roi => ROI,
            roi_category => categorize_roi(ROI),
            payback_period => calculate_payback_period(TotalCost, TotalBenefit)
        }
    catch
        _:_ ->
            #{roi => 0.0, roi_category => neutral, payback_period => infinity}
    end.

%% ============================================================================
%% 工具函数和分类函数
%% ============================================================================

%% @doc 分类查询类型
classify_query_type(Query) ->
    QueryDoc = maps:get(query, Query, #{}),
    case maps:size(QueryDoc) of
        0 -> find_all;
        1 -> simple_query;
        N when N =< 3 -> moderate_query;
        _ -> complex_query
    end.

%% @doc 计算查询字段数量
count_query_fields(Query) ->
    QueryDoc = maps:get(query, Query, #{}),
    maps:size(QueryDoc).

%% @doc 计算查询复杂度分数
calculate_query_complexity_score(Query) ->
    FieldCount = count_query_fields(Query),
    HasSort = maps:is_key(sort, Query),
    HasLimit = maps:is_key(limit, Query),
    HasProjection = maps:is_key(projection, Query),

    BaseScore = FieldCount * 0.2,
    SortScore = case HasSort of true -> 0.3; false -> 0.0 end,
    LimitScore = case HasLimit of true -> 0.1; false -> 0.0 end,
    ProjectionScore = case HasProjection of true -> 0.1; false -> 0.0 end,

    BaseScore + SortScore + LimitScore + ProjectionScore.

%% @doc 提取查询模式
extract_query_pattern(Query) ->
    QueryDoc = maps:get(query, Query, #{}),
    Fields = maps:keys(QueryDoc),
    lists:sort(Fields).

%% @doc 计算模式分布
calculate_pattern_distribution(SortedPatterns) ->
    TotalCount = lists:sum([Count || {_, Count} <- SortedPatterns]),
    lists:map(
        fun({Pattern, Count}) ->
            {Pattern, Count / TotalCount}
        end,
        SortedPatterns
    ).

%% @doc 按小时分组查询
group_queries_by_hour(QueryHistory) ->
    lists:foldl(
        fun(Query, Acc) ->
            Timestamp = maps:get(timestamp, Query, 0),
            Hour = (Timestamp div 3600000) rem 24,
            Count = maps:get(Hour, Acc, 0),
            Acc#{Hour => Count + 1}
        end,
        #{},
        QueryHistory
    ).

%% @doc 按星期几分组查询
group_queries_by_weekday(QueryHistory) ->
    lists:foldl(
        fun(Query, Acc) ->
            Timestamp = maps:get(timestamp, Query, 0),
            % 简化的星期计算
            Weekday = (Timestamp div 86400000) rem 7,
            Count = maps:get(Weekday, Acc, 0),
            Acc#{Weekday => Count + 1}
        end,
        #{},
        QueryHistory
    ).

%% @doc 识别高峰时段
identify_peak_hours(HourlyPatterns) ->
    SortedHours = lists:sort(
        fun({_, CountA}, {_, CountB}) -> CountA >= CountB end,
        maps:to_list(HourlyPatterns)
    ),
    lists:sublist(SortedHours, 3).

%% @doc 识别高峰日期
identify_peak_days(WeeklyPatterns) ->
    SortedDays = lists:sort(
        fun({_, CountA}, {_, CountB}) -> CountA >= CountB end,
        maps:to_list(WeeklyPatterns)
    ),
    lists:sublist(SortedDays, 2).

%% @doc 提取查询字段
extract_query_fields(Query) ->
    QueryDoc = maps:get(query, Query, #{}),
    maps:keys(QueryDoc).

%% @doc 分析字段组合
analyze_field_combinations(QueryHistory) ->
    Combinations = lists:foldl(
        fun(Query, Acc) ->
            Fields = extract_query_fields(Query),
            SortedFields = lists:sort(Fields),
            Count = maps:get(SortedFields, Acc, 0),
            Acc#{SortedFields => Count + 1}
        end,
        #{},
        QueryHistory
    ),

    SortedCombinations = lists:sort(
        fun({_, CountA}, {_, CountB}) -> CountA >= CountB end,
        maps:to_list(Combinations)
    ),

    lists:sublist(SortedCombinations, 10).

%% @doc 计算复杂度分布
calculate_complexity_distribution(ComplexityStats) ->
    Low = length([S || S <- ComplexityStats, S =< 0.3]),
    Medium = length([S || S <- ComplexityStats, S > 0.3, S =< 0.7]),
    High = length([S || S <- ComplexityStats, S > 0.7]),

    Total = length(ComplexityStats),
    #{
        low => Low / Total,
        medium => Medium / Total,
        high => High / Total
    }.

%% @doc 分析瓶颈原因
analyze_bottleneck_causes(SlowQueries) ->
    lists:map(
        fun(Query) ->
            PossibleCauses = [],

            % 检查是否缺少索引
            MissingIndexCause = case has_suitable_index(Query) of
                false -> [missing_index];
                true -> []
            end,

            % 检查查询复杂度
            ComplexityCause = case calculate_query_complexity_score(Query) > 0.7 of
                true -> [high_complexity];
                false -> []
            end,

            AllCauses = PossibleCauses ++ MissingIndexCause ++ ComplexityCause,

            #{
                query => Query,
                possible_causes => AllCauses,
                primary_cause => case AllCauses of
                    [] -> unknown;
                    [First | _] -> First
                end
            }
        end,
        SlowQueries
    ).

%% @doc 识别优化机会
identify_optimization_opportunities(SlowQueries) ->
    lists:map(
        fun(Query) ->
            #{
                query => Query,
                optimization_type => suggest_optimization_type(Query),
                estimated_improvement => estimate_optimization_improvement(Query)
            }
        end,
        SlowQueries
    ).

%% @doc 检查是否有合适的索引
has_suitable_index(Query) ->
    try
        QueryFields = extract_query_fields(Query),
        % 简化检查：如果查询字段少于3个，认为可能有合适索引
        length(QueryFields) =< 2
    catch
        _:_ -> false
    end.

%% @doc 建议优化类型
suggest_optimization_type(Query) ->
    ComplexityScore = calculate_query_complexity_score(Query),
    FieldCount = count_query_fields(Query),

    if
        ComplexityScore > 0.7 -> query_simplification;
        FieldCount > 3 -> compound_index;
        true -> single_field_index
    end.

%% @doc 估算优化改进
estimate_optimization_improvement(Query) ->
    ComplexityScore = calculate_query_complexity_score(Query),
    % 复杂度越高，优化潜力越大
    min(0.8, ComplexityScore * 1.2).

%% @doc 分类存储影响
categorize_storage_impact(StorageIncrease) ->
    if
        StorageIncrease < 0.1 -> low;
        StorageIncrease < 0.3 -> medium;
        true -> high
    end.

%% @doc 计算索引复杂度
calculate_index_complexity(IndexSpec) ->
    try
        Fields = maps:get(fields, IndexSpec, []),
        FieldCount = length(Fields),

        % 基础复杂度基于字段数量
        BaseComplexity = FieldCount * 0.2,

        % 检查特殊索引类型
        TypeComplexity = case maps:get(type, IndexSpec, normal) of
            text -> 0.3;
            geo -> 0.4;
            compound -> 0.2;
            _ -> 0.0
        end,

        BaseComplexity + TypeComplexity
    catch
        _:_ -> 0.2
    end.

%% @doc 分类碎片风险
categorize_fragmentation_risk(IndexComplexity) ->
    if
        IndexComplexity < 0.3 -> low;
        IndexComplexity < 0.6 -> medium;
        true -> high
    end.

%% @doc 计算维护频率
calculate_maintenance_frequency(IndexComplexity) ->
    if
        IndexComplexity < 0.3 -> quarterly;
        IndexComplexity < 0.6 -> monthly;
        true -> weekly
    end.

%% @doc 分类推荐级别
categorize_recommendation_level(OverallScore) ->
    if
        OverallScore >= 0.8 -> highly_recommended;
        OverallScore >= 0.6 -> recommended;
        OverallScore >= 0.4 -> neutral;
        OverallScore >= 0.2 -> not_recommended;
        true -> strongly_not_recommended
    end.

%% @doc 分类ROI
categorize_roi(ROI) ->
    if
        ROI >= 1.0 -> excellent;
        ROI >= 0.5 -> good;
        ROI >= 0.0 -> neutral;
        ROI >= -0.5 -> poor;
        true -> very_poor
    end.

%% @doc 计算投资回收期
calculate_payback_period(TotalCost, TotalBenefit) ->
    case TotalBenefit of
        0 -> infinity;
        _ when TotalBenefit =< TotalCost -> infinity;
        _ -> TotalCost / (TotalBenefit - TotalCost)
    end.

%% @doc 找到最近的聚类中心
find_closest_centroid(Feature, Centroids) ->
    Distances = lists:map(
        fun(Centroid) ->
            calculate_feature_distance(Feature, Centroid)
        end,
        Centroids
    ),

    {_MinDistance, MinIndex} = lists:foldl(
        fun({Distance, Index}, {MinDist, MinIdx}) ->
            case Distance < MinDist of
                true -> {Distance, Index};
                false -> {MinDist, MinIdx}
            end
        end,
        {hd(Distances), 1},
        lists:zip(Distances, lists:seq(1, length(Distances)))
    ),

    MinIndex.

%% @doc 计算特征距离
calculate_feature_distance(Feature1, Feature2) ->
    % 简化的欧几里得距离计算
    ComplexityDiff = abs(maps:get(complexity_score, Feature1, 0) - maps:get(complexity_score, Feature2, 0)),
    FieldCountDiff = abs(maps:get(field_count, Feature1, 0) - maps:get(field_count, Feature2, 0)),
    math:sqrt(ComplexityDiff * ComplexityDiff + FieldCountDiff * FieldCountDiff).

%% @doc 提取索引字段
extract_index_fields(IndexSpec) ->
    maps:get(fields, IndexSpec, []).

%% @doc 找到匹配的查询模式
find_matching_query_patterns(IndexFields, QueryPatterns) ->
    FieldAccessPatterns = maps:get(field_access_patterns, QueryPatterns, #{}),
    MostAccessedFields = maps:get(most_accessed_fields, FieldAccessPatterns, []),

    % 找到使用这些索引字段的查询
    lists:filter(
        fun({Field, _Count}) ->
            lists:member(Field, IndexFields)
        end,
        MostAccessedFields
    ).

%% @doc 计算加速因子
calculate_acceleration_factor(_IndexFields, MatchingPatterns) ->
    case length(MatchingPatterns) of
        0 -> 1.0;
        N -> min(5.0, 1.0 + N * 0.3)  % 最多5倍加速
    end.

%% @doc 分类写入影响
categorize_write_impact(WriteImpact) ->
    if
        WriteImpact < 0.05 -> low;
        WriteImpact < 0.15 -> medium;
        true -> high
    end.



%% @doc 执行基础优化
execute_basic_optimization(Collection, BasicRecommendations) ->
    lists:map(
        fun(Rec) ->
            #{action => create, status => simulated, recommendation => Rec}
        end,
        BasicRecommendations
    ).

%% @doc 生成基础索引推荐
generate_basic_index_recommendations(Collection) ->
    [
        #{index_spec => #{fields => [<<"_id">>]}, priority => low},
        #{index_spec => #{fields => [<<"timestamp">>]}, priority => medium}
    ].

%% @doc 洗牌列表
shuffle_list(List) ->
    % 简化的洗牌实现
    lists:sort(fun(_, _) -> rand:uniform() > 0.5 end, List).

%% @doc 更新聚类中心
update_centroids(Clusters) ->
    maps:fold(
        fun(_ClusterId, Features, Acc) ->
            Centroid = calculate_centroid(Features),
            [Centroid | Acc]
        end,
        [],
        Clusters
    ).

%% @doc 计算聚类中心
calculate_centroid(Features) ->
    case length(Features) of
        0 -> #{};
        N ->
            ComplexitySum = lists:sum([maps:get(complexity_score, F, 0) || F <- Features]),
            FieldCountSum = lists:sum([maps:get(field_count, F, 0) || F <- Features]),
            #{
                complexity_score => ComplexitySum / N,
                field_count => FieldCountSum / N
            }
    end.

%% @doc 检查聚类中心是否收敛
centroids_converged(OldCentroids, NewCentroids) ->
    % 简化检查：如果长度不同则未收敛
    length(OldCentroids) =:= length(NewCentroids).

%% @doc 计算查询时间改进
calculate_query_time_improvement(BaselinePerf, PostOptPerf) ->
    BaselineTime = maps:get(avg_query_time, BaselinePerf, 1000),
    PostOptTime = maps:get(avg_query_time, PostOptPerf, 1000),
    case BaselineTime of
        0 -> 0.0;
        _ -> (BaselineTime - PostOptTime) / BaselineTime
    end.

%% @doc 计算吞吐量改进
calculate_throughput_improvement(BaselinePerf, PostOptPerf) ->
    BaselineThroughput = maps:get(throughput, BaselinePerf, 100),
    PostOptThroughput = maps:get(throughput, PostOptPerf, 100),
    case BaselineThroughput of
        0 -> 0.0;
        _ -> (PostOptThroughput - BaselineThroughput) / BaselineThroughput
    end.

%% @doc 计算索引效率改进
calculate_index_efficiency_improvement(BaselinePerf, PostOptPerf) ->
    BaselineEfficiency = maps:get(index_efficiency, BaselinePerf, 0.5),
    PostOptEfficiency = maps:get(index_efficiency, PostOptPerf, 0.5),
    PostOptEfficiency - BaselineEfficiency.

%% @doc 分类改进程度
categorize_improvement(OverallImprovement) ->
    if
        OverallImprovement >= 0.5 -> excellent;
        OverallImprovement >= 0.3 -> good;
        OverallImprovement >= 0.1 -> moderate;
        OverallImprovement >= 0.0 -> minimal;
        true -> negative
    end.

%% @doc 更新模型参数
update_model_parameters(QueryPatterns, LearningFeatures) ->
    % 简化的模型参数更新
    maps:merge(QueryPatterns, #{
        learning_features => LearningFeatures,
        last_update => erlang:system_time(millisecond)
    }).

%% @doc 更新推荐权重
update_recommendation_weights(ValidationResults) ->
    % 基于验证结果调整推荐权重
    OverallImprovement = maps:get(overall_improvement,
        maps:get(query_performance_improvement, ValidationResults, #{}), 0.0),
    #{
        performance_weight => min(1.0, max(0.1, OverallImprovement)),
        cost_weight => 1.0 - min(1.0, max(0.1, OverallImprovement))
    }.

%% @doc 保存学习结果
save_learning_results(Collection, LearningFeatures, UpdatedModelParams) ->
    try
        % 保存到ETS表或文件
        ?SLOG(info, #{
            msg => "learning_results_saved",
            collection => Collection,
            features_count => length(LearningFeatures),
            model_params => UpdatedModelParams
        })
    catch
        _:_ -> ok
    end.

%% @doc 提取优化学习特征
extract_optimization_learning_features(Collection, OptimizationResults, ValidationResults) ->
    [
        #{
            collection => Collection,
            optimization_type => maps:get(optimization_type, OptimizationResults, unknown),
            performance_improvement => maps:get(overall_improvement,
                maps:get(query_performance_improvement, ValidationResults, #{}), 0.0),
            timestamp => erlang:system_time(millisecond)
        }
    ].

%% @doc 验证系统稳定性
validate_system_stability(Collection) ->
    try
        % 简化的稳定性检查
        #{
            stability_score => 0.8,
            issues_detected => [],
            recommendation => stable
        }
    catch
        _:_ ->
            #{stability_score => 0.5, issues_detected => [unknown_error], recommendation => monitor}
    end.

%% @doc 做出回滚决策
make_rollback_decision(QueryPerformanceImprovement, IndexUsageValidation,
                      StorageEfficiencyValidation, StabilityValidation) ->
    % 检查各项指标
    QueryImprovement = maps:get(overall_improvement, QueryPerformanceImprovement, 0.0),
    StabilityScore = maps:get(stability_score, StabilityValidation, 0.8),

    % 如果性能下降或稳定性差，建议回滚
    if
        QueryImprovement < -0.1 -> {rollback_required, performance_degradation};
        StabilityScore < 0.5 -> {rollback_required, stability_issues};
        true -> no_rollback_needed
    end.

%% @doc 执行优化回滚
execute_optimization_rollback(Collection, OptimizationResults, Reason) ->
    try
        ?SLOG(warning, #{
            msg => "executing_optimization_rollback",
            collection => Collection,
            reason => Reason
        }),

        % 简化的回滚实现
        #{
            rollback_status => completed,
            reason => Reason,
            actions_reverted => length(maps:get(phase_results, OptimizationResults, []))
        }
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "rollback_failed",
                collection => Collection,
                error => E,
                reason => R,
                stacktrace => S
            }),
            #{rollback_status => failed, error => R}
    end.



%% @doc 运行查询性能基准测试
run_query_performance_benchmark(Collection) ->
    % 简化实现：返回基准测试结果
    #{
        collection => Collection,
        baseline_performance => #{
            avg_query_time => 100,
            throughput => 500,
            index_efficiency => 0.8
        }
    }.

%% @doc 收集索引使用统计
collect_index_usage_statistics(Collection) ->
    % 简化实现：返回索引使用统计
    #{
        collection => Collection,
        index_stats => [
            #{name => "_id_", usage_count => 1000, efficiency => 0.95},
            #{name => "client_id_1", usage_count => 800, efficiency => 0.85}
        ]
    }.

%% @doc 分析索引存储使用
analyze_index_storage_usage(Collection) ->
    % 简化实现：返回存储使用分析
    #{
        collection => Collection,
        total_index_size => 10485760,  % 10MB
        storage_efficiency => 0.8
    }.

%% @doc 评估索引维护成本
evaluate_index_maintenance_costs(Collection) ->
    % 简化实现：返回维护成本评估
    #{
        collection => Collection,
        maintenance_cost => 0.2,
        maintenance_frequency => monthly
    }.

%% @doc 生成基于模式的推荐
generate_pattern_based_recommendations(QueryPatterns) ->
    % 简化实现：基于查询模式生成推荐
    [
        #{type => single_field, field => <<"client_id">>, priority => high},
        #{type => compound, fields => [<<"topic">>, <<"timestamp">>], priority => medium}
    ].

%% @doc 生成基于性能的推荐
generate_performance_based_recommendations(PerformanceData) ->
    % 简化实现：基于性能数据生成推荐
    [
        #{type => optimization, target => slow_queries, priority => high},
        #{type => index_creation, field => <<"timestamp">>, priority => medium}
    ].

%% @doc 生成基于机器学习的推荐
generate_ml_based_recommendations(QueryPatterns, PerformanceData, State) ->
    % 简化实现：基于ML生成推荐
    [
        #{type => ml_optimized, confidence => 0.85, priority => high},
        #{type => predictive, field => <<"predicted_field">>, priority => medium}
    ].

%% @doc 生成复合索引推荐
generate_compound_index_recommendations(QueryPatterns, PerformanceData) ->
    % 简化实现：生成复合索引推荐
    [
        #{type => compound, fields => [<<"field1">>, <<"field2">>], priority => high}
    ].

%% @doc 生成部分索引推荐
generate_partial_index_recommendations(QueryPatterns) ->
    % 简化实现：生成部分索引推荐
    [
        #{type => partial, condition => #{status => active}, priority => medium}
    ].

%% @doc 生成文本索引推荐
generate_text_index_recommendations(QueryPatterns) ->
    % 简化实现：生成文本索引推荐
    [
        #{type => text, field => <<"content">>, priority => low}
    ].

%% @doc 生成地理空间索引推荐
generate_geospatial_index_recommendations(QueryPatterns) ->
    % 简化实现：生成地理空间索引推荐
    [
        #{type => geo, field => <<"location">>, priority => low}
    ].

%% @doc 去重推荐
deduplicate_recommendations(Recommendations) ->
    % 简化实现：去除重复推荐
    lists:usort(Recommendations).

%% @doc 优先级排序推荐
prioritize_recommendations(Recommendations, QueryPatterns, PerformanceData) ->
    % 简化实现：按优先级排序
    lists:sort(fun(A, B) ->
        maps:get(priority, A, low) >= maps:get(priority, B, low)
    end, Recommendations).

%% @doc 评分推荐
score_recommendations(Recommendations, QueryPatterns, PerformanceData) ->
    % 简化实现：为推荐评分
    lists:map(fun(Rec) ->
        Score = case maps:get(priority, Rec, low) of
            high -> 0.9;
            medium -> 0.6;
            low -> 0.3
        end,
        Rec#{score => Score}
    end, Recommendations).



%% @doc 生成预测摘要
generate_prediction_summary(PredictionResults) ->
    % 简化实现：生成预测摘要
    #{
        total_predictions => length(PredictionResults),
        high_confidence => 5,
        medium_confidence => 3,
        low_confidence => 2
    }.

%% @doc 计算预测置信度
calculate_prediction_confidence(PredictionResults) ->
    % 简化实现：计算平均置信度
    0.75.

%% @doc 评估索引实施风险
assess_index_implementation_risk(IndexSpec, PerformanceData) ->
    % 简化实现：评估实施风险
    #{
        risk_level => medium,
        risk_factors => [storage_increase, maintenance_overhead],
        mitigation_strategies => [phased_implementation, monitoring]
    }.

%% @doc 计算实施优先级
calculate_implementation_priority(CostBenefitAnalysis, RiskAssessment, PerformanceData) ->
    % 简化实现：计算实施优先级
    high.

%% @doc 生成整体成本效益分析
generate_overall_cost_benefit_analysis(CostBenefitResults) ->
    % 简化实现：生成整体分析
    #{
        overall_roi => 0.8,
        recommendation => proceed_with_caution,
        key_benefits => [improved_query_performance, reduced_response_time],
        key_costs => [storage_overhead, maintenance_complexity]
    }.

%% @doc 获取嵌套值
get_nested_value(Map, Key, Default) ->
    maps:get(Key, Map, Default).

%% @doc 应用资源约束
apply_resource_constraints(Strategy, Constraints) ->
    % 简化实现：应用资源约束
    Strategy#{constraints_applied => true}.

%% @doc 创建分阶段实施策略
create_phased_implementation_strategy(OptimalStrategy) ->
    % 简化实现：创建分阶段策略
    #{
        phase_1 => #{actions => [create_basic_indexes], duration => "1 week"},
        phase_2 => #{actions => [optimize_compound_indexes], duration => "2 weeks"},
        phase_3 => #{actions => [fine_tune_performance], duration => "1 week"}
    }.

%% @doc 验证实施策略
validate_implementation_strategy(Strategy, State) ->
    % 简化实现：验证策略
    #{validation_result => passed, issues => []}.

%% @doc 创建保守策略
create_conservative_strategy(OptimalStrategy) ->
    % 简化实现：创建保守策略
    OptimalStrategy#{approach => conservative, risk_level => low}.

%% @doc 准备优化环境
prepare_optimization_environment(Collection, Strategy) ->
    % 简化实现：准备优化环境
    #{environment_ready => true, collection => Collection}.

%% @doc 执行优化阶段
execute_optimization_phase(Collection, Phase, State) ->
    % 简化实现：执行优化阶段
    #{phase_result => completed, collection => Collection}.

%% @doc 后处理优化结果
post_process_optimization_results(Results, State) ->
    % 简化实现：后处理结果
    Results#{post_processed => true}.

%% @doc 运行优化后性能测试
run_post_optimization_performance_test(Collection) ->
    % 简化实现：运行性能测试
    #{
        collection => Collection,
        post_optimization_performance => #{
            avg_query_time => 80,
            throughput => 600,
            index_efficiency => 0.9
        }
    }.

%% @doc 验证查询性能改进
validate_query_performance_improvement(BaselinePerf, PostOptPerf) ->
    % 简化实现：验证性能改进
    QueryTimeImprovement = calculate_query_time_improvement(BaselinePerf, PostOptPerf),
    ThroughputImprovement = calculate_throughput_improvement(BaselinePerf, PostOptPerf),
    IndexEfficiencyImprovement = calculate_index_efficiency_improvement(BaselinePerf, PostOptPerf),

    OverallImprovement = (QueryTimeImprovement + ThroughputImprovement + IndexEfficiencyImprovement) / 3,

    #{
        query_time_improvement => QueryTimeImprovement,
        throughput_improvement => ThroughputImprovement,
        index_efficiency_improvement => IndexEfficiencyImprovement,
        overall_improvement => OverallImprovement,
        improvement_category => categorize_improvement(OverallImprovement)
    }.

%% @doc 验证索引使用改进
validate_index_usage_improvement(Collection) ->
    % 简化实现：验证索引使用改进
    #{
        collection => Collection,
        index_usage_improvement => 0.2,
        new_index_efficiency => 0.9
    }.

%% @doc 获取查询性能数据
get_query_performance_data(StartTime, EndTime) ->
    % 从ETS表中获取指定时间范围的查询性能数据
    AllData = ets:tab2list(?QUERY_PERFORMANCE_TAB),

    lists:filter(
        fun({_Key, QueryData}) ->
            Timestamp = maps:get(timestamp, QueryData, 0),
            Timestamp >= StartTime andalso Timestamp =< EndTime
        end,
        AllData
    ).

%% @doc 计算平均延迟
calculate_average_latency([]) -> 0;
calculate_average_latency(PerformanceData) ->
    Latencies = [maps:get(duration, Data, 0) || {_Key, Data} <- PerformanceData],
    case Latencies of
        [] -> 0;
        _ -> lists:sum(Latencies) / length(Latencies)
    end.

%% @doc 统计慢查询数量
count_slow_queries(PerformanceData, Threshold) ->
    length([1 || {_Key, Data} <- PerformanceData,
                 maps:get(duration, Data, 0) >= Threshold]).

%% @doc 分析查询分布
analyze_query_distribution(PerformanceData) ->
    % 按查询类型分组统计
    Distribution = lists:foldl(
        fun({_Key, Data}, Acc) ->
            QueryType = maps:get(query_type, Data, unknown),
            Count = maps:get(QueryType, Acc, 0),
            maps:put(QueryType, Count + 1, Acc)
        end,
        #{},
        PerformanceData
    ),

    Total = lists:sum(maps:values(Distribution)),

    % 计算百分比
    maps:map(
        fun(_Type, Count) ->
            if
                Total > 0 -> (Count / Total) * 100;
                true -> 0
            end
        end,
        Distribution
    ).

%% @doc 分析性能趋势
analyze_performance_trends(PerformanceData) ->
    % 按时间排序
    SortedData = lists:sort(
        fun({_, A}, {_, B}) ->
            maps:get(timestamp, A, 0) =< maps:get(timestamp, B, 0)
        end,
        PerformanceData
    ),

    % 计算趋势
    case length(SortedData) of
        N when N < 3 ->
            #{trend => insufficient_data};
        _ ->
            Latencies = [maps:get(duration, Data, 0) || {_Key, Data} <- SortedData],
            Trend = calculate_trend_direction(Latencies),
            #{
                trend => Trend,
                data_points => length(SortedData),
                latest_latency => lists:last(Latencies),
                average_latency => lists:sum(Latencies) / length(Latencies)
            }
    end.

%% @doc 计算趋势方向
calculate_trend_direction(Values) when length(Values) < 3 ->
    stable;
calculate_trend_direction(Values) ->
    N = length(Values),
    X = lists:seq(1, N),
    Y = Values,

    % 简单线性回归
    SumX = lists:sum(X),
    SumY = lists:sum(Y),
    SumXY = lists:sum([Xi * Yi || {Xi, Yi} <- lists:zip(X, Y)]),
    SumX2 = lists:sum([Xi * Xi || Xi <- X]),

    Slope = (N * SumXY - SumX * SumY) / (N * SumX2 - SumX * SumX),

    if
        Slope > 5 -> increasing;
        Slope < -5 -> decreasing;
        true -> stable
    end.

%% @doc 分析查询结构
analyze_query_structure(Query) ->
    #{
        has_index_hints => maps:is_key(hint, Query),
        has_sort => maps:is_key(sort, Query),
        has_limit => maps:is_key(limit, Query),
        has_projection => maps:is_key(projection, Query),
        filter_complexity => analyze_filter_complexity(maps:get(filter, Query, #{}))
    }.

%% @doc 分析过滤器复杂度
analyze_filter_complexity(Filter) ->
    case maps:size(Filter) of
        0 -> simple;
        N when N =< 3 -> medium;
        _ -> complex
    end.

%% @doc 生成查询优化建议
generate_query_optimizations(Query, Collection, Analysis) ->
    Optimizations = [],

    % 检查是否需要添加索引提示
    IndexOptimizations = case maps:get(has_index_hints, Analysis, false) of
        false -> [#{type => add_index_hint, priority => medium}];
        true -> []
    end,

    % 检查是否需要添加限制
    LimitOptimizations = case maps:get(has_limit, Analysis, false) of
        false -> [#{type => add_limit, priority => low}];
        true -> []
    end,

    % 检查过滤器复杂度
    FilterOptimizations = case maps:get(filter_complexity, Analysis, simple) of
        complex -> [#{type => simplify_filter, priority => high}];
        _ -> []
    end,

    Optimizations ++ IndexOptimizations ++ LimitOptimizations ++ FilterOptimizations.

%% @doc 应用查询优化
apply_query_optimizations(Query, Optimizations) ->
    lists:foldl(
        fun(Optimization, AccQuery) ->
            apply_single_optimization(AccQuery, Optimization)
        end,
        Query,
        Optimizations
    ).

%% @doc 应用单个优化
apply_single_optimization(Query, #{type := add_limit}) ->
    case maps:is_key(limit, Query) of
        false -> maps:put(limit, 1000, Query);
        true -> Query
    end;
apply_single_optimization(Query, #{type := add_index_hint}) ->
    case maps:is_key(hint, Query) of
        false -> maps:put(hint, #{client_id => 1}, Query);
        true -> Query
    end;
apply_single_optimization(Query, _) ->
    Query.

%% @doc 估算性能改进
estimate_performance_improvement(OriginalQuery, OptimizedQuery, Collection) ->
    % 这里应该基于历史数据和查询计划来估算
    % 暂时返回模拟的改进估算
    OriginalComplexity = estimate_query_complexity(OriginalQuery),
    OptimizedComplexity = estimate_query_complexity(OptimizedQuery),

    ImprovementRatio = if
        OriginalComplexity > 0 ->
            (OriginalComplexity - OptimizedComplexity) / OriginalComplexity;
        true -> 0
    end,

    #{
        estimated_improvement_percentage => max(0, ImprovementRatio * 100),
        original_complexity => OriginalComplexity,
        optimized_complexity => OptimizedComplexity,
        collection => Collection
    }.

%% @doc 估算查询复杂度
estimate_query_complexity(Query) ->
    BaseComplexity = 10,

    % 根据查询特征调整复杂度
    FilterComplexity = case maps:get(filter, Query, #{}) of
        Filter when map_size(Filter) =:= 0 -> 0;
        Filter -> map_size(Filter) * 5
    end,

    SortComplexity = case maps:is_key(sort, Query) of
        true -> 20;
        false -> 0
    end,

    LimitBonus = case maps:is_key(limit, Query) of
        true -> -5; % 限制可以减少复杂度
        false -> 0
    end,

    BaseComplexity + FilterComplexity + SortComplexity + LimitBonus.

%% @doc 收集集合的索引使用情况
collect_index_usage_for_collection(Collection) ->
    % 这里应该调用MongoDB的索引统计API
    % 暂时返回模拟数据
    #{
        collection => Collection,
        total_operations => 1000,
        index_usage => [
            #{name => "_id_", operations => 200, efficiency => 0.95},
            #{name => "client_id_1", operations => 300, efficiency => 0.85},
            #{name => "topic_1", operations => 250, efficiency => 0.75},
            #{name => "timestamp_1", operations => 50, efficiency => 0.30}
        ]
    }.

%% @doc 统计总索引数量
count_total_indexes(UsageData) ->
    maps:fold(
        fun(_Collection, CollectionData, Acc) ->
            IndexUsage = maps:get(index_usage, CollectionData, []),
            Acc + length(IndexUsage)
        end,
        0,
        UsageData
    ).

%% @doc 统计活跃索引数量
count_active_indexes(UsageData) ->
    maps:fold(
        fun(_Collection, CollectionData, Acc) ->
            IndexUsage = maps:get(index_usage, CollectionData, []),
            ActiveCount = length([1 || Index <- IndexUsage,
                                      maps:get(operations, Index, 0) > 0]),
            Acc + ActiveCount
        end,
        0,
        UsageData
    ).

%% @doc 统计无用索引数量
count_unused_indexes(UsageData) ->
    maps:fold(
        fun(_Collection, CollectionData, Acc) ->
            IndexUsage = maps:get(index_usage, CollectionData, []),
            UnusedCount = length([1 || Index <- IndexUsage,
                                      maps:get(operations, Index, 0) =:= 0]),
            Acc + UnusedCount
        end,
        0,
        UsageData
    ).

%% @doc 获取索引统计
do_get_index_statistics(Collection, _State) ->
    try
        % 获取集合的索引信息
        Indexes = get_collection_indexes(Collection),

        % 获取索引使用统计
        UsageStats = get_index_usage_statistics(Collection),

        Statistics = #{
            collection => Collection,
            total_indexes => length(Indexes),
            index_details => Indexes,
            usage_statistics => UsageStats,
            analysis_timestamp => erlang:system_time(millisecond)
        },

        ?SLOG(debug, #{
            msg => "index_statistics_retrieved",
            collection => Collection,
            total_indexes => length(Indexes)
        }),

        Statistics
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "index_statistics_retrieval_error",
                collection => Collection,
                error => E,
                reason => R,
                stacktrace => S
            }),
            throw({index_statistics_retrieval_failed, R})
    end.

%% @doc 分析索引效率
do_analyze_index_efficiency(Collection, _State) ->
    try
        % 获取索引使用统计
        UsageStats = get_index_usage_statistics(Collection),
        Indexes = maps:get(indexes, UsageStats, []),

        % 分析每个索引的效率
        EfficiencyAnalysis = lists:map(
            fun(Index) ->
                IndexName = maps:get(name, Index),
                Hits = maps:get(hits, Index, 0),
                Misses = maps:get(misses, Index, 0),
                Total = Hits + Misses,

                Efficiency = if
                    Total > 0 -> Hits / Total;
                    true -> 0
                end,

                Status = if
                    Efficiency >= 0.8 -> excellent;
                    Efficiency >= 0.6 -> good;
                    Efficiency >= 0.3 -> fair;
                    true -> poor
                end,

                #{
                    index_name => IndexName,
                    efficiency_ratio => Efficiency,
                    status => Status,
                    hits => Hits,
                    misses => Misses,
                    total_operations => Total
                }
            end,
            Indexes
        ),

        % 计算整体效率
        TotalHits = lists:sum([maps:get(hits, Idx, 0) || Idx <- Indexes]),
        TotalMisses = lists:sum([maps:get(misses, Idx, 0) || Idx <- Indexes]),
        TotalOps = TotalHits + TotalMisses,

        OverallEfficiency = if
            TotalOps > 0 -> TotalHits / TotalOps;
            true -> 0
        end,

        Result = #{
            collection => Collection,
            overall_efficiency => OverallEfficiency,
            total_operations => TotalOps,
            index_analysis => EfficiencyAnalysis,
            recommendations => generate_efficiency_recommendations(EfficiencyAnalysis),
            analysis_timestamp => erlang:system_time(millisecond)
        },

        ?SLOG(debug, #{
            msg => "index_efficiency_analyzed",
            collection => Collection,
            overall_efficiency => OverallEfficiency
        }),

        Result
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "index_efficiency_analysis_error",
                collection => Collection,
                error => E,
                reason => R,
                stacktrace => S
            }),
            throw({index_efficiency_analysis_failed, R})
    end.

%% @doc 生成效率推荐
generate_efficiency_recommendations(EfficiencyAnalysis) ->
    lists:filtermap(
        fun(Analysis) ->
            Status = maps:get(status, Analysis),
            IndexName = maps:get(index_name, Analysis),

            case Status of
                poor ->
                    {true, #{
                        type => remove_or_optimize,
                        index_name => IndexName,
                        priority => high,
                        reason => "low_efficiency"
                    }};
                fair ->
                    {true, #{
                        type => optimize,
                        index_name => IndexName,
                        priority => medium,
                        reason => "moderate_efficiency"
                    }};
                _ ->
                    false
            end
        end,
        EfficiencyAnalysis
    ).

%% @doc 生成优化报告
do_generate_optimization_report(State) ->
    try
        Collections = get_all_collections(),

        % 收集各集合的优化信息
        CollectionReports = lists:map(
            fun(Collection) ->
                IndexStats = do_get_index_statistics(Collection, State),
                EfficiencyAnalysis = do_analyze_index_efficiency(Collection, State),

                #{
                    collection => Collection,
                    index_statistics => IndexStats,
                    efficiency_analysis => EfficiencyAnalysis
                }
            end,
            Collections
        ),

        % 生成总体报告
        Report = #{
            report_timestamp => erlang:system_time(millisecond),
            total_collections => length(Collections),
            collection_reports => CollectionReports,
            summary => generate_report_summary(CollectionReports, State),
            recommendations => generate_global_recommendations(CollectionReports)
        },

        ?SLOG(info, #{
            msg => "optimization_report_generated",
            collections_analyzed => length(Collections)
        }),

        Report
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "optimization_report_generation_error",
                error => E,
                reason => R,
                stacktrace => S
            }),
            throw({optimization_report_generation_failed, R})
    end.

%% @doc 生成报告摘要
generate_report_summary(CollectionReports, State) ->
    TotalIndexes = lists:sum([
        maps:get(total_indexes, maps:get(index_statistics, Report, #{}), 0)
        || Report <- CollectionReports
    ]),

    #{
        total_indexes => TotalIndexes,
        total_optimizations => State#state.total_optimizations,
        indexes_created => State#state.indexes_created,
        indexes_removed => State#state.indexes_removed,
        last_analysis => State#state.last_analysis_time
    }.

%% @doc 生成全局推荐
generate_global_recommendations(CollectionReports) ->
    % 从所有集合报告中提取推荐
    AllRecommendations = lists:flatten([
        maps:get(recommendations, maps:get(efficiency_analysis, Report, #{}), [])
        || Report <- CollectionReports
    ]),

    % 按优先级分组
    HighPriority = [R || R <- AllRecommendations, maps:get(priority, R) =:= high],
    MediumPriority = [R || R <- AllRecommendations, maps:get(priority, R) =:= medium],
    LowPriority = [R || R <- AllRecommendations, maps:get(priority, R) =:= low],

    #{
        high_priority => HighPriority,
        medium_priority => MediumPriority,
        low_priority => LowPriority,
        total_recommendations => length(AllRecommendations)
    }.

%% @doc 获取性能洞察
do_get_performance_insights(State) ->
    try
        % 分析查询模式
        QueryPatterns = State#state.query_patterns,

        % 分析慢查询
        SlowQueries = State#state.slow_queries,

        % 生成洞察
        Insights = #{
            query_pattern_insights => analyze_query_pattern_insights(QueryPatterns),
            slow_query_insights => analyze_slow_query_insights(SlowQueries),
            index_usage_insights => analyze_index_usage_insights(State#state.index_usage),
            optimization_insights => analyze_optimization_insights(State),
            timestamp => erlang:system_time(millisecond)
        },

        ?SLOG(debug, #{msg => "performance_insights_generated"}),

        Insights
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "performance_insights_generation_error",
                error => E,
                reason => R,
                stacktrace => S
            }),
            throw({performance_insights_generation_failed, R})
    end.

%% @doc 分析查询模式洞察
analyze_query_pattern_insights(QueryPatterns) ->
    case maps:size(QueryPatterns) of
        0 ->
            #{insight => "no_query_patterns_available"};
        _ ->
            % 找出最常见的查询模式
            MostCommonPatterns = find_most_common_patterns(QueryPatterns),
            #{
                most_common_patterns => MostCommonPatterns,
                total_patterns => maps:size(QueryPatterns),
                insight => "query_patterns_identified"
            }
    end.

%% @doc 找出最常见的查询模式
find_most_common_patterns(QueryPatterns) ->
    % 简化实现，返回前3个模式
    PatternList = maps:to_list(QueryPatterns),
    lists:sublist(PatternList, 3).

%% @doc 分析慢查询洞察
analyze_slow_query_insights(SlowQueries) ->
    case length(SlowQueries) of
        0 ->
            #{insight => "no_slow_queries_detected"};
        Count ->
            #{
                slow_query_count => Count,
                insight => "slow_queries_need_attention"
            }
    end.

%% @doc 分析索引使用洞察
analyze_index_usage_insights(IndexUsage) ->
    case maps:size(IndexUsage) of
        0 ->
            #{insight => "no_index_usage_data_available"};
        _ ->
            #{
                collections_monitored => maps:size(IndexUsage),
                insight => "index_usage_being_monitored"
            }
    end.

%% @doc 分析优化洞察
analyze_optimization_insights(State) ->
    #{
        total_optimizations => State#state.total_optimizations,
        indexes_created => State#state.indexes_created,
        indexes_removed => State#state.indexes_removed,
        optimization_enabled => State#state.optimization_enabled,
        insight => "optimization_statistics_available"
    }.

%% @doc 执行定期分析
do_periodic_analysis(State) ->
    try
        ?SLOG(debug, #{msg => "starting_periodic_index_analysis"}),

        % 分析查询模式
        {_Patterns, NewState1} = do_analyze_query_patterns(State),

        % 监控索引使用
        {_Usage, NewState2} = do_monitor_index_usage(NewState1),

        % 更新分析时间
        FinalState = NewState2#state{
            last_analysis_time = erlang:system_time(millisecond)
        },

        ?SLOG(debug, #{msg => "periodic_index_analysis_completed"}),

        FinalState
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "periodic_index_analysis_error",
                error => E,
                reason => R,
                stacktrace => S
            }),
            State
    end.
