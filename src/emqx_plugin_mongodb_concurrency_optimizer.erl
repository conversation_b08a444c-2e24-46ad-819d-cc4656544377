%%%-------------------------------------------------------------------
%%% @doc MongoDB插件并发安全和锁竞争优化模块
%%% 这个模块提供企业级并发控制和锁竞争优化功能
%%%
%%% 核心功能：
%%% 1. 锁竞争分析 - 实时监控和分析锁竞争情况
%%% 2. 并发访问优化 - 优化并发访问模式和策略
%%% 3. 竞态条件预防 - 检测和预防潜在的竞态条件
%%% 4. 死锁检测 - 智能死锁检测和解决
%%% 5. 资源争用分析 - 分析资源争用模式
%%% 6. 并发性能调优 - 基于负载动态调优并发参数
%%%
%%% 优化算法：
%%% - 锁粒度优化：动态调整锁的粒度以减少竞争
%%% - 无锁数据结构：在适当场景使用无锁算法
%%% - 读写分离：优化读写操作的并发策略
%%% - 批量操作优化：减少锁获取次数
%%%
%%% 监控指标：
%%% - 锁等待时间统计
%%% - 锁竞争频率分析
%%% - 并发度监控
%%% - 吞吐量影响评估
%%%
%%% Java等价概念：
%%% 类似于Java的并发工具包java.util.concurrent
%%% 或者JVM的锁监控工具如JConsole、VisualVM
%%%
%%% @end
%%%-------------------------------------------------------------------
-module(emqx_plugin_mongodb_concurrency_optimizer).

-behaviour(gen_server).

-include("emqx_plugin_mongodb.hrl").

%% API导出
-export([
    start_link/0,                    % 启动并发优化服务
    stop/0,                          % 停止服务
    
    % 锁竞争分析API
    analyze_lock_contention/0,       % 分析锁竞争
    get_lock_statistics/0,           % 获取锁统计
    detect_lock_hotspots/0,          % 检测锁热点
    
    % 并发优化API
    optimize_concurrency/1,          % 优化并发策略
    adjust_concurrency_level/2,      % 调整并发级别
    get_optimal_concurrency/1,       % 获取最优并发度
    
    % 竞态条件检测API
    detect_race_conditions/0,        % 检测竞态条件
    analyze_resource_contention/0,   % 分析资源争用
    get_contention_report/0,         % 获取争用报告
    
    % 死锁检测API
    detect_deadlocks/0,              % 检测死锁
    resolve_deadlock/1,              % 解决死锁
    get_deadlock_history/0,          % 获取死锁历史
    
    % 性能调优API
    tune_concurrency_parameters/0,   % 调优并发参数
    get_performance_metrics/0,       % 获取性能指标
    generate_optimization_plan/0,    % 生成优化计划
    
    % 集成API
    integrate/0                      % 集成到协调器
]).

%% gen_server回调
-export([
    init/1,
    handle_call/3,
    handle_cast/2,
    handle_info/2,
    terminate/2,
    code_change/3
]).

%% 内部状态记录
-record(state, {
    % 锁竞争监控
    lock_statistics = #{},           % 锁统计数据
    contention_hotspots = [],        % 竞争热点
    lock_wait_times = [],            % 锁等待时间记录
    
    % 并发控制
    current_concurrency = #{},       % 当前并发级别
    optimal_concurrency = #{},       % 最优并发级别
    concurrency_history = [],        % 并发历史记录
    
    % 竞态条件监控
    race_conditions = [],            % 检测到的竞态条件
    resource_contentions = #{},      % 资源争用统计
    
    % 死锁检测
    deadlock_history = [],           % 死锁历史
    deadlock_detection_enabled = true, % 是否启用死锁检测
    
    % 性能指标
    performance_metrics = #{},       % 性能指标
    throughput_history = [],         % 吞吐量历史
    latency_history = [],            % 延迟历史
    
    % 配置参数
    monitoring_enabled = true,       % 是否启用监控
    analysis_interval = 60000,       % 分析间隔（1分钟）
    optimization_threshold = 0.2,    % 优化阈值
    max_concurrency = 100,           % 最大并发数
    
    % 统计信息
    total_optimizations = 0,         % 总优化次数
    deadlocks_resolved = 0,          % 解决的死锁数量
    race_conditions_prevented = 0,   % 预防的竞态条件数量
    last_analysis_time = 0           % 上次分析时间
}).

%% ============================================================================
%% API函数
%% ============================================================================

%% @doc 启动并发优化服务
start_link() ->
    gen_server:start_link({local, ?MODULE}, ?MODULE, [], []).

%% @doc 停止服务
stop() ->
    gen_server:call(?MODULE, stop, 5000).

%% @doc 分析锁竞争
analyze_lock_contention() ->
    gen_server:call(?MODULE, analyze_lock_contention).

%% @doc 获取锁统计
get_lock_statistics() ->
    gen_server:call(?MODULE, get_lock_statistics).

%% @doc 检测锁热点
detect_lock_hotspots() ->
    gen_server:call(?MODULE, detect_lock_hotspots).

%% @doc 优化并发策略
optimize_concurrency(OperationType) ->
    gen_server:call(?MODULE, {optimize_concurrency, OperationType}).

%% @doc 调整并发级别
adjust_concurrency_level(OperationType, NewLevel) ->
    gen_server:call(?MODULE, {adjust_concurrency_level, OperationType, NewLevel}).

%% @doc 获取最优并发度
get_optimal_concurrency(OperationType) ->
    gen_server:call(?MODULE, {get_optimal_concurrency, OperationType}).

%% @doc 检测竞态条件
detect_race_conditions() ->
    gen_server:call(?MODULE, detect_race_conditions).

%% @doc 分析资源争用
analyze_resource_contention() ->
    gen_server:call(?MODULE, analyze_resource_contention).

%% @doc 获取争用报告
get_contention_report() ->
    gen_server:call(?MODULE, get_contention_report).

%% @doc 检测死锁
detect_deadlocks() ->
    gen_server:call(?MODULE, detect_deadlocks).

%% @doc 解决死锁
resolve_deadlock(DeadlockInfo) ->
    gen_server:call(?MODULE, {resolve_deadlock, DeadlockInfo}).

%% @doc 获取死锁历史
get_deadlock_history() ->
    gen_server:call(?MODULE, get_deadlock_history).

%% @doc 调优并发参数
tune_concurrency_parameters() ->
    gen_server:call(?MODULE, tune_concurrency_parameters).

%% @doc 获取性能指标
get_performance_metrics() ->
    gen_server:call(?MODULE, get_performance_metrics).

%% @doc 生成优化计划
generate_optimization_plan() ->
    gen_server:call(?MODULE, generate_optimization_plan).

%% @doc 集成到协调器
%% 将并发优化服务集成到MongoDB插件协调器中
-spec integrate() -> ok | {error, term()}.
integrate() ->
    try
        ?SLOG(info, #{msg => "integrating_concurrency_optimizer_to_coordinator"}),

        % 1. 注册到协调器
        case emqx_plugin_mongodb_coordinator:register_module(
            concurrency_optimizer,
            ?MODULE,
            #{
                priority => 8,
                type => service,
                dependencies => [resource_manager],
                capabilities => [
                    concurrency_optimization,
                    lock_contention_analysis,
                    deadlock_detection,
                    race_condition_prevention,
                    performance_tuning,
                    resource_contention_management
                ]
            }
        ) of
            ok ->
                ?SLOG(info, #{msg => "concurrency_optimizer_registered_to_coordinator"});
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_register_concurrency_optimizer_to_coordinator",
                    reason => Reason
                }),
                throw({register_failed, Reason})
        end,

        % 2. 订阅协调器事件
        subscribe_to_coordinator_events(),

        % 3. 注册容错处理器
        register_fault_tolerance_handler(),

        % 4. 启动与其他模块的联动
        setup_module_coordination(),

        ?SLOG(info, #{msg => "concurrency_optimizer_integration_completed"}),
        ok
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "failed_to_integrate_concurrency_optimizer",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {integration_failed, R}}
    end.

%% @doc 订阅协调器事件
subscribe_to_coordinator_events() ->
    try
        Events = [
            system_overload,
            memory_pressure,
            performance_degradation,
            resource_contention
        ],

        lists:foreach(fun(Event) ->
            case emqx_plugin_mongodb_coordinator:subscribe_event(Event, ?MODULE) of
                ok ->
                    ?SLOG(debug, #{msg => "subscribed_to_coordinator_event", event => Event});
                {error, Reason} ->
                    ?SLOG(warning, #{
                        msg => "failed_to_subscribe_coordinator_event",
                        event => Event,
                        reason => Reason
                    })
            end
        end, Events),

        ok
    catch
        _:_ ->
            ok
    end.

%% @doc 注册容错处理器
register_fault_tolerance_handler() ->
    try
        % 注册处理容错模块事件的处理器
        Handler = fun(Event) ->
            case maps:get(event_type, Event, unknown) of
                graceful_degradation ->
                    handle_graceful_degradation(Event);
                fault_isolation ->
                    handle_fault_isolation(Event);
                emergency_mode ->
                    handle_emergency_mode(Event);
                _ ->
                    ?SLOG(debug, #{msg => "unhandled_fault_tolerance_event", event => Event})
            end
        end,

        % 存储处理器
        ets:insert(concurrency_stats, {fault_tolerance_handler, Handler}),

        ok
    catch
        _:_ ->
            ok
    end.

%% @doc 设置模块协调
setup_module_coordination() ->
    try
        % 与内存泄漏检测模块协调
        coordinate_with_memory_leak_detector(),

        % 与批处理优化模块协调
        coordinate_with_batch_optimizer(),

        % 与索引优化模块协调
        coordinate_with_index_optimizer(),

        ok
    catch
        _:_ ->
            ok
    end.

%% ============================================================================
%% gen_server回调实现
%% ============================================================================

%% @doc 初始化服务
init([]) ->
    ?SLOG(info, #{msg => "starting_concurrency_optimizer_service"}),
    
    % 创建并发优化相关ETS表
    ets:new(?CONCURRENCY_STATS_TAB, [named_table, public, set, {read_concurrency, true}]),
    ets:new(?LOCK_CONTENTION_TAB, [named_table, public, set, {read_concurrency, true}]),
    ets:new(?DEADLOCK_DETECTION_TAB, [named_table, public, set, {read_concurrency, true}]),
    
    % 设置定期分析
    erlang:send_after(60000, self(), analyze_concurrency), % 1分钟后开始分析
    
    State = #state{
        lock_statistics = #{},
        contention_hotspots = [],
        lock_wait_times = [],
        current_concurrency = #{
            insert => 10,
            update => 8,
            delete => 5,
            query => 20
        },
        optimal_concurrency = #{},
        race_conditions = [],
        resource_contentions = #{},
        deadlock_history = [],
        performance_metrics = #{}
    },
    
    ?SLOG(info, #{msg => "concurrency_optimizer_service_started"}),
    {ok, State}.

%% @doc 处理同步调用
handle_call(analyze_lock_contention, _From, State) ->
    try
        {Analysis, NewState} = do_analyze_lock_contention(State),
        {reply, {ok, Analysis}, NewState}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "lock_contention_analysis_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {reply, {error, R}, State}
    end;

handle_call(get_lock_statistics, _From, State) ->
    try
        Statistics = do_get_lock_statistics(State),
        {reply, {ok, Statistics}, State}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "lock_statistics_retrieval_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {reply, {error, R}, State}
    end;

handle_call(detect_lock_hotspots, _From, State) ->
    try
        {Hotspots, NewState} = do_detect_lock_hotspots(State),
        {reply, {ok, Hotspots}, NewState}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "lock_hotspot_detection_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {reply, {error, R}, State}
    end;

handle_call({optimize_concurrency, OperationType}, _From, State) ->
    try
        {Result, NewState} = do_optimize_concurrency(OperationType, State),
        {reply, {ok, Result}, NewState}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "concurrency_optimization_failed",
                operation_type => OperationType,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {reply, {error, R}, State}
    end;

handle_call({adjust_concurrency_level, OperationType, NewLevel}, _From, State) ->
    try
        NewState = do_adjust_concurrency_level(OperationType, NewLevel, State),
        {reply, ok, NewState}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "concurrency_level_adjustment_failed",
                operation_type => OperationType,
                new_level => NewLevel,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {reply, {error, R}, State}
    end;

handle_call({get_optimal_concurrency, OperationType}, _From, State) ->
    try
        OptimalLevel = do_get_optimal_concurrency(OperationType, State),
        {reply, {ok, OptimalLevel}, State}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "optimal_concurrency_retrieval_failed",
                operation_type => OperationType,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {reply, {error, R}, State}
    end;

handle_call(stop, _From, State) ->
    {stop, normal, ok, State};

handle_call(_Request, _From, State) ->
    {reply, {error, unknown_request}, State}.

%% @doc 处理异步消息
handle_cast(_Msg, State) ->
    {noreply, State}.

%% @doc 处理定时器和其他消息
handle_info(analyze_concurrency, State) ->
    try
        NewState = do_periodic_concurrency_analysis(State),
        % 设置下次分析
        erlang:send_after(State#state.analysis_interval, self(), analyze_concurrency),
        {noreply, NewState}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "periodic_concurrency_analysis_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            % 即使分析失败，也要设置下次分析
            erlang:send_after(State#state.analysis_interval, self(), analyze_concurrency),
            {noreply, State}
    end;

handle_info(_Info, State) ->
    {noreply, State}.

%% @doc 服务终止处理
terminate(_Reason, _State) ->
    ?SLOG(info, #{msg => "concurrency_optimizer_service_terminated"}),
    
    % 清理ETS表
    catch ets:delete(?CONCURRENCY_STATS_TAB),
    catch ets:delete(?LOCK_CONTENTION_TAB),
    catch ets:delete(?DEADLOCK_DETECTION_TAB),
    
    ok.

%% @doc 代码更新处理
code_change(_OldVsn, State, _Extra) ->
    {ok, State}.

%% ============================================================================
%% 核心实现函数
%% ============================================================================

%% @doc 执行锁竞争分析
do_analyze_lock_contention(State) ->
    ?SLOG(debug, #{msg => "analyzing_lock_contention"}),

    % 收集当前锁统计数据
    LockStats = collect_lock_statistics(),

    % 分析竞争模式
    ContentionPatterns = analyze_contention_patterns(LockStats),

    % 识别热点
    Hotspots = identify_contention_hotspots(ContentionPatterns),

    % 计算竞争指标
    ContentionMetrics = calculate_contention_metrics(LockStats),

    Analysis = #{
        lock_statistics => LockStats,
        contention_patterns => ContentionPatterns,
        hotspots => Hotspots,
        metrics => ContentionMetrics,
        analysis_timestamp => erlang:system_time(millisecond),
        recommendations => generate_contention_recommendations(ContentionMetrics, Hotspots)
    },

    % 更新状态
    NewState = State#state{
        lock_statistics = LockStats,
        contention_hotspots = Hotspots,
        last_analysis_time = erlang:system_time(millisecond)
    },

    ?SLOG(debug, #{
        msg => "lock_contention_analysis_completed",
        hotspots_count => length(Hotspots),
        contention_score => maps:get(overall_contention_score, ContentionMetrics, 0)
    }),

    {Analysis, NewState}.

%% @doc 收集锁统计数据
collect_lock_statistics() ->
    % 从ETS表和系统中收集锁统计
    try
        % 收集MongoDB连接池锁统计
        PoolLocks = collect_connection_pool_locks(),

        % 收集ETS表锁统计
        EtsLocks = collect_ets_table_locks(),

        % 收集进程锁统计
        ProcessLocks = collect_process_locks(),

        #{
            connection_pool_locks => PoolLocks,
            ets_table_locks => EtsLocks,
            process_locks => ProcessLocks,
            collection_timestamp => erlang:system_time(millisecond)
        }
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "lock_statistics_collection_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            #{}
    end.

%% @doc 收集连接池锁统计
collect_connection_pool_locks() ->
    % 模拟连接池锁统计数据
    % 在实际实现中，这里应该调用MongoDB驱动的统计API
    #{
        pool_lock_wait_time => rand:uniform(100),
        pool_lock_acquisitions => 1000 + rand:uniform(500),
        pool_lock_contentions => rand:uniform(50),
        pool_utilization => 0.7 + rand:uniform() * 0.3
    }.

%% @doc 收集ETS表锁统计
collect_ets_table_locks() ->
    % 收集各个ETS表的锁统计
    Tables = [
        ?CONCURRENCY_STATS_TAB,
        ?LOCK_CONTENTION_TAB,
        ?DEADLOCK_DETECTION_TAB
    ],

    lists:foldl(
        fun(Table, Acc) ->
            try
                Info = ets:info(Table),
                TableStats = #{
                    size => proplists:get_value(size, Info, 0),
                    memory => proplists:get_value(memory, Info, 0),
                    read_concurrency => proplists:get_value(read_concurrency, Info, false),
                    write_concurrency => proplists:get_value(write_concurrency, Info, false)
                },
                maps:put(Table, TableStats, Acc)
            catch
                _:_ ->
                    Acc
            end
        end,
        #{},
        Tables
    ).

%% @doc 收集进程锁统计
collect_process_locks() ->
    % 收集关键进程的锁统计
    try
        % 获取当前进程的消息队列长度（间接反映锁竞争）
        {message_queue_len, MsgQueueLen} = process_info(self(), message_queue_len),

        % 获取系统进程数量
        ProcessCount = erlang:system_info(process_count),

        % 获取运行队列长度
        RunQueueLen = erlang:statistics(run_queue),

        #{
            message_queue_length => MsgQueueLen,
            total_processes => ProcessCount,
            run_queue_length => RunQueueLen,
            scheduler_utilization => get_scheduler_utilization()
        }
    catch
        _:_ ->
            #{
                message_queue_length => 0,
                total_processes => 0,
                run_queue_length => 0,
                scheduler_utilization => 0.0
            }
    end.

%% @doc 获取调度器利用率
get_scheduler_utilization() ->
    try
        % 获取调度器统计信息
        SchedulerStats = erlang:statistics(scheduler_wall_time),
        case SchedulerStats of
            undefined ->
                0.0;
            Stats when is_list(Stats) ->
                % 计算平均利用率
                TotalActive = lists:sum([Active || {_, Active, _} <- Stats]),
                TotalTotal = lists:sum([Total || {_, _, Total} <- Stats]),
                if
                    TotalTotal > 0 -> TotalActive / TotalTotal;
                    true -> 0.0
                end;
            _ ->
                0.0
        end
    catch
        _:_ ->
            0.0
    end.

%% @doc 分析竞争模式
analyze_contention_patterns(LockStats) ->
    % 分析不同类型的竞争模式
    PoolContentions = maps:get(connection_pool_locks, LockStats, #{}),
    EtsContentions = maps:get(ets_table_locks, LockStats, #{}),
    ProcessContentions = maps:get(process_locks, LockStats, #{}),

    #{
        pool_contention_pattern => analyze_pool_contention_pattern(PoolContentions),
        ets_contention_pattern => analyze_ets_contention_pattern(EtsContentions),
        process_contention_pattern => analyze_process_contention_pattern(ProcessContentions),
        overall_pattern => determine_overall_contention_pattern(PoolContentions, EtsContentions, ProcessContentions)
    }.

%% @doc 分析连接池竞争模式
analyze_pool_contention_pattern(PoolStats) ->
    PoolUtilization = maps:get(pool_utilization, PoolStats, 0.0),
    LockContentions = maps:get(pool_lock_contentions, PoolStats, 0),

    if
        PoolUtilization > 0.9 andalso LockContentions > 20 ->
            high_contention;
        PoolUtilization > 0.7 andalso LockContentions > 10 ->
            moderate_contention;
        LockContentions > 5 ->
            low_contention;
        true ->
            no_contention
    end.

%% @doc 分析ETS表竞争模式
analyze_ets_contention_pattern(EtsStats) ->
    % 基于ETS表的大小和并发设置分析竞争
    TotalSize = maps:fold(
        fun(_, TableStats, Acc) ->
            Acc + maps:get(size, TableStats, 0)
        end,
        0,
        EtsStats
    ),

    ReadConcurrencyEnabled = maps:fold(
        fun(_, TableStats, Acc) ->
            Acc orelse maps:get(read_concurrency, TableStats, false)
        end,
        false,
        EtsStats
    ),

    if
        TotalSize > 10000 andalso not ReadConcurrencyEnabled ->
            potential_read_contention;
        TotalSize > 5000 ->
            moderate_ets_usage;
        true ->
            low_ets_usage
    end.

%% @doc 分析进程竞争模式
analyze_process_contention_pattern(ProcessStats) ->
    MsgQueueLen = maps:get(message_queue_length, ProcessStats, 0),
    RunQueueLen = maps:get(run_queue_length, ProcessStats, 0),
    SchedulerUtil = maps:get(scheduler_utilization, ProcessStats, 0.0),

    if
        MsgQueueLen > 100 orelse RunQueueLen > 50 ->
            high_process_contention;
        MsgQueueLen > 20 orelse RunQueueLen > 10 ->
            moderate_process_contention;
        SchedulerUtil > 0.8 ->
            high_cpu_contention;
        true ->
            low_process_contention
    end.

%% @doc 确定整体竞争模式
determine_overall_contention_pattern(PoolStats, EtsStats, ProcessStats) ->
    PoolPattern = analyze_pool_contention_pattern(PoolStats),
    EtsPattern = analyze_ets_contention_pattern(EtsStats),
    ProcessPattern = analyze_process_contention_pattern(ProcessStats),

    % 基于各个组件的竞争模式确定整体模式
    case {PoolPattern, EtsPattern, ProcessPattern} of
        {high_contention, _, _} -> critical_contention;
        {_, _, high_process_contention} -> critical_contention;
        {_, _, high_cpu_contention} -> critical_contention;
        {moderate_contention, potential_read_contention, _} -> high_contention;
        {moderate_contention, _, moderate_process_contention} -> high_contention;
        {_, potential_read_contention, moderate_process_contention} -> moderate_contention;
        {low_contention, _, _} -> low_contention;
        {_, _, low_process_contention} -> low_contention;
        _ -> minimal_contention
    end.

%% @doc 识别竞争热点
identify_contention_hotspots(ContentionPatterns) ->
    Hotspots = [],

    % 检查连接池热点
    PoolHotspots = case maps:get(pool_contention_pattern, ContentionPatterns) of
        high_contention ->
            [#{type => connection_pool, severity => high, component => mongodb_pool}];
        moderate_contention ->
            [#{type => connection_pool, severity => medium, component => mongodb_pool}];
        _ ->
            []
    end,

    % 检查ETS表热点
    EtsHotspots = case maps:get(ets_contention_pattern, ContentionPatterns) of
        potential_read_contention ->
            [#{type => ets_table, severity => medium, component => read_operations}];
        _ ->
            []
    end,

    % 检查进程热点
    ProcessHotspots = case maps:get(process_contention_pattern, ContentionPatterns) of
        high_process_contention ->
            [#{type => process_queue, severity => high, component => message_processing}];
        high_cpu_contention ->
            [#{type => cpu_scheduler, severity => high, component => scheduler_utilization}];
        moderate_process_contention ->
            [#{type => process_queue, severity => medium, component => message_processing}];
        _ ->
            []
    end,

    Hotspots ++ PoolHotspots ++ EtsHotspots ++ ProcessHotspots.

%% @doc 计算竞争指标
calculate_contention_metrics(LockStats) ->
    PoolStats = maps:get(connection_pool_locks, LockStats, #{}),
    ProcessStats = maps:get(process_locks, LockStats, #{}),

    % 计算各种竞争指标
    PoolContentionScore = calculate_pool_contention_score(PoolStats),
    ProcessContentionScore = calculate_process_contention_score(ProcessStats),

    % 计算整体竞争分数
    OverallScore = (PoolContentionScore + ProcessContentionScore) / 2,

    #{
        pool_contention_score => PoolContentionScore,
        process_contention_score => ProcessContentionScore,
        overall_contention_score => OverallScore,
        contention_level => determine_contention_level(OverallScore),
        metrics_timestamp => erlang:system_time(millisecond)
    }.

%% @doc 计算连接池竞争分数
calculate_pool_contention_score(PoolStats) ->
    PoolUtilization = maps:get(pool_utilization, PoolStats, 0.0),
    LockContentions = maps:get(pool_lock_contentions, PoolStats, 0),
    WaitTime = maps:get(pool_lock_wait_time, PoolStats, 0),

    % 基于利用率、竞争次数和等待时间计算分数
    UtilizationScore = PoolUtilization * 40,
    ContentionScore = min(LockContentions / 10 * 30, 30),
    WaitTimeScore = min(WaitTime / 50 * 30, 30),

    UtilizationScore + ContentionScore + WaitTimeScore.

%% @doc 计算进程竞争分数
calculate_process_contention_score(ProcessStats) ->
    MsgQueueLen = maps:get(message_queue_length, ProcessStats, 0),
    RunQueueLen = maps:get(run_queue_length, ProcessStats, 0),
    SchedulerUtil = maps:get(scheduler_utilization, ProcessStats, 0.0),

    % 基于消息队列、运行队列和调度器利用率计算分数
    MsgQueueScore = min(MsgQueueLen / 20 * 30, 30),
    RunQueueScore = min(RunQueueLen / 10 * 30, 30),
    SchedulerScore = SchedulerUtil * 40,

    MsgQueueScore + RunQueueScore + SchedulerScore.

%% @doc 确定竞争级别
determine_contention_level(Score) when Score >= 80 -> critical;
determine_contention_level(Score) when Score >= 60 -> high;
determine_contention_level(Score) when Score >= 40 -> moderate;
determine_contention_level(Score) when Score >= 20 -> low;
determine_contention_level(_) -> minimal.

%% @doc 生成竞争推荐
generate_contention_recommendations(Metrics, Hotspots) ->
    _OverallScore = maps:get(overall_contention_score, Metrics, 0),
    ContentionLevel = maps:get(contention_level, Metrics, minimal),

    BaseRecommendations = case ContentionLevel of
        critical ->
            [
                #{type => urgent, action => increase_connection_pool_size, priority => high},
                #{type => urgent, action => enable_read_concurrency_ets, priority => high},
                #{type => urgent, action => optimize_message_processing, priority => high}
            ];
        high ->
            [
                #{type => optimization, action => tune_concurrency_parameters, priority => medium},
                #{type => optimization, action => implement_batching, priority => medium}
            ];
        moderate ->
            [
                #{type => monitoring, action => increase_monitoring_frequency, priority => low}
            ];
        _ ->
            []
    end,

    % 基于热点添加特定推荐
    HotspotRecommendations = lists:map(
        fun(Hotspot) ->
            generate_hotspot_recommendation(Hotspot)
        end,
        Hotspots
    ),

    BaseRecommendations ++ HotspotRecommendations.

%% @doc 生成热点推荐
generate_hotspot_recommendation(#{type := connection_pool, severity := high}) ->
    #{type => hotspot, action => scale_connection_pool, priority => high, target => connection_pool};
generate_hotspot_recommendation(#{type := ets_table, severity := medium}) ->
    #{type => hotspot, action => enable_ets_read_concurrency, priority => medium, target => ets_tables};
generate_hotspot_recommendation(#{type := process_queue, severity := high}) ->
    #{type => hotspot, action => optimize_process_scheduling, priority => high, target => process_management};
generate_hotspot_recommendation(#{type := cpu_scheduler, severity := high}) ->
    #{type => hotspot, action => reduce_cpu_intensive_operations, priority => high, target => cpu_usage};
generate_hotspot_recommendation(_) ->
    #{type => general, action => monitor_performance, priority => low, target => general}.

%% @doc 获取锁统计
do_get_lock_statistics(State) ->
    #{
        current_statistics => State#state.lock_statistics,
        contention_hotspots => State#state.contention_hotspots,
        lock_wait_times => State#state.lock_wait_times,
        last_analysis_time => State#state.last_analysis_time,
        total_optimizations => State#state.total_optimizations
    }.

%% @doc 检测锁热点
do_detect_lock_hotspots(State) ->
    % 基于当前统计数据检测热点
    CurrentStats = State#state.lock_statistics,

    % 重新分析以获取最新热点
    ContentionPatterns = analyze_contention_patterns(CurrentStats),
    Hotspots = identify_contention_hotspots(ContentionPatterns),

    % 更新状态
    NewState = State#state{contention_hotspots = Hotspots},

    {Hotspots, NewState}.

%% @doc 优化并发策略
do_optimize_concurrency(OperationType, State) ->
    CurrentLevel = maps:get(OperationType, State#state.current_concurrency, 10),
    OptimalLevel = calculate_optimal_concurrency(OperationType, State),

    % 应用优化
    OptimizationResult = apply_concurrency_optimization(OperationType, CurrentLevel, OptimalLevel),

    % 更新状态
    NewCurrentConcurrency = maps:put(OperationType, OptimalLevel, State#state.current_concurrency),
    NewOptimalConcurrency = maps:put(OperationType, OptimalLevel, State#state.optimal_concurrency),

    NewState = State#state{
        current_concurrency = NewCurrentConcurrency,
        optimal_concurrency = NewOptimalConcurrency,
        total_optimizations = State#state.total_optimizations + 1
    },

    Result = #{
        operation_type => OperationType,
        previous_level => CurrentLevel,
        optimized_level => OptimalLevel,
        optimization_result => OptimizationResult,
        improvement_percentage => calculate_improvement_percentage(CurrentLevel, OptimalLevel),
        timestamp => erlang:system_time(millisecond)
    },

    {Result, NewState}.

%% @doc 计算最优并发度
calculate_optimal_concurrency(OperationType, State) ->
    % 基于历史性能数据和当前负载计算最优并发度
    CurrentLevel = maps:get(OperationType, State#state.current_concurrency, 10),
    PerformanceMetrics = State#state.performance_metrics,

    % 获取操作类型的性能指标
    OpMetrics = maps:get(OperationType, PerformanceMetrics, #{}),

    % 基于吞吐量和延迟计算最优值
    Throughput = maps:get(throughput, OpMetrics, 100),
    Latency = maps:get(latency, OpMetrics, 50),

    % 使用简化的优化算法
    OptimalLevel = if
        Latency > 100 andalso Throughput < 50 ->
            % 高延迟低吞吐量，减少并发
            max(1, CurrentLevel - 2);
        Latency < 30 andalso Throughput > 200 ->
            % 低延迟高吞吐量，增加并发
            min(State#state.max_concurrency, CurrentLevel + 3);
        Latency > 50 ->
            % 中等延迟，适度减少并发
            max(1, CurrentLevel - 1);
        Throughput > 150 ->
            % 高吞吐量，适度增加并发
            min(State#state.max_concurrency, CurrentLevel + 1);
        true ->
            % 保持当前级别
            CurrentLevel
    end,

    OptimalLevel.

%% @doc 应用并发优化
apply_concurrency_optimization(_OperationType, CurrentLevel, OptimalLevel) ->
    if
        OptimalLevel =:= CurrentLevel ->
            #{status => no_change, reason => "already_optimal"};
        OptimalLevel > CurrentLevel ->
            #{status => increased, from => CurrentLevel, to => OptimalLevel,
              reason => "performance_improvement_expected"};
        OptimalLevel < CurrentLevel ->
            #{status => decreased, from => CurrentLevel, to => OptimalLevel,
              reason => "contention_reduction_expected"}
    end.

%% @doc 计算改进百分比
calculate_improvement_percentage(CurrentLevel, OptimalLevel) ->
    if
        CurrentLevel =:= 0 -> 0;
        true -> abs(OptimalLevel - CurrentLevel) / CurrentLevel * 100
    end.

%% @doc 调整并发级别
do_adjust_concurrency_level(OperationType, NewLevel, State) ->
    % 验证新级别的有效性
    ValidatedLevel = min(max(1, NewLevel), State#state.max_concurrency),

    % 更新并发级别
    NewCurrentConcurrency = maps:put(OperationType, ValidatedLevel, State#state.current_concurrency),

    % 记录调整历史
    AdjustmentRecord = #{
        operation_type => OperationType,
        new_level => ValidatedLevel,
        timestamp => erlang:system_time(millisecond),
        reason => manual_adjustment
    },

    NewConcurrencyHistory = [AdjustmentRecord | State#state.concurrency_history],

    State#state{
        current_concurrency = NewCurrentConcurrency,
        concurrency_history = lists:sublist(NewConcurrencyHistory, 100) % 保留最近100条记录
    }.

%% @doc 获取最优并发度
do_get_optimal_concurrency(OperationType, State) ->
    case maps:get(OperationType, State#state.optimal_concurrency, undefined) of
        undefined ->
            % 如果没有计算过最优值，使用当前值
            maps:get(OperationType, State#state.current_concurrency, 10);
        OptimalLevel ->
            OptimalLevel
    end.

%% @doc 执行定期并发分析
do_periodic_concurrency_analysis(State) ->
    try
        ?SLOG(debug, #{msg => "starting_periodic_concurrency_analysis"}),

        % 更新性能指标
        NewPerformanceMetrics = collect_performance_metrics(),

        % 分析锁竞争
        {_Analysis, StateAfterLockAnalysis} = do_analyze_lock_contention(State),

        % 检测死锁
        DeadlockDetectionResult = detect_potential_deadlocks(),

        % 更新状态
        FinalState = StateAfterLockAnalysis#state{
            performance_metrics = NewPerformanceMetrics,
            last_analysis_time = erlang:system_time(millisecond)
        },

        % 如果检测到严重问题，记录警告
        case DeadlockDetectionResult of
            {deadlocks_detected, DeadlockList} when length(DeadlockList) > 0 ->
                ?SLOG(warning, #{
                    msg => "potential_deadlocks_detected",
                    deadlock_count => length(DeadlockList)
                });
            _ ->
                ok
        end,

        ?SLOG(debug, #{msg => "periodic_concurrency_analysis_completed"}),

        FinalState
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "periodic_concurrency_analysis_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            State
    end.

%% @doc 收集性能指标
collect_performance_metrics() ->
    % 收集各种操作类型的性能指标
    OperationTypes = [insert, update, delete, query],

    lists:foldl(
        fun(OpType, Acc) ->
            Metrics = collect_operation_metrics(OpType),
            maps:put(OpType, Metrics, Acc)
        end,
        #{},
        OperationTypes
    ).

%% @doc 收集操作指标
collect_operation_metrics(OperationType) ->
    % 模拟收集操作指标
    % 在实际实现中，这里应该从MongoDB驱动或监控系统获取真实数据
    BaseLatency = case OperationType of
        insert -> 20;
        update -> 30;
        delete -> 25;
        query -> 15
    end,

    BaseThroughput = case OperationType of
        insert -> 150;
        update -> 120;
        delete -> 100;
        query -> 300
    end,

    % 添加一些随机变化来模拟真实环境
    Latency = BaseLatency + rand:uniform(20) - 10,
    Throughput = BaseThroughput + rand:uniform(100) - 50,

    #{
        latency => max(1, Latency),
        throughput => max(1, Throughput),
        success_rate => 0.95 + rand:uniform() * 0.05,
        timestamp => erlang:system_time(millisecond)
    }.

%% @doc 检测潜在死锁 - 高级算法实现
%% 基于图论的死锁检测，包括资源依赖图构建、环路检测、等待链分析
detect_potential_deadlocks() ->
    try
        ?SLOG(debug, #{msg => "starting_advanced_deadlock_detection"}),

        % 1. 构建资源依赖图
        DependencyGraph = build_resource_dependency_graph(),

        % 2. 检测环路（死锁的必要条件）
        Cycles = detect_cycles_in_dependency_graph(DependencyGraph),

        % 3. 分析等待链
        WaitChains = analyze_wait_chains(),

        % 4. 计算死锁概率
        DeadlockProbability = calculate_deadlock_probability(Cycles, WaitChains),

        % 5. 生成详细的死锁报告
        DeadlockReport = generate_deadlock_report(Cycles, WaitChains, DeadlockProbability),

        % 6. 根据分析结果返回相应的检测结果
        case {length(Cycles), DeadlockProbability} of
            {0, _} ->
                {no_deadlocks, DeadlockReport};
            {CycleCount, Probability} when Probability > 0.8 ->
                {deadlocks_detected, DeadlockReport#{
                    severity => critical,
                    cycle_count => CycleCount,
                    probability => Probability
                }};
            {CycleCount, Probability} when Probability > 0.5 ->
                {deadlocks_detected, DeadlockReport#{
                    severity => high,
                    cycle_count => CycleCount,
                    probability => Probability
                }};
            {CycleCount, Probability} when Probability > 0.2 ->
                {deadlocks_detected, DeadlockReport#{
                    severity => medium,
                    cycle_count => CycleCount,
                    probability => Probability
                }};
            {CycleCount, Probability} ->
                {potential_deadlocks, DeadlockReport#{
                    severity => low,
                    cycle_count => CycleCount,
                    probability => Probability
                }}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "deadlock_detection_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {detection_failed, #{error => R, timestamp => erlang:system_time(millisecond)}}
    end.

%% ============================================================================
%% 高级死锁检测算法支持函数
%% ============================================================================

%% @doc 构建资源依赖图
%% 分析系统中的进程、锁、资源等依赖关系，构建有向图
build_resource_dependency_graph() ->
    try
        % 获取所有相关进程
        Processes = get_mongodb_related_processes(),

        % 构建进程间的依赖关系
        Dependencies = lists:foldl(
            fun(Pid, Acc) ->
                ProcessDeps = analyze_process_dependencies(Pid),
                Acc ++ ProcessDeps
            end,
            [],
            Processes
        ),

        % 构建图结构 - 使用邻接表表示
        Graph = build_adjacency_list(Dependencies),

        ?SLOG(debug, #{
            msg => "dependency_graph_built",
            process_count => length(Processes),
            dependency_count => length(Dependencies)
        }),

        Graph
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "dependency_graph_build_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            #{}  % 返回空图
    end.

%% @doc 获取MongoDB相关的进程列表
get_mongodb_related_processes() ->
    try
        AllProcesses = erlang:processes(),

        % 过滤出MongoDB插件相关的进程
        MongoProcesses = lists:filter(
            fun(Pid) ->
                case process_info(Pid, [registered_name, initial_call, current_function]) of
                    undefined -> false;
                    ProcessInfo ->
                        is_mongodb_related_process(ProcessInfo)
                end
            end,
            AllProcesses
        ),

        MongoProcesses
    catch
        _:_ ->
            []
    end.

%% @doc 判断进程是否与MongoDB插件相关
is_mongodb_related_process(ProcessInfo) ->
    RegisteredName = proplists:get_value(registered_name, ProcessInfo),
    InitialCall = proplists:get_value(initial_call, ProcessInfo),
    CurrentFunction = proplists:get_value(current_function, ProcessInfo),

    % 检查注册名称
    NameMatch = case RegisteredName of
        undefined -> false;
        Name when is_atom(Name) ->
            NameStr = atom_to_list(Name),
            string:str(NameStr, "mongodb") > 0
    end,

    % 检查初始调用
    InitialMatch = case InitialCall of
        {Module, _, _} ->
            ModuleStr = atom_to_list(Module),
            string:str(ModuleStr, "emqx_plugin_mongodb") > 0;
        _ -> false
    end,

    % 检查当前函数
    CurrentMatch = case CurrentFunction of
        {CurrentModule, _, _} ->
            CurrentModuleStr = atom_to_list(CurrentModule),
            string:str(CurrentModuleStr, "emqx_plugin_mongodb") > 0;
        _ -> false
    end,

    NameMatch orelse InitialMatch orelse CurrentMatch.

%% @doc 分析单个进程的依赖关系
analyze_process_dependencies(Pid) ->
    try
        Dependencies = [],

        % 1. 分析消息队列中的等待关系
        QueueDeps = analyze_message_queue_dependencies(Pid),

        % 2. 分析ETS表锁依赖
        ETSDeps = analyze_ets_dependencies(Pid),

        % 3. 分析端口依赖
        PortDeps = analyze_port_dependencies(Pid),

        % 4. 分析监控关系
        MonitorDeps = analyze_monitor_dependencies(Pid),

        Dependencies ++ QueueDeps ++ ETSDeps ++ PortDeps ++ MonitorDeps
    catch
        _:_ ->
            []
    end.

%% @doc 分析消息队列依赖关系
analyze_message_queue_dependencies(Pid) ->
    try
        case process_info(Pid, [message_queue_len, messages]) of
            undefined -> [];
            ProcessInfo ->
                QueueLen = proplists:get_value(message_queue_len, ProcessInfo, 0),
                Messages = proplists:get_value(messages, ProcessInfo, []),

                % 分析消息中的依赖关系
                analyze_messages_for_dependencies(Pid, Messages, QueueLen)
        end
    catch
        _:_ -> []
    end.

%% @doc 分析ETS表依赖关系
analyze_ets_dependencies(Pid) ->
    try
        % 获取进程拥有的ETS表
        OwnedTables = ets:all(),
        ProcessTables = lists:filter(
            fun(Table) ->
                case ets:info(Table, owner) of
                    Pid -> true;
                    _ -> false
                end
            end,
            OwnedTables
        ),

        % 为每个表创建依赖关系
        lists:map(
            fun(Table) ->
                #{
                    type => ets_ownership,
                    from => Pid,
                    to => Table,
                    resource => Table,
                    weight => 1.0
                }
            end,
            ProcessTables
        )
    catch
        _:_ -> []
    end.

%% @doc 分析端口依赖关系
analyze_port_dependencies(Pid) ->
    try
        case process_info(Pid, links) of
            undefined -> [];
            {links, Links} ->
                % 过滤出端口链接
                Ports = lists:filter(fun erlang:is_port/1, Links),
                lists:map(
                    fun(Port) ->
                        #{
                            type => port_link,
                            from => Pid,
                            to => Port,
                            resource => Port,
                            weight => 0.8
                        }
                    end,
                    Ports
                )
        end
    catch
        _:_ -> []
    end.

%% @doc 分析监控依赖关系
analyze_monitor_dependencies(Pid) ->
    try
        MonitoringDeps = case process_info(Pid, monitoring) of
            undefined -> [];
            {monitoring, Monitoring} ->
                lists:map(
                    fun({process, MonitoredPid}) ->
                        #{
                            type => monitor,
                            from => Pid,
                            to => MonitoredPid,
                            resource => MonitoredPid,
                            weight => 0.6
                        }
                    end,
                    Monitoring
                )
        end,

        MonitoredByDeps = case process_info(Pid, monitored_by) of
            undefined -> [];
            {monitored_by, MonitoredBy} ->
                lists:map(
                    fun(MonitoringPid) ->
                        #{
                            type => monitored_by,
                            from => MonitoringPid,
                            to => Pid,
                            resource => Pid,
                            weight => 0.6
                        }
                    end,
                    MonitoredBy
                )
        end,

        MonitoringDeps ++ MonitoredByDeps
    catch
        _:_ -> []
    end.

%% @doc 分析消息中的依赖关系
analyze_messages_for_dependencies(Pid, Messages, QueueLen) ->
    try
        % 如果队列过长，可能存在阻塞
        if
            QueueLen > 100 ->
                % 分析消息模式，寻找可能的等待关系
                analyze_message_patterns(Pid, Messages);
            true ->
                []
        end
    catch
        _:_ -> []
    end.

%% @doc 分析消息模式
analyze_message_patterns(Pid, Messages) ->
    try
        % 统计消息类型
        MessageTypes = lists:foldl(
            fun(Msg, Acc) ->
                Type = classify_message_type(Msg),
                maps:update_with(Type, fun(Count) -> Count + 1 end, 1, Acc)
            end,
            #{},
            Messages
        ),

        % 如果某种类型的消息过多，可能存在依赖问题
        lists:foldl(
            fun({Type, Count}, Acc) ->
                if
                    Count > 50 ->
                        [#{
                            type => message_backlog,
                            from => Pid,
                            to => Type,
                            resource => {message_type, Type},
                            weight => min(Count / 100.0, 1.0)
                        } | Acc];
                    true ->
                        Acc
                end
            end,
            [],
            maps:to_list(MessageTypes)
        )
    catch
        _:_ -> []
    end.

%% @doc 分类消息类型
classify_message_type(Message) ->
    case Message of
        {mongodb_request, _} -> mongodb_request;
        {mongodb_response, _} -> mongodb_response;
        {'$gen_call', _, _} -> gen_call;
        {'$gen_cast', _} -> gen_cast;
        {'EXIT', _, _} -> exit_signal;
        {'DOWN', _, _, _, _} -> down_signal;
        _ -> other
    end.

%% @doc 构建邻接表
build_adjacency_list(Dependencies) ->
    lists:foldl(
        fun(#{from := From, to := _To} = Dep, Graph) ->
            Neighbors = maps:get(From, Graph, []),
            maps:put(From, [Dep | Neighbors], Graph)
        end,
        #{},
        Dependencies
    ).

%% @doc 在依赖图中检测环路（使用DFS算法）
detect_cycles_in_dependency_graph(Graph) ->
    try
        Vertices = maps:keys(Graph),
        {Cycles, _} = lists:foldl(
            fun(Vertex, {CyclesAcc, VisitedAcc}) ->
                case maps:is_key(Vertex, VisitedAcc) of
                    true -> {CyclesAcc, VisitedAcc};
                    false ->
                        {NewCycles, NewVisited} = dfs_detect_cycles(
                            Vertex, Graph, #{}, #{}, []
                        ),
                        {CyclesAcc ++ NewCycles, maps:merge(VisitedAcc, NewVisited)}
                end
            end,
            {[], #{}},
            Vertices
        ),
        Cycles
    catch
        _:_ -> []
    end.

%% @doc 深度优先搜索检测环路
dfs_detect_cycles(Vertex, Graph, Visited, RecStack, Path) ->
    try
        % 标记当前节点为已访问和在递归栈中
        NewVisited = maps:put(Vertex, true, Visited),
        NewRecStack = maps:put(Vertex, true, RecStack),
        NewPath = [Vertex | Path],

        % 获取邻居节点
        Neighbors = maps:get(Vertex, Graph, []),

        % 遍历所有邻居
        lists:foldl(
            fun(#{to := Neighbor} = _Dep, {CyclesAcc, VisitedAcc}) ->
                case maps:get(Neighbor, NewRecStack, false) of
                    true ->
                        % 发现环路
                        Cycle = extract_cycle(Neighbor, NewPath),
                        {[Cycle | CyclesAcc], VisitedAcc};
                    false ->
                        case maps:get(Neighbor, NewVisited, false) of
                            false ->
                                % 递归访问未访问的邻居
                                dfs_detect_cycles(
                                    Neighbor, Graph, VisitedAcc, NewRecStack, NewPath
                                );
                            true ->
                                {CyclesAcc, VisitedAcc}
                        end
                end
            end,
            {[], NewVisited},
            Neighbors
        )
    catch
        _:_ -> {[], Visited}
    end.

%% @doc 从路径中提取环路
extract_cycle(StartVertex, Path) ->
    extract_cycle_helper(StartVertex, Path, []).

extract_cycle_helper(_StartVertex, [], Acc) ->
    lists:reverse(Acc);
extract_cycle_helper(StartVertex, [Vertex | Rest], Acc) ->
    if
        Vertex =:= StartVertex ->
            lists:reverse([Vertex | Acc]);
        true ->
            extract_cycle_helper(StartVertex, Rest, [Vertex | Acc])
    end.

%% @doc 分析等待链
analyze_wait_chains() ->
    try
        % 获取所有MongoDB相关进程
        Processes = get_mongodb_related_processes(),

        % 分析每个进程的等待状态
        WaitChains = lists:foldl(
            fun(Pid, Acc) ->
                WaitInfo = analyze_process_wait_state(Pid),
                case WaitInfo of
                    #{waiting := true} = Info ->
                        [Info | Acc];
                    _ ->
                        Acc
                end
            end,
            [],
            Processes
        ),

        % 构建等待链
        build_wait_chains(WaitChains)
    catch
        _:_ -> []
    end.

%% @doc 分析进程等待状态
analyze_process_wait_state(Pid) ->
    try
        case process_info(Pid, [status, current_function, message_queue_len]) of
            undefined ->
                #{waiting => false};
            ProcessInfo ->
                Status = proplists:get_value(status, ProcessInfo),
                CurrentFunction = proplists:get_value(current_function, ProcessInfo),
                QueueLen = proplists:get_value(message_queue_len, ProcessInfo, 0),

                % 判断是否在等待
                IsWaiting = is_process_waiting(Status, CurrentFunction, QueueLen),

                #{
                    pid => Pid,
                    waiting => IsWaiting,
                    status => Status,
                    current_function => CurrentFunction,
                    queue_length => QueueLen,
                    wait_type => classify_wait_type(Status, CurrentFunction)
                }
        end
    catch
        _:_ ->
            #{waiting => false}
    end.

%% @doc 判断进程是否在等待
is_process_waiting(Status, CurrentFunction, QueueLen) ->
    % 基于状态判断
    StatusWaiting = case Status of
        waiting -> true;
        suspended -> true;
        _ -> false
    end,

    % 基于当前函数判断
    FunctionWaiting = case CurrentFunction of
        {gen_server, loop, _} -> QueueLen =:= 0;  % gen_server空闲等待
        {gen, do_call, _} -> true;  % 正在进行调用
        {erlang, hibernate, _} -> true;  % 休眠状态
        {timer, sleep, _} -> true;  % 睡眠状态
        _ -> false
    end,

    StatusWaiting orelse FunctionWaiting.

%% @doc 分类等待类型
classify_wait_type(Status, CurrentFunction) ->
    case {Status, CurrentFunction} of
        {waiting, _} -> message_wait;
        {suspended, _} -> suspended_wait;
        {_, {gen, do_call, _}} -> call_wait;
        {_, {erlang, hibernate, _}} -> hibernate_wait;
        {_, {timer, sleep, _}} -> sleep_wait;
        _ -> unknown_wait
    end.

%% @doc 构建等待链
build_wait_chains(WaitInfos) ->
    try
        % 按等待类型分组
        GroupedWaits = lists:foldl(
            fun(#{wait_type := Type} = Info, Acc) ->
                Group = maps:get(Type, Acc, []),
                maps:put(Type, [Info | Group], Acc)
            end,
            #{},
            WaitInfos
        ),

        % 为每个组构建等待链
        maps:fold(
            fun(Type, Infos, Acc) ->
                Chain = #{
                    type => Type,
                    processes => Infos,
                    length => length(Infos),
                    total_queue_length => lists:sum([
                        maps:get(queue_length, Info, 0) || Info <- Infos
                    ])
                },
                [Chain | Acc]
            end,
            [],
            GroupedWaits
        )
    catch
        _:_ -> []
    end.

%% @doc 计算死锁概率
calculate_deadlock_probability(Cycles, WaitChains) ->
    try
        % 基础概率计算
        CycleProbability = calculate_cycle_probability(Cycles),
        WaitChainProbability = calculate_wait_chain_probability(WaitChains),

        % 系统负载因子
        LoadFactor = calculate_system_load_factor(),

        % 历史数据因子
        HistoryFactor = calculate_history_factor(),

        % 综合概率计算（使用加权平均）
        Weights = #{
            cycle => 0.4,
            wait_chain => 0.3,
            load => 0.2,
            history => 0.1
        },

        FinalProbability =
            CycleProbability * maps:get(cycle, Weights) +
            WaitChainProbability * maps:get(wait_chain, Weights) +
            LoadFactor * maps:get(load, Weights) +
            HistoryFactor * maps:get(history, Weights),

        % 确保概率在[0, 1]范围内
        max(0.0, min(1.0, FinalProbability))
    catch
        _:_ -> 0.0
    end.

%% @doc 计算环路概率
calculate_cycle_probability(Cycles) ->
    case length(Cycles) of
        0 -> 0.0;
        CycleCount ->
            % 环路数量越多，死锁概率越高
            BaseProbability = min(CycleCount * 0.2, 0.8),

            % 考虑环路复杂度
            ComplexityFactor = lists:foldl(
                fun(Cycle, Acc) ->
                    CycleLength = length(Cycle),
                    % 短环路更危险
                    Factor = if
                        CycleLength =< 2 -> 0.9;
                        CycleLength =< 4 -> 0.7;
                        CycleLength =< 6 -> 0.5;
                        true -> 0.3
                    end,
                    max(Acc, Factor)
                end,
                0.0,
                Cycles
            ),

            BaseProbability * ComplexityFactor
    end.

%% @doc 计算等待链概率
calculate_wait_chain_probability(WaitChains) ->
    case length(WaitChains) of
        0 -> 0.0;
        _ ->
            % 分析等待链的严重程度
            lists:foldl(
                fun(#{type := Type, length := Length, total_queue_length := QueueLen}, Acc) ->
                    % 根据等待类型计算权重
                    TypeWeight = case Type of
                        call_wait -> 0.8;      % 调用等待最危险
                        message_wait -> 0.6;   % 消息等待次之
                        suspended_wait -> 0.7; % 挂起等待也很危险
                        _ -> 0.3
                    end,

                    % 根据长度和队列长度计算概率
                    LengthFactor = min(Length / 10.0, 1.0),
                    QueueFactor = min(QueueLen / 1000.0, 1.0),

                    ChainProbability = TypeWeight * LengthFactor * QueueFactor,
                    max(Acc, ChainProbability)
                end,
                0.0,
                WaitChains
            )
    end.

%% @doc 计算系统负载因子
calculate_system_load_factor() ->
    try
        % CPU负载
        RunQueueLen = erlang:statistics(run_queue),
        CPUFactor = min(RunQueueLen / 100.0, 1.0),

        % 内存使用
        MemoryInfo = erlang:memory(),
        TotalMemory = proplists:get_value(total, MemoryInfo, 1),
        ProcessMemory = proplists:get_value(processes, MemoryInfo, 0),
        MemoryFactor = ProcessMemory / TotalMemory,

        % 进程数量
        ProcessCount = erlang:system_info(process_count),
        ProcessLimit = erlang:system_info(process_limit),
        ProcessFactor = ProcessCount / ProcessLimit,

        % 综合负载因子
        (CPUFactor + MemoryFactor + ProcessFactor) / 3.0
    catch
        _:_ -> 0.5  % 默认中等负载
    end.

%% @doc 计算历史因子
calculate_history_factor() ->
    try
        % 这里可以基于历史死锁数据计算
        % 暂时返回默认值
        0.1
    catch
        _:_ -> 0.0
    end.

%% @doc 生成死锁报告
generate_deadlock_report(Cycles, WaitChains, Probability) ->
    #{
        timestamp => erlang:system_time(millisecond),
        cycles => #{
            count => length(Cycles),
            details => Cycles
        },
        wait_chains => #{
            count => length(WaitChains),
            details => WaitChains
        },
        probability => Probability,
        system_info => #{
            run_queue_length => erlang:statistics(run_queue),
            process_count => erlang:system_info(process_count),
            memory_usage => erlang:memory(total)
        },
        recommendations => generate_deadlock_recommendations(Cycles, WaitChains, Probability)
    }.

%% @doc 生成死锁处理建议
generate_deadlock_recommendations(Cycles, WaitChains, Probability) ->
    Recommendations = [],

    % 基于环路的建议
    CycleRecs = if
        length(Cycles) > 0 ->
            [
                "检测到资源依赖环路，建议检查进程间的锁获取顺序",
                "考虑使用超时机制避免无限等待",
                "优化资源访问模式，减少交叉依赖"
            ];
        true -> []
    end,

    % 基于等待链的建议
    WaitRecs = if
        length(WaitChains) > 0 ->
            [
                "检测到长等待链，建议优化消息处理逻辑",
                "考虑增加并发处理能力",
                "检查是否存在阻塞操作"
            ];
        true -> []
    end,

    % 基于概率的建议
    ProbRecs = if
        Probability > 0.8 ->
            [
                "死锁概率极高，建议立即采取预防措施",
                "考虑重启相关服务以清除潜在死锁"
            ];
        Probability > 0.5 ->
            [
                "死锁概率较高，建议密切监控系统状态",
                "准备应急处理方案"
            ];
        Probability > 0.2 ->
            [
                "存在死锁风险，建议优化系统设计"
            ];
        true -> []
    end,

    Recommendations ++ CycleRecs ++ WaitRecs ++ ProbRecs.

%% ============================================================================
%% 模块协调和事件处理函数
%% ============================================================================

%% @doc 处理优雅降级事件
handle_graceful_degradation(Event) ->
    try
        Level = maps:get(degradation_level, Event, 1),
        ?SLOG(info, #{msg => "handling_graceful_degradation", level => Level}),

        case Level of
            1 -> reduce_concurrency_level(0.9);  % 轻微降级
            2 -> reduce_concurrency_level(0.7);  % 中等降级
            3 -> reduce_concurrency_level(0.5);  % 重度降级
            _ -> reduce_concurrency_level(0.3)   % 严重降级
        end,

        ok
    catch
        _:_ ->
            ok
    end.

%% @doc 处理故障隔离事件
handle_fault_isolation(Event) ->
    try
        Component = maps:get(component, Event, unknown),
        ?SLOG(info, #{msg => "handling_fault_isolation", component => Component}),

        % 根据隔离的组件调整并发策略
        case Component of
            connection_pool ->
                % 连接池隔离：降低连接并发度
                adjust_connection_concurrency(conservative);
            message_persistence ->
                % 消息持久化隔离：降低写入并发度
                adjust_write_concurrency(conservative);
            _ ->
                % 其他组件：通用降级
                reduce_concurrency_level(0.8)
        end,

        ok
    catch
        _:_ ->
            ok
    end.

%% @doc 处理紧急模式事件
handle_emergency_mode(Event) ->
    try
        ?SLOG(warning, #{msg => "handling_emergency_mode", event => Event}),

        % 紧急模式：最大程度降低并发度
        reduce_concurrency_level(0.2),

        % 启用保守的锁策略
        enable_conservative_locking(),

        % 禁用非关键的并发优化
        disable_non_critical_optimizations(),

        ok
    catch
        _:_ ->
            ok
    end.

%% @doc 与内存泄漏检测模块协调
coordinate_with_memory_leak_detector() ->
    try
        case erlang:function_exported(emqx_plugin_mongodb_memory_leak_detector, register_concurrency_handler, 1) of
            true ->
                Handler = fun(Event) ->
                    case maps:get(memory_pressure, Event, false) of
                        true -> reduce_concurrency_level(0.8);
                        false -> ok
                    end
                end,
                emqx_plugin_mongodb_memory_leak_detector:register_concurrency_handler(Handler),
                ?SLOG(debug, #{msg => "coordinated_with_memory_leak_detector"});
            false ->
                ?SLOG(debug, #{msg => "memory_leak_detector_coordination_not_available"})
        end
    catch
        _:_ ->
            ok
    end.

%% @doc 与批处理优化模块协调
coordinate_with_batch_optimizer() ->
    try
        case erlang:function_exported(emqx_plugin_mongodb_batch_optimizer, register_concurrency_handler, 1) of
            true ->
                Handler = fun(Event) ->
                    case maps:get(batch_contention, Event, false) of
                        true -> adjust_batch_concurrency(reduce);
                        false -> ok
                    end
                end,
                emqx_plugin_mongodb_batch_optimizer:register_concurrency_handler(Handler),
                ?SLOG(debug, #{msg => "coordinated_with_batch_optimizer"});
            false ->
                ?SLOG(debug, #{msg => "batch_optimizer_coordination_not_available"})
        end
    catch
        _:_ ->
            ok
    end.

%% @doc 与索引优化模块协调
coordinate_with_index_optimizer() ->
    try
        case erlang:function_exported(emqx_plugin_mongodb_index_optimizer, register_concurrency_handler, 1) of
            true ->
                Handler = fun(Event) ->
                    case maps:get(index_contention, Event, false) of
                        true -> adjust_query_concurrency(reduce);
                        false -> ok
                    end
                end,
                emqx_plugin_mongodb_index_optimizer:register_concurrency_handler(Handler),
                ?SLOG(debug, #{msg => "coordinated_with_index_optimizer"});
            false ->
                ?SLOG(debug, #{msg => "index_optimizer_coordination_not_available"})
        end
    catch
        _:_ ->
            ok
    end.

%% ============================================================================
%% 辅助函数
%% ============================================================================

%% @doc 降低并发度
reduce_concurrency_level(Factor) when Factor > 0, Factor =< 1 ->
    try
        gen_server:cast(?MODULE, {reduce_concurrency, Factor}),
        ?SLOG(info, #{msg => "concurrency_level_reduced", factor => Factor})
    catch
        _:_ ->
            ok
    end.

%% @doc 调整连接并发度
adjust_connection_concurrency(Strategy) ->
    try
        gen_server:cast(?MODULE, {adjust_connection_concurrency, Strategy}),
        ?SLOG(info, #{msg => "connection_concurrency_adjusted", strategy => Strategy})
    catch
        _:_ ->
            ok
    end.

%% @doc 调整写入并发度
adjust_write_concurrency(Strategy) ->
    try
        gen_server:cast(?MODULE, {adjust_write_concurrency, Strategy}),
        ?SLOG(info, #{msg => "write_concurrency_adjusted", strategy => Strategy})
    catch
        _:_ ->
            ok
    end.

%% @doc 启用保守锁策略
enable_conservative_locking() ->
    try
        gen_server:cast(?MODULE, enable_conservative_locking),
        ?SLOG(info, #{msg => "conservative_locking_enabled"})
    catch
        _:_ ->
            ok
    end.

%% @doc 禁用非关键优化
disable_non_critical_optimizations() ->
    try
        gen_server:cast(?MODULE, disable_non_critical_optimizations),
        ?SLOG(info, #{msg => "non_critical_optimizations_disabled"})
    catch
        _:_ ->
            ok
    end.

%% @doc 调整批处理并发度
adjust_batch_concurrency(Action) ->
    try
        gen_server:cast(?MODULE, {adjust_batch_concurrency, Action}),
        ?SLOG(info, #{msg => "batch_concurrency_adjusted", action => Action})
    catch
        _:_ ->
            ok
    end.

%% @doc 调整查询并发度
adjust_query_concurrency(Action) ->
    try
        gen_server:cast(?MODULE, {adjust_query_concurrency, Action}),
        ?SLOG(info, #{msg => "query_concurrency_adjusted", action => Action})
    catch
        _:_ ->
            ok
    end.
