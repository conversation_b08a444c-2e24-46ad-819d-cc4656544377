%%%-------------------------------------------------------------------
%%% @doc MongoDB连接器模块
%%%
%%% 这个模块是MongoDB插件的核心连接器，实现了EMQX资源管理框架的接口
%%% 负责管理MongoDB连接的生命周期、执行数据库操作、处理连接池等功能
%%%
%%% 功能特性：
%%% 1. 连接管理：建立、维护、关闭MongoDB连接
%%% 2. 连接池：支持连接池管理，提高并发性能
%%% 3. 查询执行：支持同步和异步查询操作
%%% 4. 故障处理：自动重连、连接健康检查、错误恢复
%%% 5. 性能监控：连接状态监控、操作指标统计
%%% 6. 批量操作：支持高效的批量插入和更新
%%%
%%% Java等价概念：
%%% - 类似于Spring Data MongoDB的MongoTemplate
%%% - 类似于MyBatis的SqlSessionFactory
%%% - 类似于Hibernate的SessionFactory
%%% - 类似于JDBC的DataSource和Connection管理
%%%
%%% EMQX资源框架：
%%% 实现emqx_resource行为，遵循EMQX的资源管理规范
%%% 提供标准化的资源生命周期管理接口
%%%
%%% 设计模式：
%%% - 工厂模式：创建和管理MongoDB连接
%%% - 代理模式：封装MongoDB驱动的复杂性
%%% - 观察者模式：监控连接状态变化
%%% - 策略模式：支持不同的查询和连接策略
%%%
%%% @end
%%%-------------------------------------------------------------------
-module(emqx_plugin_mongodb_connector).

%% 实现emqx_resource行为
%% 这是EMQX框架定义的资源管理接口，类似于Java中的接口实现
%% 需要实现资源的启动、停止、状态查询、操作执行等回调函数
%%
%% 在Java中相当于：
%% public class MongoDBConnector implements ResourceConnector {
%%     // 实现接口定义的所有方法
%% }
-behaviour(emqx_resource).

% 引入头文件

% EMQX resource相关的宏和记录定义
%-include_lib("emqx_resource/include/emqx_resource.hrl").
% EMQX 日志宏
% 插件自定义的宏和记录
-include("emqx_plugin_mongodb.hrl").

% 定义临时日志宏，用于在缺少EMQX日志宏的情况下提供基本日志功能
-ifndef(SLOG).
-define(SLOG(Level, Msg),
    io:format("[~p] [~p] ~p~n", [Level, ?MODULE, Msg])
).
-endif.

% 定义状态常量
-define(status_connected, connected).
-define(status_disconnected, disconnected).
-define(status_connecting, connecting).

%% API导出函数 - emqx_resource行为回调函数
%% 这些是EMQX资源框架要求实现的标准回调函数
%% 类似于Java中接口定义的方法，必须全部实现
-export([
    query_mode/1,        % 查询模式配置，定义资源支持的查询类型
    callback_mode/0,     % 回调模式配置，定义异步回调的处理方式
    on_start/2,          % 资源启动回调，类似于@PostConstruct
    on_get_status/2,     % 获取资源状态回调，类似于健康检查接口
    on_stop/2,           % 资源停止回调，类似于@PreDestroy
    on_query/3,          % 同步查询处理回调，类似于同步方法调用
    on_query_async/4     % 异步查询处理回调，类似于异步方法调用
]).

%% 测试和调试函数导出
%% 这些函数主要用于单元测试、集成测试和运行时调试
%% 类似于Java中的@VisibleForTesting注解的方法
-export([
    check_connection_alive/1    % 检查连接是否存活，用于健康检查和测试
]).

%% 监控指标相关函数导出
%% 这些函数用于性能监控、指标收集和统计分析
%% 类似于Java中的Micrometer或Prometheus指标收集
-export([
    init_metrics/1,      % 初始化指标收集器，类似于MetricRegistry初始化
    inc_metric/3,        % 增加计数器指标，类似于Counter.increment()
    observe_latency/2    % 记录延迟指标，类似于Timer.record()
]).

%% 工具函数导出
%% 这些是通用的工具函数，提供常用的操作和优化功能
%% 类似于Java中的Utility类的静态方法
-export([
    with_timeout/2,              % 超时控制包装器，类似于CompletableFuture.orTimeout()
    safe_batch_insert/3,         % 安全批量插入，包含错误处理和重试
    zero_copy_batch_insert/3,    % 零拷贝批量插入，性能优化版本
    reconnect/0,                 % 手动重连功能，用于故障恢复
    refresh_topology/0           % 刷新集群拓扑，用于集群状态更新
]).

% 定义常量
% 类似于Java中的 `private static final int DEFAULT_POOL_SIZE = 8;`

% 默认连接池大小（已在头文件中定义）
% 默认操作超时时间 (毫秒)
-define(DEFAULT_TIMEOUT_MS, 15000).
% 最大重连次数 (增加到10次)
-define(MAX_RECONNECT_TIMES, 10).
% 基础重连延迟 (毫秒) (降低初始延迟)
-define(RECONNECT_DELAY, 1000).
% 最大重连延迟 (1分钟) (降低最大延迟)
-define(MAX_RECONNECT_DELAY, 60000).
% 停止操作的超时时间 (毫秒)
-define(STOP_TIMEOUT_MS, 5000).
-define(DEFAULT_CONNECTION_POOL_OPTS, [
    % 增加默认连接池大小
    {size, 16},
    % 增加最大溢出连接数
    {max_overflow, 32},
    % 保持FIFO策略
    {strategy, fifo},
    % 设置溢出连接的生存时间为5分钟
    {overflow_ttl, 300000},
    % 每分钟检查一次溢出连接
    {overflow_check_period, 60000},
    % 队列化请求而不是直接失败
    {queue_if_full, true}
]).
% 插入操作超时时间 (毫秒)
-define(INSERT_TIMEOUT_MS, 30000).
% 健康检查缓存有效期 (毫秒)
-define(HEALTH_CHECK_CACHE_TTL, 10000).
% 批量处理超时时间
-define(BATCH_TIMEOUT_MS, 100).
% 最大批量队列大小
-define(MAX_BATCH_QUEUE_SIZE, 5000).
% 等待队列超时时间 (当连接池满且无溢出连接可用时，请求在队列中等待的超时)
-define(WAIT_QUEUE_TIMEOUT_MS, 1000).

% 定义指标名称
-define(METRIC_INSERT_COUNT, mongodb_insert_count).
-define(METRIC_INSERT_ERROR, mongodb_insert_error).
-define(METRIC_QUERY_LATENCY, mongodb_query_latency).
-define(METRIC_CONNECTION_FAILURE, mongodb_connection_failure).

% 添加更详细的错误计数和监控
-define(ERROR_COUNTERS, [
    connection_error,
    authentication_error,
    timeout_error,
    query_error,
    network_error,
    unknown_error
]).

% 断路器状态
-record(circuit_breaker, {
    % closed, half_open, open
    state = closed,
    % 连续失败次数
    failure_count = 0,
    % 最后失败时间
    last_failure_time = 0,
    % 重置超时(毫秒)
    reset_timeout = 30000,
    % 失败阈值
    failure_threshold = 5,
    % 新增字段
    error_types = #{},
    partial_open = false,
    health_ratio = 1.0
}).

%% @doc 初始化熔断器
%% 这个函数创建并初始化MongoDB连接的熔断器，用于防止级联故障
%%
%% 功能说明：
%% 1. 创建熔断器记录，设置失败阈值和重置超时
%% 2. 将熔断器状态存储到persistent_term中，供全局访问
%% 3. 初始化熔断器为关闭状态（正常工作状态）
%% 4. 记录失败次数和最后失败时间
%%
%% 参数说明：
%% - FailureThreshold: 失败阈值，超过此次数将打开熔断器
%% - ResetTimeout: 重置超时时间（毫秒），熔断器从打开到半开的等待时间
%%
%% 返回值：
%% - ok: 初始化成功
%%
%% Java等价概念：
%% 类似于Netflix Hystrix的CircuitBreaker初始化
%% 或者Resilience4j的CircuitBreakerConfig
%%
%% 熔断器状态：
%% - closed: 关闭状态，正常处理请求
%% - open: 打开状态，拒绝所有请求
%% - half_open: 半开状态，允许少量请求测试服务恢复
init_circuit_breaker(FailureThreshold, ResetTimeout) ->
    %% 创建熔断器记录结构
    %% 使用Erlang记录语法定义熔断器的初始状态
    %% 在Java中相当于：
    %% CircuitBreaker cb = CircuitBreaker.builder()
    %%     .failureThreshold(failureThreshold)
    %%     .resetTimeout(resetTimeout)
    %%     .build();
    CB = #circuit_breaker{
        failure_threshold = FailureThreshold,    %% 失败阈值，超过此值打开熔断器
        reset_timeout = ResetTimeout             %% 重置超时时间（毫秒）
    },
    %% 尝试将熔断器状态保存到persistent_term全局存储
    %% persistent_term提供高效的全局只读存储，适合配置信息
    %% 类似于Java中的静态变量或Spring的ApplicationContext
    try
        persistent_term:put(mongodb_circuit_breaker, CB)
    catch
        %% 如果保存失败，记录警告但不中断初始化流程
        %% 这是一种优雅降级策略，确保系统能够继续运行
        _:_ ->
            ?SLOG(warning, #{msg => "failed_to_init_circuit_breaker"})
    end,
    %% 返回创建的熔断器实例
    CB.

%% @doc 获取熔断器状态
%% 这个函数从全局存储中获取当前的熔断器状态
%%
%% 功能说明：
%% 1. 从persistent_term中读取熔断器状态
%% 2. 如果获取失败，创建并保存默认的熔断器配置
%% 3. 提供容错机制，确保系统能够正常运行
%%
%% 返回值：
%% - #circuit_breaker{}: 熔断器记录，包含当前状态和配置
%%
%% Java等价概念：
%% 类似于获取CircuitBreaker实例的静态方法
%% 或者Spring的@Autowired注入CircuitBreaker
%%
%% 容错策略：
%% 如果获取失败，创建默认配置并保存，确保系统可用性
get_circuit_breaker() ->
    try
        %% 从persistent_term全局存储中获取熔断器状态
        %% persistent_term提供高效的全局只读访问
        %% 类似于Java中的静态变量访问
        persistent_term:get(mongodb_circuit_breaker)
    catch
        %% 如果获取失败（可能是未初始化），创建默认熔断器
        %% 这是一种防御性编程策略，确保系统的健壮性
        _:_ ->
            %% 创建默认熔断器配置
            %% 使用硬编码的默认值，确保基本功能可用
            CB = #circuit_breaker{
                failure_threshold = 5,      %% 默认失败阈值：5次
                reset_timeout = 30000       %% 默认重置超时：30秒
            },
            %% 尝试保存默认配置到全局存储
            %% 这样后续访问就能使用这个默认配置
            try
                persistent_term:put(mongodb_circuit_breaker, CB)
            catch
                %% 如果保存失败也不影响返回结果
                %% 优雅处理，不中断业务流程
                _:_ -> ok
            end,
            %% 返回创建的默认熔断器
            CB
    end.

%% @doc 更新熔断器状态
%% 这个函数将熔断器状态更新到全局存储中
%%
%% 功能说明：
%% 1. 将熔断器状态保存到persistent_term全局存储
%% 2. 确保所有进程都能访问到最新的熔断器状态
%% 3. 提供高效的全局状态同步机制
%%
%% 参数说明：
%% - CB: 熔断器记录，包含状态、失败计数、阈值等信息
%%
%% 返回值：
%% - ok: 总是返回ok
%%
%% Java等价概念：
%% 类似于更新共享状态或发布状态变更事件
%% 或者使用AtomicReference.set()更新共享对象
%%
%% 实现说明：
%% 使用persistent_term提供高效的全局只读访问
update_circuit_breaker(CB) ->
    %% 将熔断器状态保存到persistent_term全局存储
    %% persistent_term提供高效的全局只读访问，适合频繁读取的配置信息
    %% 在Java中相当于：
    %% circuitBreakerRegistry.updateState(circuitBreaker);
    %% 或者：atomicReference.set(circuitBreaker);
    persistent_term:put(mongodb_circuit_breaker, CB).

%% @doc 检查熔断器是否允许操作
%% 这个函数检查熔断器的当前状态，决定是否允许执行操作
%%
%% 功能说明：
%% 1. 获取当前熔断器状态
%% 2. 根据状态和时间条件决定是否允许操作
%% 3. 在适当时机将熔断器从打开状态转为半开状态
%% 4. 实现智能的流量控制和自动恢复机制
%%
%% 返回值：
%% - {ok, CB}: 允许执行操作，返回熔断器状态
%% - {error, circuit_open}: 拒绝执行操作，熔断器处于保护状态
%%
%% Java等价概念：
%% 类似于CircuitBreaker.allowRequest()
%% 或者Hystrix的isCircuitBreakerOpen()检查
%%
%% 状态转换：
%% - closed -> {ok, CB} (正常状态，允许所有操作)
%% - open -> half_open (超时后转为半开状态，使用健康比率控制流量)
%% - half_open -> 根据健康比率决定 (智能流量控制)
check_circuit_breaker() ->
    %% 获取当前熔断器状态
    CB = get_circuit_breaker(),
    %% 获取当前时间戳，用于计算时间差
    Now = erlang:system_time(millisecond),

    %% 根据熔断器当前状态决定是否允许操作
    %% 实现熔断器的状态机逻辑
    case CB#circuit_breaker.state of
        closed ->
            %% 关闭状态：正常工作状态，允许所有操作
            %% 在Java中相当于：
            %% if (state == CLOSED) return ALLOW;
            {ok, CB};
        open ->
            %% 打开状态：保护状态，需要检查是否可以转为半开状态
            %% 计算距离最后一次失败的时间
            TimeSinceFailure = Now - CB#circuit_breaker.last_failure_time,
            if
                TimeSinceFailure >= CB#circuit_breaker.reset_timeout ->
                    %% 超过重置超时时间，转为半开状态进行智能测试
                    %% 使用健康比率控制允许通过的请求比例
                    %% 在Java中相当于：
                    %% if (timeSinceFailure >= resetTimeout) {
                    %%     state = HALF_OPEN;
                    %%     return allowBasedOnHealthRatio();
                    %% }
                    NewCB = CB#circuit_breaker{
                        state = half_open,          %% 转为半开状态
                        partial_open = true,        %% 启用部分开放模式
                        health_ratio = 0.1          %% 允许10%的请求通过进行测试
                    },
                    update_circuit_breaker(NewCB),
                    %% 使用随机数和健康比率决定是否允许请求通过
                    %% 这是一种智能的流量控制机制
                    case rand:uniform() < NewCB#circuit_breaker.health_ratio of
                        true -> {ok, NewCB};           %% 允许请求通过
                        false -> {error, circuit_open} %% 拒绝请求
                    end;
                true ->
                    %% 还未到重置时间，仍在打开状态，拒绝所有操作
                    {error, circuit_open}
            end;
        half_open ->
            %% 半开状态：测试状态，使用智能流量控制
            %% 根据partial_open标志决定使用哪种半开策略
            case CB#circuit_breaker.partial_open of
                true ->
                    %% 智能半开模式：使用健康比率决定是否允许请求
                    %% 这种方式可以逐渐增加允许通过的请求比例
                    %% 在Java中相当于：
                    %% if (random.nextDouble() < healthRatio) {
                    %%     return ALLOW;
                    %% } else {
                    %%     return DENY;
                    %% }
                    case rand:uniform() < CB#circuit_breaker.health_ratio of
                        true -> {ok, CB};           %% 允许请求通过
                        false -> {error, circuit_open} %% 拒绝请求
                    end;
                false ->
                    %% 传统半开状态：允许所有请求通过进行测试
                    %% 这是经典的熔断器半开状态行为
                    %% 在Java中相当于：
                    %% if (state == HALF_OPEN) return ALLOW;
                    {ok, CB}
            end
    end.

%% @doc 记录成功操作
%% 这个函数用于向熔断器报告操作成功，帮助熔断器状态管理
%%
%% 功能说明：
%% 1. 检查熔断器模块是否可用
%% 2. 如果可用，调用熔断器的成功记录方法
%% 3. 如果不可用或失败，优雅处理不影响业务
%% 4. 成功记录有助于熔断器从打开状态恢复到关闭状态
%%
%% 返回值：
%% - ok: 总是返回ok，不影响业务流程
%%
%% Java等价概念：
%% 类似于CircuitBreaker.recordSuccess()
%% 或者Hystrix的成功统计记录
%%
%% 设计理念：
%% 即使熔断器不可用，也不影响主要业务逻辑
record_success() ->
    %% 尝试通过熔断器记录成功操作
    %% 使用try-catch保护，确保不影响主要业务流程
    try
        %% 检查熔断器模块是否导出了record_success函数
        %% 这是一种动态检查机制，提高系统的容错性
        %% 在Java中相当于：
        %% if (circuitBreakerModule.hasMethod("recordSuccess")) {
        %%     circuitBreakerModule.recordSuccess();
        %% }
        case erlang:function_exported(emqx_plugin_mongodb_circuit_breaker, record_success, 0) of
                true ->
                %% 熔断器模块可用，调用成功记录方法
                %% 这会更新熔断器的成功统计，有助于状态恢复
                emqx_plugin_mongodb_circuit_breaker:record_success();
            false ->
                %% 熔断器模块不可用，不执行任何操作
                %% 这是一种优雅降级策略
                ok
        end
    catch
        %% 如果记录过程中发生异常，记录警告日志但不影响主要业务
        %% 包含详细的错误信息用于调试和监控
        %% 这体现了"尽力而为"的设计原则
        E:R:S ->
            ?SLOG(warning, #{
                msg => "failed_to_record_success",
                error => E,                    %% 异常类别
                reason => R,                   %% 异常原因
                stacktrace => S                %% 调用栈信息
            }),
            %% 即使记录失败也返回ok，不影响业务流程
            ok
    end.

%% @doc 记录失败操作
%% 这个函数用于向熔断器报告操作失败，根据当前状态更新熔断器
%%
%% 功能说明：
%% 1. 获取当前熔断器状态
%% 2. 根据当前状态（closed/half_open/open）处理失败
%% 3. 在closed状态下累加失败计数，达到阈值时打开熔断器
%% 4. 在half_open状态下失败时立即打开熔断器
%% 5. 在open状态下只更新最后失败时间
%%
%% 返回值：
%% - ok: 总是返回ok
%%
%% Java等价概念：
%% 类似于CircuitBreaker.recordFailure()
%% 或者Hystrix的失败统计和状态转换
%%
%% 熔断器状态转换：
%% - closed -> open (当失败次数达到阈值时)
%% - half_open -> open (任何失败都立即打开)
%% - open -> open (保持打开状态)
record_failure() ->
    %% 获取当前熔断器状态
    %% 包含当前状态、失败阈值、失败计数等信息
    CB = get_circuit_breaker(),

    %% 获取当前时间戳（毫秒）
    %% 用于记录失败时间，后续用于重置判断
    Now = erlang:system_time(millisecond),

    %% 根据熔断器当前状态处理失败
    %% 不同状态下的失败处理逻辑不同
    %% 在Java中相当于状态机模式的状态转换
    NewCB =
        case CB#circuit_breaker.state of
            closed ->
                %% 关闭状态：正常工作状态，需要统计失败次数
                %% 增加失败计数，检查是否达到打开阈值
                NewCount = CB#circuit_breaker.failure_count + 1,
                if
                    NewCount >= CB#circuit_breaker.failure_threshold ->
                        %% 达到失败阈值，打开熔断器
                        %% 这是熔断器的核心保护机制
                        %% 在Java中相当于：
                        %% if (failureCount >= threshold) { state = OPEN; }
                        CB#circuit_breaker{
                            state = open,                    %% 状态改为打开
                            failure_count = NewCount,        %% 更新失败计数
                            last_failure_time = Now          %% 记录打开时间
                        };
                    true ->
                        %% 未达到阈值，保持关闭状态
                        %% 继续累计失败次数，等待下次判断
                        CB#circuit_breaker{
                            failure_count = NewCount,        %% 更新失败计数
                            last_failure_time = Now          %% 更新最后失败时间
                        }
                end;
            half_open ->
                %% 半开状态：测试状态，允许少量请求通过测试服务恢复
                %% 在半开状态下任何失败都会立即打开熔断器
                %% 这表明服务还没有完全恢复，需要继续保护
                %% 在Java中相当于：
                %% if (state == HALF_OPEN && failure) { state = OPEN; }
                CB#circuit_breaker{
                    state = open,                                        %% 立即打开熔断器
                    failure_count = CB#circuit_breaker.failure_count + 1, %% 增加失败计数
                    last_failure_time = Now                              %% 更新失败时间
                };
            open ->
                %% 打开状态：保护状态，拒绝所有请求
                %% 在打开状态下，只需要更新最后失败时间
                %% 不需要增加失败计数，因为请求已经被拒绝
                %% 失败时间用于计算何时可以转为半开状态
                CB#circuit_breaker{last_failure_time = Now}
        end,

    %% 将更新后的熔断器状态保存到全局存储
    %% 确保所有进程都能看到最新的熔断器状态
    %% 类似于Java中更新共享状态或发布状态变更事件
    update_circuit_breaker(NewCB).

%% @doc 记录操作延迟
%% 这个函数用于记录MongoDB操作的延迟时间，用于性能监控
%%
%% 功能说明：
%% 1. 计算从开始时间到当前时间的延迟
%% 2. 记录延迟指标用于监控和分析
%% 3. 提供性能统计数据
%% 4. 支持延迟分布分析
%%
%% 参数说明：
%% - StartTime: 操作开始时间（毫秒时间戳）
%%
%% 返回值：
%% - ok: 总是返回ok，不影响业务流程
%%
%% Java等价概念：
%% 类似于Micrometer的Timer.recordCallable()
%% 或者Prometheus的Histogram记录
%%
%% 使用场景：
%% - 数据库操作性能监控
%% - SLA监控和报警
%% - 性能优化分析
record_latency(StartTime) ->
    try
        %% 获取当前时间戳（毫秒）
        %% 用于计算操作总耗时
        EndTime = erlang:system_time(millisecond),

        %% 计算操作延迟（毫秒）
        %% 这是从操作开始到结束的总时间
        %% 在Java中相当于：
        %% long latency = System.currentTimeMillis() - startTime;
        Latency = EndTime - StartTime,

        %% 记录延迟指标到日志系统
        %% 使用debug级别避免在生产环境产生过多日志
        %% 在Java中相当于：
        %% logger.debug("Operation latency: {}ms", latency);
        %% meterRegistry.timer("mongodb.operation.latency").record(latency, TimeUnit.MILLISECONDS);
        ?SLOG(debug, #{msg => "operation_latency", latency_ms => Latency}),

        %% 这里可以扩展为：
        %% - 记录到Prometheus指标
        %% - 更新延迟直方图
        %% - 计算P95、P99延迟
        %% - 触发延迟告警
        ok
    catch
        %% 如果记录延迟过程中发生异常，不影响主要业务
        %% 监控功能不应该影响核心业务逻辑
        %% 这体现了"尽力而为"的设计原则
        _:_ ->
            ok
    end.

%% @doc 初始化错误计数器
%% 这个函数初始化各种类型的错误计数器，用于错误统计和监控
%%
%% 功能说明：
%% 1. 为每种错误类型创建高性能的原子计数器
%% 2. 将计数器存储到全局存储中，支持并发访问
%% 3. 提供错误统计的基础设施
%% 4. 支持错误分类和分析
%%
%% 返回值：
%% - [Counter]: 返回创建的计数器列表
%% - []: 如果初始化失败返回空列表
%%
%% Java等价概念：
%% 类似于Micrometer的Counter初始化
%% 或者使用AtomicLong创建并发安全的计数器
%%
%% 实现特点：
%% - 使用counters模块提供高性能原子操作
%% - write_concurrency选项优化并发写入性能
%% - 每种错误类型独立存储，便于查询和统计
init_error_counters() ->
    try
        %% 为每种错误类型创建独立的原子计数器
        %% 使用列表推导式批量创建计数器
        %% 在Java中相当于：
        %% List<AtomicLong> counters = ERROR_TYPES.stream()
        %%     .map(type -> new AtomicLong(0))
        %%     .collect(Collectors.toList());
        [begin
            %% 创建单个原子计数器，支持高并发写入
            %% write_concurrency选项优化多进程并发写入性能
            %% 在Java中相当于：AtomicLong counter = new AtomicLong(0);
            Counter = counters:new(1, [write_concurrency]),

            %% 将计数器存储到全局存储，使用错误类型作为键
            %% 这样可以快速查找特定类型的错误计数器
            %% 在Java中相当于：errorCounters.put(errorType, counter);
            persistent_term:put({mongodb_error_counter, ErrorType}, Counter),

            %% 返回创建的计数器
            Counter
         end || ErrorType <- ?ERROR_COUNTERS]  %% 遍历所有定义的错误类型
    catch
        %% 如果初始化过程中发生异常，记录警告并返回空列表
        %% 错误统计功能不应该影响核心功能的启动
        _:_ ->
            ?SLOG(warning, #{msg => "failed_to_init_error_counters"}),
            []
    end.

%% @doc 增加特定类型的错误计数
%% 这个函数用于增加指定错误类型的计数器，用于错误统计和监控
%%
%% 功能说明：
%% 1. 验证错误类型是否在预定义的错误类型列表中
%% 2. 如果是已知类型，增加对应的计数器
%% 3. 如果是未知类型，增加unknown_error计数器
%% 4. 提供完整的错误处理，确保不影响业务流程
%%
%% 参数说明：
%% - ErrorType: 错误类型，可以是任意原子
%%
%% 返回值：
%% - ok: 总是返回ok，不影响业务流程
%%
%% Java等价概念：
%% 类似于Micrometer的Counter.increment()
%% 或者AtomicLong.incrementAndGet()
%%
%% 设计理念：
%% 即使是未知错误类型也会被统计，便于发现新的错误模式
inc_error_counter(ErrorType) ->
    try
        %% 检查错误类型是否在预定义的错误类型列表中
        %% 这样可以区分已知错误和未知错误
        %% 在Java中相当于：
        %% if (KNOWN_ERROR_TYPES.contains(errorType)) {
        %%     knownErrorCounters.get(errorType).increment();
        %% } else {
        %%     unknownErrorCounter.increment();
        %% }
        case lists:member(ErrorType, ?ERROR_COUNTERS) of
            true ->
                %% 已知错误类型，增加对应的计数器
                try
                    %% 从全局存储中获取对应的原子计数器
                    Counter = persistent_term:get({mongodb_error_counter, ErrorType}),
                    %% 原子性地增加计数器的值
                    %% counters:add(Counter, Index, Value)
                    %% - Counter: 计数器引用
                    %% - 1: 计数器索引（我们只有一个计数器）
                    %% - 1: 增加的值
                    counters:add(Counter, 1, 1)
                catch
                    %% 如果操作失败，静默处理，不影响业务
                    _:_ -> ok
                end;
            false ->
                %% 未知错误类型，增加unknown_error计数
                %% 这样可以发现系统中出现的新错误模式
                %% 有助于错误分析和系统改进
                try
                    Counter = persistent_term:get({mongodb_error_counter, unknown_error}),
                    counters:add(Counter, 1, 1)
                catch
                    %% 如果操作失败，静默处理，不影响业务
                    _:_ -> ok
                end
        end
    catch
        %% 最外层异常处理，确保函数不会因为任何异常而崩溃
        %% 错误统计功能不应该影响核心业务逻辑
        _:_ -> ok
    end.

%% @doc 分类错误并增加相应计数
%% 这个函数根据错误的类型和内容，将其分类并增加对应的错误计数
%%
%% 功能说明：
%% 1. 分析错误的结构和内容
%% 2. 将错误映射到预定义的错误类型
%% 3. 调用inc_error_counter增加对应的计数
%% 4. 返回原始错误，支持错误传播
%%
%% 参数说明：
%% - Error: 错误信息，可以是各种格式的错误元组或原子
%%
%% 返回值：
%% - Error: 返回原始错误，便于错误传播和处理
%%
%% Java等价概念：
%% 类似于异常分类器或错误处理器
%% 根据异常类型更新不同的监控指标
%%
%% 错误分类：
%% - connection_error: 连接相关错误
%% - authentication_error: 认证失败错误
%% - timeout_error: 超时错误
%% - query_error: 查询执行错误
%% - network_error: 网络相关错误（TCP错误）
%% - unknown_error: 未知或其他错误
classify_and_count_error(Error) ->
    %% 根据错误的结构和内容进行分类
    %% 使用模式匹配识别不同类型的错误
    %% 在Java中相当于：
    %% ErrorType errorType = switch (error.getClass()) {
    %%     case ConnectionException -> CONNECTION_ERROR;
    %%     case AuthenticationException -> AUTHENTICATION_ERROR;
    %%     case TimeoutException -> TIMEOUT_ERROR;
    %%     case QueryException -> QUERY_ERROR;
    %%     case NetworkException -> NETWORK_ERROR;
    %%     default -> UNKNOWN_ERROR;
    %% };
    ErrorType =
        case Error of
            {connection_error, _} ->
                %% 连接错误：无法建立或维持数据库连接
                connection_error;
            {authentication_failed, _} ->
                %% 认证错误：用户名、密码或权限问题
                authentication_error;
            {timeout, _} ->
                %% 超时错误：操作执行时间超过预设阈值
                timeout_error;
            {query_failed, _} ->
                %% 查询错误：SQL语法错误、约束违反等
                query_error;
            {tcp_error, _} ->
                %% 网络错误：TCP连接、DNS解析等问题
                network_error;
            _ ->
                %% 未知错误：不在预定义分类中的其他错误
                %% 这有助于发现新的错误模式
                unknown_error
        end,
    %% 增加对应错误类型的计数
    %% 这会更新全局的错误统计信息
    inc_error_counter(ErrorType),
    %% 返回原始错误，支持错误的传播和进一步处理
    %% 在Java中相当于：
    %% errorCounter.increment(errorType);
    %% throw error; // 重新抛出异常
    Error.

%% @doc 查询模式配置
%% 这个函数定义了MongoDB连接器支持的查询处理模式
%%
%% 功能说明：
%% 1. 设置查询处理为异步模式，提高并发性能
%% 2. 启用内部缓冲区，支持批量操作优化
%% 3. 简化查询接口，减少复杂性
%%
%% 模式说明：
%% - simple: 简化的查询接口，易于使用和维护
%% - async: 异步处理，不阻塞调用线程
%% - internal_buffer: 内部缓冲区，支持批量操作和性能优化
%%
%% Java等价概念：
%% 类似于Spring的@Async注解或CompletableFuture的异步处理
%% 或者消息队列的异步消费模式
%%
%% 参数说明：
%% - _: 忽略的参数，表示此配置对所有实例都相同
%%
%% 返回值：
%% - simple_async_internal_buffer: EMQX定义的查询模式常量
query_mode(_) ->
    %% 返回异步内部缓冲模式
    %% 这种模式适合高并发的数据库操作场景
    %% 在Java中相当于配置异步处理器：
    %% @Configuration
    %% public class AsyncConfig {
    %%     @Bean
    %%     public Executor taskExecutor() {
    %%         return new ThreadPoolTaskExecutor();
    %%     }
    %% }
    simple_async_internal_buffer.

%% @doc 回调模式配置
%% 这个函数定义了异步操作完成后的回调处理模式
%%
%% 功能说明：
%% 1. 设置回调总是同步执行，确保数据一致性
%% 2. 避免回调中的竞态条件和并发问题
%% 3. 简化错误处理和状态管理
%%
%% 模式说明：
%% - always_sync: 总是同步执行回调，确保顺序性
%% - 与async查询模式配合，实现异步查询+同步回调
%%
%% Java等价概念：
%% 类似于CompletableFuture.thenApply()的同步回调
%% 或者Spring的@Transactional的同步事务处理
%%
%% 返回值：
%% - always_sync: EMQX定义的回调模式常量
callback_mode() ->
    %% 返回总是同步回调模式
    %% 确保回调函数按顺序执行，避免并发问题
    %% 在Java中相当于：
    %% CompletableFuture.supplyAsync(() -> asyncOperation())
    %%     .thenApply(result -> syncCallback(result));  // 同步回调
    always_sync.

%% @doc 资源启动回调
%% 这是EMQX资源框架的启动回调函数，在资源实例创建时被调用
%%
%% 功能说明：
%% 1. 建立MongoDB连接和连接池
%% 2. 初始化连接器状态和配置
%% 3. 设置监控和健康检查
%% 4. 准备异步查询处理环境
%%
%% 参数说明：
%% - InstId: 资源实例ID，用于标识和管理资源实例
%% - Connection: 连接配置映射，包含MongoDB连接参数
%%   格式示例：#{
%%     mongo_type => replica_set,
%%     hosts => [{"localhost", 27017}],
%%     database => "test",
%%     auth => #{username => "user", password => "pass"}
%%   }
%%
%% 返回值：
%% - {ok, State}: 启动成功，返回连接器状态
%% - {error, Reason}: 启动失败，返回错误原因
%%
%% Java等价概念：
%% 类似于Spring的@PostConstruct方法或DataSource的初始化
%% 或者连接池的startup()方法
%%
%% 生命周期：
%% 这个函数在资源生命周期的开始阶段被调用
%% 类似于Bean的初始化阶段
on_start(InstId, Connection) ->
    %% 不设置trap_exit，让EMQX资源管理器处理进程管理
    %% trap_exit是Erlang的进程链接机制，这里交给框架处理
    %% 类似于Java中不手动管理线程生命周期，而是使用框架的线程池
    %% 不设置trap_exit标志，让EMQX框架管理进程生命周期
    %% process_flag(trap_exit, true) 被注释掉，避免与框架冲突
    %% 在Java中相当于不手动管理线程，而是使用框架的线程池管理

    %% 初始化性能监控指标
    %% 创建计数器、直方图等监控指标，用于性能分析和运维监控
    %% 类似于Java中的Micrometer或Prometheus指标初始化
    %%
    %% 在Java中相当于：
    %% @PostConstruct
    %% public void initMetrics() {
    %%     meterRegistry.counter("mongodb.operations.total");
    %%     meterRegistry.timer("mongodb.operations.duration");
    %% }
    init_metrics(InstId),

    %% 初始化错误计数器
    %% 用于跟踪各种类型的错误和异常情况
    %% 类似于Java中的错误统计和监控
    init_error_counters(),

    %% 初始化熔断器（Circuit Breaker）
    %% 从配置中获取熔断器参数，提供故障保护机制
    %% 类似于Netflix Hystrix或Resilience4j的熔断器
    CircuitBreakerConfig = maps:get(circuit_breaker, Connection, #{}),
    FailureThreshold = maps:get(failure_threshold, CircuitBreakerConfig, 5),    % 失败阈值，默认5次
    ResetTimeout = maps:get(reset_timeout, CircuitBreakerConfig, 30000),        % 重置超时，默认30秒

    %% 使用try-catch保护熔断器初始化
    %% 即使熔断器初始化失败，也不影响连接器的基本功能
    try
        %% 初始化熔断器实例
        %% 在Java中相当于：
        %% CircuitBreaker circuitBreaker = CircuitBreaker.ofDefaults("mongodb")
        %%     .toBuilder()
        %%     .failureRateThreshold(failureThreshold)
        %%     .waitDurationInOpenState(Duration.ofMillis(resetTimeout))
        %%     .build();
        init_circuit_breaker(FailureThreshold, ResetTimeout)
    catch
        Class:Error ->
            %% 记录熔断器初始化失败的错误
            %% 但不中断连接器启动过程
            ?SLOG(error, #{
                msg => "failed_to_init_circuit_breaker",
                error_class => Class,
                error => Error
            })
    end,

    %% 获取批处理配置
    %% 批处理是性能优化的重要功能，将多个小操作合并为大操作
    %% 类似于JDBC的batch操作或MyBatis的批量插入
    BatchConfig = maps:get(batch, Connection, #{}),
    BatchEnabled = maps:get(enable, BatchConfig, true),

    %% 根据配置决定是否启用批处理功能
    %% 批处理可以显著提高数据库操作的吞吐量
    case BatchEnabled of
        true ->
            %% 获取批处理参数配置
            %% 这些参数控制批处理的行为和性能特征
            BatchSize = maps:get(size, BatchConfig, ?MAX_BATCH_SIZE),              % 批处理大小
            BatchTimeout = maps:get(timeout, BatchConfig, ?BATCH_TIMEOUT_MS),      % 批处理超时
            MaxQueueSize = maps:get(max_queue_size, BatchConfig, ?MAX_BATCH_QUEUE_SIZE), % 最大队列大小

            %% 将配置参数存储到进程字典中
            %% 这样批处理服务器可以动态访问这些配置
            %% 类似于Java中的ThreadLocal或配置注入
            %%
            %% 在Java中相当于：
            %% @Value("${mongodb.batch.size:1000}")
            %% private int batchSize;
            put(max_batch_size, BatchSize),
            put(batch_timeout_ms, BatchTimeout),
            put(max_batch_queue_size, MaxQueueSize),

            %% 启动批处理服务器进程
            %% 批处理服务器负责收集和执行批量操作
            %% 类似于Java中的批处理线程池或队列处理器
            case start_batch_server() of
                {error, BatchServerReason} ->
                    %% 批处理服务器启动失败，记录错误并返回失败
                    %% 这是严重错误，因为批处理是核心功能
                    ?SLOG(error, #{
                        msg => "failed_to_start_batch_server",
                        reason => BatchServerReason
                    }),
                    {error, {batch_server_start_failed, BatchServerReason}};
                _Pid ->
                    %% 批处理服务器启动成功
                    %% 记录成功日志，继续初始化流程
                    ?SLOG(info, #{msg => "batch_server_started_successfully"}),
                    ok
            end;
        false ->
            %% 批处理功能被禁用，跳过批处理服务器启动
            %% 在这种情况下，所有操作都将是单个操作
            ok
    end,

    % 初始化MongoDB类型 (single, sharded, rs)
    Type = init_mongo_type(Connection),
    % 初始化主机列表 (e.g., ["localhost:27017"])
    Hosts = init_hosts(Connection),

    % 获取拓扑配置 (如连接池大小、超时等)

    % 从Connection map中获取topology子map，默认为空map
    Topology = maps:get(topology, Connection, #{}),
    % 使用预定义的连接池选项作为基础
    TopologyOptions = init_topology_options(maps:to_list(Topology), ?DEFAULT_CONNECTION_POOL_OPTS),

    % 配置SSL选项
    SslOpts = init_ssl_opts(Connection),

    % 初始化工作者选项 (如数据库名、用户名、密码等)
    WorkerOptions = init_worker_options(maps:to_list(Connection), SslOpts),

    % 确保MongoDB客户端启动并连接
    case ensure_client(Type, Hosts, TopologyOptions, WorkerOptions) of
        % Pid是MongoDB连接进程的ID
        {ok, Pid} ->
            ?SLOG(info, #{
                msg => "mongodb_client_started",
                instance_id => InstId,
                mongodb_type => Type,
                hosts => Hosts
            }),

            % 不监控MongoDB进程，让EMQX资源管理器完全处理进程管理
            % MonitorRef = monitor(process, Pid),  % 移除监控

            % 存储MongoDB连接进程ID到进程字典，用于备份
            put(mongodb_connection_pid, Pid),

            % 确保资源ID正确存储在persistent_term中
            try
                persistent_term:put(?PLUGIN_MONGODB_RESOURCE_ID, Pid)
            catch
                C:E:S ->
                    ?SLOG(error, #{
                        msg => "failed_to_store_resource_id",
                        error_class => C,
                        error => E,
                        stack => S
                    })
            end,

            % MongoDB版本检测将在连接成功事件中异步执行
            % 这样可以确保连接完全建立后再进行版本检测
            MongoVersion = <<"unknown">>,  % 初始版本设为未知
            ?SLOG(info, #{
                msg => "mongodb_version_will_be_detected_on_connection_success",
                initial_version => MongoVersion
            }),

            % 读取缓存配置
            CacheConfig = maps:get(cache, Connection, #{}),
            HealthCheckTTL = maps:get(health_check_ttl, CacheConfig, ?HEALTH_CHECK_CACHE_TTL),

            % 初始化状态，包含MongoDB拓扑进程Pid和重连计数器
            % 添加健康检查缓存字段
            {ok, #{
                topology_pid => Pid,
                reconnect_count => 0,
                connection => Connection,
                mongo_version => MongoVersion,
                health_check_cache => #{
                    last_check_time => 0,
                    last_check_result => undefined,
                    ttl => HealthCheckTTL
                }
            }};
        {error, Reason} ->
            % 记录连接失败指标
            inc_metric(InstId, connection_failure, 1),
            {error, Reason}
    end.

% 获取资源状态回调 - 包含重连逻辑
%% @doc 获取资源状态回调 - 最大重连次数处理
%% 这是EMQX资源框架的状态查询回调函数，用于健康检查和状态监控
%%
%% 功能说明：
%% 1. 检查MongoDB连接的健康状态
%% 2. 处理达到最大重连次数的情况
%% 3. 导出Prometheus监控指标
%% 4. 尝试重新创建连接而不是放弃
%%
%% 参数说明：
%% - _InstId: 资源实例ID（此处未使用）
%% - State: 连接器状态映射，包含连接信息和重连计数
%%
%% 返回值：
%% - {StatusAtom, NewState, ExtraInfo}: 状态元组
%%   - StatusAtom: 连接状态（connected/disconnected/connecting）
%%   - NewState: 更新后的状态
%%   - ExtraInfo: 额外信息（如错误原因）
%%
%% Java等价概念：
%% 类似于Spring Boot Actuator的健康检查端点
%% 或者连接池的isValid()方法
%%
%% 守卫条件：
%% 当重连次数达到或超过最大重连次数时触发此分支
on_get_status(
    _InstId,
    #{topology_pid := Pid, reconnect_count := ReconnectCount, connection := Connection} = State
) when ReconnectCount >= ?MAX_RECONNECT_TIMES ->
    %% 检查是否需要导出Prometheus监控指标
    %% 在达到最大重连次数时，导出当前的性能指标用于监控分析
    %% 类似于Java中的监控指标导出或健康检查报告
    case should_export_prometheus(Connection) of
        true ->
            %% 导出指标到Prometheus格式
            %% 包含连接状态、重连次数、错误统计等关键指标
            %% 在Java中相当于：
            %% prometheusRegistry.export();
            _ = export_metrics_prometheus();
        false ->
            %% 不需要导出指标，跳过
            ok
    end,

    %% 达到最大重连次数，尝试重新创建连接而不是简单地放弃
    %% 这是一种更积极的恢复策略，类似于连接池的连接重建
    %% 在Java中相当于：
    %% if (reconnectCount >= maxReconnectAttempts) {
    %%     logger.warn("Max reconnect attempts reached, recreating connection");
    %%     recreateConnection();
    %% }
    ?SLOG(warning, #{
        msg => "mongodb_max_reconnect_attempts_reached_recreating_connection",
        reconnect_count => ReconnectCount
    }),
    % 尝试关闭旧连接
    _ = deallocate_client(Pid),

    % 重新初始化连接参数
    Type = init_mongo_type(Connection),
    Hosts = init_hosts(Connection),
    Topology = maps:get(topology, Connection, #{}),
    TopologyOptions = init_topology_options(maps:to_list(Topology), []),
    SslOpts = init_ssl_opts(Connection),
    WorkerOptions = init_worker_options(maps:to_list(Connection), SslOpts),

    % 尝试重新创建连接
    case ensure_client(Type, Hosts, TopologyOptions, WorkerOptions) of
        {ok, NewPid} ->
            ?SLOG(info, #{msg => "mongodb_connection_recreated_successfully"}),

            % 存储新的MongoDB连接进程ID到进程字典，用于备份
            put(mongodb_connection_pid, NewPid),

            % 确保资源ID正确存储在persistent_term中
            try
                persistent_term:put(?PLUGIN_MONGODB_RESOURCE_ID, NewPid)
            catch
                C:E:S ->
                    ?SLOG(error, #{
                        msg => "failed_to_store_resource_id",
                        error_class => C,
                        error => E,
                        stack => S
                    })
            end,

            % 重置健康检查缓存
            NewHealthCache = #{
                last_check_time => erlang:system_time(millisecond),
                last_check_result => ok
            },
            {?status_connected, State#{
                topology_pid => NewPid,
                reconnect_count => 0,
                health_check_cache => NewHealthCache
            }};
        {error, Error} ->
            ?SLOG(error, #{
                msg => "mongodb_connection_recreation_failed",
                error => Error
            }),
            % 更新健康检查缓存为错误状态
            NewHealthCache = #{
                last_check_time => erlang:system_time(millisecond),
                last_check_result => {error, Error}
            },
            {?status_disconnected,
                State#{
                    reconnect_count => ReconnectCount,
                    health_check_cache => NewHealthCache
                },
                {connection_recreation_failed, Error}}
    end;
%% @doc 获取资源状态回调 - 正常健康检查处理
%% 这个分支处理正常情况下的健康检查，使用缓存优化性能
%%
%% 功能说明：
%% 1. 使用健康检查缓存避免频繁的连接测试
%% 2. 检查缓存的有效性和上次检查结果
%% 3. 根据缓存状态决定是否需要重新检查连接
%% 4. 更新健康检查缓存和连接状态
%%
%% 缓存策略：
%% - 如果缓存有效且上次检查成功，直接返回成功状态
%% - 如果缓存过期或上次检查失败，重新执行健康检查
%% - 缓存TTL默认为?HEALTH_CHECK_CACHE_TTL
%%
%% Java等价概念：
%% 类似于Spring Cache的@Cacheable注解
%% 或者连接池的缓存健康检查机制
on_get_status(
    _InstId,
    #{
        topology_pid := Pid,
        reconnect_count := ReconnectCount,
        connection := Connection,
        health_check_cache := HealthCache
    } = State
) ->
    %% 检查健康检查缓存是否有效
    %% 获取当前时间戳，用于计算缓存是否过期
    Now = erlang:system_time(millisecond),
    LastCheckTime = maps:get(last_check_time, HealthCache, 0),
    LastCheckResult = maps:get(last_check_result, HealthCache, undefined),
    HealthCheckTTL = maps:get(ttl, HealthCache, ?HEALTH_CHECK_CACHE_TTL),

    %% 检查缓存是否有效且上次检查成功
    %% 使用andalso进行短路求值，提高性能
    %% 在Java中相当于：
    %% if ((now - lastCheckTime < healthCheckTTL) && (lastCheckResult == OK)) {
    %%     return cachedResult;
    %% }
    case (Now - LastCheckTime < HealthCheckTTL) andalso (LastCheckResult =:= ok) of
        true ->
            %% 缓存有效且连接正常，直接返回连接正常状态
            %% 避免不必要的连接测试，提高性能
            {?status_connected, State};
        false ->
            % 缓存无效或上次检查失败，执行实际检查

            % 检查MongoDB连接是否正常
            case check_topology_connectivity(Pid) of
                ok ->
                    % 连接成功，重置重连计数器并更新缓存
                    NewHealthCache = #{
                        last_check_time => Now,
                        last_check_result => ok
                    },

                    % 在连接成功时异步检测MongoDB版本 - 避免重复检测
                    CurrentVersion = maps:get(mongo_version, State, <<"unknown">>),
                    VersionDetectionStarted = maps:get(version_detection_started, State, false),
                    case {CurrentVersion, VersionDetectionStarted} of
                        {<<"unknown">>, false} ->
                            % 版本未知且未开始检测，启动异步检测
                            ?SLOG(info, #{
                                msg => "starting_mongodb_version_detection",
                                topology_pid => Pid
                            }),
                            % 使用一次性进程，避免进程监控问题
                            proc_lib:spawn(fun() ->
                                detect_mongodb_version_on_connection_success(Pid)
                            end),
                            % 标记已开始版本检测
                            UpdatedState = State#{version_detection_started => true};
                        _ ->
                            % 版本已知或检测已开始，无需重复检测
                            UpdatedState = State
                    end,

                    {?status_connected, UpdatedState#{
                        reconnect_count => 0,
                        health_check_cache => NewHealthCache
                    }};
                {error, Error} ->
                    % 连接失败，增加重连计数
                    NewCount = ReconnectCount + 1,
                    % 计算指数退避延迟 (Exponential Backoff)
                    % `1 bsl N` 等价于 `2^N`，但限制最大指数为8，避免过长延迟
                    Delay = min(
                        ?RECONNECT_DELAY * (1 bsl min(NewCount - 1, 8)), ?MAX_RECONNECT_DELAY
                    ),
                    ?SLOG(warning, #{
                        msg => "mongodb_connection_failed",
                        error => Error,
                        reconnect_count => NewCount,
                        % 下次重试的延迟
                        next_retry_delay => Delay
                    }),

                    % 更新健康检查缓存
                    NewHealthCache = #{
                        last_check_time => Now,
                        last_check_result => {error, Error}
                    },

                    % 尝试重新连接

                    % 每3次重试尝试一次重新创建连接
                    case NewCount rem 3 =:= 0 of
                        true ->
                            % 尝试关闭旧连接
                            _ = deallocate_client(Pid),

                            % 重新初始化连接参数
                            Type = init_mongo_type(Connection),
                            Hosts = init_hosts(Connection),
                            Topology = maps:get(topology, Connection, #{}),
                            TopologyOptions = init_topology_options(maps:to_list(Topology), []),
                            SslOpts = init_ssl_opts(Connection),
                            WorkerOptions = init_worker_options(maps:to_list(Connection), SslOpts),

                            % 尝试重新创建连接
                            case ensure_client(Type, Hosts, TopologyOptions, WorkerOptions) of
                                {ok, NewPid} ->
                                    ?SLOG(info, #{
                                        msg => "mongodb_connection_recreated_successfully"
                                    }),

                                    % 存储新的MongoDB连接进程ID到进程字典，用于备份
                                    put(mongodb_connection_pid, NewPid),

                                    % 确保资源ID正确存储在persistent_term中
                                    try
                                        persistent_term:put(?PLUGIN_MONGODB_RESOURCE_ID, NewPid)
                                    catch
                                        Cx:Ex:Sx ->
                                            ?SLOG(error, #{
                                                msg => "failed_to_store_resource_id",
                                                error_class => Cx,
                                                error => Ex,
                                                stack => Sx
                                            })
                                    end,

                                    % 更新健康检查缓存为成功状态
                                    RecreateHealthCache = #{
                                        last_check_time => Now,
                                        last_check_result => ok
                                    },
                                    {?status_connected, State#{
                                        topology_pid => NewPid,
                                        reconnect_count => 0,
                                        health_check_cache => RecreateHealthCache
                                    }};
                                {error, RecreateError} ->
                                    ?SLOG(error, #{
                                        msg => "mongodb_connection_recreation_failed",
                                        error => RecreateError
                                    }),
                                    % 更新健康检查缓存为错误状态
                                    RecreateHealthCache = #{
                                        last_check_time => Now,
                                        last_check_result => {error, RecreateError}
                                    },
                                    {?status_disconnected,
                                        State#{
                                            reconnect_count => NewCount,
                                            health_check_cache => RecreateHealthCache
                                        },
                                        {connection_recreation_failed, RecreateError}}
                            end;
                        false ->
                            % 更新状态并返回断开连接状态及错误信息
                            {?status_disconnected,
                                State#{
                                    reconnect_count => NewCount,
                                    health_check_cache => NewHealthCache
                                },
                                Error}
                    end
            end
    end;
% 添加处理没有reconnect_count的情况 (可能是旧状态或初始化不完整)
on_get_status(_InstId, #{topology_pid := Pid} = State) ->
    % 为旧状态创建健康检查缓存字段
    Now = erlang:system_time(millisecond),
    case check_topology_connectivity(Pid) of
        ok ->
            NewHealthCache = #{
                last_check_time => Now,
                last_check_result => ok
            },
            {?status_connected, State#{
                reconnect_count => 0,
                health_check_cache => NewHealthCache
            }};
        {error, Error} ->
            NewHealthCache = #{
                last_check_time => Now,
                last_check_result => {error, Error}
            },
            {?status_disconnected,
                State#{
                    reconnect_count => 1,
                    health_check_cache => NewHealthCache
                },
                Error}
    end.

%% @doc 资源停止回调
%% 这是EMQX资源框架的停止回调函数，在资源实例销毁时被调用
%%
%% 功能说明：
%% 1. 优雅关闭MongoDB连接和连接池
%% 2. 清理资源和释放内存
%% 3. 停止相关的后台进程
%% 4. 确保所有资源都被正确释放
%%
%% 参数说明：
%% - _InstId: 资源实例ID（此处未使用）
%% - State: 连接器状态映射，包含连接信息
%%
%% 返回值：
%% - ok: 总是返回ok，即使关闭过程中出现错误
%%
%% Java等价概念：
%% 类似于Spring的@PreDestroy方法
%% 或者AutoCloseable的close()方法
%%
%% 设计理念：
%% 即使关闭失败也返回ok，避免阻塞EMQX的关闭流程
on_stop(_InstId, #{topology_pid := Pid} = _State) ->
    %% 记录资源停止操作的开始
    %% 用于调试和运维监控，记录拓扑进程ID便于追踪
    ?SLOG(info, #{
        msg => "mongodb_client_on_stop",
        topology_pid => Pid
    }),

    %% 首先停止批处理服务器
    %% 确保没有新的批处理任务被提交，避免资源竞争
    %% 使用_忽略返回值，因为即使停止失败也要继续关闭流程
    %% 在Java中相当于：
    %% executorService.shutdown();
    _ = stop_batch_server(),

    %% 检查连接是否仍然存活
    %% 避免对已经关闭的连接执行关闭操作
    case check_connection_alive(Pid) of
        true ->
            %% 连接存在，使用超时机制尝试优雅关闭MongoDB连接
            %% 使用case表达式处理with_timeout的返回值
            case
                with_timeout(
                    %% 要执行的关闭函数
                    %% deallocate_client/1负责实际的连接清理工作
                    fun() -> deallocate_client(Pid) end,
                    %% 超时时间，防止关闭操作无限等待
                    %% 在Java中相当于：
                    %% connection.close(Duration.ofMillis(STOP_TIMEOUT_MS))
                    ?STOP_TIMEOUT_MS
                )
            of
                %% 成功关闭连接
                %% 返回ok表示关闭操作完成
                {ok, _} ->
                    ok;
                %% 关闭超时或出现错误
                %% 记录警告但不中断关闭流程
                {error, Reason} ->
                    ?SLOG(warning, #{msg => "mongodb_client_disconnect_timeout", reason => Reason})
            end;
        %% 连接已经断开或不存在
        %% 记录信息并跳过关闭操作
        false ->
            ?SLOG(info, #{msg => "mongodb_client_already_disconnected", topology_pid => Pid})
    end,

    %% 总是返回ok，即使关闭失败
    %% 这是EMQX资源框架的要求：不能因为资源关闭失败而阻塞系统关闭
    %% 遵循"尽力而为"的原则，确保系统能够正常关闭
    %% 在Java中相当于：
    %% @PreDestroy
    %% public void cleanup() {
    %%     try {
    %%         // 清理资源
    %%     } catch (Exception e) {
    %%         // 记录错误但不抛出异常
    %%         logger.error("Cleanup failed", e);
    %%     }
    %% }
    ok.

%% @doc 检查连接是否存活（辅助函数）
%% 这个函数检查MongoDB连接进程是否仍然存活
%%
%% 功能说明：
%% 1. 检查Erlang进程是否仍然存活
%% 2. 使用try-catch保护，避免检查过程中的异常
%% 3. 提供简单可靠的连接状态检查
%%
%% 参数说明：
%% - Pid: MongoDB拓扑进程ID
%%
%% 返回值：
%% - true: 进程存活
%% - false: 进程不存在或检查失败
%%
%% Java等价概念：
%% 类似于Thread.isAlive()方法
%% 或者进程状态检查
%%
%% 实现说明：
%% 使用Erlang内置函数is_process_alive/1进行检查
check_connection_alive(Pid) ->
    try
        %% 使用Erlang内置函数检查进程是否存活
        %% is_process_alive/1是Erlang的BIF（Built-In Function）
        %% 类似于Java中的Thread.isAlive()或进程状态检查
        %%
        %% 在Java中相当于：
        %% public boolean isProcessAlive(Process process) {
        %%     return process.isAlive();
        %% }
        is_process_alive(Pid)
    catch
        %% 捕获所有异常，返回false
        %% 任何异常都表示进程检查失败，认为进程不存活
        %% 这是一种保守的错误处理策略
        _:_ -> false
    end.

% 移除handle_info函数，让EMQX资源管理器完全处理进程管理
% 不再处理DOWN或EXIT消息，避免与resource_manager冲突

%% @doc 异步查询处理回调
%% 这是EMQX资源框架的异步查询处理回调函数，处理各种MongoDB操作
%%
%% 功能说明：
%% 1. 处理异步的MongoDB数据库操作请求
%% 2. 支持批量插入、查询、更新、删除等操作
%% 3. 提供高性能的异步处理能力
%% 4. 集成熔断器和错误处理机制
%%
%% 参数说明：
%% - InstId: 资源实例ID，用于标识资源实例
%% - Query: 查询请求，格式为{QueryList, Message}
%%   - QueryList: 查询元数据列表，包含操作类型、集合名等信息
%%   - Message: 要处理的数据内容
%% - WorkerPid: 工作进程ID（通常未使用）
%% - ConnectorState: 连接器状态映射，包含连接信息
%%
%% 返回值：
%% - ok: 操作成功
%% - {error, Reason}: 操作失败，返回错误原因
%%
%% Java等价概念：
%% 类似于Spring的@Async方法或CompletableFuture的异步处理
%% 或者消息队列的异步消费者
% 当规则引擎触发动作，需要通过此资源与MongoDB交互时调用。
% 处理特殊查询类型（非插入操作）
on_query_async(
    InstId,
    {find_sessions_to_restore, Collection},
    _WorkerPid,
    #{topology_pid := Pid} = State
) ->
    % 异步查询应该返回简单状态，不返回查询结果
    case on_query(InstId, {find_sessions_to_restore, Collection}, State) of
        {ok, _Result} -> ok;  % 异步查询成功返回ok
        {error, Reason} -> {error, Reason}
    end;

% 处理find查询（订阅恢复等）
on_query_async(
    InstId,
    {find, Collection, Filter, Options, Skip, Limit},
    _WorkerPid,
    State
) ->
    % 异步查询应该返回简单状态，不返回查询结果
    case on_query(InstId, {find, Collection, Filter, Options, Skip, Limit}, State) of
        {ok, _Result} -> ok;  % 异步查询成功返回ok
        {error, Reason} -> {error, Reason}
    end;

% 处理count查询
on_query_async(
    InstId,
    {count, Collection, Filter},
    _WorkerPid,
    State
) ->
    % 异步查询应该返回简单状态，不返回查询结果
    case on_query(InstId, {count, Collection, Filter}, State) of
        {ok, _Result} -> ok;  % 异步查询成功返回ok
        {error, Reason} -> {error, Reason}
    end;

% 处理distinct查询
on_query_async(
    InstId,
    {distinct, Collection, Field, Filter},
    _WorkerPid,
    State
) ->
    % 异步查询应该返回简单状态，不返回查询结果
    case on_query(InstId, {distinct, Collection, Field, Filter}, State) of
        {ok, _Result} -> ok;  % 异步查询成功返回ok
        {error, Reason} -> {error, Reason}
    end;

% 处理delete_many查询
on_query_async(
    InstId,
    {delete_many, Collection, Filter, Options},
    _WorkerPid,
    State
) ->
    % 异步查询应该返回简单状态，不返回查询结果
    case on_query(InstId, {delete_many, Collection, Filter, Options}, State) of
        {ok, _Result} -> ok;  % 异步查询成功返回ok
        {error, Reason} -> {error, Reason}
    end;

% 处理count_documents查询
on_query_async(
    InstId,
    {count_documents, Collection, Filter},
    _WorkerPid,
    State
) ->
    % 异步查询应该返回简单状态，不返回查询结果
    case on_query(InstId, {count_documents, Collection, Filter}, State) of
        {ok, _Result} -> ok;  % 异步查询成功返回ok
        {error, Reason} -> {error, Reason}
    end;

% 处理update查询
on_query_async(
    InstId,
    {update, Collection, Filter, Update},
    _WorkerPid,
    State
) ->
    % 异步查询应该返回简单状态，不返回查询结果
    case on_query(InstId, {update, Collection, Filter, Update}, State) of
        {ok, _Result} -> ok;  % 异步查询成功返回ok
        {error, Reason} -> {error, Reason}
    end;

% 处理upsert查询
on_query_async(
    InstId,
    {upsert, Collection, Filter, Document},
    _WorkerPid,
    State
) ->
    % 异步查询应该返回简单状态，不返回查询结果
    case on_query(InstId, {upsert, Collection, Filter, Document}, State) of
        {ok, _Result} -> ok;  % 异步查询成功返回ok
        {error, Reason} -> {error, Reason}
    end;

% 处理find_one查询
on_query_async(
    InstId,
    {find_one, Collection, Filter, Options},
    _WorkerPid,
    State
) ->
    % 异步查询应该返回简单状态，不返回查询结果
    case on_query(InstId, {find_one, Collection, Filter, Options}, State) of
        {ok, _Result} -> ok;  % 异步查询成功返回ok
        {error, Reason} -> {error, Reason}
    end;

% 处理delete查询
on_query_async(
    InstId,
    {delete, Collection, Filter},
    _WorkerPid,
    State
) ->
    % 异步查询应该返回简单状态，不返回查询结果
    case on_query(InstId, {delete, Collection, Filter}, State) of
        {ok, _Result} -> ok;  % 异步查询成功返回ok
        {error, Reason} -> {error, Reason}
    end;

on_query_async(
    InstId,
    % Querys 包含目标集合等信息，Message 是要存储的数据
    {Querys, Message},
    % 未使用的工作进程ID
    _WorkerPid,
    % 从状态中提取MongoDB连接进程Pid
    #{topology_pid := Pid} = _ConnectorState
) ->
    % 记录开始时间，用于计算延迟
    StartTime = erlang:system_time(millisecond),

    % 检查断路器状态
    case check_circuit_breaker() of
        {ok, _} ->
            try
                % 记录原始消息类型和内容
                ?SLOG(debug, #{
                    msg => "on_query_async_message_details",
                    message_type => case is_map(Message) of
                        true -> map;
                        false ->
                            case is_list(Message) andalso not is_binary(Message) of
                                true -> list;
                                false ->
                                    case is_binary(Message) of
                                        true -> binary;
                                        false -> other
                                    end
                            end
                    end,
                    message_keys => case is_map(Message) of
                        true -> maps:keys(Message);
                        false -> []
                    end,
                    payload_field => case is_map(Message) of
                        true ->
                            case maps:get(payload, Message, undefined) of
                                undefined -> undefined;
                                Payload ->
                                    case is_binary(Payload) of
                                        true ->
                                            try
                                                case jsx:is_json(Payload) of
                                                    true -> json_binary;
                                                    false -> plain_binary
                                                end
                                            catch
                                                _:_ -> unknown_binary
                                            end;
                                        false -> not_binary
                                    end
                            end;
                        false -> not_a_map
                    end
                }),

                % 处理消息，确保JSON格式正确处理
                ProcessedMessage = process_message_for_mongodb(Message),

                % 批量处理插入操作
                Result =
                    try
                        safe_do_batch_insert(Pid, Querys, ProcessedMessage)
                    catch
                        C0:E0:S0 ->
                            ?SLOG(error, #{
                                msg => "batch_insert_top_level_exception",
                                error_class => C0,
                                error => E0,
                                stack => S0
                            }),
                            {error, {batch_insert_exception, {C0, E0}}}
                    end,

                % 计算延迟并记录指标
                EndTime = erlang:system_time(millisecond),
                Latency = EndTime - StartTime,
                observe_latency(InstId, Latency),

                % 同时记录延迟直方图数据
                _ = record_latency(insert, StartTime),

                % 记录插入计数
                DocCount =
                    case is_list(ProcessedMessage) andalso not is_binary(ProcessedMessage) of
                        true -> length(ProcessedMessage);
                        false -> 1
                    end,
                inc_metric(InstId, insert_count, DocCount),

                % 操作成功，记录断路器成功状态
                case Result of
                    ok ->
                        record_success(),
                        ok;
                    {error, ErrorReason} ->
                        % 记录错误指标
                        inc_metric(InstId, insert_error, 1),
                        record_failure(),
                        classify_and_count_error(ErrorReason),
                        {error, ErrorReason}
                end
            catch
                % 捕获异常
                Error:Reason:Stack ->
                    % 记录错误指标
                    inc_metric(InstId, insert_error, 1),
                    record_failure(),
                    classify_and_count_error({Error, Reason}),

                    ?SLOG(error, #{
                        msg => "emqx_plugin_mongodb_producer on_query_async error",
                        error => Error,
                        instId => InstId,
                        reason => Reason,
                        stack => Stack
                    }),
                    {error, {Error, Reason}}
            end;
        {error, circuit_open} ->
            ?SLOG(warning, #{
                msg => "mongodb_circuit_breaker_open",
                inst_id => InstId
            }),
            {error, circuit_breaker_open}
    end;

% 处理异步会话保存操作
on_query_async(
    InstId,
    {save_session, Collection, ClientId, SessionDoc},
    _WorkerPid,
    #{topology_pid := Pid} = _ConnectorState
) ->
    StartTime = erlang:system_time(millisecond),
    case check_circuit_breaker() of
        {ok, _} ->
            try
                % 使用最基本的MongoDB API操作
                Filter = #{<<"client_id">> => ClientId},
                Command = #{
                    <<"update">> => Collection,
                    <<"updates">> => [
                        #{
                            <<"q">> => Filter,
                            <<"u">> => #{<<"$set">> => SessionDoc},
                            <<"upsert">> => true
                        }
                    ]
                },
                Result =
                    try
                        {ok, _} = emqx_plugin_mongodb_api:command(Pid, Command),
                        ok
                    catch
                        E1:R1:S1 ->
                            ?SLOG(error, #{
                                msg => "mongodb_command_error",
                                error => E1,
                                reason => R1,
                                stacktrace => S1,
                                command => Command
                            }),
                            {error, {E1, R1}}
                    end,

                EndTime = erlang:system_time(millisecond),
                Latency = EndTime - StartTime,
                observe_latency(InstId, Latency),
                _ = record_latency(update, StartTime),
                inc_metric(InstId, update_count, 1),

                case Result of
                    ok ->
                        record_success(),
                        ok;
                    {error, ErrorReason} ->
                        inc_metric(InstId, update_error, 1),
                        record_failure(),
                        classify_and_count_error(ErrorReason),
                        {error, ErrorReason}
                end
            catch
                Error:Reason:Stack ->
                    inc_metric(InstId, update_error, 1),
                    record_failure(),
                    classify_and_count_error({Error, Reason}),

                    ?SLOG(error, #{
                        msg => "emqx_plugin_mongodb_connector save_session error",
                        error => Error,
                        instId => InstId,
                        reason => Reason,
                        stack => Stack
                    }),
                    {error, {Error, Reason}}
            end;
        {error, circuit_open} ->
            ?SLOG(warning, #{
                msg => "mongodb_circuit_breaker_open",
                inst_id => InstId
            }),
            {error, circuit_breaker_open}
    end;

% 处理异步会话状态更新操作
on_query_async(
    InstId,
    {update_session_status, Collection, ClientId, StatusDoc},
    _WorkerPid,
    #{topology_pid := Pid} = _ConnectorState
) ->
    StartTime = erlang:system_time(millisecond),
    case check_circuit_breaker() of
        {ok, _} ->
            try
                % 使用最基本的MongoDB API操作
                Filter = #{<<"client_id">> => ClientId},
                Command = #{
                    <<"update">> => Collection,
                    <<"updates">> => [
                        #{
                            <<"q">> => Filter,
                            <<"u">> => #{<<"$set">> => StatusDoc}
                        }
                    ]
                },
                Result =
                    try
                        {ok, _} = emqx_plugin_mongodb_api:command(Pid, Command),
                        ok
                    catch
                        E1:R1:S1 ->
                            ?SLOG(error, #{
                                msg => "mongodb_command_error",
                                error => E1,
                                reason => R1,
                                stacktrace => S1,
                                command => Command
                            }),
                            {error, {E1, R1}}
                    end,

                EndTime = erlang:system_time(millisecond),
                Latency = EndTime - StartTime,
                observe_latency(InstId, Latency),
                _ = record_latency(update, StartTime),
                inc_metric(InstId, update_count, 1),

                case Result of
                    ok ->
                        record_success(),
                        ok;
                    {error, ErrorReason} ->
                        inc_metric(InstId, update_error, 1),
                        record_failure(),
                        classify_and_count_error(ErrorReason),
                        {error, ErrorReason}
                end
            catch
                Error:Reason:Stack ->
                    inc_metric(InstId, update_error, 1),
                    record_failure(),
                    classify_and_count_error({Error, Reason}),

                    ?SLOG(error, #{
                        msg => "emqx_plugin_mongodb_connector update_session_status error",
                        error => Error,
                        instId => InstId,
                        reason => Reason,
                        stack => Stack
                    }),
                    {error, {Error, Reason}}
            end;
        {error, circuit_open} ->
            ?SLOG(warning, #{
                msg => "mongodb_circuit_breaker_open",
                inst_id => InstId
            }),
            {error, circuit_breaker_open}
    end;

% 处理消息保存操作
on_query_async(
    InstId,
    {save_message, Collection, MessageId, MessageDoc},
    _WorkerPid,
    #{topology_pid := Pid} = _ConnectorState
) ->
    StartTime = erlang:system_time(millisecond),
    case check_circuit_breaker() of
        {ok, _} ->
            try
                % 记录调试信息
                ?SLOG(debug, #{
                    msg => "save_message_operation_called",
                    collection => Collection,
                    message_id => MessageId
                }),

                % 尝试插入消息文档
                Result =
                    try
                        % 使用insert而不是update，因为消息通常是新创建的
                        catch emqx_plugin_mongodb_api:insert(Pid, Collection, [MessageDoc]),
                        ok
                    catch
                        E1:R1:S1 ->
                            ?SLOG(error, #{
                                msg => "mongodb_save_message_error",
                                error => E1,
                                reason => R1,
                                stacktrace => S1,
                                collection => Collection,
                                message_id => MessageId
                            }),
                            {error, {E1, R1}}
                    end,

                % 计算延迟并记录指标
                EndTime = erlang:system_time(millisecond),
                Latency = EndTime - StartTime,
                observe_latency(InstId, Latency),
                _ = record_latency(insert, StartTime),
                inc_metric(InstId, insert_count, 1),

                case Result of
                    ok ->
                        record_success(),
                        ok;
                    {error, ErrorReason} ->
                        inc_metric(InstId, insert_error, 1),
                        record_failure(),
                        classify_and_count_error(ErrorReason),
                        {error, ErrorReason}
                end
            catch
                Error:Reason:Stack ->
                    inc_metric(InstId, insert_error, 1),
                    record_failure(),
                    classify_and_count_error({Error, Reason}),

                    ?SLOG(error, #{
                        msg => "emqx_plugin_mongodb_connector save_message error",
                        error => Error,
                        instId => InstId,
                        reason => Reason,
                        stack => Stack
                    }),
                    {error, {Error, Reason}}
            end;
        {error, circuit_open} ->
            ?SLOG(warning, #{
                msg => "mongodb_circuit_breaker_open",
                inst_id => InstId
            }),
            {error, circuit_breaker_open}
    end;

% 处理消息状态更新操作
on_query_async(
    InstId,
    {update_message_status, Collection, MessageId, StatusDoc},
    _WorkerPid,
    #{topology_pid := Pid} = _ConnectorState
) ->
    StartTime = erlang:system_time(millisecond),
    case check_circuit_breaker() of
        {ok, _} ->
            try
                % 记录调试信息
                ?SLOG(debug, #{
                    msg => "update_message_status_operation_called",
                    collection => Collection,
                    message_id => MessageId
                }),

                % 使用最基本的MongoDB API操作
                Filter = #{<<"message_id">> => MessageId},
                Command = #{
                    <<"update">> => Collection,
                    <<"updates">> => [
                        #{
                            <<"q">> => Filter,
                            <<"u">> => #{<<"$set">> => StatusDoc}
                        }
                    ]
                },

                Result =
                    try
                        {ok, _} = emqx_plugin_mongodb_api:command(Pid, Command),
                        ok
                    catch
                        E1:R1:S1 ->
                            ?SLOG(error, #{
                                msg => "mongodb_update_message_status_error",
                                error => E1,
                                reason => R1,
                                stacktrace => S1,
                                command => Command,
                                message_id => MessageId
                            }),
                            {error, {E1, R1}}
                    end,

                % 计算延迟并记录指标
                EndTime = erlang:system_time(millisecond),
                Latency = EndTime - StartTime,
                observe_latency(InstId, Latency),
                _ = record_latency(update, StartTime),
                inc_metric(InstId, update_count, 1),

                case Result of
                    ok ->
                        record_success(),
                        ok;
                    {error, ErrorReason} ->
                        inc_metric(InstId, update_error, 1),
                        record_failure(),
                        classify_and_count_error(ErrorReason),
                        {error, ErrorReason}
                end
            catch
                Error:Reason:Stack ->
                    inc_metric(InstId, update_error, 1),
                    record_failure(),
                    classify_and_count_error({Error, Reason}),

                    ?SLOG(error, #{
                        msg => "emqx_plugin_mongodb_connector update_message_status error",
                        error => Error,
                        instId => InstId,
                        reason => Reason,
                        stack => Stack
                    }),
                    {error, {Error, Reason}}
            end;
        {error, circuit_open} ->
            ?SLOG(warning, #{
                msg => "mongodb_circuit_breaker_open",
                inst_id => InstId
            }),
            {error, circuit_breaker_open}
    end;

% 处理消息删除操作（4个参数版本 - 包含ClientId）
on_query_async(
    InstId,
    {delete_message, Collection, ClientId, MessageId},
    _WorkerPid,
    #{topology_pid := Pid} = _ConnectorState
) ->
    StartTime = erlang:system_time(millisecond),
    case check_circuit_breaker() of
        {ok, _} ->
            try
                % 记录调试信息
                ?SLOG(debug, #{
                    msg => "delete_message_operation_called_with_client_id",
                    collection => Collection,
                    client_id => ClientId,
                    message_id => MessageId
                }),

                % 使用最基本的MongoDB API操作
                try
                    % 构建删除过滤器，包含客户端ID和消息ID
                    Filter = #{<<"client_id">> => ClientId, <<"message_id">> => MessageId},
                    {ok, _} = safe_do_delete(Pid, Collection, Filter),
                    ok
                catch
                    E1:R1:S1 ->
                        ?SLOG(error, #{
                            msg => "mongodb_delete_message_with_client_id_error",
                            error => E1,
                            reason => R1,
                            stacktrace => S1,
                            filter => #{<<"client_id">> => ClientId, <<"message_id">> => MessageId},
                            client_id => ClientId,
                            message_id => MessageId
                        }),
                        {error, {delete_failed, {E1, R1}}}
                end
            catch
                Error:Reason:Stack ->
                    EndTime = erlang:system_time(millisecond),
                    inc_metric(InstId, delete_error, 1),
                    record_failure(),
                    classify_and_count_error({Error, Reason}),

                    ?SLOG(error, #{
                        msg => "emqx_plugin_mongodb_connector delete_message_with_client_id error",
                        error => Error,
                        instId => InstId,
                        reason => Reason,
                        stack => Stack,
                        client_id => ClientId,
                        message_id => MessageId,
                        processing_time => EndTime - StartTime
                    }),
                    {error, {Error, Reason}}
            end;
        {error, circuit_breaker_open} ->
            ?SLOG(warning, #{
                msg => "delete_message_rejected_circuit_breaker_open",
                client_id => ClientId,
                message_id => MessageId
            }),
            {error, circuit_breaker_open}
    end;

% 处理消息删除操作（3个参数版本 - 向后兼容）
on_query_async(
    InstId,
    {delete_message, Collection, MessageId},
    _WorkerPid,
    #{topology_pid := Pid} = _ConnectorState
) ->
    StartTime = erlang:system_time(millisecond),
    case check_circuit_breaker() of
        {ok, _} ->
            try
                % 记录调试信息
                ?SLOG(debug, #{
                    msg => "delete_message_operation_called",
                    collection => Collection,
                    message_id => MessageId
                }),

                % 使用最基本的MongoDB API操作
                Filter = #{<<"message_id">> => MessageId},

                Result =
                    try
                        % 执行删除操作
                        {ok, _} = safe_do_delete(Pid, Collection, Filter),
                        ok
                    catch
                        E1:R1:S1 ->
                            ?SLOG(error, #{
                                msg => "mongodb_delete_message_error",
                                error => E1,
                                reason => R1,
                                stacktrace => S1,
                                filter => Filter,
                                message_id => MessageId
                            }),
                            {error, {E1, R1}}
                    end,

                % 计算延迟并记录指标
                EndTime = erlang:system_time(millisecond),
                Latency = EndTime - StartTime,
                observe_latency(InstId, Latency),
                _ = record_latency(delete, StartTime),
                inc_metric(InstId, delete_count, 1),

                case Result of
                    ok ->
                        record_success(),
                        ok;
                    {error, ErrorReason} ->
                        inc_metric(InstId, delete_error, 1),
                        record_failure(),
                        classify_and_count_error(ErrorReason),
                        {error, ErrorReason}
                end
            catch
                Error:Reason:Stack ->
                    inc_metric(InstId, delete_error, 1),
                    record_failure(),
                    classify_and_count_error({Error, Reason}),

                    ?SLOG(error, #{
                        msg => "emqx_plugin_mongodb_connector delete_message error",
                        error => Error,
                        instId => InstId,
                        reason => Reason,
                        stack => Stack
                    }),
                    {error, {Error, Reason}}
            end;
        {error, circuit_open} ->
            ?SLOG(warning, #{
                msg => "mongodb_circuit_breaker_open",
                inst_id => InstId
            }),
            {error, circuit_breaker_open}
    end;

% 处理过期消息删除操作
on_query_async(
    InstId,
    {delete_expired_messages, Collection, Now, BatchSize},
    _WorkerPid,
    #{topology_pid := Pid} = _ConnectorState
) ->
    StartTime = erlang:system_time(millisecond),
    case check_circuit_breaker() of
        {ok, _} ->
            try
                Filter = #{
                    <<"expiry_time">> => #{
                        <<"$lt">> => Now,
                        <<"$ne">> => null
                    }
                },
                % MongoDB删除操作不支持limit来限制删除数量，删除所有匹配的文档
                Options = #{},

                % 直接执行删除并获取结果
                DeleteResult =
                    (catch safe_do_delete_many(Pid, Collection, Filter, Options)),

                % 计算延迟并记录指标
                EndTime = erlang:system_time(millisecond),
                Latency = EndTime - StartTime,
                observe_latency(InstId, Latency),
                _ = record_latency(delete, StartTime),

                % 处理结果
                case DeleteResult of
                    {ok, DeletedCount} when is_integer(DeletedCount) ->
                        record_success(),
                        inc_metric(InstId, delete_count, DeletedCount),
                        {ok, {DeletedCount}};
                    {'EXIT', {ErrorTerm, _}} ->
                        inc_metric(InstId, delete_error, 1),
                        record_failure(),
                        classify_and_count_error(ErrorTerm),
                        {error, ErrorTerm};
                    {error, _} = ErrorTuple ->
                        inc_metric(InstId, delete_error, 1),
                        record_failure(),
                        classify_and_count_error(ErrorTuple),
                        ErrorTuple;
                    OtherError ->
                        inc_metric(InstId, delete_error, 1),
                        record_failure(),
                        classify_and_count_error(OtherError),
                        {error, {unexpected_result, OtherError}}
                end
            catch
                Error:Reason:Stack ->
                    inc_metric(InstId, delete_error, 1),
                    record_failure(),
                    classify_and_count_error({Error, Reason}),

                    ?SLOG(error, #{
                        msg => "emqx_plugin_mongodb_connector delete_expired_messages error",
                        error => Error,
                        instId => InstId,
                        reason => Reason,
                        stack => Stack
                    }),
                    {error, {Error, Reason}}
            end;
        {error, circuit_open} ->
            ?SLOG(warning, #{
                msg => "mongodb_circuit_breaker_open",
                inst_id => InstId
            }),
            {error, circuit_breaker_open}
    end;

% 处理异步订阅保存操作
on_query_async(
    InstId,
    {save_subscription, Collection, ClientId, Topic, SubDoc},
    _WorkerPid,
    #{topology_pid := Pid} = _ConnectorState
) ->
    StartTime = erlang:system_time(millisecond),
    case check_circuit_breaker() of
        {ok, _} ->
            try
                % 使用最基本的MongoDB API操作
                Filter = #{
                    <<"client_id">> => ClientId,
                    <<"topic">> => Topic
                },
                Command = #{
                    <<"update">> => Collection,
                    <<"updates">> => [
                        #{
                            <<"q">> => Filter,
                            <<"u">> => #{<<"$set">> => SubDoc},
                            <<"upsert">> => true
                        }
                    ]
                },
                Result =
                    try
                        {ok, _} = emqx_plugin_mongodb_api:command(Pid, Command),
                        ok
                    catch
                        E1:R1:S1 ->
                            ?SLOG(error, #{
                                msg => "mongodb_command_error",
                                error => E1,
                                reason => R1,
                                stacktrace => S1,
                                command => Command
                            }),
                            {error, {E1, R1}}
                    end,

                EndTime = erlang:system_time(millisecond),
                Latency = EndTime - StartTime,
                observe_latency(InstId, Latency),
                _ = record_latency(update, StartTime),
                inc_metric(InstId, update_count, 1),

                case Result of
                    ok ->
                        record_success(),
                        ok;
                    {error, ErrorReason} ->
                        inc_metric(InstId, update_error, 1),
                        record_failure(),
                        classify_and_count_error(ErrorReason),
                        {error, ErrorReason}
                end
            catch
                Error:Reason:Stack ->
                    inc_metric(InstId, update_error, 1),
                    record_failure(),
                    classify_and_count_error({Error, Reason}),

                    ?SLOG(error, #{
                        msg => "emqx_plugin_mongodb_connector save_subscription error",
                        error => Error,
                        instId => InstId,
                        reason => Reason,
                        stack => Stack
                    }),
                    {error, {Error, Reason}}
            end;
        {error, circuit_open} ->
            ?SLOG(warning, #{
                msg => "mongodb_circuit_breaker_open",
                inst_id => InstId
            }),
            {error, circuit_breaker_open}
    end;

% 处理异步订阅删除操作
on_query_async(
    InstId,
    {delete_subscription, Collection, ClientId, Topic},
    _WorkerPid,
    #{topology_pid := Pid} = _ConnectorState
) ->
    StartTime = erlang:system_time(millisecond),
    case check_circuit_breaker() of
        {ok, _} ->
            try
                % 使用最基本的MongoDB API操作
                Filter = #{
                    <<"client_id">> => ClientId,
                    <<"topic">> => Topic
                },
                Command = #{
                    <<"delete">> => Collection,
                    <<"deletes">> => [
                        #{
                            <<"q">> => Filter,
                            <<"limit">> => 0
                        }
                    ]
                },
                Result =
                    try
                        {ok, _} = emqx_plugin_mongodb_api:command(Pid, Command),
                        ok
                    catch
                        E1:R1:S1 ->
                            ?SLOG(error, #{
                                msg => "mongodb_command_error",
                                error => E1,
                                reason => R1,
                                stacktrace => S1,
                                command => Command
                            }),
                            {error, {E1, R1}}
                    end,

                EndTime = erlang:system_time(millisecond),
                Latency = EndTime - StartTime,
                observe_latency(InstId, Latency),
                _ = record_latency(delete, StartTime),
                inc_metric(InstId, delete_count, 1),

                case Result of
                    ok ->
                        record_success(),
                        ok;
                    {error, ErrorReason} ->
                        inc_metric(InstId, delete_error, 1),
                        record_failure(),
                        classify_and_count_error(ErrorReason),
                        {error, ErrorReason}
                end
            catch
                Error:Reason:Stack ->
                    inc_metric(InstId, delete_error, 1),
                    record_failure(),
                    classify_and_count_error({Error, Reason}),

                    ?SLOG(error, #{
                        msg => "emqx_plugin_mongodb_connector delete_subscription error",
                        error => Error,
                        instId => InstId,
                        reason => Reason,
                        stack => Stack
                    }),
                    {error, {Error, Reason}}
            end;
        {error, circuit_open} ->
            ?SLOG(warning, #{
                msg => "mongodb_circuit_breaker_open",
                inst_id => InstId
            }),
            {error, circuit_breaker_open}
    end;

% 处理异步查询订阅操作
on_query_async(
    InstId,
    {find_subscriptions, Collection, ClientId},
    _WorkerPid,
    #{topology_pid := Pid} = _ConnectorState
) ->
    StartTime = erlang:system_time(millisecond),
    case check_circuit_breaker() of
        {ok, _} ->
            try
                % 使用最基本的MongoDB API操作
                Filter = #{<<"client_id">> => ClientId},
                Command = #{
                    <<"find">> => Collection,
                    <<"filter">> => Filter
                },
                Result =
                    try
                        {ok, #{<<"cursor">> := #{<<"firstBatch">> := DocsResult}}} = emqx_plugin_mongodb_api:command(Pid, Command),
                        {ok, DocsResult}
                    catch
                        E1:R1:S1 ->
                            ?SLOG(error, #{
                                msg => "mongodb_command_error",
                                error => E1,
                                reason => R1,
                                stacktrace => S1,
                                command => Command
                            }),
                            {error, {E1, R1}}
                    end,

                EndTime = erlang:system_time(millisecond),
                Latency = EndTime - StartTime,
                observe_latency(InstId, Latency),
                _ = record_latency(find, StartTime),
                inc_metric(InstId, find_count, 1),

                case Result of
                    {ok, Docs} ->
                        record_success(),
                        {ok, Docs};
                    {error, ErrorReason} ->
                        inc_metric(InstId, find_error, 1),
                        record_failure(),
                        classify_and_count_error(ErrorReason),
                        {error, ErrorReason}
                end
            catch
                Error:Reason:Stack ->
                    inc_metric(InstId, find_error, 1),
                    record_failure(),
                    classify_and_count_error({Error, Reason}),

                    ?SLOG(error, #{
                        msg => "emqx_plugin_mongodb_connector find_subscriptions error",
                        error => Error,
                        instId => InstId,
                        reason => Reason,
                        stack => Stack
                    }),
                    {error, {Error, Reason}}
            end;
        {error, circuit_open} ->
            ?SLOG(warning, #{
                msg => "mongodb_circuit_breaker_open",
                inst_id => InstId
            }),
            {error, circuit_breaker_open}
    end;

% 处理异步删除过期会话操作
on_query_async(
    InstId,
    {delete_expired_sessions, Collection, ExpiryTime, BatchSize},
    _WorkerPid,
    #{topology_pid := Pid} = _ConnectorState
) ->
    StartTime = erlang:system_time(millisecond),
    case check_circuit_breaker() of
        {ok, _} ->
            try
                % 使用最基本的MongoDB API操作
                Filter = #{
                    <<"expiry_time">> => #{
                        <<"$lt">> => ExpiryTime,
                        <<"$ne">> => null
                    }
                },
                Command = #{
                    <<"delete">> => Collection,
                    <<"deletes">> => [
                        #{
                            <<"q">> => Filter,
                            <<"limit">> => 0  % 0 = delete all matching documents
                        }
                    ]
                },
                Result =
                    try
                        {ok, #{<<"n">> := DeletedCountResult}} = emqx_plugin_mongodb_api:command(Pid, Command),
                        {ok, DeletedCountResult}
                    catch
                        E1:R1:S1 ->
                            ?SLOG(error, #{
                                msg => "mongodb_command_error",
                                error => E1,
                                reason => R1,
                                stacktrace => S1,
                                command => Command
                            }),
                            {error, {E1, R1}}
                    end,

                EndTime = erlang:system_time(millisecond),
                Latency = EndTime - StartTime,
                observe_latency(InstId, Latency),
                _ = record_latency(delete, StartTime),
                inc_metric(InstId, delete_count, 1),

                case Result of
                    {ok, DeletedCount} ->
                        record_success(),
                        {ok, DeletedCount};
                    {error, ErrorReason} ->
                        inc_metric(InstId, delete_error, 1),
                        record_failure(),
                        classify_and_count_error(ErrorReason),
                        {error, ErrorReason}
                end
            catch
                Error:Reason:Stack ->
                    inc_metric(InstId, delete_error, 1),
                    record_failure(),
                    classify_and_count_error({Error, Reason}),

                    ?SLOG(error, #{
                        msg => "emqx_plugin_mongodb_connector delete_expired_sessions error",
                        error => Error,
                        instId => InstId,
                        reason => Reason,
                        stack => Stack
                    }),
                    {error, {Error, Reason}}
            end;
        {error, circuit_open} ->
            ?SLOG(warning, #{
                msg => "mongodb_circuit_breaker_open",
                inst_id => InstId
            }),
            {error, circuit_breaker_open}
    end;

% 处理创建集合操作
on_query_async(
    InstId,
    {ensure_collection, Collection},
    _WorkerPid,
    #{topology_pid := Pid} = _ConnectorState
) ->
    StartTime = erlang:system_time(millisecond),
    case check_circuit_breaker() of
        {ok, _} ->
            try
                % 检查集合是否存在，使用基本命令
                ListCommand = #{
                    <<"listCollections">> => 1,
                    <<"filter">> => #{<<"name">> => Collection}
                },
                ListResult =
                    try
                        {ok, Result} = emqx_plugin_mongodb_api:command(Pid, ListCommand),
                        {ok, Result}
                    catch
                        E1:R1:S1 ->
                            ?SLOG(error, #{
                                msg => "mongodb_command_error",
                                error => E1,
                                reason => R1,
                                stacktrace => S1,
                                command => ListCommand
                            }),
                            {error, {E1, R1}}
                    end,

                ExistResult =
                    case ListResult of
                        {ok, #{<<"cursor">> := #{<<"firstBatch">> := Batch}}} when length(Batch) > 0 ->
                            % 集合已存在
                            {ok, exists};
                        {ok, _} ->
                            % 集合不存在，创建它
                            CreateCmd = #{<<"create">> => Collection},
                            try
                                {ok, _} = emqx_plugin_mongodb_api:command(Pid, CreateCmd),
                                {ok, created}
                            catch
                                E2:R2:S2 ->
                                    ?SLOG(error, #{
                                        msg => "mongodb_command_error",
                                        error => E2,
                                        reason => R2,
                                        stacktrace => S2,
                                        command => CreateCmd
                                    }),
                                    {error, {E2, R2}}
                            end;
                        {error, CmdError} -> {error, CmdError}
                    end,

                EndTime = erlang:system_time(millisecond),
                Latency = EndTime - StartTime,
                observe_latency(InstId, Latency),
                _ = record_latency(command, StartTime),
                inc_metric(InstId, command_count, 1),

                case ExistResult of
                    {ok, _} ->
                        record_success(),
                        ok;
                    {error, ErrorReason} ->
                        inc_metric(InstId, command_error, 1),
                        record_failure(),
                        classify_and_count_error(ErrorReason),
                        {error, ErrorReason}
                end
            catch
                Error:Reason:Stack ->
                    inc_metric(InstId, command_error, 1),
                    record_failure(),
                    classify_and_count_error({Error, Reason}),

                    ?SLOG(error, #{
                        msg => "emqx_plugin_mongodb_connector ensure_collection error",
                        error => Error,
                        instId => InstId,
                        reason => Reason,
                        stack => Stack
                    }),
                    {error, {Error, Reason}}
            end;
        {error, circuit_open} ->
            ?SLOG(warning, #{
                msg => "mongodb_circuit_breaker_open",
                inst_id => InstId
            }),
            {error, circuit_breaker_open}
    end;

% 处理创建索引操作
on_query_async(
    InstId,
    {create_index, Collection, Index, Options},
    _WorkerPid,
    #{topology_pid := Pid} = _ConnectorState
) ->
    StartTime = erlang:system_time(millisecond),
    case check_circuit_breaker() of
        {ok, _} ->
            try
                % MongoDB创建索引命令
                Command = #{
                    <<"createIndexes">> => Collection,
                    <<"indexes">> => [
                        maps:merge(
                            #{
                                <<"key">> => Index,
                                <<"name">> => generate_index_name(Index)
                            },
                            Options
                        )
                    ]
                },
                Result =
                    try
                        case emqx_plugin_mongodb_api:command(Pid, Command) of
                            {ok, CmdResult} ->
                                {ok, CmdResult};
                            {error, Reason} ->
                                {error, Reason};
                            Other ->
                                ?SLOG(warning, #{
                                    msg => "unexpected_mongodb_command_result",
                                    result => Other,
                                    command => Command
                                }),
                                {error, {unexpected_result, Other}}
                        end
                    catch
                        E1:R1:S1 ->
                            ?SLOG(error, #{
                                msg => "mongodb_command_error",
                                error => E1,
                                reason => R1,
                                stacktrace => S1,
                                command => Command
                            }),
                            {error, {E1, R1}}
                    end,

                EndTime = erlang:system_time(millisecond),
                Latency = EndTime - StartTime,
                observe_latency(InstId, Latency),
                _ = record_latency(command, StartTime),
                inc_metric(InstId, command_count, 1),

                % 调试日志已移除

                case Result of
                    {ok, {ok, #{<<"ok">> := 1.0} = SuccessDoc}} ->
                        % 处理双重包装的成功响应
                        handle_mongodb_success(SuccessDoc, Collection),
                        record_success(),
                        ok;
                    {ok, #{<<"ok">> := 1.0} = SuccessDoc} ->
                        % 处理单层包装的成功响应
                        handle_mongodb_success(SuccessDoc, Collection),
                        record_success(),
                        ok;
                    {ok, #{<<"ok">> := 0.0} = ErrorDoc} ->
                        % MongoDB返回了错误文档
                        ErrorCode = maps:get(<<"code">>, ErrorDoc, undefined),
                        ErrorMsg = maps:get(<<"errmsg">>, ErrorDoc, <<"unknown_error">>),
                        CodeName = maps:get(<<"codeName">>, ErrorDoc, undefined),

                        DetailedError = #{
                            code => ErrorCode,
                            message => ErrorMsg,
                            code_name => CodeName,
                            full_response => ErrorDoc
                        },

                        ?SLOG(warning, #{
                            msg => "mongodb_command_returned_error",
                            error_code => ErrorCode,
                            error_message => ErrorMsg,
                            code_name => CodeName,
                            command => Command
                        }),

                        inc_metric(InstId, command_error, 1),
                        record_failure(),
                        classify_and_count_error(DetailedError),
                        {error, DetailedError};
                    {ok, #{<<"ok">> := OkValue} = UnexpectedResult} when OkValue =/= 1.0 ->
                        % MongoDB返回了错误（ok != 1.0）
                        ?SLOG(warning, #{
                            msg => "mongodb_command_failed",
                            result => UnexpectedResult,
                            command => Command
                        }),
                        inc_metric(InstId, command_error, 1),
                        record_failure(),
                        classify_and_count_error({mongodb_command_failed, UnexpectedResult}),
                        {error, {mongodb_command_failed, UnexpectedResult}};
                    {ok, UnexpectedResult} ->
                        % MongoDB返回了意外的结果格式（没有ok字段或其他格式）
                        ?SLOG(warning, #{
                            msg => "mongodb_command_unexpected_result",
                            result => UnexpectedResult,
                            command => Command
                        }),
                        inc_metric(InstId, command_error, 1),
                        record_failure(),
                        classify_and_count_error({unexpected_result, UnexpectedResult}),
                        {error, {unexpected_result, UnexpectedResult}};
                    {error, ErrorReason} ->
                        inc_metric(InstId, command_error, 1),
                        record_failure(),
                        classify_and_count_error(ErrorReason),
                        {error, ErrorReason}
                end
            catch
                Error:CatchReason:Stack ->
                    inc_metric(InstId, command_error, 1),
                    record_failure(),
                    classify_and_count_error({Error, CatchReason}),

                    ?SLOG(error, #{
                        msg => "emqx_plugin_mongodb_connector create_index error",
                        error => Error,
                        instId => InstId,
                        reason => CatchReason,
                        stack => Stack
                    }),
                    {error, {Error, CatchReason}}
            end;
        {error, circuit_open} ->
            ?SLOG(warning, #{
                msg => "mongodb_circuit_breaker_open",
                inst_id => InstId
            }),
            {error, circuit_breaker_open}
    end;

% 处理创建索引操作 (3参数版本)
on_query_async(
    InstId,
    {create_index, Collection, IndexSpec},
    WorkerPid,
    #{topology_pid := Pid} = ConnectorState
) ->
    % 将3参数版本转换为4参数版本
    % IndexSpec 应该是包含 key 和其他选项的 map
    IndexKey = maps:get(<<"key">>, IndexSpec, #{}),
    IndexOptions = maps:remove(<<"key">>, IndexSpec),

    % 调用4参数版本
    on_query_async(InstId, {create_index, Collection, IndexKey, IndexOptions}, WorkerPid, ConnectorState);

% 查询所有订阅
on_query_async(
    InstId,
    {find_all_subscriptions, Collection},
    _WorkerPid,
    #{topology_pid := Pid} = _ConnectorState
) ->
    StartTime = erlang:system_time(millisecond),
    case check_circuit_breaker() of
        {ok, _} ->
            try
                % 构建查询条件
                Query = #{},

                % 执行查询
                Result = with_timeout(?DEFAULT_TIMEOUT_MS, fun() ->
                    emqx_plugin_mongodb_api:smart_find(Pid, Collection, Query, #{})
                end),

                EndTime = erlang:system_time(millisecond),
                Latency = EndTime - StartTime,
                observe_latency(InstId, Latency),
                _ = record_latency(find, StartTime),
                inc_metric(InstId, find_count, 1),

                case Result of
                    {ok, Docs} ->
                        record_success(),
                        {ok, Docs};
                    {error, ErrorReason} ->
                        inc_metric(InstId, find_error, 1),
                        record_failure(),
                        classify_and_count_error(ErrorReason),
                        {error, ErrorReason}
                end
            catch
                Error:Reason:Stack ->
                    inc_metric(InstId, find_error, 1),
                    record_failure(),
                    classify_and_count_error({Error, Reason}),

                    ?SLOG(error, #{
                        msg => "emqx_plugin_mongodb_connector find_all_subscriptions error",
                        error => Error,
                        instId => InstId,
                        reason => Reason,
                        stack => Stack
                    }),
                    {error, {Error, Reason}}
            end;
        {error, circuit_open} ->
            ?SLOG(warning, #{
                msg => "mongodb_circuit_breaker_open",
                inst_id => InstId
            }),
            {error, circuit_breaker_open}
    end;

% 删除过期的订阅
on_query_async(
    InstId,
    {delete_expired_subscriptions, Collection, Now, BatchSize},
    _WorkerPid,
    #{topology_pid := Pid} = _ConnectorState
) ->
    StartTime = erlang:system_time(millisecond),
    case check_circuit_breaker() of
        {ok, _} ->
            try
                Filter = #{
                    <<"expiry_time">> => #{
                        <<"$lt">> => Now,
                        <<"$ne">> => null
                    }
                },
                % MongoDB删除操作不支持limit来限制删除数量，删除所有匹配的文档
                Options = #{},

                Result =
                    try
                        {ok, DeletedCount} = safe_do_delete_many(Pid, Collection, Filter, Options),
                        {ok, {DeletedCount}}
                    catch
                        E1:R1:S1 ->
                            ?SLOG(error, #{
                                msg => "mongodb_delete_many_error",
                                error => E1,
                                reason => R1,
                                stacktrace => S1,
                                collection => Collection,
                                filter => Filter
                            }),
                            {error, {E1, R1}}
                    end,

                EndTime = erlang:system_time(millisecond),
                Latency = EndTime - StartTime,
                observe_latency(InstId, Latency),
                _ = record_latency(delete, StartTime),
                inc_metric(InstId, delete_count, 1),

                case Result of
                    {ok, _} = Ok ->
                        record_success(),
                        Ok;
                    {error, ErrorReason} ->
                        inc_metric(InstId, delete_error, 1),
                        record_failure(),
                        classify_and_count_error(ErrorReason),
                        {error, ErrorReason}
                end
            catch
                Error:Reason:Stack ->
                    inc_metric(InstId, delete_error, 1),
                    record_failure(),
                    classify_and_count_error({Error, Reason}),

                    ?SLOG(error, #{
                        msg => "emqx_plugin_mongodb_connector delete_expired_subscriptions error",
                        error => Error,
                        instId => InstId,
                        reason => Reason,
                        stack => Stack
                    }),
                    {error, {Error, Reason}}
            end;
        {error, circuit_open} ->
            ?SLOG(warning, #{
                msg => "mongodb_circuit_breaker_open",
                inst_id => InstId
            }),
            {error, circuit_breaker_open}
    end;

% 重复的save_message函数已删除，避免冲突

% 处理消息状态更新操作
on_query_async(
    InstId,
    {update_message_status, Collection, MessageId, StatusDoc},
    _WorkerPid,
    #{topology_pid := Pid} = _ConnectorState
) ->
    StartTime = erlang:system_time(millisecond),
    case check_circuit_breaker() of
        {ok, _} ->
            try
                % 记录调试信息
                ?SLOG(debug, #{
                    msg => "update_message_status_operation_called",
                    collection => Collection,
                    message_id => MessageId
                }),

                % 使用最基本的MongoDB API操作
                Filter = #{<<"message_id">> => MessageId},
                Command = #{
                    <<"update">> => Collection,
                    <<"updates">> => [
                        #{
                            <<"q">> => Filter,
                            <<"u">> => #{<<"$set">> => StatusDoc}
                        }
                    ]
                },

                Result =
                    try
                        {ok, _} = emqx_plugin_mongodb_api:command(Pid, Command),
                        ok
                    catch
                        E1:R1:S1 ->
                            ?SLOG(error, #{
                                msg => "mongodb_update_message_status_error",
                                error => E1,
                                reason => R1,
                                stacktrace => S1,
                                command => Command,
                                message_id => MessageId
                            }),
                            {error, {E1, R1}}
                    end,

                % 计算延迟并记录指标
                EndTime = erlang:system_time(millisecond),
                Latency = EndTime - StartTime,
                observe_latency(InstId, Latency),
                _ = record_latency(update, StartTime),
                inc_metric(InstId, update_count, 1),

                case Result of
                    ok ->
                        record_success(),
                        ok;
                    {error, ErrorReason} ->
                        inc_metric(InstId, update_error, 1),
                        record_failure(),
                        classify_and_count_error(ErrorReason),
                        {error, ErrorReason}
                end
            catch
                Error:Reason:Stack ->
                    inc_metric(InstId, update_error, 1),
                    record_failure(),
                    classify_and_count_error({Error, Reason}),

                    ?SLOG(error, #{
                        msg => "emqx_plugin_mongodb_connector update_message_status error",
                        error => Error,
                        instId => InstId,
                        reason => Reason,
                        stack => Stack
                    }),
                    {error, {Error, Reason}}
            end;
        {error, circuit_open} ->
            ?SLOG(warning, #{
                msg => "mongodb_circuit_breaker_open",
                inst_id => InstId
            }),
            {error, circuit_breaker_open}
    end;



% 处理过期消息删除操作
on_query_async(
    InstId,
    {delete_expired_messages, Collection, Now, BatchSize},
    _WorkerPid,
    #{topology_pid := Pid} = _ConnectorState
) ->
    StartTime = erlang:system_time(millisecond),
    case check_circuit_breaker() of
        {ok, _} ->
            try
                Filter = #{
                    <<"expiry_time">> => #{
                        <<"$lt">> => Now,
                        <<"$ne">> => null
                    }
                },
                % MongoDB删除操作不支持limit来限制删除数量，删除所有匹配的文档
                Options = #{},

                % 直接执行删除并获取结果
                DeleteResult =
                    (catch safe_do_delete_many(Pid, Collection, Filter, Options)),

                % 计算延迟并记录指标
                EndTime = erlang:system_time(millisecond),
                Latency = EndTime - StartTime,
                observe_latency(InstId, Latency),
                _ = record_latency(delete, StartTime),

                % 处理结果
                case DeleteResult of
                    {ok, DeletedCount} when is_integer(DeletedCount) ->
                        record_success(),
                        inc_metric(InstId, delete_count, DeletedCount),
                        {ok, {DeletedCount}};
                    {'EXIT', {ErrorTerm, _}} ->
                        inc_metric(InstId, delete_error, 1),
                        record_failure(),
                        classify_and_count_error(ErrorTerm),
                        {error, ErrorTerm};
                    {error, _} = ErrorTuple ->
                        inc_metric(InstId, delete_error, 1),
                        record_failure(),
                        classify_and_count_error(ErrorTuple),
                        ErrorTuple;
                    OtherError ->
                        inc_metric(InstId, delete_error, 1),
                        record_failure(),
                        classify_and_count_error(OtherError),
                        {error, {unexpected_result, OtherError}}
                end
            catch
                Error:Reason:Stack ->
                    inc_metric(InstId, delete_error, 1),
                    record_failure(),
                    classify_and_count_error({Error, Reason}),

                    ?SLOG(error, #{
                        msg => "emqx_plugin_mongodb_connector delete_expired_messages error",
                        error => Error,
                        instId => InstId,
                        reason => Reason,
                        stack => Stack
                    }),
                    {error, {Error, Reason}}
            end;
        {error, circuit_open} ->
            ?SLOG(warning, #{
                msg => "mongodb_circuit_breaker_open",
                inst_id => InstId
            }),
            {error, circuit_breaker_open}
    end;

% 处理确保集合存在的查询
on_query_async(
    InstId,
    {ensure_collection, Collection},
    _WorkerPid,
    #{topology_pid := Pid} = _ConnectorState
) ->
    StartTime = erlang:system_time(millisecond),
    case check_circuit_breaker() of
        {ok, _} ->
            try
                % 记录调试信息
                ?SLOG(debug, #{
                    msg => "ensure_collection_operation_called",
                    collection => Collection
                }),

                % 尝试创建集合（如果不存在）
                Result =
                    try
                        % 获取当前数据库
                        {ok, DbName} = get_database_name(Pid),

                        % 获取现有集合列表
                        Command = #{
                            <<"listCollections">> => 1,
                            <<"nameOnly">> => true
                        },
                        {ok, #{<<"cursor">> := #{<<"firstBatch">> := Batch}}} =
                            emqx_plugin_mongodb_api:command(Pid, Command),

                        % 检查集合是否存在
                        CollectionNames = [maps:get(<<"name">>, Coll, <<>>) || Coll <- Batch],
                        case lists:member(Collection, CollectionNames) of
                            true ->
                                % 集合已存在
                                {ok, already_exists};
                            false ->
                                % 创建集合
                                CreateCmd = #{
                                    <<"create">> => Collection
                                },
                                {ok, _} = emqx_plugin_mongodb_api:command(Pid, CreateCmd),

                                % 创建索引
                                create_message_indices(Pid, Collection),
                                {ok, created}
                        end
                    catch
                        E1:R1:S1 ->
                            ?SLOG(error, #{
                                msg => "mongodb_ensure_collection_error",
                                error => E1,
                                reason => R1,
                                stacktrace => S1,
                                collection => Collection
                            }),
                            {error, {E1, R1}}
                    end,

                % 计算延迟并记录指标
                EndTime = erlang:system_time(millisecond),
                Latency = EndTime - StartTime,
                observe_latency(InstId, Latency),

                % 处理结果
                case Result of
                    {ok, _} = Ok ->
                        record_success(),
                        Ok;
                    {error, ErrorReason} ->
                        inc_metric(InstId, error_count, 1),
                        record_failure(),
                        classify_and_count_error(ErrorReason),
                        {error, ErrorReason}
                end
            catch
                Error:Reason:Stack ->
                    inc_metric(InstId, error_count, 1),
                    record_failure(),
                    classify_and_count_error({Error, Reason}),

                    ?SLOG(error, #{
                        msg => "emqx_plugin_mongodb_connector ensure_collection error",
                        error => Error,
                        instId => InstId,
                        reason => Reason,
                        stack => Stack
                    }),
                    {error, {Error, Reason}}
            end;
        {error, circuit_open} ->
            ?SLOG(warning, #{
                msg => "mongodb_circuit_breaker_open",
                inst_id => InstId
            }),
            {error, circuit_breaker_open}
    end;

% 处理 upsert 操作
on_query_async(
    InstId,
    {upsert, Collection, Filter, Doc},
    _WorkerPid,
    #{topology_pid := Pid} = _ConnectorState
) ->
    StartTime = erlang:system_time(millisecond),
    case check_circuit_breaker() of
        {ok, _} ->
            try
                Command = #{
                    <<"update">> => Collection,
                    <<"updates">> => [
                        #{
                            <<"q">> => Filter,
                            <<"u">> => Doc,
                            <<"upsert">> => true
                        }
                    ]
                },
                {ok, _} = emqx_plugin_mongodb_api:command(Pid, Command),
                record_success(),
                record_latency(StartTime),
                ok
            catch
                E:R:S ->
                    record_failure(),
                    ?SLOG(error, #{
                        msg => "upsert_operation_failed",
                        error => E,
                        reason => R,
                        stacktrace => S,
                        collection => Collection,
                        filter => Filter
                    }),
                    {error, {upsert_failed, {E, R}}}
            end;
        {error, circuit_breaker_open} ->
            {error, circuit_breaker_open}
    end;

% 处理 update 操作
on_query_async(
    InstId,
    {update, Collection, Filter, Update},
    _WorkerPid,
    #{topology_pid := Pid} = _ConnectorState
) ->
    StartTime = erlang:system_time(millisecond),
    case check_circuit_breaker() of
        {ok, _} ->
            try
                Command = #{
                    <<"update">> => Collection,
                    <<"updates">> => [
                        #{
                            <<"q">> => Filter,
                            <<"u">> => Update,
                            <<"upsert">> => false
                        }
                    ]
                },
                {ok, _} = emqx_plugin_mongodb_api:command(Pid, Command),
                record_success(),
                record_latency(StartTime),
                ok
            catch
                E:R:S ->
                    record_failure(),
                    ?SLOG(error, #{
                        msg => "update_operation_failed",
                        error => E,
                        reason => R,
                        stacktrace => S,
                        collection => Collection,
                        filter => Filter
                    }),
                    {error, {update_failed, {E, R}}}
            end;
        {error, circuit_breaker_open} ->
            {error, circuit_breaker_open}
    end;

% 处理简单 find 操作 - 异步查询应该返回简单状态
on_query_async(
    InstId,
    {find, Collection, Filter},
    WorkerPid,
    ConnectorState
) ->
    % 转换为完整格式
    on_query_async(InstId, {find, Collection, Filter, #{}, 0, 0}, WorkerPid, ConnectorState);

% 处理完整 find 操作 - 异步查询应该返回简单状态
on_query_async(
    InstId,
    {find, Collection, Filter, Projection, Skip, Limit},
    _WorkerPid,
    #{topology_pid := Pid} = _ConnectorState
) ->
    StartTime = erlang:system_time(millisecond),
    case check_circuit_breaker() of
        {ok, _} ->
            try
                Command = #{
                    <<"find">> => Collection,
                    <<"filter">> => Filter
                },
                % 添加可选参数
                Command1 = case Projection of
                    #{} when map_size(Projection) =:= 0 -> Command;
                    _ -> Command#{<<"projection">> => Projection}
                end,
                Command2 = case Skip of
                    0 -> Command1;
                    _ -> Command1#{<<"skip">> => Skip}
                end,
                Command3 = case Limit of
                    0 -> Command2;
                    _ -> Command2#{<<"limit">> => Limit}
                end,

                case emqx_plugin_mongodb_api:command(Pid, Command3) of
                    {ok, #{<<"cursor">> := #{<<"firstBatch">> := _Docs}}} ->
                        record_success(),
                        record_latency(StartTime),
                        ok;  % 异步查询返回简单状态
                    {ok, _Other} ->
                        record_success(),
                        record_latency(StartTime),
                        ok;  % 异步查询返回简单状态
                    {error, Reason} ->
                        record_failure(),
                        {error, {find_failed, Reason}}
                end
            catch
                E:R:S ->
                    record_failure(),
                    ?SLOG(error, #{
                        msg => "find_operation_failed",
                        error => E,
                        reason => R,
                        stacktrace => S,
                        collection => Collection,
                        filter => Filter
                    }),
                    {error, {find_failed, {E, R}}}
            end;
        {error, circuit_breaker_open} ->
            {error, circuit_breaker_open}
    end;

% 处理 find_one 操作 - 异步查询应该返回简单状态
on_query_async(
    InstId,
    {find_one, Collection, Filter, Projection},
    _WorkerPid,
    #{topology_pid := Pid} = _ConnectorState
) ->
    case check_circuit_breaker() of
        {ok, _} ->
            try
                % 使用find_one操作查找单个文档
                case emqx_plugin_mongodb_api:find_one_safe(Pid, Collection, Filter, #{projection => Projection}) of
                    {ok, undefined} ->
                        % 没有找到文档，返回ok表示查询成功
                        ok;
                    {ok, _Doc} ->
                        % 找到一个文档，返回ok表示查询成功
                        % 注意：异步查询通常不返回实际数据，只返回状态
                        ok;
                    {ok, Docs} when is_list(Docs) ->
                        % 找到多个文档，取第一个，返回ok
                        ok;
                    {error, Reason} ->
                        ?SLOG(error, #{
                            msg => "find_one_operation_failed",
                            inst_id => InstId,
                            collection => Collection,
                            filter => Filter,
                            reason => Reason
                        }),
                        {error, Reason}
                end
            catch
                E:R:S ->
                    ?SLOG(error, #{
                        msg => "find_one_operation_exception",
                        inst_id => InstId,
                        collection => Collection,
                        filter => Filter,
                        error => E,
                        reason => R,
                        stacktrace => S
                    }),
                    {error, {E, R}}
            end;
        {error, circuit_breaker_open} ->
            {error, circuit_breaker_open}
    end;

% 处理 delete_many 操作 - 异步查询应该返回简单状态
on_query_async(
    InstId,
    {delete_many, Collection, Filter},
    _WorkerPid,
    #{topology_pid := Pid} = _ConnectorState
) ->
    StartTime = erlang:system_time(millisecond),
    case check_circuit_breaker() of
        {ok, _} ->
            try
                Command = #{
                    <<"delete">> => Collection,
                    <<"deletes">> => [
                        #{
                            <<"q">> => Filter,
                            <<"limit">> => 0  % 0表示删除所有匹配的文档
                        }
                    ]
                },

                case emqx_plugin_mongodb_api:command(Pid, Command) of
                    {ok, #{<<"n">> := _DeletedCount}} ->
                        record_success(),
                        record_latency(StartTime),
                        ok;  % 异步查询返回简单状态
                    {ok, _Other} ->
                        record_success(),
                        record_latency(StartTime),
                        ok;  % 异步查询返回简单状态
                    {error, Reason} ->
                        record_failure(),
                        {error, {delete_many_failed, Reason}}
                end
            catch
                E:R:S ->
                    record_failure(),
                    ?SLOG(error, #{
                        msg => "delete_many_operation_failed",
                        error => E,
                        reason => R,
                        stacktrace => S,
                        collection => Collection,
                        filter => Filter
                    }),
                    {error, {delete_many_failed, {E, R}}}
            end;
        {error, circuit_breaker_open} ->
            {error, circuit_breaker_open}
    end;

% 处理 delete 操作 - 异步查询应该返回简单状态
on_query_async(
    InstId,
    {delete, Collection, Filter},
    _WorkerPid,
    #{topology_pid := Pid} = _ConnectorState
) ->
    StartTime = erlang:system_time(millisecond),
    case check_circuit_breaker() of
        {ok, _} ->
            try
                Command = #{
                    <<"delete">> => Collection,
                    <<"deletes">> => [
                        #{
                            <<"q">> => Filter,
                            <<"limit">> => 1  % 1表示只删除一个匹配的文档
                        }
                    ]
                },

                case emqx_plugin_mongodb_api:command(Pid, Command) of
                    % 处理标准格式
                    {ok, #{<<"n">> := _DeletedCount}} ->
                        record_success(),
                        record_latency(StartTime),
                        ok;  % 异步查询返回简单状态
                    % 处理嵌套的 {ok, {ok, Result}} 格式
                    {ok, {ok, #{<<"n">> := _DeletedCount}}} ->
                        record_success(),
                        record_latency(StartTime),
                        ok;  % 异步查询返回简单状态
                    {ok, {ok, _NestedResult}} ->
                        record_success(),
                        record_latency(StartTime),
                        ok;  % 异步查询返回简单状态
                    {ok, _Other} ->
                        record_success(),
                        record_latency(StartTime),
                        ok;  % 异步查询返回简单状态
                    {error, Reason} ->
                        record_failure(),
                        {error, {delete_failed, Reason}}
                end
            catch
                E:R:S ->
                    record_failure(),
                    ?SLOG(error, #{
                        msg => "delete_operation_exception",
                        error => E,
                        reason => R,
                        stacktrace => S,
                        collection => Collection,
                        filter => Filter
                    }),
                    {error, {delete_failed, {E, R}}}
            end;
        {error, circuit_breaker_open} ->
            {error, circuit_breaker_open}
    end;

% 默认处理子句
on_query_async(InstId, Query, _WorkerPid, ConnectorState) ->
    % 首先检查ConnectorState是否有效
    case ConnectorState of
        undefined ->
            ?SLOG(error, #{
                msg => "connector_state_undefined",
                inst_id => InstId,
                query => Query,
                action => "attempting_to_recover_connection"
            }),
            % 尝试从persistent_term恢复连接
            case try_recover_connection() of
                {ok, RecoveredState} ->
                    ?SLOG(info, #{msg => "connection_recovered_from_persistent_term"}),
                    on_query_async(InstId, Query, _WorkerPid, RecoveredState);
                {error, _} ->
                    {error, connector_state_undefined}
            end;
        _ when not is_map(ConnectorState) ->
            ?SLOG(error, #{
                msg => "connector_state_invalid",
                inst_id => InstId,
                query => Query,
                state_type => type_of(ConnectorState)
            }),
            {error, connector_state_invalid};
        _ ->
            % 添加更详细的日志
            case Query of
                {Querys, EvtMsg} when is_list(Querys) ->
                    ?SLOG(info, #{
                        msg => "handling_topic_match_query",
                        inst_id => InstId,
                        querys_count => length(Querys),
                        first_query => case Querys of
                            [] -> undefined;
                            [First|_] -> First
                        end,
                        evt_msg_summary => maps:get(id, EvtMsg, <<"unknown_id">>)
                    }),

                    % 尝试处理主题匹配查询
                    try
                        % 从ConnectorState获取MongoDB连接
                        case maps:get(topology_pid, ConnectorState, undefined) of
                            undefined ->
                                ?SLOG(error, #{
                                    msg => "topology_pid_not_found_in_connector_state",
                                    inst_id => InstId,
                                    connector_state_keys => maps:keys(ConnectorState)
                                }),
                                {error, topology_pid_not_found};
                            Pid when is_pid(Pid) ->
                                % 处理每个查询
                                Results = lists:map(
                                    fun({_Name, Collection}) ->
                                        try
                                            % 确保EvtMsg是一个map
                                            Doc = case is_map(EvtMsg) of
                                                true -> EvtMsg;
                                                false -> #{data => EvtMsg}
                                            end,

                                            % 执行插入
                                            emqx_plugin_mongodb_api:smart_insert(Pid, Collection, [Doc]),
                                            ok
                                        catch
                                            E:R:S ->
                                                ?SLOG(error, #{
                                                    msg => "insert_failed",
                                                    error => E,
                                                    reason => R,
                                                    stack => S,
                                                    collection => Collection
                                                }),
                                                {error, {insert_failed, {E, R}}}
                                        end
                                    end,
                                    Querys
                                ),

                                % 检查结果
                                case [E || E <- Results, E =/= ok] of
                                    [] ->
                                        % 全部成功
                                        record_success(),
                                        ok;
                                    Errors ->
                                        % 部分失败
                                        record_failure(),
                                        ?SLOG(error, #{
                                            msg => "some_inserts_failed",
                                            errors_count => length(Errors),
                                            first_error => hd(Errors)
                                        }),
                                        {error, {partial_failure, length(Errors)}}
                                end;
                            _ ->
                                ?SLOG(error, #{
                                    msg => "invalid_topology_pid",
                                    inst_id => InstId,
                                    topology_pid => maps:get(topology_pid, ConnectorState, undefined)
                                }),
                                {error, invalid_topology_pid}
                        end
                    catch
                        E:R:S ->
                            record_failure(),
                            ?SLOG(error, #{
                                msg => "exception_handling_topic_match_query",
                                error => E,
                                reason => R,
                                stack => S,
                                inst_id => InstId
                            }),
                            {error, {query_exception, {E, R}}}
                    end;
                {save_message, Collection, MessageDoc} ->
                    % 处理保存消息的查询
                    try
                        case maps:get(topology_pid, ConnectorState, undefined) of
                            undefined ->
                                {error, topology_pid_not_found};
                            Pid when is_pid(Pid) ->
                                emqx_plugin_mongodb_api:smart_insert(Pid, Collection, [MessageDoc]),
                                record_success(),
                                ok;
                            _ ->
                                {error, invalid_topology_pid}
                        end
                    catch
                        E:R:S ->
                            record_failure(),
                            ?SLOG(error, #{
                                msg => "save_message_failed",
                                error => E,
                                reason => R,
                                stack => S,
                                collection => Collection
                            }),
                            {error, {save_message_failed, {E, R}}}
                    end;
                {save_session, Collection, ClientId, SessionDoc} ->
                    % 处理保存会话的查询
                    try
                        case maps:get(topology_pid, ConnectorState, undefined) of
                            undefined ->
                                {error, topology_pid_not_found};
                            Pid when is_pid(Pid) ->
                                % 使用最基本的MongoDB API操作
                                Filter = #{<<"client_id">> => ClientId},
                                Command = #{
                                    <<"update">> => Collection,
                                    <<"updates">> => [
                                        #{
                                            <<"q">> => Filter,
                                            <<"u">> => #{<<"$set">> => SessionDoc},
                                            <<"upsert">> => true
                                        }
                                    ]
                                },
                                {ok, _} = emqx_plugin_mongodb_api:command(Pid, Command),
                                record_success(),
                                ok;
                            _ ->
                                {error, invalid_topology_pid}
                        end
                    catch
                        E:R:S ->
                            record_failure(),
                            ?SLOG(error, #{
                                msg => "save_session_failed",
                                error => E,
                                reason => R,
                                stack => S,
                                collection => Collection,
                                client_id => ClientId
                            }),
                            {error, {save_session_failed, {E, R}}}
                    end;
        {find_subscriptions, Collection, ClientId} ->
            % 处理查询订阅的查询
            try
                #{topology_pid := Pid} = ConnectorState,
                % 使用最基本的MongoDB API操作
                Filter = #{<<"client_id">> => ClientId},
                Command = #{
                    <<"find">> => Collection,
                    <<"filter">> => Filter
                },
                {true, #{<<"cursor">> := #{<<"firstBatch">> := DocsResult}}} = emqx_plugin_mongodb_api:command(Pid, Command),
                record_success(),
                {ok, DocsResult}
            catch
                E:R:S ->
                    record_failure(),
                    ?SLOG(error, #{
                        msg => "find_subscriptions_failed",
                        error => E,
                        reason => R,
                        stack => S,
                        collection => Collection,
                        client_id => ClientId
                    }),
                    {error, {find_subscriptions_failed, {E, R}}}
            end;
        {count_documents, Collection, Filter} ->
            % 处理文档计数查询
            try
                case maps:get(topology_pid, ConnectorState, undefined) of
                    undefined ->
                        {error, topology_pid_not_found};
                    Pid when is_pid(Pid) ->
                        Command = #{
                            <<"count">> => Collection,
                            <<"query">> => Filter
                        },
                        case emqx_plugin_mongodb_api:command(Pid, Command) of
                            {true, #{<<"n">> := Count}} ->
                                record_success(),
                                {ok, Count};
                            {true, Result} ->
                                % 兼容不同版本的MongoDB响应格式
                                Count = maps:get(<<"count">>, Result, maps:get(<<"n">>, Result, 0)),
                                record_success(),
                                {ok, Count};
                            {false, Error} ->
                                record_failure(),
                                {error, Error};
                            {ok, {ok, #{<<"n">> := Count}}} ->
                                % 处理嵌套的成功响应
                                record_success(),
                                {ok, Count};
                            {ok, {ok, Result}} when is_map(Result) ->
                                % 处理嵌套的成功响应，兼容不同格式
                                Count = maps:get(<<"count">>, Result, maps:get(<<"n">>, Result, 0)),
                                record_success(),
                                {ok, Count};
                            {ok, Result} when is_map(Result) ->
                                % 处理单层成功响应
                                Count = maps:get(<<"count">>, Result, maps:get(<<"n">>, Result, 0)),
                                record_success(),
                                {ok, Count};
                            Other ->
                                % 处理其他未预期的响应格式
                                ?SLOG(warning, #{
                                    msg => "unexpected_count_response_format",
                                    response => Other,
                                    collection => Collection
                                }),
                                record_failure(),
                                {error, {unexpected_response, Other}}
                        end;
                    _ ->
                        {error, invalid_topology_pid}
                end
            catch
                E:R:S ->
                    record_failure(),
                    ?SLOG(error, #{
                        msg => "count_documents_failed",
                        error => E,
                        reason => R,
                        stack => S,
                        collection => Collection,
                        filter => Filter
                    }),
                    {error, {count_documents_failed, {E, R}}}
            end;
        _ ->
            % 对于其他类型的查询，尝试通用处理而不是返回unsupported_query_type错误
            ?SLOG(warning, #{
                msg => "unknown_query_type_fallback_handling",
                inst_id => InstId,
                query_type => case Query of
                    {Type, _} when is_atom(Type) -> Type;
                    {Type, _, _} when is_atom(Type) -> Type;
                    {Type, _, _, _} when is_atom(Type) -> Type;
                    _ -> unknown
                end,
                query => Query
            }),

            % 尝试通用处理
            try
                % 从ConnectorState获取MongoDB连接
                case maps:get(topology_pid, ConnectorState, undefined) of
                    undefined ->
                        ?SLOG(error, #{
                            msg => "topology_pid_not_found_in_fallback_handler",
                            inst_id => InstId,
                            connector_state_keys => maps:keys(ConnectorState)
                        }),
                        {error, topology_pid_not_found};
                    Pid when is_pid(Pid) ->
                        % 根据查询类型进行通用处理
                        case Query of
                            % 处理 upsert 操作
                            {upsert, Collection, Filter, Doc} when is_binary(Collection) ->
                                try
                                    Command = #{
                                        <<"update">> => Collection,
                                        <<"updates">> => [
                                            #{
                                                <<"q">> => Filter,
                                                <<"u">> => Doc,
                                                <<"upsert">> => true
                                            }
                                        ]
                                    },
                                    {ok, _} = emqx_plugin_mongodb_api:command(Pid, Command),
                                    record_success(),
                                    ok
                                catch
                                    E:R:S ->
                                        ?SLOG(error, #{
                                            msg => "upsert_operation_failed",
                                            error => E,
                                            reason => R,
                                            stacktrace => S,
                                            collection => Collection
                                        }),
                                        record_failure(),
                                        {error, {upsert_failed, {E, R}}}
                                end;

                            % 处理 update 操作
                            {update, Collection, Filter, Update} when is_binary(Collection) ->
                                try
                                    Command = #{
                                        <<"update">> => Collection,
                                        <<"updates">> => [
                                            #{
                                                <<"q">> => Filter,
                                                <<"u">> => Update,
                                                <<"upsert">> => false
                                            }
                                        ]
                                    },
                                    {ok, _} = emqx_plugin_mongodb_api:command(Pid, Command),
                                    record_success(),
                                    ok
                                catch
                                    E:R:S ->
                                        ?SLOG(error, #{
                                            msg => "update_operation_failed",
                                            error => E,
                                            reason => R,
                                            stacktrace => S,
                                            collection => Collection
                                        }),
                                        record_failure(),
                                        {error, {update_failed, {E, R}}}
                                end;

                            % 处理 delete 操作
                            {delete, Collection, Filter} when is_binary(Collection) ->
                                try
                                    Command = #{
                                        <<"delete">> => Collection,
                                        <<"deletes">> => [
                                            #{
                                                <<"q">> => Filter,
                                                <<"limit">> => 0  % 0表示删除所有匹配的文档
                                            }
                                        ]
                                    },
                                    case emqx_plugin_mongodb_api:command(Pid, Command) of
                                        % 处理标准格式
                                        {ok, #{<<"n">> := DeletedCount}} ->
                                            record_success(),
                                            ?SLOG(debug, #{
                                                msg => "delete_operation_success",
                                                collection => Collection,
                                                deleted_count => DeletedCount
                                            }),
                                            {ok, DeletedCount};
                                        % 处理嵌套的 {ok, {ok, Result}} 格式
                                        {ok, {ok, #{<<"n">> := DeletedCount}}} ->
                                            record_success(),
                                            ?SLOG(debug, #{
                                                msg => "delete_operation_success_nested",
                                                collection => Collection,
                                                deleted_count => DeletedCount
                                            }),
                                            {ok, DeletedCount};
                                        {ok, {ok, NestedResult}} when is_map(NestedResult) ->
                                            % 安全地从嵌套结果中提取删除计数
                                            DeletedCount = maps:get(<<"n">>, NestedResult,
                                                          maps:get(<<"deletedCount">>, NestedResult, 0)),
                                            record_success(),
                                            ?SLOG(debug, #{
                                                msg => "delete_operation_success_nested_extracted",
                                                collection => Collection,
                                                deleted_count => DeletedCount,
                                                result => NestedResult
                                            }),
                                            {ok, DeletedCount};
                                        {ok, Result} when is_map(Result) ->
                                            % 安全地从结果中提取删除计数
                                            DeletedCount = maps:get(<<"n">>, Result,
                                                          maps:get(<<"deletedCount">>, Result, 0)),
                                            record_success(),
                                            ?SLOG(debug, #{
                                                msg => "delete_operation_success_extracted",
                                                collection => Collection,
                                                deleted_count => DeletedCount,
                                                result => Result
                                            }),
                                            {ok, DeletedCount};
                                        {ok, Result} ->
                                            % 处理非map类型的结果
                                            record_success(),
                                            ?SLOG(debug, #{
                                                msg => "delete_operation_success_unknown_format",
                                                collection => Collection,
                                                result => Result
                                            }),
                                            {ok, 0};  % 假设删除了0个文档
                                        {error, Error} ->
                                            record_failure(),
                                            ?SLOG(error, #{
                                                msg => "delete_operation_failed",
                                                collection => Collection,
                                                error => Error
                                            }),
                                            {error, {delete_failed, Error}}
                                    end
                                catch
                                    E:R:S ->
                                        ?SLOG(error, #{
                                            msg => "delete_operation_exception",
                                            error => E,
                                            reason => R,
                                            stacktrace => S,
                                            collection => Collection,
                                            filter => Filter
                                        }),
                                        record_failure(),
                                        {error, {delete_failed, {E, R}}}
                                end;

                            {_QueryType1, Collection} when is_binary(Collection) ->
                                % 简单的查询，假设是查找操作
                                Command = #{
                                    <<"find">> => Collection,
                                    <<"limit">> => 10
                                },
                                {ok, #{<<"cursor">> := #{<<"firstBatch">> := DocsResult}}} = emqx_plugin_mongodb_api:command(Pid, Command),
                                record_success(),
                                {ok, DocsResult};

                            {_QueryType2, Collection, Doc} when is_binary(Collection) ->
                                % 假设是插入操作
                                emqx_plugin_mongodb_api:smart_insert(Pid, Collection, [Doc]),
                                record_success(),
                                ok;

                            {_QueryType3, Collection, Filter, Update} when is_binary(Collection) ->
                                % 假设是更新操作
                                Command = #{
                                    <<"update">> => Collection,
                                    <<"updates">> => [
                                        #{
                                            <<"q">> => Filter,
                                            <<"u">> => Update,
                                            <<"upsert">> => false
                                        }
                                    ]
                                },
                                {ok, _} = emqx_plugin_mongodb_api:command(Pid, Command),
                                record_success(),
                                ok;

                    ping ->
                        % 处理ping查询 - 使用扩展API
                        try
                            case emqx_plugin_mongodb_api:ping_with_timeout(Pid, 5000) of
                                {ok, Latency} ->
                                    ?SLOG(debug, #{msg => "ping_success", latency => Latency}),
                                    record_success(),
                                    ok;
                                {error, Reason} ->
                                    ?SLOG(warning, #{msg => "ping_failed", reason => Reason}),
                                    record_failure(),
                                    {error, ping_failed}
                            end
                        catch
                            PE:PR:PS ->
                                ?SLOG(error, #{
                                    msg => "ping_command_failed",
                                    error => PE,
                                    reason => PR,
                                    stack => PS
                                }),
                                record_failure(),
                                {error, ping_failed}
                        end;

                            _ ->
                                % 实在无法处理的查询，返回空结果而不是错误
                                ?SLOG(warning, #{
                                    msg => "using_empty_result_fallback",
                                    inst_id => InstId,
                                    query => Query
                                }),
                                record_success(),
                                ok
                        end;
                    _ ->
                        ?SLOG(error, #{
                            msg => "invalid_topology_pid_in_fallback",
                            inst_id => InstId,
                            topology_pid => maps:get(topology_pid, ConnectorState, undefined)
                        }),
                        {error, invalid_topology_pid}
                end
            catch
                E1:R1:S1 ->
                    ?SLOG(error, #{
                        msg => "fallback_handler_failed",
                        error => E1,
                        reason => R1,
                        stack => S1,
                        query => Query
                    }),
                    % 记录失败并返回错误
                    record_failure(),
                    {error, {fallback_failed, {E1, R1}}}
            end
            end
    end.

%% @doc 获取数据类型的辅助函数
type_of(X) when is_integer(X) -> integer;
type_of(X) when is_float(X) -> float;
type_of(X) when is_list(X) -> list;
type_of(X) when is_tuple(X) -> tuple;
type_of(X) when is_binary(X) -> binary;
type_of(X) when is_atom(X) -> atom;
type_of(X) when is_boolean(X) -> boolean;
type_of(X) when is_map(X) -> map;
type_of(X) when is_pid(X) -> pid;
type_of(X) when is_port(X) -> port;
type_of(X) when is_reference(X) -> reference;
type_of(X) when is_function(X) -> function;
type_of(_) -> unknown.

%% @doc 生成索引名称的辅助函数
generate_index_name(Index) ->
    Parts = maps:fold(
        fun(Key, Value, Acc) ->
            KeyBin =
                if is_binary(Key) -> Key;
                   true -> list_to_binary(io_lib:format("~p", [Key]))
                end,
            ValueBin =
                if is_binary(Value) -> Value;
                   is_integer(Value) -> integer_to_binary(Value);
                   is_atom(Value) -> atom_to_binary(Value, utf8);
                   true -> list_to_binary(io_lib:format("~p", [Value]))
                end,
            [<<KeyBin/binary, "_", ValueBin/binary>> | Acc]
        end,
        [],
        Index
    ),
    iolist_to_binary(lists:join("_", Parts)).

%% @doc 确保MongoDB客户端连接 - 添加重试机制（辅助函数）
%% 这个函数负责创建MongoDB客户端连接，提供默认的重试机制
%%
%% 功能说明：
%% 1. 使用默认重试次数（3次）创建MongoDB连接
%% 2. 支持不同的MongoDB部署模式（单节点、分片、副本集）
%% 3. 提供连接失败时的自动重试机制
%% 4. 封装复杂的连接参数处理逻辑
%%
%% 参数说明：
%% - Type: MongoDB部署类型（single/sharded/replica_set）
%% - Hosts: MongoDB主机列表
%% - TopologyOptions: 拓扑选项配置
%% - WorkerOptions: 工作进程选项配置
%%
%% 返回值：
%% - {ok, Pid}: 连接创建成功，返回连接进程ID
%% - {error, Reason}: 连接创建失败，返回错误原因
%%
%% Java等价概念：
%% 类似于MongoClient.create()的重载方法
%% 或者连接池的createConnection()方法
%%
%% 设计模式：
%% 使用重载模式提供默认参数
ensure_client(Type, Hosts, TopologyOptions, WorkerOptions) ->
    %% 使用默认重试次数（3次）调用完整版本的ensure_client
    %% 这是一种常见的函数重载模式，提供便利的默认参数
    %% 在Java中相当于：
    %% public Connection ensureClient(params) {
    %%     return ensureClient(params, DEFAULT_RETRIES);
    %% }
    ensure_client(Type, Hosts, TopologyOptions, WorkerOptions, 3).

% @spec ensure_client(Type, Hosts, TopologyOptions, WorkerOptions, RetriesLeft :: integer()) -> {ok, Pid} | {error, Reason}

%% @doc 确保MongoDB客户端连接 - 重试次数耗尽处理
%% 当重试次数用完时，返回最大重试次数超出错误
%%
%% 功能说明：
%% 1. 处理重试次数耗尽的情况
%% 2. 返回明确的错误信息
%% 3. 避免无限重试导致的资源浪费
%%
%% 参数说明：
%% - _Type, _Hosts, _TopologyOptions, _WorkerOptions: 未使用的参数（用_前缀标识）
%% - 0: 重试次数为0，表示已耗尽
%%
%% 返回值：
%% - {error, max_retries_exceeded}: 最大重试次数超出错误
%%
%% Java等价概念：
%% 类似于连接池的最大重试次数限制
%% 或者网络客户端的重试机制
ensure_client(_Type, _Hosts, _TopologyOptions, _WorkerOptions, 0) ->
    %% 重试次数耗尽，返回明确的错误信息
    %% 这是一种防御性编程策略，避免无限重试
    %% 在Java中相当于：
    %% if (retriesLeft == 0) {
    %%     throw new MaxRetriesExceededException();
    %% }
    {error, max_retries_exceeded};
ensure_client(Type, Hosts, TopologyOptions, WorkerOptions, Retries) ->
    % 将所有参数合并为一个选项列表
    ConnectOptions =
        case Type of
            single ->
                % 单节点模式，需要分离主机和端口
                [Host | _] = Hosts,
                {HostStr, PortStr} = parse_host_port(Host),
                Port = list_to_integer(binary_to_list(PortStr)),
                [{type, single}, {host, binary_to_list(HostStr)}, {port, Port}];
            sharded ->
                % 分片模式，需要分离每个主机和端口
                ParsedHosts = [parse_host_port(H) || H <- Hosts],
                ShardHosts = [
                    {binary_to_list(H), list_to_integer(binary_to_list(P))}
                 || {H, P} <- ParsedHosts
                ],
                [{type, sharded}, {hosts, ShardHosts}];
            {rs, ReplicaSetName} ->
                % 副本集模式，需要分离每个主机和端口
                ParsedHosts = [parse_host_port(H) || H <- Hosts],
                RsHosts = [
                    {binary_to_list(H), list_to_integer(binary_to_list(P))}
                 || {H, P} <- ParsedHosts
                ],
                [
                    {type, rs},
                    {rs_name, ReplicaSetName},
                    {hosts, RsHosts}
                    % 移除硬编码的超时设置，使用配置文件中的设置
                ]
        end ++ TopologyOptions ++ WorkerOptions,

    % 尝试直接连接到单个节点，而不是副本集
    DirectConnectOptions =
        case Type of
            {rs, _ReplicaSetName} when Retries =:= 1 ->
                % 最后一次尝试时，改为单节点直连模式
                SingleHost = lists:nth(1, Hosts),
                {SingleHostStr, SinglePortStr} = parse_host_port(SingleHost),
                SinglePort = list_to_integer(binary_to_list(SinglePortStr)),
                [{type, single}, {host, binary_to_list(SingleHostStr)}, {port, SinglePort}] ++
                    [
                        {K, V}
                     || {K, V} <- TopologyOptions ++ WorkerOptions,
                        K =/= rs_name,
                        K =/= hosts
                    ];
            _ ->
                ConnectOptions
        end,

    % 选择当前尝试使用的选项
    CurrentOptions =
        case Retries of
            1 -> DirectConnectOptions;
            _ -> ConnectOptions
        end,

    ?SLOG(info, #{
        msg => "connecting_to_mongodb",
        connect_options => CurrentOptions,
        mongodb_type => Type,
        mongodb_hosts => Hosts,
        retry_count => Retries
    }),

    % 使用事件包装器连接，确保能够触发数据恢复操作
    % 修复：使用connect_with_event_wrapper而不是connect_with_options
    % 这样可以确保MongoDB连接事件被正确处理，触发遗嘱消息恢复
    case emqx_plugin_mongodb_api:connect_with_event_wrapper(CurrentOptions) of
        % 连接成功
        {ok, Pid} ->
            ?SLOG(info, #{
                msg => "mongodb_connection_successful",
                pid => Pid
            }),
            {ok, Pid};
        % 如果已启动，先尝试关闭再重试 (可能是状态不一致)
        {error, {already_started, Pid}} ->
            ?SLOG(warning, #{
                msg => "mongodb_connection_already_started",
                pid => Pid
            }),
            deallocate_client(Pid),
            % 保持重试计数不变
            ensure_client(Type, Hosts, TopologyOptions, WorkerOptions, Retries);
        % 连接失败，但仍有重试次数
        {error, Reason} when Retries > 1 ->
            ?SLOG(warning, #{
                msg => "failed_to_start_mongodb_client_retrying",
                mongodb_type => Type,
                mongodb_hosts => Hosts,
                reason => Reason,
                retries_left => Retries - 1,
                connect_options => CurrentOptions
            }),
            % 等待1秒再重试
            timer:sleep(1000),
            ensure_client(Type, Hosts, TopologyOptions, WorkerOptions, Retries - 1);
        % 连接失败且无更多重试次数
        {error, Reason} ->
            ?SLOG(error, #{
                msg => "failed_to_start_mongodb_client",
                mongodb_type => Type,
                mongodb_hosts => Hosts,
                reason => Reason,
                connect_options => CurrentOptions
            }),
            {error, Reason}
    end.

%% @doc 解析主机地址和端口（辅助函数）
%% 这个函数从"host:port"格式的字符串中分离主机名和端口号
%%
%% 功能说明：
%% 1. 解析"host:port"格式的连接字符串
%% 2. 如果没有指定端口，使用MongoDB默认端口27017
%% 3. 支持二进制字符串和字符串列表两种输入格式
%% 4. 返回标准化的主机名和端口号
%%
%% 参数说明：
%% - HostPort: 主机端口字符串，格式为"host:port"或"host"
%%
%% 返回值：
%% - {Host, Port}: 主机名和端口号的二进制字符串元组
%%
%% Java等价概念：
%% 类似于URL解析或InetSocketAddress的解析
%% 或者Apache Commons的HostAndPort.fromString()
%%
%% 示例：
%% - parse_host_port(<<"localhost:27017">>) -> {<<"localhost">>, <<"27017">>}
%% - parse_host_port(<<"localhost">>) -> {<<"localhost">>, <<"27017">>}
parse_host_port(HostPort) when is_binary(HostPort) ->
    %% 使用冒号分割主机名和端口号
    %% binary:split/2返回分割后的二进制字符串列表
    %% 在Java中相当于：
    %% String[] parts = hostPort.split(":");
    case binary:split(HostPort, <<":">>) of
        [Host, Port] ->
            %% 有明确的端口号，直接使用
            {Host, Port};
        %% 没有端口号，使用MongoDB默认端口27017
        %% 这是MongoDB的标准默认端口
        [Host] ->
            {Host, <<"27017">>}
    end;
%% 处理字符串列表格式的输入
%% 先转换为二进制字符串，然后递归调用
%% 在Java中相当于：
%% public HostPort parse(String hostPort) {
%%     return parse(hostPort.getBytes());
%% }
parse_host_port(HostPort) when is_list(HostPort) ->
    parse_host_port(list_to_binary(HostPort)).

%% @doc 初始化MongoDB部署类型（辅助函数）
%% 这个函数从连接配置中确定MongoDB的部署类型
%%
%% 功能说明：
%% 1. 使用模式匹配从配置映射中读取MongoDB部署类型
%% 2. 支持单节点、分片、副本集三种部署模式
%% 3. 为副本集模式确保副本集名称为二进制格式
%% 4. 提供默认的单节点模式作为后备选项
%%
%% 参数说明：
%% - Connection: 连接配置映射，包含mongo_type等配置项
%%
%% 返回值：
%% - single: 单节点部署
%% - sharded: 分片集群部署
%% - {rs, ReplicaSetName}: 副本集部署，包含副本集名称
%%
%% Java等价概念：
%% 类似于MongoClientSettings中的集群类型配置
%% 或者Spring Data MongoDB的连接模式配置

%% 单节点部署模式
%% 最简单的部署模式，适用于开发环境或小规模应用
%% 在Java中相当于：
%% if (config.getMongoType() == MongoType.SINGLE) return SINGLE;
init_mongo_type(#{mongo_type := single}) ->
    single;
%% 分片集群部署模式
%% 用于大规模数据和高吞吐量场景，需要连接到mongos路由器
init_mongo_type(#{mongo_type := sharded}) ->
    sharded;
%% 副本集部署模式
%% 提供高可用性和数据冗余，需要指定副本集名称用于连接验证
init_mongo_type(#{mongo_type := rs, replica_set_name := Name}) ->
    %% 确保副本集名称是二进制类型
    %% 这是MongoDB驱动的要求，副本集名称必须是二进制字符串
    %% 在Java中相当于：
    %% return new ReplicaSetConfig(ensureBinary(name));
    {rs, ensure_binary(Name)};
%% 默认情况：如果配置不匹配任何已知模式，使用单节点模式
%% 这是一种防御性编程策略，确保系统能够正常启动
%% 在Java中相当于：
%% default: return MongoType.SINGLE;
init_mongo_type(_) ->
    single.

%% @doc 确保值为二进制类型（辅助函数）
%% 这个函数将各种类型的值转换为二进制字符串
%%
%% 功能说明：
%% 1. 处理已经是二进制类型的值，直接返回
%% 2. 将字符串列表转换为二进制字符串
%% 3. 将原子转换为UTF-8编码的二进制字符串
%% 4. 对其他类型返回默认的"unknown"标识
%%
%% 参数说明：
%% - Value: 需要转换的值，可以是任意类型
%%
%% 返回值：
%% - binary(): 转换后的二进制字符串
%%
%% Java等价概念：
%% 类似于String.valueOf()或Objects.toString()
%% 或者自定义的类型转换工具方法
%%
%% 使用场景：
%% - 配置参数标准化
%% - 数据库字段值转换
%% - 副本集名称格式化
ensure_binary(Value) when is_binary(Value) ->
    %% 已经是二进制类型，直接返回
    %% 在Java中相当于：
    %% if (value instanceof byte[]) return value;
    Value;
ensure_binary(Value) when is_list(Value) ->
    %% 字符串列表转换为二进制字符串
    %% 在Java中相当于：
    %% if (value instanceof String) return value.getBytes(UTF_8);
    list_to_binary(Value);
ensure_binary(Value) when is_atom(Value) ->
    %% 原子转换为UTF-8编码的二进制字符串
    %% 在Java中相当于：
    %% if (value instanceof Enum) return value.name().getBytes(UTF_8);
    atom_to_binary(Value, utf8);
ensure_binary(_) ->
    %% 对于其他类型，返回默认的"unknown"标识
    %% 这是一种安全的后备策略，避免转换失败
    %% 在Java中相当于：
    %% default: return "unknown".getBytes(UTF_8);
    <<"unknown">>.

%% @doc 初始化主机配置（辅助函数）
%% 这个函数从连接配置中提取和标准化主机列表
%%
%% 功能说明：
%% 1. 支持多种主机配置字段名（bootstrap_hosts、hosts、host）
%% 2. 将不同格式的主机配置统一转换为二进制字符串列表
%% 3. 提供向后兼容性，支持旧的配置格式
%% 4. 处理单节点和多节点配置
%%
%% 参数说明：
%% - Connection: 连接配置映射，可能包含不同的主机配置字段
%%
%% 返回值：
%% - list(binary()): 标准化的主机列表，格式为["host:port", ...]
%%
%% Java等价概念：
%% 类似于配置解析器或连接字符串解析
%% 或者Spring Boot的多环境配置处理
%%
%% 配置兼容性：
%% - bootstrap_hosts: 推荐的配置字段名
%% - hosts: 兼容字段名
%% - host: 单节点配置字段名

%% 从配置中提取bootstrap_hosts字段
%% 这是推荐的配置字段名，用于指定MongoDB集群的引导主机
%% 在Java中相当于：
%% if (config.containsKey("bootstrap_hosts")) {
%%     return parseHosts(config.get("bootstrap_hosts"));
%% }
init_hosts(#{bootstrap_hosts := Hosts}) ->
    %% 调用hosts/1函数处理主机列表的具体解析
    hosts(Hosts);
%% 兼容直接使用hosts字段的配置
%% 这是为了向后兼容旧的配置格式
init_hosts(#{hosts := Hosts}) ->
    hosts(Hosts);
%% 兼容单节点配置
%% 当只有一个MongoDB实例时，可以直接指定host字段
%% 在Java中相当于：
%% if (config.containsKey("host")) {
%%     return Arrays.asList(ensureBinary(config.get("host")));
%% }
init_hosts(#{host := Host}) ->
    [ensure_binary(Host)].

%% @doc 解析主机配置（辅助函数）
%% 这个函数将配置文件中的主机列表转换为MongoDB驱动期望的格式
%%
%% 功能说明：
%% 1. 支持多种主机配置格式的解析和转换
%% 2. 统一转换为二进制字符串列表格式
%% 3. 处理单个主机和多个主机的配置
%% 4. 提供默认端口和格式标准化
%%
%% 参数说明：
%% - HostsConfig: 主机配置，支持多种格式
%%
%% 返回值：
%% - list(binary()): 标准化的主机列表，格式为[<<"host:port">>, ...]
%%
%% Java等价概念：
%% 类似于配置解析器或连接字符串解析工具
%% 或者Spring Boot的服务器列表解析
%%
%% 支持的格式：
%% - 映射列表: [#{hostname => "host1", port => 27017}, ...]
%% - 二进制列表: [<<"host1:port1">>, <<"host2:port2">>]
%% - 字符串列表: ["host1:port1", "host2:port2"]
%% - 逗号分隔字符串: "host1:port1,host2:port2"
hosts(Hosts) when is_list(Hosts) andalso is_map(hd(Hosts)) ->
    %% 处理映射列表形式的主机配置
    %% 格式：[#{hostname => "host1", port => 27017}, ...]
    %% 这是最结构化的配置格式，提供最大的灵活性
    %% 在Java中相当于：
    %% List<ServerConfig> configs = parseServerConfigs(hosts);
    %% return configs.stream().map(c -> c.getHost() + ":" + c.getPort()).collect(toList());
    lists:map(
        %% 对每个主机配置映射进行转换
        %% 提取hostname和port字段，拼接成"hostname:port"格式
        fun(#{hostname := Host, port := Port}) ->
            %% 拼接主机名和端口号
            %% iolist_to_binary提供高效的字符串拼接
            iolist_to_binary([Host, ":", integer_to_list(Port)])
        end,
        %% 使用EMQX的schema解析器处理服务器列表
        %% 提供默认端口27017，确保所有主机都有端口号
        emqx_schema:parse_servers(Hosts, #{default_port => 27017})
    );
hosts(Hosts) when is_list(Hosts) andalso is_binary(hd(Hosts)) ->
    %% 已经是二进制列表形式，直接返回
    %% 格式：[<<"host1:port1">>, <<"host2:port2">>]
    %% 这是目标格式，无需转换
    %% 在Java中相当于：
    %% if (hosts instanceof List<byte[]>) return hosts;
    Hosts;
hosts(Hosts) when is_list(Hosts) andalso is_list(hd(Hosts)) ->
    %% 字符串列表形式，转换为二进制列表
    %% 格式：["host1:port1", "host2:port2"]
    %% 在Java中相当于：
    %% return hosts.stream().map(String::getBytes).collect(toList());
    [list_to_binary(H) || H <- Hosts];
hosts(Hosts) when is_binary(Hosts) ->
    %% 单个二进制字符串，可能包含逗号分隔的多个主机
    %% 格式：<<"host1:port1,host2:port2">>
    %% 需要分割并清理空白字符
    %% 在Java中相当于：
    %% return Arrays.stream(hosts.split(",")).map(String::trim).filter(s -> !s.isEmpty()).collect(toList());
    HostsList = binary:split(Hosts, <<",">>, [global]),
    [trim_binary(H) || H <- HostsList, size(H) > 0];
hosts(Hosts) when is_list(Hosts) andalso not is_tuple(Hosts) andalso not is_map(Hosts) ->
    %% 字符串形式，可能包含逗号分隔的多个主机
    %% 格式："host1:port1,host2:port2"
    %% 需要展平、分割并清理
    HostsStr = lists:flatten(Hosts),
    HostsList = string:tokens(HostsStr, ","),
    [list_to_binary(string:trim(H)) || H <- HostsList, H /= ""];
hosts(Host) ->
    %% 其他情况的后备处理
    %% 尝试将单个值转换为二进制并包装成列表
    %% 这是一种防御性编程策略
    %% 在Java中相当于：
    %% default: return Arrays.asList(String.valueOf(host).getBytes());
    [ensure_binary(Host)].

%% @doc 去除二进制字符串两端的空白字符（辅助函数）
%% 这个函数使用正则表达式去除二进制字符串两端的空白字符
%%
%% 功能说明：
%% 1. 使用正则表达式匹配字符串开头和结尾的空白字符
%% 2. 将匹配的空白字符替换为空字符串
%% 3. 返回清理后的二进制字符串
%% 4. 支持各种空白字符（空格、制表符、换行符等）
%%
%% 参数说明：
%% - Bin: 需要清理的二进制字符串
%%
%% 返回值：
%% - binary(): 去除两端空白字符后的二进制字符串
%%
%% Java等价概念：
%% 类似于String.trim()方法
%% 或者Apache Commons的StringUtils.trim()
%%
%% 正则表达式说明：
%% - ^\\s+: 匹配字符串开头的一个或多个空白字符
%% - \\s+$: 匹配字符串结尾的一个或多个空白字符
%% - |: 逻辑或操作符，匹配开头或结尾的空白
trim_binary(Bin) ->
    %% 使用正则表达式去除两端空白字符
    %% ^\\s+ 匹配开头的空白字符，\\s+$ 匹配结尾的空白字符
    %% 将匹配的内容替换为空字符串
    %% 在Java中相当于：
    %% return new String(bin).trim().getBytes();
    re:replace(Bin, "^\\s+|\\s+$", "", [{return, binary}, global]).

% 配置SSL选项 (辅助函数)
% @spec init_ssl_opts(ConnectionConf :: map()) -> list(tuple())
% 从连接配置中提取SSL相关的选项，并转换为MongoDB驱动期望的格式 (proplist)。
init_ssl_opts(Connection) ->
    % 获取ssl配置项
    case maps:get(ssl, Connection, undefined) of
        % SSL未启用
        #{enable := false} ->
            [];
        % SSL配置是map但没有enable键，视为禁用
        SSL when is_map(SSL) andalso not is_map_key(enable, SSL) -> [];
        % SSL启用，SslMap包含具体SSL参数
        #{enable := true} = SslMap ->
            % 添加 {ssl, true} 并提取其他选项
            [{ssl, true} | extract_ssl_opts(SslMap)];
        % SSL配置为false原子
        false ->
            [];
        % SSL配置为true原子
        true ->
            [{ssl, true}];
        % SSL配置为列表 [{ssl, false}]
        [{ssl, false}] ->
            [];
        % SSL配置为列表 [{ssl, true}]
        [{ssl, true}] ->
            [{ssl, true}];
        % 未配置SSL
        undefined ->
            [];
        % 其他未知情况，视为空SSL配置
        _ ->
            []
    end.

%% @doc 从SSL配置映射中提取具体的SSL参数（辅助函数）
%% 这个函数从SSL配置映射中提取证书文件、密钥文件等具体参数
%%
%% 功能说明：
%% 1. 从SSL配置映射中提取各种SSL相关的文件路径和选项
%% 2. 构建MongoDB驱动期望的SSL选项列表
%% 3. 只包含已配置的选项，忽略未定义的选项
%% 4. 支持客户端证书、私钥、CA证书、验证模式等配置
%%
%% 参数说明：
%% - SslMap: SSL配置映射，包含各种SSL参数
%%
%% 返回值：
%% - list(tuple()): SSL选项列表，格式为[{certfile, Path}, {keyfile, Path}, ...]
%%
%% Java等价概念：
%% 类似于SSLContext的配置构建
%% 或者KeyStore和TrustStore的配置
%%
%% 支持的SSL参数：
%% - certfile: 客户端证书文件路径
%% - keyfile: 客户端私钥文件路径
%% - cacertfile: CA证书文件路径
%% - verify: 证书验证模式（verify_peer/verify_none）
%% - server_name_indication: SNI服务器名称指示
extract_ssl_opts(SslMap) ->
    %% 初始化空的SSL选项列表，逐步添加配置的SSL参数
    SslOpts = [],
    %% 处理客户端证书文件配置
    %% 在Java中相当于：
    %% if (sslMap.containsKey("certfile")) {
    %%     keyManager.loadCertificate(sslMap.get("certfile"));
    %% }
    SslOpts1 =
        case maps:get(certfile, SslMap, undefined) of
            undefined ->
                SslOpts;
            CertFile ->
                [{certfile, CertFile} | SslOpts]
        end,
    %% 处理客户端私钥文件配置
    %% 私钥文件通常与证书文件配对使用
    SslOpts2 =
        case maps:get(keyfile, SslMap, undefined) of
            undefined ->
                SslOpts1;
            KeyFile ->
                [{keyfile, KeyFile} | SslOpts1]
        end,
    %% 处理CA证书文件配置
    %% CA证书用于验证服务器证书的有效性
    SslOpts3 =
        case maps:get(cacertfile, SslMap, undefined) of
            undefined ->
                SslOpts2;
            CACertFile ->
                [{cacertfile, CACertFile} | SslOpts2]
        end,
    %% 处理证书验证模式配置
    %% verify_peer: 验证对等证书，verify_none: 不验证证书
    %% 在Java中相当于：
    %% sslContext.setVerifyMode(sslMap.get("verify"));
    SslOpts4 =
        case maps:get(verify, SslMap, undefined) of
            undefined ->
                SslOpts3;
            %% 例如：verify_peer（验证对等证书）、verify_none（不验证证书）
            Verify ->
                [{verify, Verify} | SslOpts3]
        end,
    %% 处理SNI（服务器名称指示）配置
    %% SNI用于在单个IP地址上托管多个SSL证书
    %% 在Java中相当于：
    %% sslParameters.setServerNames(Arrays.asList(new SNIHostName(sni)));
    case maps:get(server_name_indication, SslMap, undefined) of
        undefined ->
            SslOpts4;
        %% SNI（Server Name Indication）服务器名称指示
        %% 用于支持虚拟主机的SSL连接
        SNI ->
            [{server_name_indication, SNI} | SslOpts4]
    end.

%% @doc 初始化拓扑选项 - 使用映射表简化代码（辅助函数）
%% 这个函数将配置文件中的拓扑选项名转换为MongoDB驱动期望的选项名
%%
%% 功能说明：
%% 1. 将下划线命名的配置选项转换为驼峰命名或特定原子
%% 2. 使用映射表定义配置文件键名到驱动程序键名的对应关系
%% 3. 过滤和转换有效的拓扑配置选项
%% 4. 构建MongoDB驱动期望的选项列表
%%
%% 参数说明：
%% - Options: 配置选项列表，格式为[{atom(), any()}, ...]
%% - Acc: 累积器，用于收集转换后的选项
%%
%% 返回值：
%% - list(tuple()): 转换后的拓扑选项列表
%%
%% Java等价概念：
%% 类似于MongoClientSettings.Builder的各种配置方法
%% 或者配置属性的映射和转换工具
%%
%% 支持的拓扑选项：
%% - 连接池配置：pool_size、max_overflow等
%% - 超时配置：connect_timeout_ms、socket_timeout_ms等
%% - 读取配置：read_preference等
init_topology_options(Options, Acc) ->
    %% OptionMap定义了配置文件键名到驱动程序键名的映射
    %% 这种映射表方式使代码更清晰，易于维护
    %% 在Java中相当于：
    %% Map<String, String> optionMapping = Map.of(
    %%     "pool_size", "pool_size",
    %%     "connect_timeout_ms", "timeout", ...
    %% );
    OptionMap = [
        %% {ConfigKey, DriverKey} - 配置键到驱动键的映射

        %% 连接池配置
        {pool_size, pool_size},                           %% 连接池大小
        {max_overflow, max_overflow},                     %% 最大溢出连接数

        %% 延迟和阈值配置
        {local_threshold_ms, localThresholdMS},           %% 本地阈值毫秒数

        %% 连接超时映射到MongoDB驱动期望的timeout参数
        %% 这是一个特殊映射，因为驱动使用不同的参数名
        {connect_timeout_ms, timeout},

        %% 注意：socketTimeoutMS可能不被MongoDB驱动直接支持，保留原名
        %% 这种情况下保持原始名称，让驱动自行处理
        {socket_timeout_ms, socketTimeoutMS},
        {server_selection_timeout_ms, serverSelectionTimeoutMS}, %% 服务器选择超时
        {wait_queue_timeout_ms, waitQueueTimeoutMS},      %% 等待队列超时
        {heartbeat_frequency_ms, heartbeatFrequencyMS},   %% 心跳频率
        {min_heartbeat_frequency_ms, minHeartbeatFrequencyMS}, %% 最小心跳频率

        %% 读偏好设置 - 映射到MongoDB驱动的rp_mode
        %% 这是MongoDB特有的读取偏好配置
        {read_preference, rp_mode}
    ],

    % 从配置中获取连接池策略，默认为FIFO (先进先出)，更均衡地使用连接
    PoolStrategy = proplists:get_value(pool_strategy, Options, fifo),

    % 添加连接池策略
    AccWithStrategy =
        case proplists:get_value(strategy, Acc) of
            % 如果未指定，使用配置中的策略
            undefined -> [{strategy, PoolStrategy} | Acc];
            % 已指定，保持不变
            _ -> Acc
        end,

    % 添加连接池优化选项 - 只添加不存在的选项
    AccWithPoolOpts = lists:foldl(
        fun({Key, Value}, Acc) ->
            case proplists:get_value(Key, Acc) of
                undefined -> [{Key, Value} | Acc];
                _ -> Acc  % 已存在，不重复添加
            end
        end,
        AccWithStrategy,
        [
            % 优化请求处理
            {next_req_fun, fun() -> ok end},
            % 设置溢出连接的生存时间为5分钟（如果不存在）
            {overflow_ttl, 300000},
            % 每分钟检查一次溢出连接（如果不存在）
            {overflow_check_period, 60000}
        ]
    ),

    % 处理其他选项
    lists:foldl(
        fun({Key, Value}, AccIn) ->
            % 在OptionMap中查找Key
            case proplists:get_value(Key, OptionMap) of
                % 如果未找到映射，则忽略此选项
                undefined ->
                    AccIn;
                MappedKey ->
                    % 检查是否已经有相同的键
                    case proplists:get_value(MappedKey, AccIn) of
                        % 如果没有，添加新的
                        undefined -> [{MappedKey, Value} | AccIn];
                        % 如果已有，替换
                        _ -> lists:keyreplace(MappedKey, 1, AccIn, {MappedKey, Value})
                    end
            end
        end,
        % 初始累加器
        AccWithPoolOpts,
        % 输入的选项列表 (proplist)
        Options
    ).

%% @doc 初始化工作者选项 - 使用映射表简化代码（辅助函数）
%% 这个函数处理工作者相关的配置选项，类似于init_topology_options
%%
%% 功能说明：
%% 1. 从配置选项中提取工作者相关的参数
%% 2. 使用映射表将配置键名转换为驱动期望的键名
%% 3. 处理认证、数据库、读关注等工作者选项
%% 4. 合并SSL选项和其他配置选项
%%
%% 参数说明：
%% - Options: 配置选项列表，格式为[{atom(), any()}, ...]
%% - SslOpts: SSL选项列表，已经处理过的SSL配置
%%
%% 返回值：
%% - list(tuple()): 工作者选项列表，包含数据库、认证、SSL等配置
%%
%% Java等价概念：
%% 类似于MongoCredential和MongoClientSettings的构建
%% 或者JDBC连接字符串的参数配置
%%
%% 支持的工作者选项：
%% - database: 目标数据库名称
%% - username/login: 认证用户名（配置文件用username，驱动用login）
%% - password: 认证密码
%% - auth_source: 认证数据库
%% - auth_mechanism: 认证机制（SCRAM-SHA-1、SCRAM-SHA-256等）
%% - read_concern_level: 读关注级别
init_worker_options(Options, SslOpts) ->
    %% OptionMap定义了配置文件键名到驱动程序键名的映射
    %% 这种映射表方式使代码更清晰，易于维护
    %% 在Java中相当于：
    %% Map<String, String> workerOptionMapping = Map.of(
    %%     "database", "database",
    %%     "username", "login", ...
    %% );
    OptionMap = [
        %% 数据库配置
        {database, database},                             %% 目标数据库名称

        %% 认证配置
        %% 注意：配置文件中的username映射到驱动的login
        %% 这是MongoDB Erlang驱动的特殊要求
        {username, login},                                %% 用户名（配置->驱动映射）
        {password, password},                             %% 密码

        %% MongoDB 5.x中更重要的认证源选项
        %% 认证源通常是存储用户凭据的数据库，默认通常是admin
        {auth_source, auth_source},                       %% 认证数据库

        %% MongoDB 5.x认证机制配置
        %% 支持SCRAM-SHA-1、SCRAM-SHA-256等现代认证机制
        {auth_mechanism, auth_mechanism},                 %% 认证机制

        %% 读关注级别配置
        %% 直接映射到MongoDB驱动，控制读取的一致性级别
        {read_concern_level, read_concern_level}          %% 读关注级别
    ],

    % 基础选项
    BaseOptions = lists:foldl(
        fun({Key, Value}, Acc) ->
            case proplists:get_value(Key, OptionMap) of
                undefined -> Acc;
                MappedKey -> [{MappedKey, Value} | Acc]
            end
        end,
        % 初始累加器是SSL选项，其他工作者选项会加到这个列表前面
        SslOpts,
        Options
    ),

    % 处理write_concern和write_concern_timeout参数
    % 根据MongoDB Erlang驱动，需要通过w_mode参数来设置
    WriteConcern = proplists:get_value(write_concern, Options),
    WriteConcernTimeout = proplists:get_value(write_concern_timeout, Options, 10000),

    % 构建w_mode参数
    WModeOptions = case WriteConcern of
        undefined ->
            % 如果没有指定write_concern，使用默认的unsafe模式
            [{w_mode, unsafe}];
        <<"majority">> ->
            % majority写关注，带超时
            [{w_mode, {safe, #{<<"w">> => <<"majority">>, <<"wtimeout">> => WriteConcernTimeout}}}];
        majority ->
            % majority写关注，带超时
            [{w_mode, {safe, #{<<"w">> => <<"majority">>, <<"wtimeout">> => WriteConcernTimeout}}}];
        N when is_integer(N) ->
            % 数字写关注，带超时
            [{w_mode, {safe, #{<<"w">> => N, <<"wtimeout">> => WriteConcernTimeout}}}];
        Tag when is_binary(Tag) ->
            % 自定义标签写关注，带超时
            [{w_mode, {safe, #{<<"w">> => Tag, <<"wtimeout">> => WriteConcernTimeout}}}];
        _ ->
            % 其他情况使用safe模式
            [{w_mode, safe}]
    end,

    % 添加连接超时参数，防止连接挂起
    % 从配置中获取超时时间，默认30秒
    ConnectTimeout = proplists:get_value(connect_timeout, Options, 30000),
    TimeoutOptions = [{timeout, ConnectTimeout}],

    % 合并所有选项：超时选项 + write_concern选项 + 基础选项 + MongoDB 5.x兼容性选项
    % 使用自动协议检测，让MongoDB驱动自动选择最佳协议
    % 这样可以兼容MongoDB 4.x和5.x版本
    TimeoutOptions ++ WModeOptions ++ [{use_legacy_protocol, auto} | BaseOptions].

% 检查拓扑连接性 - 添加超时处理和备用方法 (辅助函数)
% @spec check_topology_connectivity(Pid :: pid()) -> ok | {error, Reason :: term()}
% 通过执行一个简单的操作来检查与MongoDB的连接。
check_topology_connectivity(Pid) ->
    try
        % 首先尝试 hello 命令 (MongoDB 5.x 推荐的命令)
        case
            with_timeout(
                fun() ->
                    % 使用更轻量的命令
                    emqx_plugin_mongodb_api:command(Pid, #{<<"ping">> => 1})
                end,
                % 减少超时时间，加快检测速度
                5000
            ) of
            % 成功
            {ok, #{<<"ok">> := 1.0}} ->
                ok;
            % 意外结果，直接尝试备用方法
            {ok, _OtherResult} ->
                try_alternative_connectivity_check(Pid);
            % 失败，尝试备用方法
            {error, _Reason} ->
                try_alternative_connectivity_check(Pid)
        end
    catch
        % 简化异常处理
        _:_:_ ->
            try_alternative_connectivity_check(Pid)
    end.

% 简化备用连接检测方法
try_alternative_connectivity_check(Pid) ->
    try
        case is_process_alive(Pid) of
            true ->
                % 进程存活，尝试简单的find操作
                case
                    with_timeout(
                        fun() ->
                            emqx_plugin_mongodb_api:find_one_safe(Pid, ?PING_COLLECTION, #{}, #{})
                        end,
                        % 减少超时时间
                        5000
                    )
                of
                    {ok, _} -> ok;
                    _ -> {error, connection_error}
                end;
            false ->
                {error, process_not_alive}
        end
    catch
        _:_ ->
            {error, connection_check_failed}
    end.

%% @doc 释放客户端连接函数，添加强制终止逻辑（辅助函数）
%% 这个函数安全地关闭与MongoDB的连接，包含超时和强制终止机制
%%
%% 功能说明：
%% 1. 尝试优雅地断开MongoDB连接
%% 2. 使用超时机制防止断开操作无限期阻塞
%% 3. 如果优雅断开失败，强制终止连接进程
%% 4. 记录详细的错误日志用于调试和监控
%%
%% 参数说明：
%% - Pid: MongoDB连接进程ID
%%
%% 返回值：
%% - ok: 总是返回ok，无论连接是否成功断开
%%
%% Java等价概念：
%% 类似于Connection.close()或MongoClient.close()
%% 或者带超时的资源清理操作
%%
%% 错误处理策略：
%% 1. 优雅断开：调用disconnect_safe
%% 2. 超时处理：强制终止进程
%% 3. 错误处理：返回错误信息
%% 4. 异常处理：使用with_log_at_error包装
deallocate_client(Pid) ->
    %% 使用with_log_at_error包装，确保在出错时记录详细日志
    %% 这是一种防御性编程策略，确保错误不会被忽略
    %% 在Java中相当于：
    %% try {
    %%     closeConnection(pid);
    %% } catch (Exception e) {
    %%     logger.error("Failed to deallocate client", e);
    %% }
    _ = with_log_at_error(
        fun() ->
            case
                %% 使用超时机制尝试优雅地断开连接
                %% 防止断开操作无限期阻塞
                with_timeout(
                    %% 尝试安全断开连接
                    %% 使用自定义的disconnect_safe函数，提供更好的错误处理
                    fun() -> emqx_plugin_mongodb_api:disconnect_safe(Pid) end,
                    %% 超时时间：使用默认超时配置
                    ?DEFAULT_TIMEOUT_MS
                )
            of
                {ok, _} ->
                    %% 连接成功断开，无需进一步操作
                    ok;
                %% 如果断开连接超时
                %% 这通常表示连接进程已经无响应
                {error, timeout} ->
                    ?SLOG(warning, #{msg => "mongodb_disconnect_timeout_force_exit", pid => Pid}),
                    %% 强制终止MongoDB连接进程
                    %% 在Java中相当于：connection.forceClose();
                    exit(Pid, kill);
                %% 其他断开连接错误
                %% 可能是网络错误、协议错误等，返回错误信息
                Error ->
                    Error
            end
        end,
        %% 传递给with_log_at_error的日志上下文
        %% 提供错误发生时的上下文信息
        #{
            msg => "failed_to_delete_mongodb_client",
            topology_pid => Pid
        }
    ),
    %% 总是返回ok，确保调用者不会因为连接断开失败而受到影响
    %% 这是一种容错设计，优先保证系统的稳定性
    ok.

%% @doc 错误日志处理（辅助函数）
%% 这个函数执行一个函数，如果函数执行过程中抛出异常，则记录错误日志
%%
%% 功能说明：
%% 1. 执行传入的函数，捕获可能的异常
%% 2. 如果发生异常，记录详细的错误日志
%% 3. 不重新抛出异常，允许程序继续执行
%% 4. 提供统一的错误日志格式和上下文信息
%%
%% 参数说明：
%% - Fun: 要执行的函数
%% - Log: 日志上下文映射，包含错误发生时的上下文信息
%%
%% 返回值：
%% - any(): 函数执行的结果，或者异常时的undefined
%%
%% Java等价概念：
%% 类似于try-catch-log模式（不重新抛出异常）
%% 或者异常吞噬的日志记录器
%%
%% 异常处理策略：
%% 1. 捕获所有类型的异常（C:E:Stack）
%% 2. 记录异常的详细信息（类别、原因、调用栈）
%% 3. 不重新抛出异常，允许程序继续执行
%%
%% 注意事项：
%% - 这里没有重新抛出异常，所以如果Fun出错，此函数会正常返回
%% - 如果需要传播错误，应该在catch块中添加erlang:raise(C, E, Stack)
with_log_at_error(Fun, Log) ->
    try
        %% 执行传入的函数
        %% 在Java中相当于：
        %% return function.apply();
        Fun()
    catch
        %% 捕获所有类型的异常
        %% C: 异常类别（error、exit、throw）
        %% E: 异常原因
        %% Stack: 调用栈信息
        %% 在Java中相当于：
        %% catch (Exception e) {
        %%     logger.error("Function execution failed", e);
        %%     // 注意：这里不重新抛出异常
        %% }
        C:E:Stack ->
            %% 合并传入的Log上下文和异常信息进行记录
            %% 提供完整的错误上下文用于调试
            ?SLOG(error, Log#{
                exception => C,           %% 异常类别
                reason => E,              %% 异常原因
                stack => Stack            %% 调用栈
            })
        %% 注意：这里没有重新抛出异常，所以如果Fun出错，此函数会正常返回
        %% 这取决于SLOG的行为，如果SLOG本身会抛异常，则会抛出SLOG的异常
        %% 通常，如果想在记录后继续传播错误，会在此处添加：
        %% erlang:raise(C, E, Stack)
    end.

% 超时处理函数 - 使用进程和timer实现真正的超时控制 (辅助函数)
% @spec with_timeout(Fun :: function(), Timeout :: non_neg_integer()) -> {ok, Result :: any()} | {error, timeout | {process_down, Reason :: any()}}
%% @doc 带超时保护的函数执行器
%% 这个函数提供了一个健壮的超时执行机制，防止函数无限等待
%%
%% 功能说明：
%% 1. 在独立进程中执行目标函数，避免阻塞调用者
%% 2. 设置超时时间，防止函数执行时间过长
%% 3. 监控执行进程，处理进程异常退出
%% 4. 提供完整的错误处理和资源清理
%%
%% 参数说明：
%% - Fun: 要执行的函数，类型为fun() -> any()
%% - Timeout: 超时时间（毫秒），超过此时间将终止执行
%%
%% 返回值：
%% - {ok, Result}: 函数在超时时间内成功执行，返回结果
%% - {error, timeout}: 函数执行超时，进程被终止
%% - {error, {process_down, Reason}}: 执行进程异常退出
%% - {error, {Class, Error, Stack}}: 函数执行过程中发生异常
%%
%% Java等价概念：
%% 类似于CompletableFuture.orTimeout()或ExecutorService.submit()配合Future.get(timeout)
%% 或者Guava的TimeLimiter.callWithTimeout()
%%
%% 使用场景：
%% - 数据库操作超时保护
%% - 网络请求超时控制
%% - 任何可能长时间运行的操作
%%
%% 示例：
%% Result = with_timeout(fun() -> expensive_operation() end, 5000)
with_timeout(Fun, Timeout) ->
    %% 获取当前进程（父进程）的PID
    %% 用于子进程向父进程发送执行结果
    Parent = self(),

    %% 创建子进程执行目标函数并监控其状态
    %% spawn_monitor/1同时创建进程和监控器，返回{Pid, MonitorRef}
    %% 类似于Java中的ExecutorService.submit()创建Future
    %%
    %% 在Java中相当于：
    %% Future<T> future = executorService.submit(() -> {
    %%     try {
    %%         return function.apply();
    %%     } catch (Exception e) {
    %%         return new ErrorResult(e);
    %%     }
    %% });
    {Pid, Ref} = spawn_monitor(
        fun() ->
            %% 在子进程中执行目标函数
            %% 使用try-catch保护函数执行，确保异常被正确捕获
            Result =
                try
                    %% 执行用户提供的函数
                    Fun()
                catch
                    %% 捕获所有类型的异常并包装成错误元组
                    %% C:E:Stack 分别是异常类别、异常内容、调用栈
                    %% 类似于Java中的try-catch(Exception e)
                    C:E:Stack ->
                        {error, {C, E, Stack}}
                end,
            %% 将执行结果发送给父进程
            %% 使用消息传递机制，类似于Java中的CompletableFuture.complete()
            %% 包含子进程PID用于消息识别
            Parent ! {self(), result, Result}
        end
    ),
    % 启动一个Erlang计时器，在Timeout毫秒后向父进程发送一个 `timeout` 消息。
    TimerRef = erlang:start_timer(Timeout, self(), timeout),
    % 父进程等待消息
    receive
        % 收到了子进程的结果
        {Pid, result, Result} ->
            % 取消计时器
            erlang:cancel_timer(TimerRef),
            % 取消监控，并清除可能已在邮箱中的 'DOWN' 消息
            demonitor(Ref, [flush]),
            % 返回成功和结果
            {ok, Result};
        % 收到了计时器发送的超时消息
        {timeout, TimerRef, _} ->
            % 强制终止子进程 (kill信号，不可捕获)
            exit(Pid, kill),
            % 清理监控
            demonitor(Ref, [flush]),
            ?SLOG(warning, #{msg => "mongodb_operation_timeout"}),
            % 返回超时错误
            {error, timeout};
        % 子进程异常退出了 (不是正常退出)
        {'DOWN', Ref, process, Pid, Reason} when Reason =/= normal ->
            % 取消计时器
            erlang:cancel_timer(TimerRef),
            % 返回进程崩溃错误
            {error, {process_down, Reason}}
    after Timeout + 100 ->
        % 额外的安全超时，以防主超时机制失效
        exit(Pid, kill),
        demonitor(Ref, [flush]),
        ?SLOG(error, #{msg => "mongodb_operation_timeout_failsafe_triggered"}),
        {error, timeout_failsafe}
    end.

%% @doc 安全批量插入消息到MongoDB（辅助函数）
%% 这个函数实现了高性能的批量插入操作，支持多集合插入优化
%%
%% 功能说明：
%% 1. 将单个消息插入到多个MongoDB集合中
%% 2. 按集合分组优化，减少数据库操作次数
%% 3. 提供完整的错误处理和重试机制
%% 4. 支持事务性操作，确保数据一致性
%%
%% 参数说明：
%% - Pid: MongoDB连接进程ID
%% - QueryList: 查询列表，格式为[{Name, Collection}, ...]
%%   - Name: 查询名称或标识符
%%   - Collection: 目标集合名称
%% - Message: 要插入的消息数据
%%
%% 返回值：
%% - ok: 所有插入操作成功
%% - {error, Reason}: 插入失败，返回错误原因
%%
%% 优化策略：
%% 1. 按集合分组，避免重复连接
%% 2. 批量插入，提高吞吐量
%% 3. 并行处理，减少总体延迟
%%
%% Java等价概念：
%% 类似于Spring Data MongoDB的批量插入
%% 或者JDBC的batch操作
%%
%% 使用场景：
%% - MQTT消息持久化到多个集合
%% - 数据复制和备份
%% - 多租户数据分发
safe_do_batch_insert(Pid, QueryList, Message) ->
    try
        %% 添加更详细的调试日志
        %% 记录批量插入操作的基本信息，用于调试和监控
        ?SLOG(debug, #{
            msg => "safe_do_batch_insert_called",
            querys_type => case is_list(QueryList) of true -> list; false -> other end,
            querys_sample => case is_list(QueryList) andalso length(QueryList) > 0 of
                true -> hd(QueryList);
                false -> QueryList
            end,
            message_type => case is_map(Message) of
                true -> map;
                false ->
                    case is_list(Message) andalso not is_binary(Message) of
                        true -> list;
                        false -> other
                    end
            end
        }),

        % 获取配置的批处理大小，如果不存在则使用默认值
        BatchSize = get(max_batch_size, ?MAX_BATCH_SIZE),

        %% 按集合名称对查询进行分组
        %% 将查询列表按集合名称分组，每个集合执行一次批量插入
        %% 这样可以减少数据库连接次数，提高性能
        CollectionGroups =
            try
                group_by_collection(QueryList)
            catch
                C1:E1:S1 ->
                    ?SLOG(error, #{
                        msg => "group_by_collection_failed",
                        error_class => C1,
                        error => E1,
                        stack => S1,
                        querys => QueryList
                    }),
                    throw({error, {group_by_collection_failed, {C1, E1}}})
            end,

        % 使用并行映射处理不同集合的插入
        Results =
            try
                % 记录处理开始
                ?SLOG(debug, #{
                    msg => "starting_batch_insert",
                    collections_count => length(CollectionGroups),
                    message_type =>
                        case is_list(Message) andalso not is_binary(Message) of
                            true -> list;
                            false -> single
                        end
                }),

                % 使用普通列表映射而不是并行映射，避免潜在的并发问题
                lists:map(
                    fun({Collection, _Names}) ->
                        try
                            % 确保Collection是二进制类型
                            BinCollection =
                                case is_binary(Collection) of
                                    true -> Collection;
                                    false ->
                                        case is_list(Collection) of
                                            true ->
                                                % 处理可能带引号的字符串
                                                CollStr = string:trim(Collection, both, "\""),
                                                list_to_binary(CollStr);
                                            false ->
                                                case is_atom(Collection) of
                                                    true -> atom_to_binary(Collection, utf8);
                                                    false ->
                                                        % 处理其他可能的类型
                                                        try
                                                            CollStr = if
                                                                is_list(Collection) -> Collection;
                                                                true -> io_lib:format("~p", [Collection])
                                                            end,
                                                            % 处理可能带引号的字符串
                                                            TrimmedStr = string:trim(CollStr, both, "\""),
                                                            list_to_binary(TrimmedStr)
                                                        catch
                                                            _:_ -> <<"unknown_collection">>
                                                        end
                                                end
                                        end
                                end,

                            % 如果Message是列表，则直接作为批量插入的文档列表
                            % 否则，将其作为单个文档插入
                            InsertDocs =
                                case is_list(Message) andalso not is_binary(Message) of
                                    true -> Message;
                                    false ->
                                        % 确保Message是一个map
                                        Doc = case is_map(Message) of
                                            true -> Message;
                                            false -> #{data => Message}
                                        end,
                                        [Doc]
                                end,

                            % 使用安全的批量插入函数，传递MongoDB连接进程ID和配置的批处理大小
                            Result = safe_batch_insert(Pid, BinCollection, InsertDocs, BatchSize),

                            % 记录插入结果
                            ?SLOG(debug, #{
                                msg => "batch_insert_result",
                                collection => BinCollection,
                                docs_count => length(InsertDocs),
                                result => Result
                            }),

                            % 特殊处理进程ID返回值
                            case Result of
                                % 如果返回的是进程ID，视为成功
                                Pid2 when is_pid(Pid2) -> ok;
                                % 其他情况保持原样
                                _ -> Result
                            end
                        catch
                            C2:E2:S2 ->
                                ?SLOG(error, #{
                                    msg => "mongodb_batch_insert_failed",
                                    collection => Collection,
                                    error_class => C2,
                                    error => E2,
                                    stack => S2
                                }),
                                {error, {Collection, {C2, E2}}}
                        end
                    end,
                    CollectionGroups
                )
            catch
                C3:E3:S3 ->
                    ?SLOG(error, #{
                        msg => "map_failed",
                        error_class => C3,
                        error => E3,
                        stack => S3
                    }),
                    throw({error, {map_failed, {C3, E3}}})
            end,

        % 检查结果
        case [E || E <- Results, E =/= ok, is_tuple(E), element(1, E) =:= error] of
            % 全部成功
            [] -> ok;
            % 有错误发生
            Errors ->
                ?SLOG(error, #{
                    msg => "batch_insert_errors",
                    errors_count => length(Errors),
                    first_error => hd(Errors)
                }),
                {error, Errors}
        end
    catch
        throw:{error, Reason} ->
            {error, Reason};
        C4:E4:S4 ->
            ?SLOG(error, #{
                msg => "do_batch_insert_fatal_error",
                error_class => C4,
                error => E4,
                stack => S4
            }),
            {error, {batch_insert_failed, {C4, E4}}}
    end.

% 获取进程字典中的值，如果不存在则返回默认值
get(Key, Default) ->
    case get(Key) of
        undefined -> Default;
        Value -> Value
    end.

% 将文档列表分割成多个批次 (辅助函数)
split_to_batches(Docs, BatchSize) when is_list(Docs) ->
    split_to_batches(Docs, BatchSize, []).

split_to_batches([], _BatchSize, Acc) ->
    lists:reverse(Acc);
split_to_batches(Docs, BatchSize, Acc) ->
    {Batch, Rest} =
        case length(Docs) =< BatchSize of
            true -> {Docs, []};
            false -> lists:split(BatchSize, Docs)
        end,
    split_to_batches(Rest, BatchSize, [Batch | Acc]).

% 按集合分组查询 (辅助函数)
% @spec group_by_collection(Querys :: list({Name :: any(), Collection :: binary()})) -> list({Collection :: binary(), Names :: list(any())})
% 将查询列表按目标集合名称进行分组。
% 例如: [{n1, c1}, {n2, c2}, {n3, c1}] -> [{c1, [n3, n1]}, {c2, [n2]}]
group_by_collection(Querys) ->
    lists:foldl(
        % 对每个查询 {Name, Collection} 进行处理
        fun({Name, Collection}, Acc) ->
            % 在累加器Acc中查找是否已有此Collection
            case lists:keyfind(Collection, 1, Acc) of
                % 如果没有
                false ->
                    % 添加新的 {Collection, [Name]} 到Acc
                    [{Collection, [Name]} | Acc];
                % 如果已存在
                {Collection, Names} ->
                    % 更新Names列表
                    lists:keyreplace(Collection, 1, Acc, {Collection, [Name | Names]})
            end
        end,
        % 初始累加器为空列表
        [],
        Querys
    ).

% 检测 MongoDB 版本
% @spec detect_mongodb_version(Pid :: pid()) -> binary() | undefined
% 通过执行 buildInfo 命令获取 MongoDB 版本信息
detect_mongodb_version(Pid) ->
    try
        case
            with_timeout(
                fun() ->
                    % 使用标准的 MongoDB 命令格式执行 buildInfo 命令
                    emqx_plugin_mongodb_api:command(Pid, #{<<"buildInfo">> => 1})
                end,
                ?DEFAULT_TIMEOUT_MS
            ) of
            {ok, #{<<"version">> := Version}} when is_binary(Version) ->
                Version;
            {ok, #{<<"versionString">> := Version}} when is_binary(Version) ->
                Version;
            {ok, _} ->
                ?SLOG(warning, #{msg => "mongodb_version_detection_unexpected_response"}),
                <<"unknown">>;
            {error, Reason} ->
                ?SLOG(warning, #{
                    msg => "mongodb_version_detection_failed",
                    reason => Reason
                }),
                <<"unknown">>
        end
    catch
        Class:Error:Stack ->
            ?SLOG(warning, #{
                msg => "mongodb_version_detection_exception",
                class => Class,
                error => Error,
                stack => Stack
            }),
            <<"unknown">>
    end.

% 初始化指标
% @spec init_metrics(InstId :: binary()) -> ok
% 初始化指标，为每个资源实例创建指标记录
init_metrics(InstId) ->
    % 创建指标记录
    MetricRecord = #{
        instance_id => InstId,
        insert_count => 0,
        insert_error => 0,
        query_latency => 0,
        connection_failure => 0
    },
    % 将指标记录存储在模块的进程字典中
    % 使用 {metrics, InstId} 作为键，避免不同实例之间的冲突
    put({metrics, InstId}, MetricRecord),
    ok.

% 增加指标计数
% @spec inc_metric(InstId :: binary(), MetricName :: atom(), Value :: integer()) -> ok
% 增加指定指标的计数
inc_metric(InstId, MetricName, Value) ->
    try
        % 获取指标记录，如果不存在则创建一个新的
        MetricRecord =
            case get({metrics, InstId}) of
                undefined ->
                    % 如果指标记录不存在，创建一个新的
                    #{
                        instance_id => InstId,
                        insert_count => 0,
                        insert_error => 0,
                        query_latency => 0,
                        connection_failure => 0
                    };
                Record ->
                    Record
            end,

        % 安全地更新指标计数
        CurrentValue = maps:get(MetricName, MetricRecord, 0),
        UpdatedRecord = maps:put(MetricName, CurrentValue + Value, MetricRecord),

        % 将更新后的指标记录存储在模块的进程字典中
        put({metrics, InstId}, UpdatedRecord),
        ok
    catch
        _:_ -> ok
    end.

% 记录延迟指标
% @spec observe_latency(InstId :: binary(), Latency :: integer()) -> ok
% 记录指定资源的查询延迟指标
observe_latency(InstId, Latency) ->
    % 获取指标记录，如果不存在则创建一个新的
    MetricRecord =
        case get({metrics, InstId}) of
            undefined ->
                % 如果指标记录不存在，创建一个新的
                #{
                    instance_id => InstId,
                    insert_count => 0,
                    insert_error => 0,
                    query_latency => 0,
                    connection_failure => 0
                };
            Record ->
                Record
        end,

    % 安全地更新查询延迟指标
    CurrentLatency = maps:get(query_latency, MetricRecord, 0),
    UpdatedRecord = maps:put(query_latency, CurrentLatency + Latency, MetricRecord),

    % 将更新后的指标记录存储在模块的进程字典中
    put({metrics, InstId}, UpdatedRecord),
    ok.

% 批量处理服务器
start_batch_server() ->
    % 检查进程名是否已被注册
    case whereis(mongodb_batch_server) of
        undefined ->
            % 进程名未注册，正常启动
            BatchServerPid = spawn_link(fun() -> batch_server_loop(#{}, 0) end),
            try
                register(mongodb_batch_server, BatchServerPid),
                ?SLOG(info, #{msg => "batch_server_started_successfully", pid => BatchServerPid}),
                BatchServerPid
            catch
                error:badarg ->
                    % 注册失败，可能是并发注册，杀掉进程并返回错误
                    exit(BatchServerPid, kill),
                    ?SLOG(error, #{msg => "failed_to_register_batch_server_name"}),
                    {error, registration_failed}
            end;
        ExistingPid when is_pid(ExistingPid) ->
            % 进程名已存在，检查进程是否存活
            case is_process_alive(ExistingPid) of
                true ->
                    ?SLOG(info, #{msg => "batch_server_already_running", pid => ExistingPid}),
                    ExistingPid;
                false ->
                    % 进程已死，先注销再重新注册
                    try
                        unregister(mongodb_batch_server),
                        timer:sleep(50), % 等待注销完成
                        BatchServerPid = spawn_link(fun() -> batch_server_loop(#{}, 0) end),
                        register(mongodb_batch_server, BatchServerPid),
                        ?SLOG(info, #{msg => "batch_server_restarted_after_cleanup", pid => BatchServerPid}),
                        BatchServerPid
                    catch
                        error:badarg ->
                            ?SLOG(error, #{msg => "failed_to_restart_batch_server_after_cleanup"}),
                            {error, restart_failed}
                    end
            end
    end.

% 停止批处理服务器
stop_batch_server() ->
    case whereis(mongodb_batch_server) of
        undefined ->
            ?SLOG(info, #{msg => "batch_server_not_running"}),
            ok;
        Pid when is_pid(Pid) ->
            try
                % 先注销进程名
                unregister(mongodb_batch_server),
                % 然后终止进程
                exit(Pid, shutdown),
                % 等待进程退出
                wait_for_process_exit(Pid, 5000),
                ?SLOG(info, #{msg => "batch_server_stopped_successfully", pid => Pid}),
                ok
            catch
                error:badarg ->
                    % 进程名已被注销，直接终止进程
                    exit(Pid, shutdown),
                    wait_for_process_exit(Pid, 5000),
                    ?SLOG(info, #{msg => "batch_server_stopped_after_unregister", pid => Pid}),
                    ok;
                _:Reason ->
                    ?SLOG(warning, #{msg => "failed_to_stop_batch_server", reason => Reason, pid => Pid}),
                    % 强制终止进程
                    exit(Pid, kill),
                    ok
            end
    end.

% 等待进程退出的辅助函数
wait_for_process_exit(Pid, Timeout) ->
    wait_for_process_exit(Pid, Timeout, 100).

wait_for_process_exit(_Pid, 0, _Interval) ->
    timeout;
wait_for_process_exit(Pid, Timeout, Interval) ->
    case is_process_alive(Pid) of
        true ->
            timer:sleep(Interval),
            wait_for_process_exit(Pid, Timeout - Interval, Interval);
        false ->
            ok
    end.

% 批量服务器循环
batch_server_loop(BatchMap, QueueSize) ->
    % 获取配置的批处理超时和最大队列大小
    BatchTimeout = get(batch_timeout_ms, ?BATCH_TIMEOUT_MS),
    MaxQueueSize = get(max_batch_queue_size, ?MAX_BATCH_QUEUE_SIZE),

    receive
        {batch_insert, Collection, Doc, From} ->
            % 添加到批次
            NewBatchMap = add_to_batch(BatchMap, Collection, Doc, From),
            NewQueueSize = QueueSize + 1,

            % 检查是否需要立即处理
            case NewQueueSize >= MaxQueueSize of
                true ->
                    % 队列太大，立即处理
                    process_all_batches(NewBatchMap),
                    batch_server_loop(#{}, 0);
                false ->
                    % 继续收集批次
                    batch_server_loop(NewBatchMap, NewQueueSize)
            end;
        process_batches ->
            % 处理所有批次
            process_all_batches(BatchMap),
            batch_server_loop(#{}, 0)
    after BatchTimeout ->
        % 超时，处理所有批次
        case map_size(BatchMap) of
            0 ->
                batch_server_loop(#{}, 0);
            _ ->
                process_all_batches(BatchMap),
                batch_server_loop(#{}, 0)
        end
    end.

% 添加到批次
add_to_batch(BatchMap, Collection, Doc, From) ->
    case maps:get(Collection, BatchMap, undefined) of
        undefined ->
            BatchMap#{Collection => {[Doc], [From]}};
        {Docs, Froms} ->
            BatchMap#{Collection => {[Doc | Docs], [From | Froms]}}
    end.

% 处理所有批次
process_all_batches(BatchMap) ->
    maps:foreach(
        fun(Collection, {Docs, Froms}) ->
            Result = safe_batch_insert(Collection, lists:reverse(Docs), get(max_batch_size, ?MAX_BATCH_SIZE)),
            % 通知所有等待的进程
            [From ! {batch_result, Result} || From <- Froms]
        end,
        BatchMap
    ).

% 增强性能指标
-define(METRICS, [
    insert_count,
    insert_error,
    query_latency,
    connection_failure,
    batch_size,
    queue_length,
    circuit_breaker_trips
]).

% 初始化指标
init_metrics() ->
    try
        [begin
            Counter = counters:new(1, [write_concurrency]),
            persistent_term:put({mongodb_metric, Metric}, Counter),
            Counter
         end || Metric <- ?METRICS]
    catch
        _:_ ->
            ?SLOG(warning, #{msg => "failed_to_init_metrics"}),
            []
    end.

% 记录操作延迟直方图
record_latency(Operation, StartTime) ->
    try
        EndTime = erlang:system_time(microsecond),
        Latency = EndTime - StartTime,
        % 将延迟分桶记录
        Bucket = get_latency_bucket(Latency),
        % 尝试获取计数器，如果不存在则创建
        Counter =
            try
                persistent_term:get({mongodb_latency, Operation, Bucket})
            catch
                _:_ ->
                    % 如果计数器不存在，创建一个新的
                    NewCounter = counters:new(1, [write_concurrency]),
                    persistent_term:put({mongodb_latency, Operation, Bucket}, NewCounter),
                    NewCounter
            end,
        counters:add(Counter, 1, 1),
        Latency
    catch
        _:_ -> 0
    end.

% 获取延迟桶
get_latency_bucket(Latency) ->
    Buckets = [0, 1, 5, 10, 25, 50, 100, 250, 500, 1000, 2500, 5000, 10000],
    get_latency_bucket(Latency, Buckets).

get_latency_bucket(_Latency, []) ->
    infinity;
get_latency_bucket(Latency, [Bucket | Rest]) ->
    if
        Latency =< Bucket -> Bucket;
        true -> get_latency_bucket(Latency, Rest)
    end.

% 导出指标为Prometheus格式
export_metrics_prometheus() ->
    % 实现指标导出逻辑...
    ok.

% 安全的批量插入操作，处理异常和错误
safe_batch_insert(Pid, Collection, Docs, BatchSize) ->
    try
        % 限制批量大小
        DocBatches = split_to_batches(Docs, BatchSize),

        % 记录更多详细的调试信息
        ?SLOG(debug, #{
            msg => "safe_batch_insert_called",
            collection => Collection,
            total_docs => length(Docs),
            batches_count => length(DocBatches),
            docs_sample => case Docs of
                [FirstDoc|_] when is_map(FirstDoc) ->
                    % 记录第一个文档的关键字段
                    try
                        % 检查payload字段
                        case maps:get(payload, FirstDoc, undefined) of
                            undefined -> #{sample_type => no_payload};
                            Payload when is_map(Payload) ->
                                #{
                                    sample_type => map_payload,
                                    payload_keys => maps:keys(Payload)
                                };
                            Payload when is_binary(Payload) ->
                                case jsx:is_json(Payload) of
                                    true -> #{sample_type => json_binary_payload};
                                    false -> #{sample_type => plain_binary_payload}
                                end;
                            _ -> #{sample_type => other_payload}
                        end
                    catch
                        _:_ -> #{sample_error => true}
                    end;
                _ -> #{sample_type => not_map_or_empty}
            end
        }),

        % 处理每个批次
        Results = lists:map(
            fun(Batch) ->
                try
                    % 确保Pid是有效的进程ID
                    case is_pid(Pid) of
                        true ->
                            % 确保每个文档的JSON格式正确处理
                            ProcessedBatch = [process_message_for_mongodb(Doc) || Doc <- Batch],

                            % 直接调用插入函数，不使用with_timeout包装
                            try
                                % 记录更详细的调试信息
                                ?SLOG(debug, #{
                                    msg => "mongodb_before_insert",
                                    collection => Collection,
                                    batch_size => length(ProcessedBatch),
                                    first_doc_sample => case ProcessedBatch of
                                        [First|_] ->
                                            % 安全地获取样本，避免大文档
                                            case is_map(First) of
                                                true ->
                                                    % 只取前几个键值对作为样本
                                                    Keys = lists:sublist(maps:keys(First), 3),
                                                    maps:with(Keys, First);
                                                false -> First
                                            end;
                                        [] -> undefined
                                    end
                                }),

                                % 使用扩展API进行批量插入
                                case emqx_plugin_mongodb_api:bulk_insert(Pid, Collection, ProcessedBatch) of
                                    #{inserted_count := Count, errors := []} ->
                                        ?SLOG(debug, #{
                                            msg => "bulk_insert_success",
                                            collection => Collection,
                                            inserted_count => Count
                                        });
                                    #{errors := Errors} when length(Errors) > 0 ->
                                        ?SLOG(warning, #{
                                            msg => "bulk_insert_partial_failure",
                                            collection => Collection,
                                            errors => Errors
                                        });
                                    Other ->
                                        ?SLOG(debug, #{
                                            msg => "bulk_insert_result",
                                            collection => Collection,
                                            result => Other
                                        })
                                end,

                                % 假设成功，简单返回ok
                                ?SLOG(debug, #{
                                    msg => "mongodb_insert_success",
                                    collection => Collection,
                                    docs_count => length(ProcessedBatch)
                                }),
                                ok
                            catch
                                EC:EE:ES ->
                                    ?SLOG(error, #{
                                        msg => "mongodb_insert_direct_exception",
                                        collection => Collection,
                                        error_class => EC,
                                        error => EE,
                                        stack => ES
                                    }),
                                    {error, {insert_exception, {EC, EE}}}
                            end;
                        false ->
                            {error, invalid_mongodb_pid}
                    end
                catch
                    C2:E2:S2 ->
                        ?SLOG(error, #{
                            msg => "batch_insert_exception",
                            collection => Collection,
                            error_class => C2,
                            error => E2,
                            stack => S2
                        }),
                        {error, {C2, E2}}
                end
            end,
            DocBatches
        ),

        % 检查所有批次是否都成功
        case lists:keyfind(error, 1, Results) of
            % 全部批次都成功
            false -> ok;
            % 返回第一个错误
            Error -> Error
        end
    catch
        throw:{error, Reason} ->
            {error, Reason};
        C3:E3:S3 ->
            ?SLOG(error, #{
                msg => "safe_batch_insert_fatal_error",
                collection => Collection,
                docs_count => length(Docs),
                error_class => C3,
                error => E3,
                stack => S3
            }),
            {error, {batch_insert_failed, {C3, E3}}}
    end.

% 为了兼容性保留3参数版本，内部调用4参数版本
safe_batch_insert(Collection, Docs, BatchSize) ->
    try
        % 获取当前MongoDB连接
        PidResult =
            try
                % 首先尝试从persistent_term获取
                case persistent_term:get(?PLUGIN_MONGODB_RESOURCE_ID) of
                    undefined ->
                        % 如果不存在，尝试从进程字典获取
                        case get(mongodb_connection_pid) of
                            undefined ->
                                ?SLOG(warning, #{
                                    msg => "mongodb_connection_not_found_in_both_persistent_term_and_process_dictionary"
                                }),
                                throw({error, resource_not_found});
                            CachedPid ->
                                % 检查进程是否存活
                                case is_process_alive(CachedPid) of
                                    true ->
                                        % 尝试重新存储到persistent_term
                                        try
                                            persistent_term:put(?PLUGIN_MONGODB_RESOURCE_ID, CachedPid),
                                            ?SLOG(info, #{
                                                msg => "restored_mongodb_resource_id_from_process_dictionary"
                                            })
                                        catch
                                            _:_ -> ok
                                        end,
                                        CachedPid;
                                    false ->
                                        ?SLOG(warning, #{msg => "cached_mongodb_connection_pid_not_alive"}),
                                        throw({error, cached_pid_not_alive})
                                end
                        end;
                    P -> P
                end
            catch
                C1:E1:S1 ->
                    ?SLOG(error, #{
                        msg => "failed_to_get_mongodb_resource",
                        error_class => C1,
                        error => E1,
                        stack => S1
                    }),
                    throw({error, resource_not_found})
            end,

        % 调用4参数版本
        safe_batch_insert(PidResult, Collection, Docs, BatchSize)
    catch
        throw:{error, Reason} ->
            {error, Reason};
        C3:E3:S3 ->
            ?SLOG(error, #{
                msg => "safe_batch_insert_3params_fatal_error",
                collection => Collection,
                docs_count => length(Docs),
                error_class => C3,
                error => E3,
                stack => S3
            }),
            {error, {batch_insert_failed, {C3, E3}}}
    end.

% 检查是否应该导出Prometheus指标
should_export_prometheus(Connection) ->
    case maps:get(metrics, Connection, #{}) of
        #{enable := true, prometheus_export := true} -> true;
        _ -> false
    end.

% 在emqx_plugin_mongodb_connector.erl中添加
% 错误分类函数
classify_mongodb_error(Error) ->
    case Error of
        {connection_failure, _} ->
            connection_error;
        {not_master, _} ->
            topology_error;
        {database_down, _} ->
            availability_error;
        {write_concern_error, _} ->
            write_concern_error;
        {unauthorized, _} ->
            authentication_error;
        {timeout, _} ->
            timeout_error;
        {socket_error, _} ->
            network_error;
        _ ->
            unknown_error
    end.

% 在错误处理中使用
handle_mongodb_error(Error, _Collection, _Documents) ->
    ErrorType = classify_mongodb_error(Error),
    % 增加指定类型的错误计数
    inc_error_counter(ErrorType),

    % 根据错误类型决定重试策略
    case ErrorType of
        connection_error ->
            % 立即启动重连过程
            spawn(fun() -> reconnect() end),
            {retry, 5000};
        topology_error ->
            % 重新获取拓扑信息后重试
            spawn(fun() -> refresh_topology() end),
            {retry, 2000};
        timeout_error ->
            % 超时错误，短暂等待后重试
            {retry, 1000};
        authentication_error ->
            % 认证错误，不重试
            {error, auth_failed};
        _ ->
            % 其他错误，使用默认重试策略
            {retry, 3000}
    end.

% 添加缺失的重连函数
reconnect() ->
    try
        % 获取当前MongoDB连接
        case get(mongodb_connection_pid) of
            undefined ->
                ?SLOG(warning, #{msg => "cannot_reconnect_no_cached_pid"});
            Pid when is_pid(Pid) ->
                % 检查进程是否还活着
                case is_process_alive(Pid) of
                    true ->
                        % 先尝试断开连接
                        _ = deallocate_client(Pid),
                        % 等待短暂时间后重新尝试连接
                        timer:sleep(1000);
                    false ->
                        ?SLOG(warning, #{msg => "mongodb_connection_already_dead"})
                end
        end,
        ?SLOG(info, #{msg => "mongodb_reconnect_initiated"})
    catch
        C:E:S ->
            ?SLOG(error, #{
                msg => "mongodb_reconnect_failed",
                error_class => C,
                error => E,
                stack => S
            })
    end.

% 添加缺失的拓扑刷新函数
refresh_topology() ->
    try
        % 获取当前MongoDB连接
        case get(mongodb_connection_pid) of
            undefined ->
                ?SLOG(warning, #{msg => "cannot_refresh_topology_no_cached_pid"});
            Pid when is_pid(Pid) ->
                % 尝试ping一下MongoDB以刷新拓扑
                case with_timeout(
                    fun() -> emqx_plugin_mongodb_api:command(Pid, #{<<"ping">> => 1}) end,
                    5000
                ) of
                    {ok, _} ->
                        ?SLOG(info, #{msg => "mongodb_topology_refresh_successful"});
                    {error, Reason} ->
                        ?SLOG(warning, #{
                            msg => "mongodb_topology_refresh_failed",
                            reason => Reason
                        })
                end
        end
    catch
        C:E:S ->
            ?SLOG(error, #{
                msg => "mongodb_topology_refresh_exception",
                error_class => C,
                error => E,
                stack => S
            })
    end.

%% @doc 使用零拷贝技术进行批量插入
%% 通过直接传递二进制数据到MongoDB驱动，避免多次复制
zero_copy_batch_insert(Collection, Docs, BatchSize) ->
    try
        % 获取MongoDB连接
        PidResult =
            try
                % 首先尝试从persistent_term获取
                case persistent_term:get(?PLUGIN_MONGODB_RESOURCE_ID) of
                    undefined ->
                        % 如果不存在，尝试从进程字典获取
                        case get(mongodb_connection_pid) of
                            undefined ->
                                ?SLOG(warning, #{
                                    msg => "mongodb_connection_not_found_in_both_persistent_term_and_process_dictionary"
                                }),
                                throw({error, resource_not_found});
                            CachedPid ->
                                % 检查进程是否存活
                                case is_process_alive(CachedPid) of
                                    true -> CachedPid;
                                    false ->
                                        ?SLOG(warning, #{msg => "cached_mongodb_connection_pid_not_alive"}),
                                        throw({error, cached_pid_not_alive})
                                end
                        end;
                    P -> P
                end
            catch
                C1:E1:S1 ->
                    ?SLOG(error, #{
                        msg => "failed_to_get_mongodb_resource",
                        error_class => C1,
                        error => E1,
                        stack => S1
                    }),
                    throw({error, resource_not_found})
            end,

        % 限制批量大小
        DocBatches = split_to_batches(Docs, BatchSize),

        % 处理每个批次
        Results = lists:map(
            fun(Batch) ->
                try
                    % 确保Pid是有效的进程ID
                    case is_pid(PidResult) of
                        true ->
                            % 创建零拷贝缓冲区
                            {Buffer, DocCount, BufferId} = prepare_zero_copy_buffer(Batch),

                            % 直接使用缓冲区内容调用MongoDB驱动
                            try
                                % 直接调用零拷贝插入函数
                                zero_copy_insert(PidResult, Collection, {Buffer, DocCount, 0}, DocCount),

                                % 释放缓冲区
                                emqx_plugin_mongodb_zero_copy:release_buffer(BufferId),

                                % 假设成功，简单返回ok
                                ?SLOG(debug, #{
                                    msg => "mongodb_zero_copy_insert_success",
                                    collection => Collection,
                                    docs_count => length(Batch)
                                }),
                                ok
                            catch
                                EC:EE:ES ->
                                    ?SLOG(error, #{
                                        msg => "mongodb_zero_copy_insert_exception",
                                        collection => Collection,
                                        error_class => EC,
                                        error => EE,
                                        stack => ES
                                    }),
                                    {error, {insert_exception, {EC, EE}}}
                            end;
                        false ->
                            {error, invalid_mongodb_pid}
                    end
                catch
                    C2:E2:S2 ->
                        ?SLOG(error, #{
                            msg => "zero_copy_batch_insert_exception",
                            collection => Collection,
                            error_class => C2,
                            error => E2,
                            stack => S2
                        }),
                        {error, {C2, E2}}
                end
            end,
            DocBatches
        ),

        % 检查所有批次是否都成功
        case lists:keyfind(error, 1, Results) of
            % 全部批次都成功
            false -> ok;
            % 返回第一个错误
            Error -> Error
        end
    catch
        throw:{error, Reason} ->
            {error, Reason};
        C3:E3:S3 ->
            ?SLOG(error, #{
                msg => "zero_copy_batch_insert_fatal_error",
                collection => Collection,
                docs_count => length(Docs),
                error_class => C3,
                error => E3,
                stack => S3
            }),
            {error, {batch_insert_failed, {C3, E3}}}
    end.

%% @doc 准备零拷贝缓冲区
prepare_zero_copy_buffer(Documents) ->
    % 使用零拷贝模块准备文档
    % 确保零拷贝模块已初始化
    emqx_plugin_mongodb_zero_copy:init(),

    % 使用零拷贝模块准备文档
    {Buffer, DocCount, BufferId} = emqx_plugin_mongodb_zero_copy:prepare_documents(Documents),

    % 注意：这里我们返回BufferId以便后续释放缓冲区
    {Buffer, DocCount, BufferId}.

% 检查是否应该导出Prometheus指标

%% @doc 零拷贝插入实现
zero_copy_insert(Connection, Collection, {Buffer, Count, _Size}, DocCount) ->
    % 使用现有的insert函数，但避免不必要的数据转换和复制
    % 在实际生产环境中，这里可以使用MongoDB驱动的底层API直接传输二进制数据
    % 或者扩展驱动添加专门的零拷贝API
    emqx_plugin_mongodb_api:smart_insert(Connection, Collection, Buffer).

%% 添加会话持久化相关的MongoDB操作函数

%% 保存会话信息
save_session(Pool, Collection, ClientId, Doc) ->
    Filter = #{<<"client_id">> => ClientId},
    Command = #{<<"$set">> => Doc},
    Options = #{<<"upsert">> => true},

    try
        Result = with_timeout(?INSERT_TIMEOUT_MS, fun() ->
            mongodb_api:update(Pool, Collection, Filter, Command, Options)
        end),
        case Result of
            {ok, _} = Ok ->
                record_success(),
                Ok;
            {error, Reason} = Error ->
                record_failure(query_error),
                ?SLOG(error, #{msg => "failed_to_save_session", client_id => ClientId, reason => Reason}),
                Error
        end
    catch
        E:R:S ->
            record_failure(timeout_error),
            ?SLOG(error, #{
                msg => "error_saving_session",
                error => E,
                reason => R,
                stacktrace => S,
                client_id => ClientId
            }),
            {error, {E, R}}
    end.

%% 保存订阅信息
save_subscription(Pool, Collection, ClientId, Topic, Doc) ->
    Filter = #{<<"client_id">> => ClientId, <<"topic">> => Topic},
    Command = #{<<"$set">> => Doc},
    Options = #{<<"upsert">> => true},

    try
        Result = with_timeout(?INSERT_TIMEOUT_MS, fun() ->
            mongodb_api:update(Pool, Collection, Filter, Command, Options)
        end),
        case Result of
            {ok, _} = Ok ->
                record_success(),
                Ok;
            {error, Reason} = Error ->
                record_failure(query_error),
                ?SLOG(error, #{msg => "failed_to_save_subscription", client_id => ClientId, topic => Topic, reason => Reason}),
                Error
        end
    catch
        E:R:S ->
            record_failure(timeout_error),
            ?SLOG(error, #{
                msg => "error_saving_subscription",
                error => E,
                reason => R,
                stacktrace => S,
                client_id => ClientId,
                topic => Topic
            }),
            {error, {E, R}}
    end.

%% 删除订阅信息
delete_subscription(Pool, Collection, ClientId, Topic) ->
    Filter = #{<<"client_id">> => ClientId, <<"topic">> => Topic},

    try
        Result = with_timeout(?INSERT_TIMEOUT_MS, fun() ->
            mongodb_api:delete(Pool, Collection, Filter)
        end),
        case Result of
            {ok, _} = Ok ->
                record_success(),
                Ok;
            {error, Reason} = Error ->
                record_failure(query_error),
                ?SLOG(error, #{msg => "failed_to_delete_subscription", client_id => ClientId, topic => Topic, reason => Reason}),
                Error
        end
    catch
        E:R:S ->
            record_failure(timeout_error),
            ?SLOG(error, #{
                msg => "error_deleting_subscription",
                error => E,
                reason => R,
                stacktrace => S,
                client_id => ClientId,
                topic => Topic
            }),
            {error, {E, R}}
    end.

%% 更新会话状态
update_session_status(Pool, Collection, ClientId, UpdateDoc) ->
    Filter = #{<<"client_id">> => ClientId},
    Command = #{<<"$set">> => UpdateDoc},

    try
        Result = with_timeout(?INSERT_TIMEOUT_MS, fun() ->
            mongodb_api:update(Pool, Collection, Filter, Command, #{})
        end),
        case Result of
            {ok, _} = Ok ->
                record_success(),
                Ok;
            {error, Reason} = Error ->
                record_failure(query_error),
                ?SLOG(error, #{msg => "failed_to_update_session_status", client_id => ClientId, reason => Reason}),
                Error
        end
    catch
        E:R:S ->
            record_failure(timeout_error),
            ?SLOG(error, #{
                msg => "error_updating_session_status",
                error => E,
                reason => R,
                stacktrace => S,
                client_id => ClientId
            }),
            {error, {E, R}}
    end.

%% 查找需要恢复的会话
find_sessions_to_restore(Pool, Collection) ->
    Filter = #{
        <<"status">> => #{<<"$in">> => [<<"created">>, <<"connected">>, <<"disconnected">>]},
        <<"expiry_time">> => #{<<"$gt">> => erlang:system_time(millisecond)}
    },
    % 使用更安全的批次大小，避免内存问题
    BatchSize = application:get_env(emqx_plugin_mongodb, session_restore_batch_size, 500),
    Options = #{
        <<"limit">> => BatchSize,
        <<"sort">> => #{<<"updated_at">> => -1}
    },

    try
        Result = with_timeout(?INSERT_TIMEOUT_MS, fun() ->
            mongodb_api:find(Pool, Collection, Filter, Options)
        end),
        case Result of
            {ok, Cursor} ->
                % 获取所有文档
                AllDocs = mongodb_cursor:to_list(Cursor),
                record_success(),
                {ok, AllDocs};
            {error, Reason} = Error ->
                record_failure(query_error),
                ?SLOG(error, #{msg => "failed_to_find_sessions_to_restore", reason => Reason}),
                Error
        end
    catch
        E:R:S ->
            record_failure(timeout_error),
            ?SLOG(error, #{
                msg => "error_finding_sessions_to_restore",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%% @doc 获取数据库名称（辅助函数）
%% 这个函数从MongoDB连接中获取当前使用的数据库名称
%%
%% 功能说明：
%% 1. 使用connectionStatus命令获取连接状态信息
%% 2. 从认证信息中提取数据库名称
%% 3. 如果认证信息不可用，尝试从isMaster命令获取
%% 4. 从连接字符串中解析数据库名称，失败时使用默认值
%%
%% 参数说明：
%% - Pid: MongoDB连接进程ID
%%
%% 返回值：
%% - {ok, DbName}: 成功获取数据库名称
%% - {error, {failed_to_get_db_name, Reason}}: 获取失败
%%
%% Java等价概念：
%% 类似于MongoDatabase.getName()
%% 或者Connection.getCatalog()
%%
%% 实现策略：
%% 1. 优先从认证用户角色信息中获取
%% 2. 备选方案：从连接字符串中解析
%% 3. 最终后备：使用默认数据库名"emqx"
%% 4. 提供完整的错误处理和日志记录
get_database_name(Pid) ->
    try
        %% 使用MongoDB connectionStatus命令获取当前连接的状态信息
        %% 这个命令返回连接的详细信息，包括认证状态
        %% 在Java中相当于：
        %% Document result = database.runCommand(new Document("connectionStatus", 1));
        Command = #{<<"connectionStatus">> => 1},
        {ok, Result} = emqx_plugin_mongodb_api:command(Pid, Command),

        %% 尝试从认证信息中获取数据库名称
        %% 认证用户通常关联到特定的数据库
        case maps:get(<<"authInfo">>, Result, #{}) of
            #{<<"authenticatedUserRoles">> := [#{<<"db">> := DbName} | _]} ->
                %% 从认证用户角色中获取数据库名称
                %% 这是最可靠的方法，因为用户必须在特定数据库中认证
                {ok, DbName};
            _ ->
                %% 如果没有认证信息，尝试从进程状态中获取
                %% 使用isMaster命令获取连接信息
                %% 在Java中相当于：
                %% Document isMaster = database.runCommand(new Document("isMaster", 1));
                case emqx_plugin_mongodb_api:command(Pid, #{<<"isMaster">> => 1}) of
                    {ok, #{<<"me">> := ConnectionString}} ->
                        %% 从连接字符串中提取数据库名
                        %% 连接字符串格式通常为：host:port/database
                        %% 在Java中相当于：
                        %% String[] parts = connectionString.split("/");
                        %% return parts.length > 1 ? parts[1] : "emqx";
                        case binary:split(ConnectionString, <<"/">>, [global]) of
                            [_, DbBin | _] ->
                                %% 成功从连接字符串中提取数据库名
                                {ok, DbBin};
                            _ ->
                                %% 连接字符串格式不符合预期，使用默认数据库名
                                %% 这是一种优雅降级策略
                                {ok, <<"emqx">>}
                        end;
                    _ ->
                        %% isMaster命令失败，使用默认数据库名
                        %% 确保函数总是能返回一个可用的数据库名
                        {ok, <<"emqx">>}
                end
        end
    catch
        %% 捕获所有异常，记录详细的错误信息
        %% 这确保函数不会因为异常而崩溃
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_getting_database_name",
                error => E,                    %% 异常类别
                reason => R,                   %% 异常原因
                stacktrace => S                %% 调用栈
            }),
            %% 返回结构化的错误信息
            {error, {failed_to_get_db_name, R}}
    end.

%% @doc 为主题过滤集合创建索引 - 使用统一命名规则（辅助函数）
%% 这个函数为主题过滤集合创建必要的索引，提高查询性能
%%
%% 功能说明：
%% 1. 生成集合缩写，用于索引命名的统一规范
%% 2. 创建消息ID唯一索引，确保消息不重复
%% 3. 创建客户端ID、主题、时间戳等单字段索引
%% 4. 创建复合索引，支持高效的范围查询
%%
%% 参数说明：
%% - Pid: MongoDB连接进程ID
%% - Collection: 主题过滤集合名称
%%
%% 返回值：
%% - {ok, indices_created}: 索引创建成功
%% - {error, {failed_to_create_indices, Reason}}: 索引创建失败
%%
%% Java等价概念：
%% 类似于JPA的@Index注解或MongoDB Java驱动的createIndex()
%% 或者Spring Data MongoDB的索引创建
%%
%% 索引设计：
%% - message_id: 唯一索引，防止消息重复
%% - client_id: 单字段索引，支持按客户端查询
%% - topic: 单字段索引，支持主题查询
%% - expiry_time: 单字段索引，支持过期清理
%% - status: 单字段索引，支持状态过滤
%% - timestamp: 单字段索引，支持时间排序
%% - qos: 单字段索引，支持QoS过滤
%% - topic+timestamp: 复合索引，支持范围查询
create_message_indices(Pid, Collection) ->
    try
        %% 生成集合缩写，用于索引命名的统一规范
        %% 例如：emqx_mqtt_data -> emd
        %% 这有助于保持索引名称的简洁和一致性
        DataAbbr = generate_collection_abbreviation(Collection),

        %% 记录索引创建开始的日志
        ?SLOG(info, #{
            msg => "creating_topic_filter_indexes",
            collection => Collection,
            collection_abbr => DataAbbr
        }),

        %% 定义主题过滤集合的所有索引
        %% 每个索引都有明确的用途和命名规范
        TopicFilterIndexes = [
            %% 消息ID唯一索引 - 确保消息不重复存储
            %% 在Java中相当于：
            %% @Index(name = "emd_message_id_unique", unique = true)
            #{
                <<"key">> => #{<<"message_id">> => 1},
                <<"name">> => <<DataAbbr/binary, "_message_id_unique">>,
                <<"unique">> => true                    %% 唯一约束
            },
            %% 客户端ID索引 - 支持按客户端查询消息
            #{
                <<"key">> => #{<<"client_id">> => 1},
                <<"name">> => <<DataAbbr/binary, "_client_id_asc">>
            },
            %% 主题索引 - 支持按主题查询消息（最常用）
            #{
                <<"key">> => #{<<"topic">> => 1},
                <<"name">> => <<DataAbbr/binary, "_topic_asc">>
            },
            %% 过期时间索引 - 支持过期消息的自动清理
            #{
                <<"key">> => #{<<"expiry_time">> => 1},
                <<"name">> => <<DataAbbr/binary, "_expiry_time_asc">>
            },
            %% 状态索引 - 支持按消息状态过滤
            #{
                <<"key">> => #{<<"status">> => 1},
                <<"name">> => <<DataAbbr/binary, "_status_asc">>
            },
            %% 时间戳索引 - 支持按时间排序和范围查询
            #{
                <<"key">> => #{<<"timestamp">> => 1},
                <<"name">> => <<DataAbbr/binary, "_timestamp_asc">>
            },
            %% QoS索引 - 支持按消息质量等级过滤
            #{
                <<"key">> => #{<<"qos">> => 1},
                <<"name">> => <<DataAbbr/binary, "_qos_asc">>
            },
            %% TTL索引 - 基于publish_received_at字段自动删除一年前的数据
            %% MongoDB TTL索引会自动删除过期文档，保持数据库大小可控
            %% 在Java中相当于：
            %% @Indexed(expireAfterSeconds = 31536000) // 365 * 24 * 3600 = 31536000秒（一年）
            %% private Date publishReceivedAt;
            #{
                <<"key">> => #{<<"publish_received_at">> => 1},
                <<"name">> => <<DataAbbr/binary, "_publish_received_at_ttl">>,
                <<"expireAfterSeconds">> => 31536000   %% 一年 = 365 * 24 * 3600 秒
            },
            %% 复合索引 (主题 + 时间戳) - 支持高效的范围查询
            %% 例如："查找特定主题在某个时间范围内的消息"
            %% 注意：timestamp使用-1（降序），便于获取最新消息
            #{
                <<"key">> => #{<<"topic">> => 1, <<"timestamp">> => -1},
                <<"name">> => <<DataAbbr/binary, "_topic_timestamp_compound">>
            }
        ],

        %% 逐个创建索引，使用智能重命名机制
        %% 智能机制会检查索引是否已存在，避免重复创建
        %% 在Java中相当于：
        %% indexes.forEach(index -> createIndexWithSmartNaming(collection, index));
        lists:foreach(fun(IndexSpec) ->
            create_index_with_smart_naming(Pid, Collection, IndexSpec)
        end, TopicFilterIndexes),

        %% 所有索引创建完成
        {ok, indices_created}
    catch
        %% 捕获所有异常，记录详细的错误信息
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_creating_topic_filter_indices",
                error => E,                    %% 异常类别
                reason => R,                   %% 异常原因
                stacktrace => S,               %% 调用栈
                collection => Collection       %% 集合名称
            }),
            %% 返回结构化的错误信息
            {error, {failed_to_create_indices, R}}
    end.

%% @doc 智能索引创建 - 检查存在性并自动重命名
create_index_with_smart_naming(Pid, Collection, IndexSpec) ->
    IndexName = maps:get(<<"name">>, IndexSpec),
    IndexKey = maps:get(<<"key">>, IndexSpec),

    % 首先检查索引是否已存在
    case check_index_exists(Pid, Collection, IndexName) of
        {ok, true} ->
            % 索引已存在，检查是否配置相同
            case check_index_compatibility(Pid, Collection, IndexName, IndexSpec) of
                {ok, compatible} ->
                    ?SLOG(info, #{
                        msg => "index_already_exists_and_compatible",
                        collection => Collection,
                        index_name => IndexName
                    });
                {ok, incompatible} ->
                    % 配置不兼容，生成新的索引名称
                    NewIndexName = generate_unique_index_name(Pid, Collection, IndexName),
                    NewIndexSpec = IndexSpec#{<<"name">> => NewIndexName},
                    create_single_index(Pid, Collection, NewIndexSpec);
                {error, Reason} ->
                    ?SLOG(warning, #{
                        msg => "failed_to_check_index_compatibility",
                        collection => Collection,
                        index_name => IndexName,
                        reason => Reason
                    })
            end;
        {ok, false} ->
            % 索引不存在，直接创建
            create_single_index(Pid, Collection, IndexSpec);
        {error, Reason} ->
            ?SLOG(warning, #{
                msg => "failed_to_check_index_existence",
                collection => Collection,
                index_name => IndexName,
                reason => Reason
            }),
            % 检查失败，尝试直接创建
            create_single_index(Pid, Collection, IndexSpec)
    end.

%% @doc 检查索引是否存在
check_index_exists(Pid, Collection, IndexName) ->
    try
        Command = #{
            <<"listIndexes">> => Collection
        },
        case emqx_plugin_mongodb_api:command(Pid, Command) of
            {ok, #{<<"cursor">> := #{<<"firstBatch">> := Indexes}}} ->
                Exists = lists:any(fun(Index) ->
                    maps:get(<<"name">>, Index, undefined) =:= IndexName
                end, Indexes),
                {ok, Exists};
            {ok, _} ->
                {ok, false};
            {error, Reason} ->
                {error, Reason}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_checking_index_existence",
                error => E,
                reason => R,
                stacktrace => S,
                collection => Collection,
                index_name => IndexName
            }),
            {error, {E, R}}
    end.

%% @doc 检查索引兼容性
check_index_compatibility(Pid, Collection, IndexName, ExpectedSpec) ->
    try
        Command = #{
            <<"listIndexes">> => Collection
        },
        case emqx_plugin_mongodb_api:command(Pid, Command) of
            {ok, #{<<"cursor">> := #{<<"firstBatch">> := Indexes}}} ->
                case lists:find(fun(Index) ->
                    maps:get(<<"name">>, Index, undefined) =:= IndexName
                end, Indexes) of
                    {value, ExistingIndex} ->
                        % 比较索引键和选项
                        ExistingKey = maps:get(<<"key">>, ExistingIndex, #{}),
                        ExpectedKey = maps:get(<<"key">>, ExpectedSpec, #{}),

                        ExistingUnique = maps:get(<<"unique">>, ExistingIndex, false),
                        ExpectedUnique = maps:get(<<"unique">>, ExpectedSpec, false),

                        if
                            ExistingKey =:= ExpectedKey andalso ExistingUnique =:= ExpectedUnique ->
                                {ok, compatible};
                            true ->
                                {ok, incompatible}
                        end;
                    false ->
                        {ok, compatible}  % 索引不存在，认为兼容
                end;
            {error, Reason} ->
                {error, Reason}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_checking_index_compatibility",
                error => E,
                reason => R,
                stacktrace => S,
                collection => Collection,
                index_name => IndexName
            }),
            {error, {E, R}}
    end.

%% @doc 生成唯一的索引名称
generate_unique_index_name(Pid, Collection, BaseIndexName) ->
    generate_unique_index_name(Pid, Collection, BaseIndexName, 1).

generate_unique_index_name(Pid, Collection, BaseIndexName, Suffix) ->
    NewIndexName = <<BaseIndexName/binary, "_v", (integer_to_binary(Suffix))/binary>>,
    case check_index_exists(Pid, Collection, NewIndexName) of
        {ok, false} ->
            NewIndexName;
        {ok, true} ->
            generate_unique_index_name(Pid, Collection, BaseIndexName, Suffix + 1);
        {error, _} ->
            % 检查失败，使用带时间戳的名称
            Timestamp = integer_to_binary(erlang:system_time(millisecond)),
            <<BaseIndexName/binary, "_", Timestamp/binary>>
    end.

%% @doc 创建单个索引
create_single_index(Pid, Collection, IndexSpec) ->
    IndexName = maps:get(<<"name">>, IndexSpec),
    try
        Command = #{
            <<"createIndexes">> => Collection,
            <<"indexes">> => [IndexSpec]
        },
        case emqx_plugin_mongodb_api:command(Pid, Command) of
            {ok, _} ->
                ?SLOG(info, #{
                    msg => "index_created_successfully",
                    collection => Collection,
                    index_name => IndexName
                });
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_create_index",
                    collection => Collection,
                    index_name => IndexName,
                    reason => Reason
                })
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_creating_single_index",
                error => E,
                reason => R,
                stacktrace => S,
                collection => Collection,
                index_name => IndexName
            })
    end.

%% @doc 生成集合名称缩写
generate_collection_abbreviation(Collection) when is_binary(Collection) ->
    % 将集合名称转换为缩写
    % emqx_mqtt_data -> emd
    % emqx_mqtt_messages -> emm
    % emqx_mqtt_sessions -> ems
    % emqx_mqtt_subscriptions -> emsub
    % emqx_mqtt_will_messages -> emwm
    Parts = binary:split(Collection, <<"_">>, [global]),
    Abbreviation = lists:foldl(fun(Part, Acc) ->
        case Part of
            <<>> -> Acc;
            <<First:8, _/binary>> -> <<Acc/binary, First>>
        end
    end, <<>>, Parts),

    % 确保缩写不为空，至少有2个字符
    case byte_size(Abbreviation) of
        0 -> <<"idx">>;
        1 -> <<Abbreviation/binary, "x">>;
        _ -> Abbreviation
    end;
generate_collection_abbreviation(_) ->
    <<"idx">>.

%% @doc 临时调试函数 - 检查结果类型
type_of_result({ok, Data}) when is_map(Data) ->
    OkField = maps:get(<<"ok">>, Data, not_found),
    {ok_map, #{ok_field => OkField, ok_type => type_of_value(OkField), keys => maps:keys(Data)}};
type_of_result({ok, Data}) ->
    {ok_other, type_of_value(Data)};
type_of_result({error, Reason}) ->
    {error, type_of_value(Reason)};
type_of_result(Other) ->
    {other, type_of_value(Other)}.

type_of_value(X) when is_integer(X) -> integer;
type_of_value(X) when is_float(X) -> float;
type_of_value(X) when is_binary(X) -> binary;
type_of_value(X) when is_atom(X) -> atom;
type_of_value(X) when is_list(X) -> list;
type_of_value(X) when is_map(X) -> map;
type_of_value(X) when is_tuple(X) -> tuple;
type_of_value(_) -> unknown.

%% @doc 处理MongoDB成功响应
handle_mongodb_success(SuccessDoc, Collection) ->
    % 检查是否有特殊的成功消息
    case maps:get(<<"note">>, SuccessDoc, undefined) of
        <<"all indexes already exist">> ->
            ?SLOG(info, #{
                msg => "mongodb_indexes_already_exist",
                collection => Collection,
                response => SuccessDoc
            });
        _ ->
            % 检查是否是索引创建成功
            case {maps:get(<<"numIndexesBefore">>, SuccessDoc, undefined),
                  maps:get(<<"numIndexesAfter">>, SuccessDoc, undefined)} of
                {Before, After} when is_integer(Before), is_integer(After), After > Before ->
                    ?SLOG(info, #{
                        msg => "mongodb_indexes_created_successfully",
                        collection => Collection,
                        indexes_before => Before,
                        indexes_after => After,
                        created_collection => maps:get(<<"createdCollectionAutomatically">>, SuccessDoc, false)
                    });
                _ ->
                    ?SLOG(debug, #{
                        msg => "mongodb_command_success",
                        response => SuccessDoc
                    })
            end
    end.

% 记录操作失败
record_failure(Error) ->
    % 尝试通过断路器记录错误
    try
        case erlang:function_exported(emqx_plugin_mongodb_circuit_breaker, record_failure, 1) of
            true ->
                emqx_plugin_mongodb_circuit_breaker:record_failure(Error);
            false ->
                % 断路器不可用，仅记录日志
                ?SLOG(error, #{msg => "operation_failed", error => Error})
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "failed_to_record_failure",
                original_error => Error,
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% @doc 安全地执行MongoDB的update操作
safe_do_update(Pid, Collection, Filter, Command, Options) ->
    try
        % 检查函数是否存在
        Result = case erlang:function_exported(mc_worker_api, update, 5) of
            true ->
                % 如果update/5存在
                {ok, _} = emqx_plugin_mongodb_api:smart_update(Pid, Collection, Filter, Command, Options),
                ok;
            false ->
                % 如果update/5不存在，尝试使用其他函数
                ?SLOG(warning, #{
                    msg => "emqx_plugin_mongodb_api:smart_update/5_not_found_trying_alternatives",
                    collection => Collection
                }),
                % 尝试使用mongo_api模块
                case code:ensure_loaded(mongo_api) of
                    {module, _} ->
                        % 如果mongo_api模块存在
                        case erlang:function_exported(mongo_api, update, 5) of
                            true ->
                                {ok, _} = emqx_plugin_mongodb_api:smart_update(Pid, Collection, Filter, Command, Options),
                                ok;
                            false ->
                                % 最后尝试直接使用命令
                                Command2 = #{
                                    <<"update">> => Collection,
                                    <<"updates">> => [
                                        #{
                                            <<"q">> => Filter,
                                            <<"u">> => Command,
                                            <<"upsert">> => maps:get(upsert, Options, false)
                                        }
                                    ]
                                },
                                % 使用扩展API执行更新命令
                                case emqx_plugin_mongodb_api:bulk_upsert(Pid, Collection, [#{
                                    filter => Filter,
                                    update => Command
                                }]) of
                                    #{modified_count := Count, errors := []} when Count > 0 ->
                                        ok;
                                    #{errors := Errors} when length(Errors) > 0 ->
                                        {error, Errors};
                                    _ ->
                                        ok
                                end
                        end;
                    _ ->
                        % 如果mongo_api模块不存在，尝试直接使用命令
                        Command2 = #{
                            <<"update">> => Collection,
                            <<"updates">> => [
                                #{
                                    <<"q">> => Filter,
                                    <<"u">> => Command,
                                    <<"upsert">> => maps:get(upsert, Options, false)
                                }
                            ]
                        },
                        {ok, _} = emqx_plugin_mongodb_api:command(Pid, Command2),
                        ok
                end
        end,
        Result
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "mongodb_update_error",
                error => E,
                reason => R,
                stacktrace => S,
                collection => Collection,
                filter => Filter
            }),
            {error, {E, R}}
    end.

%% @doc 安全地执行MongoDB的find操作，返回所有匹配的文档
safe_do_find(Pid, Collection, Filter, Options) ->
    try
        {ok, Docs} = emqx_plugin_mongodb_api:smart_find(Pid, Collection, Filter, Options),
        {ok, Docs}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "mongodb_find_error",
                error => E,
                reason => R,
                stacktrace => S,
                collection => Collection,
                filter => Filter
            }),
            {error, {E, R}}
    end.

%% @doc 安全地执行MongoDB的delete操作
safe_do_delete(Pid, Collection, Filter) ->
    try
        % 使用扩展API进行删除操作
        case emqx_plugin_mongodb_api:bulk_delete(Pid, Collection, [Filter]) of
            #{deleted_count := Count, errors := []} when Count > 0 ->
                ok;
            #{deleted_count := 0} ->
                ok; % 没有匹配的文档也算成功
            #{errors := Errors} when length(Errors) > 0 ->
                {error, Errors};
            _ ->
                ok
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "mongodb_delete_error",
                error => E,
                reason => R,
                stacktrace => S,
                collection => Collection,
                filter => Filter
            }),
            {error, {E, R}}
    end.

%% @doc 安全地执行MongoDB的delete_many操作
safe_do_delete_many(Pid, Collection, Filter, Options) ->
    try
        % 使用扩展API进行批量删除操作
        case emqx_plugin_mongodb_api:bulk_delete(Pid, Collection, [Filter], Options) of
            #{deleted_count := DeletedCount, errors := []} ->
                {ok, DeletedCount};
            #{deleted_count := DeletedCount, errors := Errors} when length(Errors) > 0 ->
                ?SLOG(warning, #{
                    msg => "delete_many_with_errors",
                    deleted_count => DeletedCount,
                    errors => Errors
                }),
                {ok, DeletedCount};
            #{errors := Errors} when length(Errors) > 0 ->
                {error, Errors};
            _ ->
                {ok, 0}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "mongodb_delete_many_error",
                error => E,
                reason => R,
                stacktrace => S,
                collection => Collection,
                filter => Filter
            }),
            {error, {E, R}}
    end.

%% @doc 安全地执行MongoDB的command操作
safe_do_command(Pid, Command) ->
    try
        {ok, Result} = emqx_plugin_mongodb_api:command(Pid, Command),
        {ok, Result}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "mongodb_command_error",
                error => E,
                reason => R,
                stacktrace => S,
                command => Command
            }),
            {error, {E, R}}
    end.

%% @doc 获取MongoDB连接池
get_pool() ->
    % 尝试获取默认连接池名称
    case erlang:get(mongodb_pool) of
        undefined ->
            % 如果未定义，使用默认连接池名称
            DefaultPool = mongodb_default_pool,
            erlang:put(mongodb_pool, DefaultPool),
            DefaultPool;
        Pool ->
            Pool
    end.

%% @doc 处理同步查询请求 - 主题匹配查询
%% 这个函数处理包含查询列表的同步查询请求，主要用于主题匹配和过滤
%%
%% 功能说明：
%% 1. 处理多个查询的批量同步执行
%% 2. 支持主题匹配和过滤逻辑
%% 3. 提供详细的查询日志和错误处理
%% 4. 返回所有查询的执行结果
%%
%% 参数说明：
%% - InstId: 资源实例ID
%% - {Querys, EvtMsg}: 查询请求元组
%%   - Querys: 查询列表，包含多个查询操作
%%   - EvtMsg: 事件消息，包含上下文信息
%% - State: 连接器状态，包含MongoDB连接信息
%%
%% 守卫条件：
%% - is_list(Querys): 确保Querys是列表类型
%%
%% 返回值：
%% - {ok, Results}: 查询成功，返回结果列表
%% - {error, Reason}: 查询失败，返回错误原因
%%
%% Java等价概念：
%% 类似于批量查询处理或主题过滤器的执行
on_query(InstId, {Querys, EvtMsg}, #{topology_pid := Pid} = _State) when is_list(Querys) ->
    %% 记录同步主题匹配查询的详细信息
    %% 包含查询数量、首个查询内容、事件消息摘要等调试信息
    ?SLOG(info, #{
        msg => "handling_sync_topic_match_query",
        inst_id => InstId,
        querys_count => length(Querys),                    % 查询数量
        first_query => case Querys of                      % 首个查询内容（用于调试）
            [] -> undefined;
            [First|_] -> First
        end,
        evt_msg_summary => maps:get(id, EvtMsg, <<"unknown_id">>)  % 事件消息ID
    }),

    %% 尝试处理主题匹配查询
    %% 使用try-catch保护整个查询处理过程
    try
        % 处理每个查询
        Results = lists:map(
            fun(Query) ->
                % 支持两种格式：{Name, Collection} 或 {Name, Filter, Collection}
                {_Name, Collection} = case Query of
                    {N, C} -> {N, C};  % 2元组格式
                    {N, _F, C} -> {N, C}  % 3元组格式，忽略Filter
                end,
                try
                    % 确保EvtMsg是一个map
                    Doc = case is_map(EvtMsg) of
                        true -> EvtMsg;
                        false -> #{data => EvtMsg}
                    end,

                    % 使用insert操作避免覆盖已存在的主题过滤数据
                    % 为主题过滤数据生成基于消息内容的稳定唯一标识
                    UniqueId = case maps:get(id, Doc, undefined) of
                        undefined ->
                            % 如果没有id，基于消息内容生成稳定的ID
                            % 使用客户端ID、主题、时间戳等生成确定性ID
                            ClientId = maps:get(clientid, Doc, <<"unknown">>),
                            Topic = maps:get(topic, Doc, <<"unknown">>),
                            Timestamp = maps:get(publish_received_at, Doc, erlang:system_time(millisecond)),
                            Payload = maps:get(payload, Doc, <<>>),
                            % 生成基于内容的哈希ID，确保相同消息产生相同ID
                            ContentHash = erlang:phash2({ClientId, Topic, Timestamp, Payload}),
                            iolist_to_binary([ClientId, "_", integer_to_binary(Timestamp), "_", integer_to_binary(ContentHash)]);
                        Id -> Id
                    end,

                    % 先检查数据是否已存在，避免覆盖
                    case emqx_plugin_mongodb_api:find_one(
                        Pid,
                        Collection,
                        #{<<"_id">> => UniqueId}
                    ) of
                        {ok, undefined} ->
                            % 数据不存在，可以安全插入
                            DocWithId = Doc#{<<"_id">> => UniqueId},
                            case emqx_plugin_mongodb_api:insert(
                                Pid,
                                Collection,
                                DocWithId
                            ) of
                                ok ->
                                    ?SLOG(debug, #{
                                        msg => "topic_filter_data_saved_successfully",
                                        collection => Collection,
                                        unique_id => UniqueId,
                                        method => "insert_new"
                                    }),
                                    ok;
                                {error, Reason} ->
                                    ?SLOG(error, #{
                                        msg => "topic_filter_data_save_failed",
                                        collection => Collection,
                                        reason => Reason,
                                        unique_id => UniqueId,
                                        method => "insert_new"
                                    }),
                                    {error, insert_failed}
                            end;
                        {ok, _ExistingDoc} ->
                            % 数据已存在，不覆盖，保持数据一致性
                            ?SLOG(debug, #{
                                msg => "topic_filter_data_already_exists_skipping",
                                collection => Collection,
                                unique_id => UniqueId,
                                reason => "preserving_existing_data_integrity"
                            }),
                            ok;
                        {error, FindReason} ->
                            ?SLOG(error, #{
                                msg => "topic_filter_data_existence_check_failed",
                                collection => Collection,
                                reason => FindReason,
                                unique_id => UniqueId
                            }),
                            {error, existence_check_failed}
                    end
                catch
                    E:R:S ->
                        ?SLOG(error, #{
                            msg => "insert_failed",
                            error => E,
                            reason => R,
                            stack => S,
                            collection => Collection
                        }),
                        {error, {insert_failed, {E, R}}}
                end
            end,
            Querys
        ),

        % 检查结果
        case [E || E <- Results, E =/= ok] of
            [] ->
                % 全部成功
                record_success(),
                ok;
            Errors ->
                % 部分失败
                record_failure(),
                ?SLOG(error, #{
                    msg => "some_inserts_failed",
                    errors_count => length(Errors),
                    first_error => hd(Errors)
                }),
                {error, {partial_failure, length(Errors)}}
        end
    catch
        E:R:S ->
            record_failure(),
            ?SLOG(error, #{
                msg => "exception_handling_sync_topic_match_query",
                error => E,
                reason => R,
                stack => S,
                inst_id => InstId
            }),
            {error, {query_exception, {E, R}}}
    end;

%% @doc 处理同步查询请求 - 保存会话
%% 这个函数处理会话持久化的同步查询请求
%%
%% 功能说明：
%% 1. 将MQTT客户端会话信息保存到MongoDB
%% 2. 使用upsert操作确保会话信息的唯一性
%% 3. 支持会话状态的更新和创建
%% 4. 提供完整的错误处理和日志记录
%%
%% 参数说明：
%% - InstId: 资源实例ID
%% - {save_session, Collection, ClientId, SessionDoc}: 保存会话请求
%%   - Collection: 会话集合名称
%%   - ClientId: MQTT客户端ID
%%   - SessionDoc: 会话文档，包含会话的所有信息
%% - State: 连接器状态，包含MongoDB连接信息
%%
%% 返回值：
%% - ok: 保存成功
%% - {error, Reason}: 保存失败，返回错误原因
%%
%% Java等价概念：
%% 类似于JPA的save()或MyBatis的insert/update操作
on_query(InstId, {save_session, Collection, ClientId, SessionDoc}, #{topology_pid := Pid} = _State) ->
    %% 记录保存会话操作的详细信息
    %% 用于调试和运维监控
    ?SLOG(info, #{
        msg => "handling_sync_save_session",
        inst_id => InstId,
        client_id => ClientId,
        collection => Collection
    }),

    %% 处理保存会话的查询
    %% 使用try-catch保护数据库操作
    try
        % 使用最基本的MongoDB API操作
        Filter = #{<<"client_id">> => ClientId},
        Command = #{
            <<"update">> => Collection,
            <<"updates">> => [
                #{
                    <<"q">> => Filter,
                    <<"u">> => #{<<"$set">> => SessionDoc},
                    <<"upsert">> => true
                }
            ]
        },
        {ok, _} = emqx_plugin_mongodb_api:command(Pid, Command),
        record_success(),
        ok
    catch
        E:R:S ->
            record_failure(),
            ?SLOG(error, #{
                msg => "save_session_failed",
                error => E,
                reason => R,
                stack => S,
                collection => Collection,
                client_id => ClientId
            }),
            {error, {save_session_failed, {E, R}}}
    end;

on_query(InstId, {find_subscriptions, Collection, ClientId}, #{topology_pid := Pid} = _State) ->
    ?SLOG(info, #{
        msg => "handling_sync_find_subscriptions",
        inst_id => InstId,
        client_id => ClientId,
        collection => Collection
    }),

    % 处理查询订阅的查询
    try
        % 使用最基本的MongoDB API操作
        Filter = #{<<"client_id">> => ClientId},
        Command = #{
            <<"find">> => Collection,
            <<"filter">> => Filter
        },
        {true, #{<<"cursor">> := #{<<"firstBatch">> := DocsResult}}} = emqx_plugin_mongodb_api:command(Pid, Command),
        record_success(),
        {ok, DocsResult}
    catch
        E:R:S ->
            record_failure(),
            ?SLOG(error, #{
                msg => "find_subscriptions_failed",
                error => E,
                reason => R,
                stack => S,
                collection => Collection,
                client_id => ClientId
            }),
            {error, {find_subscriptions_failed, {E, R}}}
    end;

on_query(InstId, {save_message, Collection, Doc}, #{topology_pid := Pid} = _State) ->
    ?SLOG(info, #{
        msg => "handling_sync_save_message",
        inst_id => InstId,
        collection => Collection
    }),

    % 处理保存消息的查询
    try
        emqx_plugin_mongodb_api:smart_insert(Pid, Collection, [Doc]),
        record_success(),
        ok
    catch
        E:R:S ->
            record_failure(),
            ?SLOG(error, #{
                msg => "save_message_failed",
                error => E,
                reason => R,
                stack => S,
                collection => Collection
            }),
            {error, {save_message_failed, {E, R}}}
    end;

on_query(InstId, {create_index, Collection, Index}, #{topology_pid := Pid} = _State) ->
    ?SLOG(info, #{
        msg => "handling_sync_create_index",
        inst_id => InstId,
        collection => Collection,
        index => Index
    }),

    % 处理创建索引的查询
    try
        Command = #{
            <<"createIndexes">> => Collection,
            <<"indexes">> => [Index]
        },
        {ok, Result} = emqx_plugin_mongodb_api:command(Pid, Command),
        record_success(),
        {ok, Result}
    catch
        E:R:S ->
            record_failure(),
            ?SLOG(error, #{
                msg => "create_index_failed",
                error => E,
                reason => R,
                stacktrace => S,
                collection => Collection,
                index => Index
            }),
            {error, {create_index_failed, {E, R}}}
    end;

% 处理删除索引的查询
on_query(InstId, {drop_index, Collection, IndexName}, #{topology_pid := Pid} = _State) ->
    ?SLOG(info, #{
        msg => "handling_sync_drop_index",
        inst_id => InstId,
        collection => Collection,
        index_name => IndexName
    }),

    % 处理删除索引的查询
    try
        Command = #{
            <<"dropIndexes">> => Collection,
            <<"index">> => IndexName
        },
        {ok, Result} = emqx_plugin_mongodb_api:command(Pid, Command),
        record_success(),
        {ok, Result}
    catch
        E:R:S ->
            record_failure(),
            ?SLOG(error, #{
                msg => "drop_index_failed",
                error => E,
                reason => R,
                stacktrace => S,
                collection => Collection,
                index_name => IndexName
            }),
            {error, {drop_index_failed, {E, R}}}
    end;

% 处理同步 find 操作
on_query(InstId, {find, Collection, Filter, Projection, Skip, Limit}, #{topology_pid := Pid} = _State) ->
    ?SLOG(info, #{
        msg => "handling_sync_find",
        inst_id => InstId,
        collection => Collection,
        filter => Filter
    }),
    try
        Command = #{
            <<"find">> => Collection,
            <<"filter">> => Filter
        },
        % 添加可选参数
        Command1 = case Projection of
            #{} when map_size(Projection) =:= 0 -> Command;
            _ -> Command#{<<"projection">> => Projection}
        end,
        Command2 = case Skip of
            0 -> Command1;
            _ -> Command1#{<<"skip">> => Skip}
        end,
        Command3 = case Limit of
            0 -> Command2;
            _ -> Command2#{<<"limit">> => Limit}
        end,

        case emqx_plugin_mongodb_api:command(Pid, Command3) of
            % 处理双重嵌套的响应格式
            {ok, {ok, #{<<"cursor">> := #{<<"firstBatch">> := Docs}}}} ->
                record_success(),
                {ok, Docs};
            {ok, {ok, Result}} when is_map(Result) ->
                record_success(),
                {ok, []};  % 如果没有cursor字段，返回空列表
            % 处理单层响应格式（向后兼容）
            {ok, #{<<"cursor">> := #{<<"firstBatch">> := Docs}}} ->
                record_success(),
                {ok, Docs};
            {ok, _Other} ->
                record_success(),
                {ok, []};
            {error, Reason} ->
                record_failure(),
                {error, {find_failed, Reason}}
        end
    catch
        E:R:S ->
            record_failure(),
            ?SLOG(error, #{
                msg => "sync_find_operation_failed",
                error => E,
                reason => R,
                stacktrace => S,
                collection => Collection,
                filter => Filter
            }),
            {error, {find_failed, {E, R}}}
    end;

% 处理会话恢复查询
on_query(InstId, {find_sessions_to_restore, Collection}, #{topology_pid := Pid} = _State) ->
    ?SLOG(info, #{
        msg => "handling_sync_find_sessions_to_restore",
        inst_id => InstId,
        collection => Collection
    }),
    try
        Filter = #{
            <<"status">> => #{<<"$in">> => [<<"created">>, <<"connected">>, <<"disconnected">>]},
            <<"expiry_time">> => #{<<"$gt">> => erlang:system_time(millisecond)}
        },
        Command = #{
            <<"find">> => Collection,
            <<"filter">> => Filter,
            <<"limit">> => 1000,
            <<"sort">> => #{<<"updated_at">> => -1}
        },

        case emqx_plugin_mongodb_api:command(Pid, Command) of
            {ok, #{<<"cursor">> := #{<<"firstBatch">> := Docs}}} ->
                record_success(),
                {ok, Docs};
            {ok, _Other} ->
                record_success(),
                {ok, []};
            {error, Reason} ->
                record_failure(),
                {error, {find_sessions_failed, Reason}}
        end
    catch
        E:R:S ->
            record_failure(),
            ?SLOG(error, #{
                msg => "sync_find_sessions_to_restore_failed",
                error => E,
                reason => R,
                stacktrace => S,
                collection => Collection
            }),
            {error, {find_sessions_failed, {E, R}}}
    end;

% 处理遗嘱消息删除查询
on_query(InstId, {delete_will_message, Collection, ClientId}, #{topology_pid := Pid} = _State) ->
    ?SLOG(info, #{
        msg => "handling_sync_delete_will_message",
        inst_id => InstId,
        collection => Collection,
        client_id => ClientId
    }),
    try
        Filter = #{<<"client_id">> => ClientId},
        Command = #{
            <<"delete">> => Collection,
            <<"deletes">> => [
                #{
                    <<"q">> => Filter,
                    <<"limit">> => 1
                }
            ]
        },

        case emqx_plugin_mongodb_api:command(Pid, Command) of
            {ok, #{<<"n">> := Count}} ->
                record_success(),
                {ok, #{deleted_count => Count}};
            {ok, _Other} ->
                record_success(),
                {ok, #{deleted_count => 0}};
            {error, Reason} ->
                record_failure(),
                {error, {delete_will_failed, Reason}}
        end
    catch
        E:R:S ->
            record_failure(),
            ?SLOG(error, #{
                msg => "sync_delete_will_message_failed",
                error => E,
                reason => R,
                stacktrace => S,
                collection => Collection,
                client_id => ClientId
            }),
            {error, {delete_will_failed, {E, R}}}
    end;

% 处理计数查询
on_query(InstId, {count, Collection, Filter}, #{topology_pid := Pid} = _State) ->
    ?SLOG(info, #{
        msg => "handling_sync_count",
        inst_id => InstId,
        collection => Collection,
        filter => Filter
    }),
    try
        Command = #{
            <<"count">> => Collection,
            <<"query">> => Filter
        },

        case emqx_plugin_mongodb_api:command(Pid, Command) of
            {ok, #{<<"n">> := Count}} ->
                record_success(),
                {ok, Count};
            {ok, _Other} ->
                record_success(),
                {ok, 0};
            {error, Reason} ->
                record_failure(),
                {error, {count_failed, Reason}}
        end
    catch
        E:R:S ->
            record_failure(),
            ?SLOG(error, #{
                msg => "sync_count_operation_failed",
                error => E,
                reason => R,
                stacktrace => S,
                collection => Collection,
                filter => Filter
            }),
            {error, {count_failed, {E, R}}}
    end;

% 处理count_documents查询
on_query(InstId, {count_documents, Collection, Filter}, #{topology_pid := Pid} = _State) ->
    ?SLOG(info, #{
        msg => "handling_sync_count_documents",
        inst_id => InstId,
        collection => Collection,
        filter => Filter
    }),
    try
        case emqx_plugin_mongodb_api:count_documents(Pid, Collection, Filter) of
            {ok, Count} ->
                record_success(),
                {ok, Count};
            {error, Reason} ->
                record_failure(),
                ?SLOG(error, #{
                    msg => "count_documents_query_failed",
                    inst_id => InstId,
                    collection => Collection,
                    filter => Filter,
                    reason => Reason
                }),
                {error, Reason}
        end
    catch
        E:R:S ->
            record_failure(),
            ?SLOG(error, #{
                msg => "count_documents_failed",
                error => E,
                reason => R,
                stack => S,
                collection => Collection,
                filter => Filter
            }),
            {error, {count_documents_failed, {E, R}}}
    end;

% 处理delete_many查询
on_query(InstId, {delete_many, Collection, Filter, Options}, #{topology_pid := Pid} = _State) ->
    ?SLOG(info, #{
        msg => "handling_sync_delete_many",
        inst_id => InstId,
        collection => Collection,
        filter => Filter,
        options => Options
    }),
    try
        case safe_do_delete_many(Pid, Collection, Filter, Options) of
            {ok, DeletedCount} ->
                record_success(),
                {ok, #{<<"deletedCount">> => DeletedCount}};
            {error, Reason} ->
                record_failure(),
                ?SLOG(error, #{
                    msg => "delete_many_query_failed",
                    inst_id => InstId,
                    collection => Collection,
                    filter => Filter,
                    options => Options,
                    reason => Reason
                }),
                {error, Reason}
        end
    catch
        E:R:S ->
            record_failure(),
            ?SLOG(error, #{
                msg => "delete_many_failed",
                error => E,
                reason => R,
                stack => S,
                collection => Collection,
                filter => Filter,
                options => Options
            }),
            {error, {delete_many_failed, {E, R}}}
    end;

% 处理update查询
on_query(InstId, {update, Collection, Filter, Update}, #{topology_pid := Pid} = _State) ->
    ?SLOG(info, #{
        msg => "handling_sync_update",
        inst_id => InstId,
        collection => Collection,
        filter => Filter,
        update => Update
    }),
    try
        case emqx_plugin_mongodb_api:update(Pid, Collection, Filter, Update, #{}) of
            {ok, UpdateResult} ->
                record_success(),
                % 返回简化的结果，只保留关键信息
                SimplifiedResult = case UpdateResult of
                    #{<<"n">> := N, <<"nModified">> := Modified} ->
                        #{matched_count => N, modified_count => Modified};
                    #{<<"n">> := N} ->
                        #{matched_count => N, modified_count => N};
                    _ ->
                        #{matched_count => 1, modified_count => 1}
                end,
                {ok, SimplifiedResult};
            {error, Reason} ->
                record_failure(),
                ?SLOG(error, #{
                    msg => "update_query_failed",
                    inst_id => InstId,
                    collection => Collection,
                    filter => Filter,
                    update => Update,
                    reason => Reason
                }),
                {error, Reason}
        end
    catch
        E:R:S ->
            record_failure(),
            ?SLOG(error, #{
                msg => "update_failed",
                error => E,
                reason => R,
                stack => S,
                collection => Collection,
                filter => Filter,
                update => Update
            }),
            {error, {update_failed, {E, R}}}
    end;

% 处理upsert查询
on_query(InstId, {upsert, Collection, Filter, Document}, #{topology_pid := Pid} = _State) ->
    ?SLOG(info, #{
        msg => "handling_sync_upsert",
        inst_id => InstId,
        collection => Collection,
        filter => Filter,
        document => Document
    }),
    try
        case emqx_plugin_mongodb_api:upsert(Pid, Collection, Filter, Document) of
            {ok, UpsertResult} ->
                record_success(),
                % 返回简化的结果，只保留关键信息
                SimplifiedResult = case UpsertResult of
                    #{<<"n">> := N, <<"nModified">> := Modified, <<"upserted">> := Upserted} when is_list(Upserted) ->
                        #{matched_count => N, modified_count => Modified, upserted_count => length(Upserted)};
                    #{<<"n">> := N, <<"nModified">> := Modified} ->
                        #{matched_count => N, modified_count => Modified, upserted_count => 0};
                    #{<<"n">> := N} ->
                        #{matched_count => N, modified_count => N, upserted_count => 0};
                    _ ->
                        #{matched_count => 1, modified_count => 1, upserted_count => 0}
                end,
                {ok, SimplifiedResult};
            {error, Reason} ->
                record_failure(),
                ?SLOG(error, #{
                    msg => "upsert_query_failed",
                    inst_id => InstId,
                    collection => Collection,
                    filter => Filter,
                    document => Document,
                    reason => Reason
                }),
                {error, Reason}
        end
    catch
        E:R:S ->
            record_failure(),
            ?SLOG(error, #{
                msg => "upsert_failed",
                error => E,
                reason => R,
                stack => S,
                collection => Collection,
                filter => Filter,
                document => Document
            }),
            {error, {upsert_failed, {E, R}}}
    end;

% 处理find_one查询
on_query(InstId, {find_one, Collection, Filter, Options}, #{topology_pid := Pid} = _State) ->
    ?SLOG(info, #{
        msg => "handling_sync_find_one",
        inst_id => InstId,
        collection => Collection,
        filter => Filter,
        options => Options
    }),
    try
        case emqx_plugin_mongodb_api:find_one(Pid, Collection, Filter, Options) of
            {ok, Document} ->
                record_success(),
                {ok, Document};
            {error, Reason} ->
                record_failure(),
                ?SLOG(error, #{
                    msg => "find_one_query_failed",
                    inst_id => InstId,
                    collection => Collection,
                    filter => Filter,
                    options => Options,
                    reason => Reason
                }),
                {error, Reason}
        end
    catch
        E:R:S ->
            record_failure(),
            ?SLOG(error, #{
                msg => "find_one_failed",
                error => E,
                reason => R,
                stack => S,
                collection => Collection,
                filter => Filter,
                options => Options
            }),
            {error, {find_one_failed, {E, R}}}
    end;

% 处理delete查询
on_query(InstId, {delete, Collection, Filter}, #{topology_pid := Pid} = _State) ->
    ?SLOG(info, #{
        msg => "handling_sync_delete",
        inst_id => InstId,
        collection => Collection,
        filter => Filter
    }),
    try
        case emqx_plugin_mongodb_api:delete(Pid, Collection, Filter) of
            {ok, DeleteResult} ->
                record_success(),
                % 返回简化的结果，只保留关键信息
                SimplifiedResult = case DeleteResult of
                    #{<<"n">> := N} ->
                        #{deleted_count => N};
                    _ ->
                        #{deleted_count => 1}
                end,
                {ok, SimplifiedResult};
            {error, Reason} ->
                record_failure(),
                ?SLOG(error, #{
                    msg => "delete_query_failed",
                    inst_id => InstId,
                    collection => Collection,
                    filter => Filter,
                    reason => Reason
                }),
                {error, Reason}
        end
    catch
        E:R:S ->
            record_failure(),
            ?SLOG(error, #{
                msg => "delete_failed",
                error => E,
                reason => R,
                stack => S,
                collection => Collection,
                filter => Filter
            }),
            {error, {delete_failed, {E, R}}}
    end;

% 处理delete_message同步查询（4个参数版本）
on_query(InstId, {delete_message, Collection, ClientId, MessageId}, #{topology_pid := Pid} = _State) ->
    ?SLOG(debug, #{
        msg => "handling_sync_delete_message_with_client_id",
        inst_id => InstId,
        collection => Collection,
        client_id => ClientId,
        message_id => MessageId
    }),
    try
        Filter = #{<<"client_id">> => ClientId, <<"message_id">> => MessageId},
        case emqx_plugin_mongodb_api:delete(Pid, Collection, Filter) of
            {ok, DeleteResult} ->
                record_success(),
                SimplifiedResult = case DeleteResult of
                    #{<<"n">> := N} ->
                        #{deleted_count => N};
                    _ ->
                        #{deleted_count => 1}
                end,
                {ok, SimplifiedResult};
            {error, Reason} ->
                record_failure(),
                ?SLOG(error, #{
                    msg => "sync_delete_message_failed",
                    inst_id => InstId,
                    collection => Collection,
                    client_id => ClientId,
                    message_id => MessageId,
                    reason => Reason
                }),
                {error, Reason}
        end
    catch
        E:R:S ->
            record_failure(),
            ?SLOG(error, #{
                msg => "sync_delete_message_error",
                error => E,
                reason => R,
                stack => S,
                collection => Collection,
                client_id => ClientId,
                message_id => MessageId
            }),
            {error, {delete_message_failed, {E, R}}}
    end;

% 处理delete_message同步查询（3个参数版本）
on_query(InstId, {delete_message, Collection, MessageId}, #{topology_pid := Pid} = _State) ->
    ?SLOG(debug, #{
        msg => "handling_sync_delete_message",
        inst_id => InstId,
        collection => Collection,
        message_id => MessageId
    }),
    try
        Filter = #{<<"message_id">> => MessageId},
        case emqx_plugin_mongodb_api:delete(Pid, Collection, Filter) of
            {ok, DeleteResult} ->
                record_success(),
                SimplifiedResult = case DeleteResult of
                    #{<<"n">> := N} ->
                        #{deleted_count => N};
                    _ ->
                        #{deleted_count => 1}
                end,
                {ok, SimplifiedResult};
            {error, Reason} ->
                record_failure(),
                ?SLOG(error, #{
                    msg => "sync_delete_message_failed",
                    inst_id => InstId,
                    collection => Collection,
                    message_id => MessageId,
                    reason => Reason
                }),
                {error, Reason}
        end
    catch
        E:R:S ->
            record_failure(),
            ?SLOG(error, #{
                msg => "sync_delete_message_error",
                error => E,
                reason => R,
                stack => S,
                collection => Collection,
                message_id => MessageId
            }),
            {error, {delete_message_failed, {E, R}}}
    end;

% 处理insert_one同步查询
on_query(InstId, {insert_one, Collection, Document}, #{topology_pid := Pid} = _State) ->
    ?SLOG(debug, #{
        msg => "handling_sync_insert_one",
        inst_id => InstId,
        collection => Collection
    }),
    try
        case emqx_plugin_mongodb_api:insert(Pid, Collection, Document) of
            ok ->
                record_success(),
                % API的insert函数返回ok，构造标准结果
                SimplifiedResult = #{inserted_count => 1},
                {ok, SimplifiedResult};
            {ok, InsertResult} ->
                % 兼容可能的其他返回格式
                record_success(),
                SimplifiedResult = case InsertResult of
                    #{<<"insertedId">> := Id} ->
                        #{inserted_id => Id, inserted_count => 1};
                    _ ->
                        #{inserted_count => 1}
                end,
                {ok, SimplifiedResult};
            {error, Reason} ->
                record_failure(),
                ?SLOG(error, #{
                    msg => "sync_insert_one_failed",
                    inst_id => InstId,
                    collection => Collection,
                    reason => Reason
                }),
                {error, Reason}
        end
    catch
        E:R:S ->
            record_failure(),
            ?SLOG(error, #{
                msg => "sync_insert_one_error",
                error => E,
                reason => R,
                stack => S,
                collection => Collection
            }),
            {error, {insert_one_failed, {E, R}}}
    end;

% 处理ping查询 - 避免触发回退处理警告
on_query(InstId, {call, ping}, #{topology_pid := Pid} = _State) ->
    ?SLOG(debug, #{
        msg => "handling_sync_ping",
        inst_id => InstId
    }),
    try
        % 使用MongoDB原生ping命令
        case mc_worker_api:command(Pid, #{<<"ping">> => 1}) of
            {true, #{<<"ok">> := 1.0}} ->
                record_success(),
                {ok, pong};
            {true, _Result} ->
                record_success(),
                {ok, pong};
            {false, Error} ->
                record_failure(),
                {error, Error};
            true ->
                % 某些情况下只返回true
                record_success(),
                {ok, pong};
            Other ->
                % 对于未知响应，记录详细信息但仍然返回成功
                % 因为ping命令能执行通常意味着连接是好的
                record_success(),
                ?SLOG(debug, #{
                    msg => "ping_unexpected_response_but_treating_as_success",
                    response => Other
                }),
                {ok, pong}
        end
    catch
        E:R:S ->
            record_failure(),
            ?SLOG(error, #{
                msg => "ping_command_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {ping_failed, {E, R}}}
    end;

on_query(InstId, Query, #{topology_pid := Pid} = _State) ->
    % 对于其他类型的查询，尝试通用处理而不是返回unsupported_query_type错误
    ?SLOG(warning, #{
        msg => "unknown_sync_query_type_fallback_handling",
        inst_id => InstId,
        query_type => case Query of
            {Type, _} when is_atom(Type) -> Type;
            {Type, _, _} when is_atom(Type) -> Type;
            {Type, _, _, _} when is_atom(Type) -> Type;
            _ -> unknown
        end,
        query => Query
    }),

    % 尝试通用处理
    try
        % 根据查询类型进行通用处理
        case Query of
            {_QueryType1, Collection} when is_binary(Collection) ->
                % 使用扩展API进行查找操作
                case emqx_plugin_mongodb_api:find_with_cursor(Pid, Collection, #{}, #{}, #{limit => 10}) of
                    {ok, CursorInfo} ->
                        DocsResult = maps:get(<<"firstBatch">>, CursorInfo, []),
                        record_success(),
                        {ok, DocsResult};
                    {error, Reason} ->
                        record_failure(),
                        {error, Reason}
                end;

            {_QueryType2, Collection, Doc} when is_binary(Collection) ->
                % 使用扩展API进行插入操作
                case emqx_plugin_mongodb_api:bulk_insert(Pid, Collection, [Doc]) of
                    #{inserted_count := 1, errors := []} ->
                        record_success(),
                        ok;
                    #{errors := Errors} when length(Errors) > 0 ->
                        record_failure(),
                        {error, {insert_failed, Errors}};
                    Other ->
                        record_success(),
                        {ok, Other}
                end;

            {_QueryType3, Collection, Filter, Update} when is_binary(Collection) ->
                % 假设是更新操作
                Command = #{
                    <<"update">> => Collection,
                    <<"updates">> => [
                        #{
                            <<"q">> => Filter,
                            <<"u">> => Update,
                            <<"upsert">> => false
                        }
                    ]
                },
                {ok, _} = emqx_plugin_mongodb_api:command(Pid, Command),
                record_success(),
                ok;

            _ ->
                % 实在无法处理的查询，返回空结果而不是错误
                ?SLOG(warning, #{
                    msg => "using_empty_result_fallback_for_sync",
                    inst_id => InstId,
                    query => Query
                }),
                record_success(),
                {ok, []}
        end
    catch
        EC:ER:ES ->
            ?SLOG(error, #{
                msg => "fallback_handler_failed_for_sync",
                error => EC,
                reason => ER,
                stack => ES,
                query => Query
            }),
            % 即使处理失败，也返回空结果而不是错误
            record_success(),
            {ok, []}
    end;

% 保留原始函数子句，但添加一个更通用的匹配
on_query(InstId, Query, _State) ->
    % 无法获取MongoDB连接时，返回空结果而不是错误
    ?SLOG(warning, #{
        msg => "no_mongodb_connection_available_fallback",
        inst_id => InstId,
        query => Query
    }),
    % 即使没有连接，也返回空结果而不是错误
    record_success(),
    {ok, []}.

%% @doc 处理批量同步查询请求
on_batch_query(InstId, Requests, #{topology_pid := Pid} = _State) ->
    ?SLOG(info, #{
        msg => "handling_batch_sync_query",
        inst_id => InstId,
        requests_count => length(Requests),
        first_request => case Requests of
            [] -> undefined;
            [First|_] -> First
        end
    }),

    % 尝试处理批量查询
    try
        % 处理每个请求
        Results = lists:map(
            fun(Request) ->
                try
                    % 根据请求类型调用相应的处理函数
                    case Request of
                        {Querys, EvtMsg} when is_list(Querys) ->
                            % 处理主题匹配查询
                            on_query(InstId, Request, #{topology_pid => Pid});
                        {save_session, Collection, ClientId, SessionDoc} ->
                            % 处理保存会话的查询
                            on_query(InstId, Request, #{topology_pid => Pid});
                        {find_subscriptions, Collection, ClientId} ->
                            % 处理查询订阅的查询
                            on_query(InstId, Request, #{topology_pid => Pid});
                        {save_message, Collection, Doc} ->
                            % 处理保存消息的查询
                            on_query(InstId, Request, #{topology_pid => Pid});
                        {create_index, Collection, Index} ->
                            % 处理创建索引的查询
                            on_query(InstId, Request, #{topology_pid => Pid});
                        _ ->
                            % 对于其他类型的查询，尝试通用处理
                            ?SLOG(warning, #{
                                msg => "unknown_batch_query_type_fallback_handling",
                                inst_id => InstId,
                                request => Request
                            }),

                            % 尝试通用处理
                            try
                                % 根据请求类型进行通用处理
                                case Request of
                                    {_QueryType1, Collection} when is_binary(Collection) ->
                                        % 简单的查询，假设是查找操作
                                        Command = #{
                                            <<"find">> => Collection,
                                            <<"limit">> => 10
                                        },
                                        {true, #{<<"cursor">> := #{<<"firstBatch">> := DocsResult}}} = emqx_plugin_mongodb_api:command(Pid, Command),
                                        {ok, DocsResult};

                                    {_QueryType2, Collection, Doc} when is_binary(Collection) ->
                                        % 假设是插入操作
                                        emqx_plugin_mongodb_api:smart_insert(Pid, Collection, [Doc]),
                                        ok;

                                    {_QueryType3, Collection, Filter, Update} when is_binary(Collection) ->
                                        % 假设是更新操作
                                        Command = #{
                                            <<"update">> => Collection,
                                            <<"updates">> => [
                                                #{
                                                    <<"q">> => Filter,
                                                    <<"u">> => Update,
                                                    <<"upsert">> => false
                                                }
                                            ]
                                        },
                                        {ok, _} = emqx_plugin_mongodb_api:command(Pid, Command),
                                        ok;

                                    _ ->
                                        % 实在无法处理的查询，返回空结果而不是错误
                                        ?SLOG(warning, #{
                                            msg => "using_empty_result_fallback_for_batch",
                                            inst_id => InstId,
                                            request => Request
                                        }),
                                        {ok, []}
                                end
                            catch
                                EC:ER:ES ->
                                    ?SLOG(error, #{
                                        msg => "fallback_handler_failed_for_batch",
                                        error => EC,
                                        reason => ER,
                                        stack => ES,
                                        request => Request
                                    }),
                                    % 即使处理失败，也返回空结果而不是错误
                                    {ok, []}
                            end
                    end
                catch
                    E:R:S ->
                        ?SLOG(error, #{
                            msg => "batch_query_request_failed",
                            error => E,
                            reason => R,
                            stack => S,
                            request => Request
                        }),
                        {error, {batch_query_failed, {E, R}}}
                end
            end,
            Requests
        ),

        % 检查结果
        case [E || E <- Results, is_tuple(E), element(1, E) =:= error] of
            [] ->
                % 全部成功
                record_success(),
                ok;
            Errors ->
                % 部分失败
                record_failure(),
                ?SLOG(error, #{
                    msg => "some_batch_queries_failed",
                    errors_count => length(Errors),
                    first_error => hd(Errors)
                }),
                {error, {partial_failure, length(Errors)}}
        end
    catch
        E:R:S ->
            record_failure(),
            ?SLOG(error, #{
                msg => "exception_handling_batch_query",
                error => E,
                reason => R,
                stack => S,
                inst_id => InstId
            }),
            {error, {batch_query_exception, {E, R}}}
    end.

%% @doc 处理批量异步查询请求
on_batch_query_async(InstId, Requests, {ReplyFun, ReplyArgs}, #{topology_pid := Pid} = _State) ->
    ?SLOG(info, #{
        msg => "handling_batch_async_query",
        inst_id => InstId,
        requests_count => length(Requests),
        first_request => case Requests of
            [] -> undefined;
            [First|_] -> First
        end
    }),

    % 尝试处理批量异步查询
    try
        % 处理每个请求
        Results = lists:map(
            fun(Request) ->
                try
                    % 根据请求类型调用相应的处理函数
                    case Request of
                        {Querys, EvtMsg} when is_list(Querys) ->
                            % 处理主题匹配查询
                            on_query(InstId, Request, #{topology_pid => Pid});
                        {save_session, Collection, ClientId, SessionDoc} ->
                            % 处理保存会话的查询
                            on_query(InstId, Request, #{topology_pid => Pid});
                        {find_subscriptions, Collection, ClientId} ->
                            % 处理查询订阅的查询
                            on_query(InstId, Request, #{topology_pid => Pid});
                        {save_message, Collection, Doc} ->
                            % 处理保存消息的查询
                            on_query(InstId, Request, #{topology_pid => Pid});
                        {create_index, Collection, Index} ->
                            % 处理创建索引的查询
                            on_query(InstId, Request, #{topology_pid => Pid});
                        _ ->
                            % 对于其他类型的查询，尝试通用处理
                            ?SLOG(warning, #{
                                msg => "unknown_batch_async_query_type_fallback_handling",
                                inst_id => InstId,
                                request => Request
                            }),

                            % 尝试通用处理
                            try
                                % 根据请求类型进行通用处理
                                case Request of
                                    {_QueryType1, Collection} when is_binary(Collection) ->
                                        % 简单的查询，假设是查找操作
                                        Command = #{
                                            <<"find">> => Collection,
                                            <<"limit">> => 10
                                        },
                                        {true, #{<<"cursor">> := #{<<"firstBatch">> := DocsResult}}} = emqx_plugin_mongodb_api:command(Pid, Command),
                                        {ok, DocsResult};

                                    {_QueryType2, Collection, Doc} when is_binary(Collection) ->
                                        % 假设是插入操作
                                        emqx_plugin_mongodb_api:smart_insert(Pid, Collection, [Doc]),
                                        ok;

                                    {_QueryType3, Collection, Filter, Update} when is_binary(Collection) ->
                                        % 假设是更新操作
                                        Command = #{
                                            <<"update">> => Collection,
                                            <<"updates">> => [
                                                #{
                                                    <<"q">> => Filter,
                                                    <<"u">> => Update,
                                                    <<"upsert">> => false
                                                }
                                            ]
                                        },
                                        {ok, _} = emqx_plugin_mongodb_api:command(Pid, Command),
                                        ok;

                                    _ ->
                                        % 实在无法处理的查询，返回空结果而不是错误
                                        ?SLOG(warning, #{
                                            msg => "using_empty_result_fallback_for_batch",
                                            inst_id => InstId,
                                            request => Request
                                        }),
                                        {ok, []}
                                end
                            catch
                                EC:ER:ES ->
                                    ?SLOG(error, #{
                                        msg => "fallback_handler_failed_for_batch",
                                        error => EC,
                                        reason => ER,
                                        stack => ES,
                                        request => Request
                                    }),
                                    % 即使处理失败，也返回空结果而不是错误
                                    {ok, []}
                            end
                    end
                catch
                    E:R:S ->
                        ?SLOG(error, #{
                            msg => "batch_async_query_request_failed",
                            error => E,
                            reason => R,
                            stack => S,
                            request => Request
                        }),
                        {error, {batch_async_query_failed, {E, R}}}
                end
            end,
            Requests
        ),

        % 检查结果
        case [E || E <- Results, is_tuple(E), element(1, E) =:= error] of
            [] ->
                % 全部成功
                record_success(),
                % 调用回调函数
                erlang:apply(ReplyFun, ReplyArgs ++ [ok]),
                {ok, self()};
            Errors ->
                % 部分失败
                record_failure(),
                ?SLOG(error, #{
                    msg => "some_batch_async_queries_failed",
                    errors_count => length(Errors),
                    first_error => hd(Errors)
                }),
                % 调用回调函数
                erlang:apply(ReplyFun, ReplyArgs ++ [{error, {partial_failure, length(Errors)}}]),
                {ok, self()}
        end
    catch
        E:R:S ->
            record_failure(),
            ?SLOG(error, #{
                msg => "exception_handling_batch_async_query",
                error => E,
                reason => R,
                stack => S,
                inst_id => InstId
            }),
            % 调用回调函数
            erlang:apply(ReplyFun, ReplyArgs ++ [{error, {batch_async_query_exception, {E, R}}}]),
            {ok, self()}
    end.

%% @doc 执行批量更新操作
do_query(bulk_update, {Collection, BulkUpdates}, State = #{poolname := Pool}) ->
    try
        % 获取MongoDB连接
        Conn = ecpool:with_client(Pool, fun(Worker) -> Worker end),

        % 执行批量更新操作
        Command = #{
            <<"update">> => Collection,
            <<"updates">> => BulkUpdates,
            <<"ordered">> => false  % 允许并行处理
        },

        % 执行命令
        case emqx_plugin_mongodb_api:command(Conn, Command) of
            {ok, Result} ->
                {reply, {ok, Result}, State};
            {error, Error} ->
                {reply, {error, Error}, State}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "mongodb_bulk_update_error",
                collection => Collection,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {reply, {error, {E, R}}, State}
    end;

%% @doc 执行批量更新操作 - 多个文档
do_query(update_many, {Collection, Filter, Update}, State = #{poolname := Pool}) ->
    try
        % 获取MongoDB连接
        Conn = ecpool:with_client(Pool, fun(Worker) -> Worker end),

        % 执行批量更新操作
        Command = #{
            <<"update">> => Collection,
            <<"updates">> => [
                #{
                    <<"q">> => Filter,
                    <<"u">> => Update,
                    <<"multi">> => true
                }
            ]
        },

        % 执行命令
        case emqx_plugin_mongodb_api:command(Conn, Command) of
            {ok, Result} ->
                {reply, {ok, Result}, State};
            {error, Error} ->
                {reply, {error, Error}, State}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "mongodb_update_many_error",
                collection => Collection,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {reply, {error, {E, R}}, State}
    end;

%% @doc 执行批量插入操作
do_query(bulk_insert, {Collection, Documents}, State = #{poolname := Pool}) ->
    try
        % 获取MongoDB连接
        Conn = ecpool:with_client(Pool, fun(Worker) -> Worker end),

        % 执行批量插入操作
        case emqx_plugin_mongodb_api:smart_insert(Conn, Collection, Documents) of
            {ok, Result} ->
                {reply, {ok, Result}, State};
            {error, Error} ->
                {reply, {error, Error}, State}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "mongodb_bulk_insert_error",
                collection => Collection,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {reply, {error, {E, R}}, State}
    end;

%% 默认处理子句
do_query(Type, Query, State) ->
    ?SLOG(info, #{msg => "unhandled_query_type", type => Type, query => Query}),
    {reply, {error, {unhandled_query_type, Type}}, State}.

% 处理消息以确保原始格式保存到MongoDB
process_message_for_mongodb(Message) when is_map(Message) ->
    % 处理map类型的消息，检查payload字段
    case maps:get(payload, Message, undefined) of
        undefined ->
            % 没有payload字段，返回原始消息
            Message;
        Payload when is_binary(Payload) ->
            % 记录原始payload信息，不进行解析
            ?SLOG(debug, #{
                msg => "keeping_original_payload_in_mongodb",
                payload_size => byte_size(Payload),
                payload_sample => case byte_size(Payload) > 100 of
                    true -> binary:part(Payload, 0, 100);
                    false -> Payload
                end
            }),
            % 直接返回原始消息，不修改payload
            Message;
        _ ->
            % payload不是二进制，保持原样
            Message
    end;
process_message_for_mongodb(Message) when is_list(Message) ->
    % 处理列表类型的消息，可能是多个文档
    [process_message_for_mongodb(Doc) || Doc <- Message];
process_message_for_mongodb(Message) ->
    % 其他类型的消息，保持原样
    Message.

% @doc 执行查询 - 一般查询接口
do_query({Querys, Message}, State = #{poolname := Pool}) ->
    try
        % 记录查询信息
        ?SLOG(debug, #{
            msg => "processing_regular_query",
            querys => Querys,
            message_keys => case is_map(Message) of
                true -> maps:keys(Message);
                false -> []
            end
        }),

        % 处理消息，确保JSON格式正确处理
        ProcessedMessage = process_message_for_mongodb(Message),

        % 从连接池获取MongoDB连接
        Conn = ecpool:with_client(Pool, fun(Worker) -> Worker end),

        % 执行插入操作
        Result = safe_do_batch_insert(Conn, Querys, ProcessedMessage),

        % 返回结果
        {reply, Result, State}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "query_execution_error",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {reply, {error, {E, R}}, State}
    end.



%% @doc 尝试从persistent_term恢复连接
-spec try_recover_connection() -> {ok, map()} | {error, term()}.
try_recover_connection() ->
    try
        case persistent_term:get(?PLUGIN_MONGODB_RESOURCE_ID, undefined) of
            undefined ->
                {error, no_persistent_connection};
            Pid when is_pid(Pid) ->
                % 检查进程是否还活着
                case is_process_alive(Pid) of
                    true ->
                        % 构造一个基本的连接器状态
                        {ok, #{
                            topology_pid => Pid,
                            reconnect_count => 0,
                            connection => #{},
                            mongo_version => <<"unknown">>,
                            health_check_cache => #{
                                last_check_time => 0,
                                last_check_result => undefined,
                                ttl => 10000
                            }
                        }};
                    false ->
                        {error, connection_process_dead}
                end;
            _ ->
                {error, invalid_persistent_connection}
        end
    catch
        E:R ->
            ?SLOG(warning, #{
                msg => "failed_to_recover_connection",
                error => E,
                reason => R
            }),
            {error, {E, R}}
    end.



%% @doc 在连接成功时检测MongoDB版本 - 简化版本
-spec detect_mongodb_version_on_connection_success(pid()) -> ok.
detect_mongodb_version_on_connection_success(Pid) ->
    % 直接执行，不再创建额外的进程
        try
            % 等待连接稳定
            timer:sleep(2000),

            ?SLOG(info, #{
                msg => "starting_mongodb_version_detection_on_connection_success",
                connection_pid => Pid
            }),

            % 执行版本检测
            case emqx_plugin_mongodb_api:command(Pid, #{<<"buildInfo">> => 1}) of
                % 处理双重嵌套的 {ok, {ok, Response}} 格式
                {ok, {ok, #{<<"version">> := Version}}} when is_binary(Version) ->
                    ?SLOG(info, #{
                        msg => "mongodb_version_detected_successfully_on_connection_success",
                        version => Version
                    });
                {ok, {ok, Response}} when is_map(Response) ->
                    case maps:get(<<"version">>, Response, undefined) of
                        Version when is_binary(Version) ->
                            ?SLOG(info, #{
                                msg => "mongodb_version_extracted_successfully_on_connection_success",
                                version => Version
                            });
                        _ ->
                            ?SLOG(warning, #{
                                msg => "mongodb_version_not_found_in_response_on_connection_success",
                                response_keys => maps:keys(Response)
                            })
                    end;
                % 处理单层 {ok, Response} 格式（向后兼容）
                {ok, #{<<"version">> := Version}} when is_binary(Version) ->
                    ?SLOG(info, #{
                        msg => "mongodb_version_detected_successfully_on_connection_success_single_ok",
                        version => Version
                    });
                {ok, Response} when is_map(Response) ->
                    case maps:get(<<"version">>, Response, undefined) of
                        Version when is_binary(Version) ->
                            ?SLOG(info, #{
                                msg => "mongodb_version_extracted_successfully_on_connection_success_single_ok",
                                version => Version
                            });
                        _ ->
                            ?SLOG(warning, #{
                                msg => "mongodb_version_not_found_in_response_on_connection_success_single_ok",
                                response_keys => maps:keys(Response)
                            })
                    end;
                {error, Reason} ->
                    ?SLOG(warning, #{
                        msg => "mongodb_version_detection_failed_on_connection_success",
                        reason => Reason
                    });
                UnexpectedResult ->
                    ?SLOG(warning, #{
                        msg => "mongodb_version_detection_unexpected_result_on_connection_success",
                        result => UnexpectedResult
                    })
            end
        catch
            Class:Error:Stack ->
                ?SLOG(warning, #{
                    msg => "mongodb_version_detection_exception_on_connection_success",
                    class => Class,
                    error => Error,
                    stack => Stack
                })
        end,
    ok.
