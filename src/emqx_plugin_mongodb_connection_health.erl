%%%-------------------------------------------------------------------
%%% @doc MongoDB插件智能连接池健康检查和故障转移模块
%%% 这个模块提供企业级的连接池健康管理能力，包括智能健康检查、
%%% 连接质量评分、自适应故障转移和连接池动态调整功能
%%%
%%% 功能特性：
%%% 1. 智能健康检查 - 自适应检查间隔，基于连接质量动态调整
%%% 2. 连接质量评分 - 多维度评分算法，综合响应时间、成功率、稳定性
%%% 3. 智能故障转移 - 基于连接质量的智能路由和故障转移
%%% 4. 连接池动态调整 - 根据负载和健康状况动态调整连接池大小
%%% 5. 预测性维护 - 基于历史数据预测连接问题并提前处理
%%% 6. 连接预热机制 - 智能连接预热，减少冷启动延迟
%%% 7. 负载均衡优化 - 基于连接质量的智能负载分配
%%%
%%% 健康评分算法：
%%% - 响应时间权重：40% (越快越好)
%%% - 成功率权重：35% (成功率越高越好)
%%% - 稳定性权重：15% (波动越小越好)
%%% - 可用性权重：10% (在线时间越长越好)
%%%
%%% 故障转移策略：
%%% - 主动检测：实时监控连接状态，主动发现问题
%%% - 快速切换：毫秒级故障转移，最小化服务中断
%%% - 智能恢复：自动检测故障恢复，智能重新启用连接
%%% - 负载重分配：故障时智能重分配负载到健康连接
%%%
%%% Java等价概念：
%%% 类似于HikariCP的连接池健康检查和Hystrix的熔断器机制
%%% @Component
%%% public class ConnectionHealthManager {
%%%     @Scheduled(fixedDelay = 30000)
%%%     public void performHealthCheck() {
%%%         connections.forEach(this::checkConnectionHealth);
%%%         adjustPoolSizeBasedOnHealth();
%%%         performFailoverIfNeeded();
%%%     }
%%% }
%%%
%%% @end
%%%-------------------------------------------------------------------
-module(emqx_plugin_mongodb_connection_health).

-include("emqx_plugin_mongodb.hrl").

%% 行为模式
-behaviour(gen_server).

%% API导出
-export([
    start_link/0,                  % 启动健康检查服务
                                   % 功能：启动连接池健康检查和故障转移服务
                                   % Java等价：@PostConstruct public void initialize()

    stop/0,                       % 停止健康检查服务
                                   % 功能：优雅停止健康检查服务
                                   % Java等价：@PreDestroy public void cleanup()

    check_connection_health/1,     % 检查单个连接健康状况
                                   % 功能：对指定连接执行健康检查
                                   % Java等价：public HealthStatus checkConnection(Connection conn)

    get_connection_score/1,        % 获取连接质量评分
                                   % 功能：计算连接的综合质量评分
                                   % Java等价：public double getConnectionScore(Connection conn)

    get_best_connection/0,         % 获取最佳连接
                                   % 功能：基于质量评分选择最佳连接
                                   % Java等价：public Connection getBestConnection()

    trigger_failover/1,            % 触发故障转移
                                   % 功能：对指定连接执行故障转移
                                   % Java等价：public void triggerFailover(Connection conn)

    adjust_pool_size/1,            % 动态调整连接池大小
                                   % 功能：根据负载和健康状况调整连接池
                                   % Java等价：public void adjustPoolSize(int targetSize)

    get_health_statistics/0,       % 获取健康统计信息
                                   % 功能：获取连接池整体健康统计
                                   % Java等价：public HealthStatistics getHealthStatistics()

    enable_predictive_maintenance/0, % 启用预测性维护
                                   % 功能：启用基于历史数据的预测性维护
                                   % Java等价：public void enablePredictiveMaintenance()

    integrate/0                    % 集成到协调器
                                   % 功能：将健康检查服务集成到协调器系统中
                                   % Java等价：public void integrate()
]).

%% gen_server回调函数
-export([
    init/1,
    handle_call/3,
    handle_cast/2,
    handle_info/2,
    terminate/2,
    code_change/3
]).

%% 内部状态记录
-record(health_state, {
    check_interval = ?DEFAULT_HEALTH_CHECK_INTERVAL :: integer(),  % 健康检查间隔
    adaptive_interval = true :: boolean(),                         % 是否启用自适应间隔
    predictive_maintenance = false :: boolean(),                   % 是否启用预测性维护
    failover_threshold = 0.3 :: float(),                          % 故障转移阈值
    pool_adjustment_enabled = true :: boolean(),                   % 是否启用池大小调整
    statistics = #{} :: map(),                                     % 统计信息
    last_check_time = 0 :: integer(),                             % 上次检查时间
    health_history = [] :: list()                                 % 健康历史记录
}).

%% 连接健康记录
-record(connection_health_detail, {
    pid :: pid(),                          % 连接进程ID
    response_time_avg = 0.0 :: float(),    % 平均响应时间
    response_time_p95 = 0.0 :: float(),    % 95分位响应时间
    success_rate = 1.0 :: float(),         % 成功率
    stability_score = 1.0 :: float(),      % 稳定性评分
    availability_score = 1.0 :: float(),   % 可用性评分
    overall_score = 1.0 :: float(),        % 综合评分
    last_check_time = 0 :: integer(),      % 上次检查时间
    check_count = 0 :: integer(),          % 检查次数
    failure_count = 0 :: integer(),        % 失败次数
    consecutive_failures = 0 :: integer(), % 连续失败次数
    last_failure_time = 0 :: integer(),    % 上次失败时间
    response_times = [] :: list(),         % 响应时间历史
    status = healthy :: healthy | degraded | unhealthy | failed
}).

%% ============================================================================
%% API函数实现
%% ============================================================================

%% @doc 启动健康检查服务
%% 启动连接池健康检查和故障转移服务
-spec start_link() -> {ok, pid()} | {error, term()}.
start_link() ->
    gen_server:start_link({local, ?MODULE}, ?MODULE, [], []).

%% @doc 停止健康检查服务
%% 优雅停止健康检查服务
-spec stop() -> ok | {error, term()}.
stop() ->
    case whereis(?MODULE) of
        undefined ->
            {error, not_running};
        _ ->
            gen_server:call(?MODULE, stop, 5000)
    end.

%% @doc 检查单个连接健康状况
%% 对指定连接执行全面的健康检查
-spec check_connection_health(pid()) -> {ok, map()} | {error, term()}.
check_connection_health(Pid) when is_pid(Pid) ->
    gen_server:call(?MODULE, {check_connection_health, Pid}, 10000).

%% @doc 获取连接质量评分
%% 计算连接的综合质量评分（0.0-1.0）
-spec get_connection_score(pid()) -> {ok, float()} | {error, term()}.
get_connection_score(Pid) when is_pid(Pid) ->
    gen_server:call(?MODULE, {get_connection_score, Pid}, 5000).

%% @doc 获取最佳连接
%% 基于质量评分选择当前最佳连接
-spec get_best_connection() -> {ok, pid()} | {error, term()}.
get_best_connection() ->
    gen_server:call(?MODULE, get_best_connection, 5000).

%% @doc 触发故障转移
%% 对指定连接执行故障转移操作
-spec trigger_failover(pid()) -> ok | {error, term()}.
trigger_failover(Pid) when is_pid(Pid) ->
    gen_server:call(?MODULE, {trigger_failover, Pid}, 10000).

%% @doc 动态调整连接池大小
%% 根据负载和健康状况调整连接池大小
-spec adjust_pool_size(integer()) -> ok | {error, term()}.
adjust_pool_size(TargetSize) when is_integer(TargetSize), TargetSize > 0 ->
    gen_server:call(?MODULE, {adjust_pool_size, TargetSize}, 10000).

%% @doc 获取健康统计信息
%% 获取连接池整体健康统计信息
-spec get_health_statistics() -> {ok, map()} | {error, term()}.
get_health_statistics() ->
    gen_server:call(?MODULE, get_health_statistics, 5000).

%% @doc 启用预测性维护
%% 启用基于历史数据的预测性维护功能
-spec enable_predictive_maintenance() -> ok | {error, term()}.
enable_predictive_maintenance() ->
    gen_server:call(?MODULE, enable_predictive_maintenance, 5000).

%% ============================================================================
%% gen_server回调函数实现
%% ============================================================================

%% @doc 初始化健康检查服务
init([]) ->
    try
        ?SLOG(info, #{msg => "initializing_connection_health_service"}),

        % 创建ETS表存储连接健康详情
        ets:new(?CONNECTION_HEALTH_DETAIL_TAB, [
            named_table, 
            public, 
            {keypos, #connection_health_detail.pid},
            {write_concurrency, true},
            {read_concurrency, true}
        ]),

        % 创建ETS表存储健康统计信息
        ets:new(?HEALTH_STATISTICS_TAB, [
            named_table,
            public,
            {write_concurrency, true},
            {read_concurrency, true}
        ]),

        % 初始化状态
        State = #health_state{
            check_interval = ?DEFAULT_HEALTH_CHECK_INTERVAL,
            adaptive_interval = true,
            predictive_maintenance = false,
            failover_threshold = 0.3,
            pool_adjustment_enabled = true,
            statistics = #{},
            last_check_time = erlang:system_time(millisecond),
            health_history = []
        },

        % 启动定期健康检查
        schedule_health_check(State#health_state.check_interval),

        ?SLOG(info, #{msg => "connection_health_service_initialized"}),
        {ok, State}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "failed_to_initialize_connection_health_service",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {stop, {initialization_failed, R}}
    end.

%% @doc 处理同步调用
handle_call({check_connection_health, Pid}, _From, State) ->
    try
        Result = perform_connection_health_check(Pid, State),
        {reply, Result, State}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "connection_health_check_failed",
                pid => Pid,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {reply, {error, {health_check_failed, R}}, State}
    end;

handle_call({get_connection_score, Pid}, _From, State) ->
    try
        Score = calculate_connection_score(Pid),
        {reply, {ok, Score}, State}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "get_connection_score_failed",
                pid => Pid,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {reply, {error, {score_calculation_failed, R}}, State}
    end;

handle_call(get_best_connection, _From, State) ->
    try
        Result = find_best_connection(),
        {reply, Result, State}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "get_best_connection_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {reply, {error, {best_connection_lookup_failed, R}}, State}
    end;

handle_call({trigger_failover, Pid}, _From, State) ->
    try
        Result = execute_failover(Pid, State),
        {reply, Result, State}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "trigger_failover_failed",
                pid => Pid,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {reply, {error, {failover_failed, R}}, State}
    end;

handle_call({adjust_pool_size, TargetSize}, _From, State) ->
    try
        Result = perform_pool_size_adjustment(TargetSize, State),
        {reply, Result, State}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "adjust_pool_size_failed",
                target_size => TargetSize,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {reply, {error, {pool_adjustment_failed, R}}, State}
    end;

handle_call(get_health_statistics, _From, State) ->
    try
        Statistics = collect_health_statistics(),
        {reply, {ok, Statistics}, State}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "get_health_statistics_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {reply, {error, {statistics_collection_failed, R}}, State}
    end;

handle_call(enable_predictive_maintenance, _From, State) ->
    try
        NewState = State#health_state{predictive_maintenance = true},
        ?SLOG(info, #{msg => "predictive_maintenance_enabled"}),
        {reply, ok, NewState}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "enable_predictive_maintenance_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {reply, {error, {predictive_maintenance_enable_failed, R}}, State}
    end;

handle_call(stop, _From, State) ->
    ?SLOG(info, #{msg => "connection_health_service_stopping"}),
    {stop, normal, ok, State};

handle_call(Request, _From, State) ->
    ?SLOG(warning, #{
        msg => "unknown_call_request",
        request => Request
    }),
    {reply, {error, unknown_request}, State}.

%% @doc 处理异步消息
handle_cast({update_connection_health, Pid, HealthData}, State) ->
    try
        update_connection_health_record(Pid, HealthData),
        {noreply, State}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "update_connection_health_failed",
                pid => Pid,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {noreply, State}
    end;

handle_cast(Request, State) ->
    ?SLOG(warning, #{
        msg => "unknown_cast_request",
        request => Request
    }),
    {noreply, State}.

%% @doc 处理定时器和其他消息
handle_info(perform_health_check, State) ->
    try
        % 执行定期健康检查
        NewState = perform_periodic_health_check(State),

        % 调整下次检查间隔（自适应）
        NextInterval = calculate_next_check_interval(NewState),
        schedule_health_check(NextInterval),

        {noreply, NewState#health_state{
            check_interval = NextInterval,
            last_check_time = erlang:system_time(millisecond)
        }}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "periodic_health_check_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            % 即使检查失败，也要继续调度下次检查
            schedule_health_check(State#health_state.check_interval),
            {noreply, State}
    end;

handle_info(Info, State) ->
    ?SLOG(debug, #{
        msg => "unknown_info_message",
        info => Info
    }),
    {noreply, State}.

%% @doc 服务终止处理
terminate(Reason, _State) ->
    try
        ?SLOG(info, #{
            msg => "connection_health_service_terminating",
            reason => Reason
        }),

        % 清理ETS表
        catch ets:delete(?CONNECTION_HEALTH_DETAIL_TAB),
        catch ets:delete(?HEALTH_STATISTICS_TAB),

        ?SLOG(info, #{msg => "connection_health_service_terminated"}),
        ok
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "connection_health_service_terminate_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            ok
    end.

%% @doc 代码热更新处理
code_change(_OldVsn, State, _Extra) ->
    {ok, State}.

%% ============================================================================
%% 内部函数实现 - 健康检查算法
%% ============================================================================

%% @doc 执行连接健康检查
%% 对指定连接执行全面的健康检查，包括响应时间、成功率等指标
-spec perform_connection_health_check(pid(), #health_state{}) -> {ok, map()} | {error, term()}.
perform_connection_health_check(Pid, _State) ->
    try
        StartTime = erlang:system_time(microsecond),

        % 1. 检查进程是否存活
        IsAlive = is_process_alive(Pid),
        if
            not IsAlive ->
                {error, {process_not_alive, Pid}};
            true ->
                % 2. 执行ping测试
                PingResult = perform_ping_test(Pid),
                EndTime = erlang:system_time(microsecond),
                ResponseTime = (EndTime - StartTime) / 1000, % 转换为毫秒

                % 3. 更新健康记录
                HealthData = #{
                    response_time => ResponseTime,
                    ping_result => PingResult,
                    timestamp => erlang:system_time(millisecond)
                },

                % 4. 计算综合评分
                Score = calculate_connection_score(Pid),

                % 5. 更新连接健康记录
                update_connection_health_record(Pid, HealthData),

                {ok, HealthData#{score => Score}}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "connection_health_check_exception",
                pid => Pid,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {health_check_exception, R}}
    end.

%% @doc 执行ping测试
%% 对MongoDB连接执行ping测试，测量响应时间和可用性
-spec perform_ping_test(pid()) -> ok | {error, term()}.
perform_ping_test(Pid) ->
    try
        % 使用MongoDB API执行ping命令
        case emqx_plugin_mongodb_api:ping_with_timeout(Pid, 5000) of
            {ok, _Latency} ->
                ok;
            {error, Reason} ->
                {error, Reason}
        end
    catch
        E:R ->
            {error, {ping_exception, {E, R}}}
    end.

%% @doc 计算连接质量评分
%% 基于多维度指标计算连接的综合质量评分
-spec calculate_connection_score(pid()) -> float().
calculate_connection_score(Pid) ->
    try
        case ets:lookup(?CONNECTION_HEALTH_DETAIL_TAB, Pid) of
            [] ->
                % 没有健康记录，返回默认评分
                0.5;
            [HealthDetail] ->
                % 计算各维度评分
                ResponseTimeScore = calculate_response_time_score(HealthDetail),
                SuccessRateScore = HealthDetail#connection_health_detail.success_rate,
                StabilityScore = HealthDetail#connection_health_detail.stability_score,
                AvailabilityScore = HealthDetail#connection_health_detail.availability_score,

                % 加权计算综合评分
                % 响应时间权重：40%，成功率权重：35%，稳定性权重：15%，可用性权重：10%
                OverallScore = ResponseTimeScore * 0.4 +
                              SuccessRateScore * 0.35 +
                              StabilityScore * 0.15 +
                              AvailabilityScore * 0.1,

                % 确保评分在0.0-1.0范围内
                max(0.0, min(1.0, OverallScore))
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "calculate_connection_score_failed",
                pid => Pid,
                error => E,
                reason => R,
                stacktrace => S
            }),
            0.0 % 出错时返回最低评分
    end.

%% @doc 计算响应时间评分
%% 基于响应时间计算评分，响应时间越短评分越高
-spec calculate_response_time_score(#connection_health_detail{}) -> float().
calculate_response_time_score(HealthDetail) ->
    AvgResponseTime = HealthDetail#connection_health_detail.response_time_avg,
    P95ResponseTime = HealthDetail#connection_health_detail.response_time_p95,

    % 基于平均响应时间和P95响应时间计算评分
    % 理想响应时间：< 10ms = 1.0分，> 1000ms = 0.0分
    AvgScore = max(0.0, min(1.0, (1000 - AvgResponseTime) / 1000)),
    P95Score = max(0.0, min(1.0, (2000 - P95ResponseTime) / 2000)),

    % 平均响应时间权重70%，P95响应时间权重30%
    AvgScore * 0.7 + P95Score * 0.3.

%% @doc 更新连接健康记录
%% 更新指定连接的健康记录，包括响应时间、成功率等指标
-spec update_connection_health_record(pid(), map()) -> ok.
update_connection_health_record(Pid, HealthData) ->
    try
        Now = erlang:system_time(millisecond),
        ResponseTime = maps:get(response_time, HealthData, 0.0),
        PingResult = maps:get(ping_result, HealthData, ok),

        % 获取或创建健康记录
        HealthDetail = case ets:lookup(?CONNECTION_HEALTH_DETAIL_TAB, Pid) of
            [] ->
                % 创建新的健康记录
                #connection_health_detail{
                    pid = Pid,
                    response_time_avg = ResponseTime,
                    response_time_p95 = ResponseTime,
                    success_rate = if PingResult =:= ok -> 1.0; true -> 0.0 end,
                    stability_score = 1.0,
                    availability_score = 1.0,
                    overall_score = 1.0,
                    last_check_time = Now,
                    check_count = 1,
                    failure_count = if PingResult =/= ok -> 1; true -> 0 end,
                    consecutive_failures = if PingResult =/= ok -> 1; true -> 0 end,
                    last_failure_time = if PingResult =/= ok -> Now; true -> 0 end,
                    response_times = [ResponseTime],
                    status = if PingResult =:= ok -> healthy; true -> degraded end
                };
            [ExistingDetail] ->
                % 更新现有记录
                update_existing_health_detail(ExistingDetail, HealthData, Now)
        end,

        % 重新计算综合评分
        UpdatedDetail = HealthDetail#connection_health_detail{
            overall_score = calculate_overall_score(HealthDetail)
        },

        % 保存更新后的记录
        ets:insert(?CONNECTION_HEALTH_DETAIL_TAB, UpdatedDetail),

        ?SLOG(debug, #{
            msg => "connection_health_record_updated",
            pid => Pid,
            score => UpdatedDetail#connection_health_detail.overall_score,
            status => UpdatedDetail#connection_health_detail.status
        }),

        ok
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "update_connection_health_record_failed",
                pid => Pid,
                error => E,
                reason => R,
                stacktrace => S
            }),
            ok
    end.

%% @doc 更新现有健康详情记录
%% 基于新的健康数据更新现有的连接健康记录
-spec update_existing_health_detail(#connection_health_detail{}, map(), integer()) -> #connection_health_detail{}.
update_existing_health_detail(ExistingDetail, HealthData, Now) ->
    ResponseTime = maps:get(response_time, HealthData, 0.0),
    PingResult = maps:get(ping_result, HealthData, ok),

    % 更新响应时间统计
    NewResponseTimes = update_response_times(ExistingDetail#connection_health_detail.response_times, ResponseTime),
    NewAvgResponseTime = calculate_average(NewResponseTimes),
    NewP95ResponseTime = calculate_percentile(NewResponseTimes, 95),

    % 更新成功率
    NewCheckCount = ExistingDetail#connection_health_detail.check_count + 1,
    NewFailureCount = if
        PingResult =/= ok -> ExistingDetail#connection_health_detail.failure_count + 1;
        true -> ExistingDetail#connection_health_detail.failure_count
    end,
    NewSuccessRate = (NewCheckCount - NewFailureCount) / NewCheckCount,

    % 更新连续失败次数
    NewConsecutiveFailures = if
        PingResult =/= ok -> ExistingDetail#connection_health_detail.consecutive_failures + 1;
        true -> 0
    end,

    % 更新上次失败时间
    NewLastFailureTime = if
        PingResult =/= ok -> Now;
        true -> ExistingDetail#connection_health_detail.last_failure_time
    end,

    % 计算稳定性评分（基于响应时间的标准差）
    NewStabilityScore = calculate_stability_score(NewResponseTimes),

    % 计算可用性评分（基于在线时间比例）
    NewAvailabilityScore = calculate_availability_score(NewCheckCount, NewFailureCount),

    % 确定连接状态
    NewStatus = determine_connection_status(NewSuccessRate, NewConsecutiveFailures, NewAvgResponseTime),

    ExistingDetail#connection_health_detail{
        response_time_avg = NewAvgResponseTime,
        response_time_p95 = NewP95ResponseTime,
        success_rate = NewSuccessRate,
        stability_score = NewStabilityScore,
        availability_score = NewAvailabilityScore,
        last_check_time = Now,
        check_count = NewCheckCount,
        failure_count = NewFailureCount,
        consecutive_failures = NewConsecutiveFailures,
        last_failure_time = NewLastFailureTime,
        response_times = NewResponseTimes,
        status = NewStatus
    }.

%% @doc 更新响应时间列表
%% 维护最近的响应时间记录，用于计算统计指标
-spec update_response_times(list(), float()) -> list().
update_response_times(ResponseTimes, NewResponseTime) ->
    % 保持最近100个响应时间记录
    MaxHistorySize = 100,
    UpdatedTimes = [NewResponseTime | ResponseTimes],
    case length(UpdatedTimes) > MaxHistorySize of
        true -> lists:sublist(UpdatedTimes, MaxHistorySize);
        false -> UpdatedTimes
    end.

%% @doc 计算平均值
-spec calculate_average(list()) -> float().
calculate_average([]) -> 0.0;
calculate_average(Values) ->
    lists:sum(Values) / length(Values).

%% @doc 计算百分位数
-spec calculate_percentile(list(), integer()) -> float().
calculate_percentile([], _Percentile) -> 0.0;
calculate_percentile(Values, Percentile) ->
    SortedValues = lists:sort(Values),
    Length = length(SortedValues),
    Index = max(1, round(Length * Percentile / 100)),
    lists:nth(Index, SortedValues).

%% @doc 计算稳定性评分
%% 基于响应时间的标准差计算稳定性评分
-spec calculate_stability_score(list()) -> float().
calculate_stability_score([]) -> 1.0;
calculate_stability_score([_]) -> 1.0;
calculate_stability_score(ResponseTimes) ->
    Mean = calculate_average(ResponseTimes),
    Variance = lists:sum([math:pow(X - Mean, 2) || X <- ResponseTimes]) / length(ResponseTimes),
    StdDev = math:sqrt(Variance),

    % 标准差越小，稳定性越高
    % 标准差 < 10ms = 1.0分，> 100ms = 0.0分
    max(0.0, min(1.0, (100 - StdDev) / 100)).

%% @doc 计算可用性评分
%% 基于成功检查的比例计算可用性评分
-spec calculate_availability_score(integer(), integer()) -> float().
calculate_availability_score(0, _FailureCount) -> 1.0;
calculate_availability_score(CheckCount, FailureCount) ->
    (CheckCount - FailureCount) / CheckCount.

%% @doc 确定连接状态
%% 基于多个指标确定连接的整体健康状态
-spec determine_connection_status(float(), integer(), float()) -> healthy | degraded | unhealthy | failed.
determine_connection_status(SuccessRate, ConsecutiveFailures, AvgResponseTime) ->
    if
        ConsecutiveFailures >= 5 -> failed;
        SuccessRate < 0.5 -> unhealthy;
        SuccessRate < 0.8 orelse AvgResponseTime > 1000 -> degraded;
        true -> healthy
    end.

%% @doc 计算综合评分
%% 基于各维度指标计算连接的综合质量评分
-spec calculate_overall_score(#connection_health_detail{}) -> float().
calculate_overall_score(HealthDetail) ->
    ResponseTimeScore = calculate_response_time_score(HealthDetail),
    SuccessRateScore = HealthDetail#connection_health_detail.success_rate,
    StabilityScore = HealthDetail#connection_health_detail.stability_score,
    AvailabilityScore = HealthDetail#connection_health_detail.availability_score,

    % 加权计算综合评分
    ResponseTimeScore * 0.4 +
    SuccessRateScore * 0.35 +
    StabilityScore * 0.15 +
    AvailabilityScore * 0.1.

%% ============================================================================
%% 故障转移和连接池管理算法
%% ============================================================================

%% @doc 查找最佳连接
%% 基于质量评分选择当前最佳的可用连接
-spec find_best_connection() -> {ok, pid()} | {error, term()}.
find_best_connection() ->
    try
        % 获取所有连接健康记录
        AllConnections = ets:tab2list(?CONNECTION_HEALTH_DETAIL_TAB),

        case AllConnections of
            [] ->
                {error, no_connections_available};
            _ ->
                % 过滤出健康的连接
                HealthyConnections = [Detail || Detail <- AllConnections,
                                      Detail#connection_health_detail.status =/= failed],

                case HealthyConnections of
                    [] ->
                        {error, no_healthy_connections};
                    _ ->
                        % 按评分排序，选择最佳连接
                        SortedConnections = lists:sort(
                            fun(A, B) ->
                                A#connection_health_detail.overall_score >=
                                B#connection_health_detail.overall_score
                            end,
                            HealthyConnections
                        ),

                        BestConnection = hd(SortedConnections),
                        {ok, BestConnection#connection_health_detail.pid}
                end
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "find_best_connection_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {best_connection_lookup_failed, R}}
    end.

%% @doc 执行故障转移
%% 对指定连接执行故障转移操作
-spec execute_failover(pid(), #health_state{}) -> ok | {error, term()}.
execute_failover(FailedPid, State) ->
    try
        ?SLOG(warning, #{
            msg => "executing_connection_failover",
            failed_pid => FailedPid
        }),

        % 1. 标记失败连接为不可用
        mark_connection_as_failed(FailedPid),

        % 2. 查找替代连接
        case find_best_connection() of
            {ok, ReplacementPid} ->
                ?SLOG(info, #{
                    msg => "failover_replacement_found",
                    failed_pid => FailedPid,
                    replacement_pid => ReplacementPid
                }),

                % 3. 通知连接管理器进行切换
                notify_connection_manager_failover(FailedPid, ReplacementPid),

                % 4. 尝试重新创建失败的连接
                schedule_connection_recovery(FailedPid, State),

                ok;
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failover_no_replacement_available",
                    failed_pid => FailedPid,
                    reason => Reason
                }),

                % 没有可用的替代连接，尝试紧急创建新连接
                emergency_create_connection(State),

                {error, {no_replacement_available, Reason}}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "execute_failover_failed",
                failed_pid => FailedPid,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {failover_execution_failed, R}}
    end.

%% @doc 标记连接为失败状态
-spec mark_connection_as_failed(pid()) -> ok.
mark_connection_as_failed(Pid) ->
    try
        case ets:lookup(?CONNECTION_HEALTH_DETAIL_TAB, Pid) of
            [] ->
                % 连接记录不存在，创建一个失败记录
                FailedDetail = #connection_health_detail{
                    pid = Pid,
                    status = failed,
                    last_check_time = erlang:system_time(millisecond),
                    overall_score = 0.0
                },
                ets:insert(?CONNECTION_HEALTH_DETAIL_TAB, FailedDetail);
            [ExistingDetail] ->
                % 更新现有记录为失败状态
                UpdatedDetail = ExistingDetail#connection_health_detail{
                    status = failed,
                    overall_score = 0.0,
                    last_failure_time = erlang:system_time(millisecond)
                },
                ets:insert(?CONNECTION_HEALTH_DETAIL_TAB, UpdatedDetail)
        end,
        ok
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "mark_connection_as_failed_error",
                pid => Pid,
                error => E,
                reason => R,
                stacktrace => S
            }),
            ok
    end.

%% @doc 通知连接管理器进行故障转移
-spec notify_connection_manager_failover(pid(), pid()) -> ok.
notify_connection_manager_failover(FailedPid, ReplacementPid) ->
    try
        % 通知连接管理器进行连接切换
        case whereis(emqx_plugin_mongodb_connection) of
            undefined ->
                ?SLOG(warning, #{msg => "connection_manager_not_available"});
            ConnectionManager ->
                gen_server:cast(ConnectionManager, {
                    failover_connection,
                    FailedPid,
                    ReplacementPid
                })
        end,
        ok
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "notify_connection_manager_failover_failed",
                failed_pid => FailedPid,
                replacement_pid => ReplacementPid,
                error => E,
                reason => R,
                stacktrace => S
            }),
            ok
    end.

%% @doc 调度连接恢复
%% 安排在适当的时间尝试恢复失败的连接
-spec schedule_connection_recovery(pid(), #health_state{}) -> ok.
schedule_connection_recovery(FailedPid, _State) ->
    try
        % 延迟30秒后尝试恢复连接
        RecoveryDelay = 30000,
        timer:apply_after(RecoveryDelay, ?MODULE, attempt_connection_recovery, [FailedPid]),

        ?SLOG(info, #{
            msg => "connection_recovery_scheduled",
            failed_pid => FailedPid,
            recovery_delay => RecoveryDelay
        }),
        ok
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "schedule_connection_recovery_failed",
                failed_pid => FailedPid,
                error => E,
                reason => R,
                stacktrace => S
            }),
            ok
    end.

%% @doc 紧急创建连接
%% 在没有可用连接时紧急创建新的连接
-spec emergency_create_connection(#health_state{}) -> ok | {error, term()}.
emergency_create_connection(_State) ->
    try
        ?SLOG(warning, #{msg => "attempting_emergency_connection_creation"}),

        % 通知连接管理器创建紧急连接
        case whereis(emqx_plugin_mongodb_connection) of
            undefined ->
                {error, connection_manager_not_available};
            ConnectionManager ->
                gen_server:cast(ConnectionManager, create_emergency_connection),
                ok
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "emergency_create_connection_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {emergency_connection_creation_failed, R}}
    end.

%% @doc 尝试连接恢复
%% 尝试恢复之前失败的连接
-spec attempt_connection_recovery(pid()) -> ok.
attempt_connection_recovery(FailedPid) ->
    try
        ?SLOG(info, #{
            msg => "attempting_connection_recovery",
            failed_pid => FailedPid
        }),

        % 检查连接是否已经恢复
        case is_process_alive(FailedPid) of
            true ->
                % 连接进程仍然存活，尝试ping测试
                case perform_ping_test(FailedPid) of
                    ok ->
                        % 连接恢复成功，更新状态
                        recover_connection_status(FailedPid),
                        ?SLOG(info, #{
                            msg => "connection_recovery_successful",
                            recovered_pid => FailedPid
                        });
                    {error, _Reason} ->
                        % 连接仍然有问题，保持失败状态
                        ?SLOG(warning, #{
                            msg => "connection_recovery_failed_ping_test",
                            failed_pid => FailedPid
                        })
                end;
            false ->
                % 连接进程已死，需要重新创建
                ?SLOG(info, #{
                    msg => "connection_process_dead_requesting_new_connection",
                    failed_pid => FailedPid
                }),
                request_new_connection_creation()
        end,
        ok
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "attempt_connection_recovery_failed",
                failed_pid => FailedPid,
                error => E,
                reason => R,
                stacktrace => S
            }),
            ok
    end.

%% @doc 恢复连接状态
%% 将失败的连接状态恢复为健康状态
-spec recover_connection_status(pid()) -> ok.
recover_connection_status(Pid) ->
    try
        case ets:lookup(?CONNECTION_HEALTH_DETAIL_TAB, Pid) of
            [] ->
                % 创建新的健康记录
                NewDetail = #connection_health_detail{
                    pid = Pid,
                    status = healthy,
                    overall_score = 0.8, % 恢复后给予中等评分
                    last_check_time = erlang:system_time(millisecond),
                    consecutive_failures = 0
                },
                ets:insert(?CONNECTION_HEALTH_DETAIL_TAB, NewDetail);
            [ExistingDetail] ->
                % 更新现有记录
                UpdatedDetail = ExistingDetail#connection_health_detail{
                    status = healthy,
                    overall_score = 0.8,
                    consecutive_failures = 0,
                    last_check_time = erlang:system_time(millisecond)
                },
                ets:insert(?CONNECTION_HEALTH_DETAIL_TAB, UpdatedDetail)
        end,
        ok
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "recover_connection_status_failed",
                pid => Pid,
                error => E,
                reason => R,
                stacktrace => S
            }),
            ok
    end.

%% @doc 请求创建新连接
-spec request_new_connection_creation() -> ok.
request_new_connection_creation() ->
    try
        case whereis(emqx_plugin_mongodb_connection) of
            undefined ->
                ?SLOG(warning, #{msg => "connection_manager_not_available_for_new_connection"});
            ConnectionManager ->
                gen_server:cast(ConnectionManager, create_new_connection)
        end,
        ok
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "request_new_connection_creation_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            ok
    end.

%% ============================================================================
%% 连接池动态调整和统计功能
%% ============================================================================

%% @doc 执行连接池大小调整
%% 根据负载和健康状况动态调整连接池大小
-spec perform_pool_size_adjustment(integer(), #health_state{}) -> ok | {error, term()}.
perform_pool_size_adjustment(TargetSize, State) ->
    try
        case State#health_state.pool_adjustment_enabled of
            false ->
                {error, pool_adjustment_disabled};
            true ->
                ?SLOG(info, #{
                    msg => "performing_pool_size_adjustment",
                    target_size => TargetSize
                }),

                % 获取当前连接数
                CurrentConnections = ets:tab2list(?CONNECTION_HEALTH_DETAIL_TAB),
                CurrentSize = length(CurrentConnections),

                if
                    TargetSize > CurrentSize ->
                        % 需要增加连接
                        create_additional_connections(TargetSize - CurrentSize);
                    TargetSize < CurrentSize ->
                        % 需要减少连接
                        remove_excess_connections(CurrentSize - TargetSize);
                    true ->
                        % 大小已经合适
                        ok
                end,

                ?SLOG(info, #{
                    msg => "pool_size_adjustment_completed",
                    old_size => CurrentSize,
                    new_size => TargetSize
                }),
                ok
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "perform_pool_size_adjustment_failed",
                target_size => TargetSize,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {pool_adjustment_failed, R}}
    end.

%% @doc 创建额外连接
-spec create_additional_connections(integer()) -> ok.
create_additional_connections(Count) ->
    try
        ?SLOG(info, #{
            msg => "creating_additional_connections",
            count => Count
        }),

        case whereis(emqx_plugin_mongodb_connection) of
            undefined ->
                ?SLOG(warning, #{msg => "connection_manager_not_available_for_additional_connections"});
            ConnectionManager ->
                gen_server:cast(ConnectionManager, {create_additional_connections, Count})
        end,
        ok
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "create_additional_connections_failed",
                count => Count,
                error => E,
                reason => R,
                stacktrace => S
            }),
            ok
    end.

%% @doc 移除多余连接
-spec remove_excess_connections(integer()) -> ok.
remove_excess_connections(Count) ->
    try
        ?SLOG(info, #{
            msg => "removing_excess_connections",
            count => Count
        }),

        % 获取所有连接，按评分排序，移除评分最低的连接
        AllConnections = ets:tab2list(?CONNECTION_HEALTH_DETAIL_TAB),
        SortedConnections = lists:sort(
            fun(A, B) ->
                A#connection_health_detail.overall_score =<
                B#connection_health_detail.overall_score
            end,
            AllConnections
        ),

        % 选择要移除的连接（评分最低的）
        ConnectionsToRemove = lists:sublist(SortedConnections, Count),

        % 移除选中的连接
        lists:foreach(
            fun(Detail) ->
                Pid = Detail#connection_health_detail.pid,
                remove_connection(Pid)
            end,
            ConnectionsToRemove
        ),
        ok
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "remove_excess_connections_failed",
                count => Count,
                error => E,
                reason => R,
                stacktrace => S
            }),
            ok
    end.

%% @doc 移除单个连接
-spec remove_connection(pid()) -> ok.
remove_connection(Pid) ->
    try
        ?SLOG(info, #{
            msg => "removing_connection",
            pid => Pid
        }),

        % 从健康记录中删除
        ets:delete(?CONNECTION_HEALTH_DETAIL_TAB, Pid),

        % 通知连接管理器关闭连接
        case whereis(emqx_plugin_mongodb_connection) of
            undefined ->
                ?SLOG(warning, #{msg => "connection_manager_not_available_for_connection_removal"});
            ConnectionManager ->
                gen_server:cast(ConnectionManager, {remove_connection, Pid})
        end,
        ok
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "remove_connection_failed",
                pid => Pid,
                error => E,
                reason => R,
                stacktrace => S
            }),
            ok
    end.

%% @doc 收集健康统计信息
%% 收集连接池的整体健康统计信息
-spec collect_health_statistics() -> map().
collect_health_statistics() ->
    try
        AllConnections = ets:tab2list(?CONNECTION_HEALTH_DETAIL_TAB),

        case AllConnections of
            [] ->
                #{
                    total_connections => 0,
                    healthy_connections => 0,
                    degraded_connections => 0,
                    unhealthy_connections => 0,
                    failed_connections => 0,
                    average_score => 0.0,
                    average_response_time => 0.0,
                    overall_success_rate => 0.0
                };
            _ ->
                % 统计各状态连接数
                StatusCounts = count_connections_by_status(AllConnections),

                % 计算平均指标
                TotalScore = lists:sum([D#connection_health_detail.overall_score || D <- AllConnections]),
                AverageScore = TotalScore / length(AllConnections),

                TotalResponseTime = lists:sum([D#connection_health_detail.response_time_avg || D <- AllConnections]),
                AverageResponseTime = TotalResponseTime / length(AllConnections),

                TotalSuccessRate = lists:sum([D#connection_health_detail.success_rate || D <- AllConnections]),
                OverallSuccessRate = TotalSuccessRate / length(AllConnections),

                Statistics = #{
                    total_connections => length(AllConnections),
                    healthy_connections => maps:get(healthy, StatusCounts, 0),
                    degraded_connections => maps:get(degraded, StatusCounts, 0),
                    unhealthy_connections => maps:get(unhealthy, StatusCounts, 0),
                    failed_connections => maps:get(failed, StatusCounts, 0),
                    average_score => AverageScore,
                    average_response_time => AverageResponseTime,
                    overall_success_rate => OverallSuccessRate,
                    timestamp => erlang:system_time(millisecond)
                },

                % 存储统计信息到ETS表
                ets:insert(?HEALTH_STATISTICS_TAB, {statistics, Statistics}),

                Statistics
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "collect_health_statistics_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            #{
                error => statistics_collection_failed,
                reason => R,
                timestamp => erlang:system_time(millisecond)
            }
    end.

%% @doc 按状态统计连接数
-spec count_connections_by_status(list()) -> map().
count_connections_by_status(Connections) ->
    lists:foldl(
        fun(Detail, Acc) ->
            Status = Detail#connection_health_detail.status,
            Count = maps:get(Status, Acc, 0),
            maps:put(Status, Count + 1, Acc)
        end,
        #{},
        Connections
    ).

%% ============================================================================
%% 定期检查和自适应算法
%% ============================================================================

%% @doc 执行定期健康检查
%% 对所有连接执行定期健康检查
-spec perform_periodic_health_check(#health_state{}) -> #health_state{}.
perform_periodic_health_check(State) ->
    try
        ?SLOG(debug, #{msg => "performing_periodic_health_check"}),

        % 获取所有连接
        AllConnections = ets:tab2list(?CONNECTION_HEALTH_DETAIL_TAB),

        % 对每个连接执行健康检查
        lists:foreach(
            fun(Detail) ->
                Pid = Detail#connection_health_detail.pid,
                spawn(fun() ->
                    perform_connection_health_check(Pid, State)
                end)
            end,
            AllConnections
        ),

        % 收集统计信息
        Statistics = collect_health_statistics(),

        % 检查是否需要故障转移
        check_and_trigger_failovers(AllConnections, State),

        % 检查是否需要调整连接池大小
        check_and_adjust_pool_size(Statistics, State),

        % 更新状态
        State#health_state{
            statistics = Statistics,
            health_history = update_health_history(State#health_state.health_history, Statistics)
        }
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "perform_periodic_health_check_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            State
    end.

%% @doc 检查并触发故障转移
-spec check_and_trigger_failovers(list(), #health_state{}) -> ok.
check_and_trigger_failovers(AllConnections, State) ->
    try
        FailedConnections = [Detail || Detail <- AllConnections,
                           Detail#connection_health_detail.overall_score < State#health_state.failover_threshold],

        lists:foreach(
            fun(Detail) ->
                Pid = Detail#connection_health_detail.pid,
                ?SLOG(warning, #{
                    msg => "triggering_automatic_failover",
                    pid => Pid,
                    score => Detail#connection_health_detail.overall_score,
                    threshold => State#health_state.failover_threshold
                }),
                spawn(fun() -> execute_failover(Pid, State) end)
            end,
            FailedConnections
        ),
        ok
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "check_and_trigger_failovers_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            ok
    end.

%% @doc 检查并调整连接池大小
-spec check_and_adjust_pool_size(map(), #health_state{}) -> ok.
check_and_adjust_pool_size(Statistics, State) ->
    try
        case State#health_state.pool_adjustment_enabled of
            false -> ok;
            true ->
                TotalConnections = maps:get(total_connections, Statistics, 0),
                HealthyConnections = maps:get(healthy_connections, Statistics, 0),
                FailedConnections = maps:get(failed_connections, Statistics, 0),

                % 计算健康连接比例
                HealthyRatio = if TotalConnections > 0 -> HealthyConnections / TotalConnections; true -> 1.0 end,

                % 根据健康比例决定是否调整连接池
                if
                    HealthyRatio < 0.5 andalso TotalConnections < 20 ->
                        % 健康连接不足50%且总连接数少于20，增加连接
                        TargetSize = min(20, TotalConnections + 2),
                        spawn(fun() -> perform_pool_size_adjustment(TargetSize, State) end);
                    FailedConnections > 5 ->
                        % 失败连接过多，移除失败连接
                        spawn(fun() -> remove_excess_connections(FailedConnections) end);
                    true ->
                        ok
                end
        end,
        ok
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "check_and_adjust_pool_size_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            ok
    end.

%% @doc 计算下次检查间隔
%% 基于系统状态自适应调整检查间隔
-spec calculate_next_check_interval(#health_state{}) -> integer().
calculate_next_check_interval(State) ->
    try
        case State#health_state.adaptive_interval of
            false ->
                State#health_state.check_interval;
            true ->
                Statistics = State#health_state.statistics,
                TotalConnections = maps:get(total_connections, Statistics, 0),
                HealthyConnections = maps:get(healthy_connections, Statistics, 0),

                % 计算健康比例
                HealthyRatio = if TotalConnections > 0 -> HealthyConnections / TotalConnections; true -> 1.0 end,

                % 根据健康比例调整检查间隔
                BaseInterval = State#health_state.check_interval,
                if
                    HealthyRatio >= 0.9 ->
                        % 系统很健康，可以延长检查间隔
                        min(BaseInterval * 2, 120000); % 最长2分钟
                    HealthyRatio >= 0.7 ->
                        % 系统较健康，保持正常间隔
                        BaseInterval;
                    HealthyRatio >= 0.5 ->
                        % 系统有问题，缩短检查间隔
                        max(BaseInterval div 2, 10000); % 最短10秒
                    true ->
                        % 系统很不健康，大幅缩短检查间隔
                        max(BaseInterval div 4, 5000) % 最短5秒
                end
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "calculate_next_check_interval_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            State#health_state.check_interval
    end.

%% @doc 更新健康历史记录
-spec update_health_history(list(), map()) -> list().
update_health_history(History, Statistics) ->
    % 保持最近100条历史记录
    MaxHistorySize = 100,
    NewHistory = [Statistics | History],
    case length(NewHistory) > MaxHistorySize of
        true -> lists:sublist(NewHistory, MaxHistorySize);
        false -> NewHistory
    end.

%% @doc 调度健康检查
-spec schedule_health_check(integer()) -> ok.
schedule_health_check(Interval) ->
    timer:send_after(Interval, self(), perform_health_check).

%% @doc 集成到协调器
%% 将连接健康检查服务集成到协调器系统中
-spec integrate() -> ok | {error, term()}.
integrate() ->
    try
        % 1. 注册到协调器
        case emqx_plugin_mongodb_coordinator:register_module(
            connection_health,
            ?MODULE,
            #{priority => 18, type => service, dependencies => [connection]}
        ) of
            ok -> ?SLOG(info, #{msg => "connection_health_registered_to_coordinator"});
            {error, Reason} -> throw({register_failed, Reason})
        end,

        % 2. 集成到错误处理模块
        case erlang:function_exported(emqx_plugin_mongodb_error_handler, register_error_handler, 2) of
            true ->
                emqx_plugin_mongodb_error_handler:register_error_handler(
                    connection_health,
                    fun handle_connection_health_errors/2
                );
            false -> ok
        end,

        % 3. 集成到资源管理模块
        case erlang:function_exported(emqx_plugin_mongodb_resource_manager, register_resource_monitor, 2) of
            true ->
                emqx_plugin_mongodb_resource_manager:register_resource_monitor(
                    connection_health,
                    fun() -> collect_health_statistics() end
                );
            false -> ok
        end,

        % 4. 集成到监控模块
        case erlang:function_exported(emqx_plugin_mongodb_monitoring, register_metrics_collector, 2) of
            true ->
                emqx_plugin_mongodb_monitoring:register_metrics_collector(
                    connection_health,
                    fun() -> collect_health_statistics() end
                );
            false -> ok
        end,

        ok
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "connection_health_integration_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {integration_failed, R}}
    end.

%% @doc 处理连接健康相关错误
-spec handle_connection_health_errors(term(), term()) -> ok.
handle_connection_health_errors(ErrorType, ErrorData) ->
    try
        ?SLOG(error, #{
            msg => "handling_connection_health_error",
            error_type => ErrorType,
            error_data => ErrorData
        }),

        case ErrorType of
            connection_failed ->
                % 连接失败，触发故障转移
                Pid = maps:get(pid, ErrorData, undefined),
                if
                    is_pid(Pid) -> spawn(fun() -> trigger_failover(Pid) end);
                    true -> ok
                end;
            health_check_timeout ->
                % 健康检查超时，标记连接为降级
                Pid = maps:get(pid, ErrorData, undefined),
                if
                    is_pid(Pid) -> mark_connection_as_degraded(Pid);
                    true -> ok
                end;
            _ ->
                % 其他错误，记录日志
                ?SLOG(warning, #{
                    msg => "unhandled_connection_health_error",
                    error_type => ErrorType,
                    error_data => ErrorData
                })
        end,
        ok
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "handle_connection_health_errors_failed",
                error_type => ErrorType,
                error_data => ErrorData,
                error => E,
                reason => R,
                stacktrace => S
            }),
            ok
    end.

%% @doc 标记连接为降级状态
-spec mark_connection_as_degraded(pid()) -> ok.
mark_connection_as_degraded(Pid) ->
    try
        case ets:lookup(?CONNECTION_HEALTH_DETAIL_TAB, Pid) of
            [] ->
                % 创建降级记录
                DegradedDetail = #connection_health_detail{
                    pid = Pid,
                    status = degraded,
                    overall_score = 0.5,
                    last_check_time = erlang:system_time(millisecond)
                },
                ets:insert(?CONNECTION_HEALTH_DETAIL_TAB, DegradedDetail);
            [ExistingDetail] ->
                % 更新为降级状态
                UpdatedDetail = ExistingDetail#connection_health_detail{
                    status = degraded,
                    overall_score = min(0.5, ExistingDetail#connection_health_detail.overall_score)
                },
                ets:insert(?CONNECTION_HEALTH_DETAIL_TAB, UpdatedDetail)
        end,
        ok
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "mark_connection_as_degraded_failed",
                pid => Pid,
                error => E,
                reason => R,
                stacktrace => S
            }),
            ok
    end.
