%% @doc MQTT订阅持久化模块 - 企业级订阅管理和持久化系统
%% 这个模块是MQTT订阅持久化的核心组件，提供完整的订阅生命周期管理
%%
%% 功能概述：
%% 1. 订阅持久化 - 将MQTT客户端订阅信息持久化到MongoDB
%% 2. 订阅恢复 - 系统启动时自动恢复持久化的订阅到EMQX内存
%% 3. 共享订阅支持 - 完整支持MQTT 5.0的共享订阅功能
%% 4. 过期清理 - 定期清理过期的订阅，防止存储膨胀
%% 5. 统计监控 - 提供详细的订阅统计信息和监控指标
%% 6. 主题查询 - 支持按主题查找相关订阅
%% 7. 批量操作 - 支持批量订阅操作，提高性能
%%
%% MQTT订阅机制：
%% - 普通订阅：客户端订阅特定主题，接收匹配的消息
%% - 共享订阅：多个客户端共享同一订阅，消息负载均衡分发
%% - 订阅持久化：持久会话的订阅在客户端断开后保持
%% - 订阅恢复：客户端重连时恢复之前的订阅
%%
%% 架构设计：
%% - 事件驱动：通过EMQX钩子系统响应订阅生命周期事件
%% - 异步处理：所有持久化操作都是异步的，不阻塞订阅处理
%% - 状态同步：确保MongoDB和EMQX内存中的订阅状态一致
%% - 容错恢复：支持系统故障后的订阅状态恢复
%% - 性能优化：使用批量操作和索引优化，提高处理性能
%%
%% Java等价概念：
%% 类似于Spring Boot中的订阅管理服务：
%% @Service
%% @Component
%% @Transactional
%% public class SubscriptionPersistenceService {
%%     @Autowired private MongoTemplate mongoTemplate;
%%     @Autowired private SubscriptionRepository subscriptionRepository;
%%
%%     @EventListener
%%     @Async
%%     public void onSubscribed(SubscriptionEvent event) {
%%         // 异步保存订阅信息
%%         Subscription subscription = convertToSubscription(event);
%%         subscriptionRepository.save(subscription);
%%     }
%%
%%     @EventListener
%%     @Async
%%     public void onUnsubscribed(UnsubscriptionEvent event) {
%%         // 异步删除订阅信息
%%         subscriptionRepository.deleteByClientIdAndTopic(
%%             event.getClientId(), event.getTopic());
%%     }
%%
%%     @PostConstruct
%%     public void restoreSubscriptions() {
%%         // 系统启动时恢复持久化订阅
%%         List<Subscription> subscriptions = subscriptionRepository.findAll();
%%         for (Subscription sub : subscriptions) {
%%             emqxSubscriptionManager.restoreSubscription(sub);
%%         }
%%     }
%%
%%     @Scheduled(fixedDelay = 1800000) // 30分钟清理过期订阅
%%     public void cleanupExpiredSubscriptions() {
%%         subscriptionRepository.deleteExpiredSubscriptions();
%%     }
%% }
%%
%% 设计模式：
%% - 观察者模式：监听EMQX的订阅生命周期事件
%% - 策略模式：普通订阅和共享订阅使用不同的处理策略
%% - 模板方法模式：统一的订阅处理流程模板
%% - 工厂模式：订阅对象的创建和转换
%% @end

-module(emqx_plugin_mongodb_subscription).

-include("emqx_plugin_mongodb.hrl").

%% ============================================================================
%% 生命周期管理API - 模块启动、配置和关闭管理
%% 这些函数管理订阅持久化模块的完整生命周期
%% 类似于Java Spring Boot的生命周期管理：
%% @PostConstruct - 初始化资源和配置
%% @PreDestroy - 清理资源和保存状态
%% @ConfigurationProperties - 配置加载和验证
%% ============================================================================
-export([
    init/0,                 % 初始化模块 - 类似于@PostConstruct，设置基础环境
    load/1,                 % 加载配置 - 类似于@ConfigurationProperties，加载和验证配置
    unload/0                % 卸载模块 - 类似于@PreDestroy，清理资源和保存状态
]).

%% ============================================================================
%% EMQX订阅钩子回调函数 - 响应MQTT订阅生命周期事件
%% 这些函数作为EMQX钩子回调，监听订阅和取消订阅事件
%% 类似于Java Spring的事件监听器：
%% @EventListener
%% @Async  // 异步处理，不阻塞订阅处理
%% public void onSubscriptionEvent(SubscriptionEvent event) { ... }
%% ============================================================================
-export([
    on_session_subscribed/3,    % 会话订阅事件监听器
                               % 功能：当客户端订阅主题时触发，保存订阅信息
                               % Java等价：@EventListener(SessionSubscribedEvent.class)

    on_session_unsubscribed/3   % 会话取消订阅事件监听器
                               % 功能：当客户端取消订阅主题时触发，删除订阅信息
                               % Java等价：@EventListener(SessionUnsubscribedEvent.class)
]).

%% ============================================================================
%% 订阅管理核心API - 订阅的CRUD操作和管理功能
%% 这些函数提供订阅的完整生命周期管理
%% 类似于Java中的Repository层或Service层方法
%% ============================================================================
-export([
    save_subscription/3,            % 保存订阅信息
                                   % 功能：将订阅信息持久化到MongoDB
                                   % Java等价：public void saveSubscription(String clientId, String topic, SubscriptionOptions options)

    delete_subscription/2,          % 删除订阅信息
                                   % 功能：从MongoDB删除指定的订阅信息
                                   % Java等价：public void deleteSubscription(String clientId, String topic)

    get_client_subscriptions/1,     % 获取客户端的所有订阅
                                   % 功能：查询指定客户端的所有订阅信息

    restore_subscriptions_for_client/1, % 为特定客户端恢复订阅
                                       % 功能：从MongoDB恢复指定客户端的订阅到EMQX

    delete_client_subscriptions/1,     % 删除客户端的所有订阅
                                      % 功能：删除指定客户端的所有订阅数据
                                   % Java等价：public List<Subscription> getClientSubscriptions(String clientId)

    handle_persisted_subscriptions_after_restart/0, % 处理系统重启后的持久化订阅
                                                   % 功能：恢复持久化订阅到EMQX会话
                                                   % Java等价：@PostConstruct public void handlePersistedSubscriptionsAfterRestart()

    restore_client_subscriptions/1,                % 恢复客户端订阅
                                                   % 功能：恢复指定客户端的所有持久化订阅
                                                   % Java等价：public void restoreClientSubscriptions(String clientId)

    check_persisted_normal_subscriptions/2,        % 检查持久化的普通订阅
                                                   % 功能：记录普通订阅的详细信息
                                                   % Java等价：public void checkPersistedNormalSubscriptions(String collection, int skip)

    check_persisted_shared_subscriptions/2,        % 检查持久化的共享订阅
                                                   % 功能：记录共享订阅的详细信息
                                                   % Java等价：public void checkPersistedSharedSubscriptions(String collection, int skip)

    cleanup_expired_subscriptions/0, % 清理过期订阅
                                    % 功能：定期清理过期的订阅，防止存储膨胀
                                    % Java等价：@Scheduled public void cleanupExpiredSubscriptions()

    get_subscription_stats/0,       % 获取订阅统计信息
                                   % 功能：获取订阅数量、类型分布等统计数据
                                   % Java等价：public SubscriptionStats getSubscriptionStats()

    find_subscriptions_by_topic/1,  % 按主题查找订阅
                                   % 功能：查找订阅了指定主题的所有客户端
                                   % Java等价：public List<Subscription> findSubscriptionsByTopic(String topic)

    extract_subscription_expiry_interval/1, % 提取Subscription Expiry Interval属性
                                           % 功能：从MQTT 5.0属性中提取订阅过期时间
                                           % Java等价：public Integer extractSubscriptionExpiryInterval(SubscriptionOptions opts)

    extract_maximum_qos/1,                 % 提取Maximum QoS属性
                                          % 功能：从MQTT 5.0属性中提取最大QoS级别
                                          % Java等价：public Integer extractMaximumQoS(SubscriptionOptions opts)

    calculate_subscription_expiry_time/1   % 计算订阅过期时间
                                          % 功能：根据MQTT协议计算订阅的过期时间
                                          % Java等价：public long calculateSubscriptionExpiryTime(Integer expiryInterval)
]).

%% ============================================================================
%% 共享订阅管理API - MQTT 5.0共享订阅功能支持
%% 这些函数提供完整的共享订阅管理能力
%% 类似于Java中的共享订阅服务：
%% @Service
%% public class SharedSubscriptionService { ... }
%% ============================================================================
-export([
    is_shared_subscription/1,       % 判断是否为共享订阅
                                   % 功能：检查主题是否为共享订阅格式
                                   % Java等价：public boolean isSharedSubscription(String topic)

    parse_shared_subscription/1,    % 解析共享订阅
                                   % 功能：解析共享订阅的组名和实际主题
                                   % Java等价：public SharedSubscriptionInfo parseSharedSubscription(String topic)

    save_shared_subscription/4,     % 保存共享订阅
                                   % 功能：保存共享订阅信息，包括组成员管理
                                   % Java等价：public void saveSharedSubscription(String clientId, String group, String topic, SubscriptionOptions options)

    get_shared_subscriptions/1,     % 获取共享订阅
                                   % 功能：获取指定客户端的所有共享订阅
                                   % Java等价：public List<SharedSubscription> getSharedSubscriptions(String clientId)

    get_shared_group_members/2,     % 获取共享组成员
                                   % 功能：获取指定共享组的所有成员
                                   % Java等价：public List<String> getSharedGroupMembers(String group, String topic)

    get_shared_groups_by_topic/1,   % 按主题获取共享组
                                   % 功能：获取订阅了指定主题的所有共享组
                                   % Java等价：public List<String> getSharedGroupsByTopic(String topic)

    restore_shared_subscription_group/3 % 恢复共享订阅组
                                       % 功能：恢复共享订阅组的状态和成员
                                       % Java等价：public void restoreSharedSubscriptionGroup(String group, String topic, List<String> members)
]).

%% ============================================================================
%% 内部辅助函数 - 模块内部使用的工具和配置函数
%% 这些函数提供模块内部的辅助功能，类似于Java的private方法
%% ============================================================================
-export([
    start_cleanup_timer/0,          % 启动清理定时器 - 类似于@Scheduled任务启动
    do_cleanup_expired_subscriptions/0, % 执行过期订阅清理 - 定时任务的具体实现
    get_subscription_collection/0,  % 获取订阅集合名称 - 配置获取方法
    wait_for_resource/0,            % 等待MongoDB资源可用 - 资源就绪检查
    integrate/0,                    % 集成到系统协调器 - 系统集成方法
    ensure_subscription_collection_and_indexes/1 % 确保订阅集合和索引存在 - 集合初始化
]).

%% 注意：MongoDB集合名称现在统一定义在 emqx_plugin_mongodb.hrl 中
%% 这样可以保持整个项目的命名一致性和集中管理

%% ============================================================================
%% 配置常量定义 - 订阅持久化的核心配置参数
%% 这些常量控制订阅持久化的行为和性能特征
%% 类似于Java中的配置常量或@ConfigurationProperties
%% ============================================================================

%% 默认订阅过期时间：2天（172800000毫秒）
%% 功能：控制持久化订阅在MongoDB中的保存时间
%% 超过此时间的订阅将被自动清理，防止存储无限增长
%% Java等价：@Value("${subscription.expiry.time:172800000}")
%% 或者：spring.subscription.expiry-time=172800000
-define(DEFAULT_SUBSCRIPTION_EXPIRY, 172800000).

%% 默认清理间隔：30分钟（1800000毫秒）
%% 功能：控制过期订阅清理任务的执行频率
%% 定时任务会按此间隔清理过期的持久化订阅
%% Java等价：@Scheduled(fixedDelay = 1800000)
%% 或者：spring.task.scheduling.subscription-cleanup.interval=1800000
-define(DEFAULT_CLEANUP_INTERVAL, 1800000).

%% 默认批次大小：200条订阅记录
%% 功能：控制批量操作的大小，平衡性能和内存使用
%% 清理任务每次处理的订阅数量，避免一次性处理过多数据
%% Java等价：@Value("${subscription.cleanup.batch-size:200}")
%% 或者：spring.data.mongodb.subscription.batch-size=200
-define(DEFAULT_CLEANUP_BATCH_SIZE, 200).

%% ============================================================================
%% 模块初始化函数 - 订阅持久化模块的启动入口
%% ============================================================================

%% @doc 初始化订阅持久化模块
%% 这个函数是模块的启动入口，负责初始化所有必要的资源和服务
%%
%% 功能说明：
%% 1. 检查订阅持久化功能是否启用
%% 2. 等待MongoDB资源就绪
%% 3. 创建必要的集合和索引
%% 4. 启动过期订阅清理定时器
%% 5. 记录初始化状态日志
%%
%% 返回值：
%% - ok: 初始化成功
%%
%% Java等价概念：
%% @PostConstruct
%% @ConditionalOnProperty(name = "subscription.persistence.enabled", havingValue = "true")
%% public void initializeSubscriptionPersistence() {
%%     if (subscriptionPersistenceEnabled) {
%%         waitForMongoDBReady();
%%         createCollectionsAndIndexes();
%%         startCleanupScheduler();
%%         logger.info("Subscription persistence initialized successfully");
%%     }
%% }
%%
%% 设计特点：
%% - 条件初始化：只有在启用订阅持久化时才执行初始化
%% - 资源等待：确保MongoDB资源可用后再进行后续操作
%% - 完整设置：一次性完成所有必要的初始化工作
init() ->
    %% 记录模块初始化开始的日志
    %% 这有助于系统启动过程的跟踪和调试
    ?SLOG(info, #{msg => "initializing_mongodb_subscription_module"}),

    %% 检查订阅持久化功能是否启用
    %% 这是一个功能开关，允许运行时控制订阅持久化
    %% 在Java中相当于：
    %% @ConditionalOnProperty(name = "subscription.persistence.enabled", havingValue = "true")
    case application:get_env(emqx_plugin_mongodb, subscription_persistence_enabled, false) of
        true ->
            %% 订阅持久化已启用，开始初始化所有组件
            ?SLOG(info, #{msg => "subscription_persistence_enabled", module => ?MODULE}),

            %% 等待MongoDB资源就绪
            %% 确保数据库连接可用后再进行后续操作
            %% 在Java中相当于：
            %% @Autowired private MongoHealthIndicator mongoHealth;
            %% while (!mongoHealth.isHealthy()) { Thread.sleep(1000); }
            wait_for_resource(),

            %% 创建必要的集合和索引
            %% 确保订阅相关的MongoDB集合和索引存在
            %% 在Java中相当于：
            %% mongoTemplate.createCollection(collectionName);
            %% createIndexes(collectionName);
            ensure_collections(),

            %% 启动过期订阅清理定时器
            %% 定期清理过期的持久化订阅
            %% 在Java中相当于：
            %% @Scheduled(fixedDelay = 1800000)
            %% public void cleanupExpiredSubscriptions() { ... }
            start_cleanup_timer();
        false ->
            %% 订阅持久化未启用，跳过初始化
            %% 记录信息日志，说明功能被禁用
            ?SLOG(info, #{msg => "subscription_persistence_disabled", module => ?MODULE})
    end,
    %% 无论是否启用，都返回ok，确保模块加载不会失败
    ok.

%% 等待MongoDB资源就绪
wait_for_resource() ->
    wait_for_resource(10).

wait_for_resource(0) ->
    ?SLOG(warning, #{msg => "mongodb_resource_not_ready_after_retries"}),
    ok;
wait_for_resource(Retries) ->
    % 使用健康检查而不是get_instance和ping查询
    case emqx_resource:health_check(?PLUGIN_MONGODB_RESOURCE_ID) of
        ok ->
            ?SLOG(info, #{msg => "mongodb_resource_ready"}),
            ok;
        {ok, _} ->
            ?SLOG(info, #{msg => "mongodb_resource_ready"}),
            ok;
        _ ->
            ?SLOG(warning, #{msg => "mongodb_resource_not_ready", retries_left => Retries}),
            timer:sleep(1000),
            wait_for_resource(Retries - 1)
    end.

%% 加载订阅模块
load(Config) ->
    ?SLOG(info, #{msg => "loading_mongodb_subscription_module", config => Config}),
    % 检查订阅持久化是否启用
    SubscriptionConfig = maps:get(subscription_persistence, Config, #{}),
    SubscriptionPersistenceEnabled = maps:get(enabled, SubscriptionConfig, false),

    % 保存启用状态到应用环境变量中
    application:set_env(emqx_plugin_mongodb, subscription_persistence_enabled, SubscriptionPersistenceEnabled),

    % 无论是否启用订阅持久化，总是注册钩子
    try
        register_hooks(),
        ?SLOG(info, #{msg => "subscription_hooks_registered"})
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "failed_to_register_subscription_hooks",
                error => E,
                reason => R,
                stacktrace => S
            })
    end,

    % 如果订阅持久化启用，检查连接状态
    case SubscriptionPersistenceEnabled of
        true ->
            ?SLOG(info, #{msg => "subscription_persistence_enabled_checking_connection"}),
            % 检查MongoDB连接是否已建立
            case check_mongodb_connection_ready() of
                true ->
                    ?SLOG(info, #{msg => "mongodb_connection_ready_initializing_subscription_persistence"}),
                    initialize_subscription_persistence();
                false ->
                    ?SLOG(info, #{msg => "mongodb_connection_not_ready_deferring_subscription_persistence_initialization"}),
                    % 启动后台任务等待连接建立
                    spawn_link(fun() -> wait_and_initialize_subscription_persistence() end),
                    ok
            end,
                % 如果配置了启动时恢复订阅，则执行恢复
                % 订阅恢复现在通过MongoDB连接事件触发，不再使用时间延迟
                ?SLOG(info, #{msg => "subscription_restoration_will_be_triggered_on_mongodb_connection"}),
                ok;
        false ->
            ?SLOG(info, #{msg => "subscription_persistence_disabled"})
    end,
    ok.

%% @doc 检查MongoDB连接是否已建立
-spec check_mongodb_connection_ready() -> boolean().
check_mongodb_connection_ready() ->
    try
        % 使用简单的资源健康检查而不是查询
        case emqx_resource:health_check(?PLUGIN_MONGODB_RESOURCE_ID) of
            ok ->
                true;
            {ok, _} ->
                true;
            _ ->
                false
        end
    catch
        _:_ ->
            false
    end.

%% @doc 等待MongoDB连接建立并初始化订阅持久化
-spec wait_and_initialize_subscription_persistence() -> ok.
wait_and_initialize_subscription_persistence() ->
    wait_and_initialize_subscription_persistence(60). % 最多等待60次，每次2秒

%% @doc 等待循环
-spec wait_and_initialize_subscription_persistence(integer()) -> ok.
wait_and_initialize_subscription_persistence(0) ->
    ?SLOG(error, #{msg => "subscription_persistence_initialization_timeout"}),
    ok;
wait_and_initialize_subscription_persistence(Retries) ->
    timer:sleep(2000), % 每2秒检查一次
    case check_mongodb_connection_ready() of
        true ->
            ?SLOG(info, #{msg => "mongodb_connection_ready_initializing_subscription_persistence_delayed"}),
            initialize_subscription_persistence();
        false ->
            wait_and_initialize_subscription_persistence(Retries - 1)
    end.

%% @doc 初始化订阅持久化
-spec initialize_subscription_persistence() -> ok.
initialize_subscription_persistence() ->
    try
        % 确保集合存在
        ensure_collections(),
        % 启动清理定时器
        start_cleanup_timer(),
        ?SLOG(info, #{msg => "subscription_persistence_initialized_successfully"}),
        ok
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "failed_to_initialize_subscription_persistence",
                error => E,
                reason => R,
                stacktrace => S
            }),
            ok
    end.

%% 卸载订阅模块
unload() ->
    ?SLOG(info, #{msg => "unloading_mongodb_subscription_module"}),
    % 注销订阅相关钩子
    unregister_hooks(),
    ok.

%% 确保订阅相关的集合存在
ensure_collections() ->
    try
        % 获取集合名称
        SubscriptionCollection = get_subscription_collection(),

        ?SLOG(info, #{
            msg => "ensuring_subscription_collections",
            subscription_collection => SubscriptionCollection
        }),

        % 统一集合创建策略：主动创建集合和索引，与保留消息模块保持一致
        % 这样确保所有集合在插件启动时就存在，便于管理和调试
        ensure_subscription_collection_and_indexes(SubscriptionCollection),

        ?SLOG(info, #{msg => "subscription_collections_ensured", collection => SubscriptionCollection})
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_ensuring_collections",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {ensure_collections_failed, R}}
    end.

%% 创建订阅集合索引
create_subscription_indexes(Collection) ->
    % 生成集合缩写 emqx_mqtt_subscriptions -> emsub
    SubscriptionAbbr = generate_collection_abbreviation(Collection),

    ?SLOG(info, #{
        msg => "creating_subscription_indexes",
        collection => Collection,
        collection_abbr => SubscriptionAbbr
    }),

    % 定义订阅集合索引
    SubscriptionIndexes = [
        % 客户端ID索引
        #{
            <<"key">> => #{<<"client_id">> => 1},
            <<"name">> => <<SubscriptionAbbr/binary, "_client_id_asc">>
        },
        % 主题索引
        #{
            <<"key">> => #{<<"topic">> => 1},
            <<"name">> => <<SubscriptionAbbr/binary, "_topic_asc">>
        },
        % 过期时间TTL索引
        #{
            <<"key">> => #{<<"expiry_time">> => 1},
            <<"expireAfterSeconds">> => 0,
            <<"name">> => <<SubscriptionAbbr/binary, "_expiry_time_ttl">>
        },
        % 复合唯一索引 (client_id + topic)
        #{
            <<"key">> => #{<<"client_id">> => 1, <<"topic">> => 1},
            <<"unique">> => true,
            <<"name">> => <<SubscriptionAbbr/binary, "_client_id_topic_unique">>
        },
        % 共享订阅索引 (shared_group + topic)
        #{
            <<"key">> => #{<<"shared_group">> => 1, <<"topic">> => 1},
            <<"name">> => <<SubscriptionAbbr/binary, "_shared_group_topic_compound">>
        },
        % 共享订阅标志索引
        #{
            <<"key">> => #{<<"is_shared">> => 1},
            <<"name">> => <<SubscriptionAbbr/binary, "_is_shared_asc">>
        },
        % QoS索引
        #{
            <<"key">> => #{<<"qos">> => 1},
            <<"name">> => <<SubscriptionAbbr/binary, "_qos_asc">>
        },
        % 创建时间索引
        #{
            <<"key">> => #{<<"created_at">> => 1},
            <<"name">> => <<SubscriptionAbbr/binary, "_created_at_asc">>
        }
    ],

    % 逐个创建索引
    lists:foreach(fun(IndexSpec) ->
        IndexName = maps:get(<<"name">>, IndexSpec),
        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                               {create_index, Collection, IndexSpec}) of
            {ok, _} ->
                ?SLOG(info, #{
                    msg => "subscription_index_created",
                    collection => Collection,
                    index_name => IndexName
                });
            {async_return, ok} ->
                ?SLOG(info, #{
                    msg => "subscription_index_created_async",
                    collection => Collection,
                    index_name => IndexName
                });
            {async_return, {ok, _}} ->
                ?SLOG(info, #{
                    msg => "subscription_index_created_async",
                    collection => Collection,
                    index_name => IndexName
                });
            {async_return, {error, Reason}} ->
                handle_subscription_index_error(Collection, IndexName, Reason);
            {error, Reason} ->
                handle_subscription_index_error(Collection, IndexName, Reason)
        end
    end, SubscriptionIndexes).

%% 注册订阅相关钩子
register_hooks() ->
    emqx_hooks:add('session.subscribed', {?MODULE, on_session_subscribed, []}, 100),
    emqx_hooks:add('session.unsubscribed', {?MODULE, on_session_unsubscribed, []}, 100).

%% 注销订阅相关钩子
unregister_hooks() ->
    emqx_hooks:del('session.subscribed', {?MODULE, on_session_subscribed}),
    emqx_hooks:del('session.unsubscribed', {?MODULE, on_session_unsubscribed}).

%% 会话订阅钩子回调
on_session_subscribed(ClientInfo, Topic, SubOpts) ->
    % 检查订阅持久化是否启用
    case application:get_env(emqx_plugin_mongodb, subscription_persistence_enabled, false) of
        true ->
            ?SLOG(debug, #{msg => "session_subscribed", client_info => ClientInfo, topic => Topic, sub_opts => SubOpts}),
            % 检查是否是共享订阅
            case is_shared_subscription(Topic) of
                true ->
                    % 检查是否配置了保存共享订阅
                    case should_save_shared_subscription() of
                        true ->
                            % 解析共享订阅
                            case parse_shared_subscription(Topic) of
                                {ok, {Group, RealTopic}} ->
                                    % 异步保存共享订阅
                                    spawn(fun() -> save_shared_subscription(ClientInfo, Group, RealTopic, SubOpts) end);
                                {error, Reason} ->
                                    ?SLOG(error, #{
                                        msg => "failed_to_parse_shared_subscription",
                                        topic => Topic,
                                        reason => Reason
                                    })
                            end;
                        false ->
                            ok
                    end;
                false ->
                    % 普通订阅，直接保存
                    spawn(fun() -> save_subscription(ClientInfo, Topic, SubOpts) end)
            end;
        false ->
            ok
    end,
    ok.

%% 会话取消订阅钩子回调
on_session_unsubscribed(ClientInfo, Topic, _SubOpts) ->
    % 检查订阅持久化是否启用
    case application:get_env(emqx_plugin_mongodb, subscription_persistence_enabled, false) of
        true ->
            ?SLOG(debug, #{msg => "session_unsubscribed", client_info => ClientInfo, topic => Topic}),
            % 异步删除订阅记录
            spawn(fun() -> delete_subscription(ClientInfo, Topic) end);
        false ->
            ok
    end,
    ok.

%% 保存订阅信息
save_subscription(ClientInfo, Topic, SubOpts) ->
    try
        ClientId = maps:get(clientid, ClientInfo, undefined),
        Username = maps:get(username, ClientInfo, undefined),

        % 跳过未知客户端ID的订阅
        case ClientId of
            undefined ->
                ?SLOG(warning, #{msg => "skip_subscription_with_undefined_client_id", topic => Topic}),
                ok;
            _ ->
                % 获取集合名称
                SubscriptionCollection = get_subscription_collection(),

                % 解析MQTT 5.0订阅属性
                SubscriptionExpiryInterval = extract_subscription_expiry_interval(SubOpts),
                MaximumQoS = extract_maximum_qos(SubOpts),
                RetainAsPublished = maps:get(rap, SubOpts, false),
                NoLocal = maps:get(nl, SubOpts, false),
                RetainHandling = maps:get(rh, SubOpts, 0),
                SubscriptionIdentifier = maps:get(subid, SubOpts, undefined),
                UserProperties = maps:get(user_properties, SubOpts, #{}),

                % 计算订阅过期时间（优先使用订阅自身的过期设置）
                Now = erlang:system_time(millisecond),
                ExpiryTime = calculate_subscription_expiry_time(SubscriptionExpiryInterval),

                % 构造符合MQTT协议的订阅文档
                SubDoc = #{
                    <<"client_id">> => ClientId,
                    <<"username">> => Username,
                    <<"topic">> => Topic,
                    <<"is_shared">> => false,
                    <<"qos">> => maps:get(qos, SubOpts, 0),

                    % MQTT 5.0订阅选项
                    <<"retain_as_published">> => RetainAsPublished,
                    <<"no_local">> => NoLocal,
                    <<"retain_handling">> => RetainHandling,

                    % MQTT 5.0订阅属性
                    <<"subscription_expiry_interval">> => SubscriptionExpiryInterval,
                    <<"maximum_qos">> => MaximumQoS,
                    <<"subscription_identifier">> => SubscriptionIdentifier,
                    <<"user_properties">> => encode_subscription_user_properties(UserProperties),

                    % 扩展属性
                    <<"shared_group">> => undefined,  % 普通订阅无共享组
                    <<"node">> => atom_to_binary(node(), utf8),
                    <<"created_at">> => Now,
                    <<"updated_at">> => Now,
                    <<"expiry_time">> => ExpiryTime,

                    % 兼容性：保留原始选项
                    <<"rap">> => RetainAsPublished,  % 向后兼容
                    <<"nl">> => NoLocal,             % 向后兼容
                    <<"rh">> => RetainHandling       % 向后兼容
                },

                % 保存订阅信息 - 使用upsert操作
                case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                                        {upsert, SubscriptionCollection,
                                         #{<<"client_id">> => ClientId, <<"topic">> => Topic},
                                         SubDoc}) of
                    {ok, _} ->
                        ?SLOG(debug, #{msg => "subscription_saved", client_id => ClientId, topic => Topic});
                    {async_return, ok} ->
                        ?SLOG(debug, #{msg => "subscription_saved_async", client_id => ClientId, topic => Topic});
                    {async_return, {ok, _}} ->
                        ?SLOG(debug, #{msg => "subscription_saved_async", client_id => ClientId, topic => Topic});
                    {error, Reason} ->
                        ?SLOG(error, #{
                            msg => "failed_to_save_subscription",
                            client_id => ClientId,
                            topic => Topic,
                            reason => Reason
                        });
                    {async_return, {error, Reason}} ->
                        ?SLOG(error, #{
                            msg => "failed_to_save_subscription_async",
                            client_id => ClientId,
                            topic => Topic,
                            reason => Reason
                        })
                end
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_saving_subscription",
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% 删除订阅信息
delete_subscription(ClientInfo, Topic) ->
    try
        ClientId = maps:get(clientid, ClientInfo, undefined),

        case ClientId of
            undefined ->
                ?SLOG(warning, #{msg => "skip_delete_subscription_with_undefined_client_id", topic => Topic}),
                ok;
            _ ->
                % 获取集合名称
                SubscriptionCollection = get_subscription_collection(),

                % 删除订阅信息
                Filter = #{
                    <<"client_id">> => ClientId,
                    <<"topic">> => Topic
                },

                case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                                        {delete, SubscriptionCollection, Filter}) of
                    {ok, _} ->
                        ?SLOG(debug, #{msg => "subscription_deleted", client_id => ClientId, topic => Topic});
                    {async_return, ok} ->
                        % 处理异步返回的成功响应
                        ?SLOG(debug, #{msg => "subscription_deleted_async", client_id => ClientId, topic => Topic});
                    {async_return, {ok, _}} ->
                        % 处理异步返回的成功响应（带结果）
                        ?SLOG(debug, #{msg => "subscription_deleted_async_with_result", client_id => ClientId, topic => Topic});
                    {async_return, {error, Reason}} ->
                        % 处理异步返回的错误响应
                        ?SLOG(error, #{
                            msg => "failed_to_delete_subscription_async",
                            client_id => ClientId,
                            topic => Topic,
                            reason => Reason
                        });
                    {error, Reason} ->
                        ?SLOG(error, #{
                            msg => "failed_to_delete_subscription",
                            client_id => ClientId,
                            topic => Topic,
                            reason => Reason
                        });
                    Other ->
                        % 处理其他未预期的响应格式
                        ?SLOG(warning, #{
                            msg => "unexpected_delete_subscription_response",
                            client_id => ClientId,
                            topic => Topic,
                            response => Other
                        })
                end
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_deleting_subscription",
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% 获取客户端的所有订阅
get_client_subscriptions(ClientId) ->
    try
        % 获取集合名称
        SubscriptionCollection = get_subscription_collection(),

        % 查询客户端的所有订阅
        Filter = #{<<"client_id">> => ClientId},

        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                                {find, SubscriptionCollection, Filter, #{}, 0, 1000}) of
            {ok, Subscriptions} ->
                % 转换为内部订阅格式
                {ok, lists:map(fun(Sub) ->
                    Topic = maps:get(<<"topic">>, Sub, <<>>),
                    QoS = maps:get(<<"qos">>, Sub, 0),
                    SubOpts = #{
                        qos => QoS,
                        rap => maps:get(<<"rap">>, Sub, false),
                        nl => maps:get(<<"nl">>, Sub, false),
                        rh => maps:get(<<"rh">>, Sub, 0)
                    },
                    {Topic, SubOpts}
                end, Subscriptions)};
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_get_client_subscriptions",
                    client_id => ClientId,
                    reason => Reason
                }),
                {error, Reason}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_getting_client_subscriptions",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, R}
    end.

%% @doc 为特定客户端恢复订阅
%% 从MongoDB恢复指定客户端的订阅到EMQX
restore_subscriptions_for_client(ClientId) ->
    try
        ?SLOG(info, #{msg => "restoring_subscriptions_for_client", client_id => ClientId}),

        % 获取客户端的所有订阅
        case get_client_subscriptions(ClientId) of
            {ok, Subscriptions} ->
                case length(Subscriptions) of
                    0 ->
                        ?SLOG(info, #{msg => "no_subscriptions_to_restore", client_id => ClientId}),
                        ok;
                    Count ->
                        ?SLOG(info, #{msg => "restoring_subscriptions", client_id => ClientId, count => Count}),

                        % 恢复每个订阅
                        Results = lists:map(fun({Topic, SubOpts}) ->
                            restore_single_subscription(ClientId, Topic, SubOpts)
                        end, Subscriptions),

                        % 统计结果
                        SuccessCount = length([ok || ok <- Results]),
                        ?SLOG(info, #{
                            msg => "subscription_restoration_completed",
                            client_id => ClientId,
                            total => Count,
                            success => SuccessCount,
                            failed => Count - SuccessCount
                        }),
                        ok
                end;
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_get_subscriptions_for_restoration",
                    client_id => ClientId,
                    reason => Reason
                }),
                {error, Reason}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_restoring_subscriptions_for_client",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, R}
    end.

%% @doc 恢复单个订阅
restore_single_subscription(ClientId, Topic, SubOpts) ->
    try
        % 使用EMQX的订阅API恢复订阅
        case emqx_broker:subscribe(Topic, ClientId, SubOpts) of
            ok ->
                ?SLOG(debug, #{
                    msg => "subscription_restored",
                    client_id => ClientId,
                    topic => Topic,
                    sub_opts => SubOpts
                }),
                ok;
            {error, Reason} ->
                ?SLOG(warning, #{
                    msg => "failed_to_restore_subscription",
                    client_id => ClientId,
                    topic => Topic,
                    reason => Reason
                }),
                {error, Reason}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_restoring_single_subscription",
                client_id => ClientId,
                topic => Topic,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, R}
    end.

%% @doc 删除客户端的所有订阅
delete_client_subscriptions(ClientId) ->
    try
        ?SLOG(info, #{msg => "deleting_client_subscriptions", client_id => ClientId}),

        Collection = get_subscription_collection(),
        Filter = #{<<"client_id">> => ClientId},

        case emqx_plugin_mongodb_api:delete_many(Collection, Filter) of
            {ok, Result} ->
                DeletedCount = maps:get(<<"deletedCount">>, Result, 0),
                ?SLOG(info, #{
                    msg => "client_subscriptions_deleted",
                    client_id => ClientId,
                    deleted_count => DeletedCount
                }),
                ok;
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_delete_client_subscriptions",
                    client_id => ClientId,
                    reason => Reason
                }),
                {error, Reason}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_deleting_client_subscriptions",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, R}
    end.

%% @doc 处理系统重启后的持久化订阅
%% 修复：采用方案A - 纯持久化方案
%%
%% 功能说明：
%% 1. 不直接恢复订阅，避免与EMQX内置订阅管理冲突
%% 2. 记录持久化订阅的信息供管理员参考
%% 3. 让EMQX在客户端重连时自然恢复订阅状态
%%
%% 设计原理：
%% - 插件只负责持久化，不干扰EMQX的订阅管理逻辑
%% - EMQX的内置订阅管理会在客户端重连时自动处理订阅恢复
%% - 避免订阅状态冲突和路由表不一致
%%
%% 修复说明：
%% - 原方案：插件直接调用emqx_broker:subscribe创建订阅 → 可能与EMQX内置功能严重冲突
%% - 新方案：插件只记录信息 → 让EMQX内置功能处理
%%
%% Java等价概念：
%% @PostConstruct
%% public void handlePersistedSubscriptionsAfterRestart() {
%%     List<PersistedSubscription> subscriptions = subscriptionRepository.findAll();
%%
%%     // 不直接恢复订阅，而是记录信息供管理员参考
%%     for (PersistedSubscription sub : subscriptions) {
%%         logger.warn("Found persisted subscription after restart: client={}, topic={}",
%%                    sub.getClientId(), sub.getTopic());
%%     }
%%
%%     // 让MQTT broker在客户端重连时自然恢复订阅
%% }
handle_persisted_subscriptions_after_restart() ->
    %% 检查是否为全新安装，如果是则跳过数据恢复
    case emqx_plugin_mongodb_api:is_fresh_installation() of
        true ->
            ?SLOG(info, #{
                msg => "fresh_installation_detected_skipping_subscription_restoration",
                reason => "no_existing_subscription_data_to_restore"
            }),
            ok;
        false ->
            ?SLOG(info, #{
                msg => "restoring_persisted_subscriptions_after_emqx_restart",
                approach => "restore_to_emqx_sessions_per_mqtt_protocol"
            }),
    try
        %% 获取集合名称
        SubscriptionCollection = get_subscription_collection(),
        Now = erlang:system_time(millisecond),

        %% 修复：查询需要恢复的持久化订阅
        %% 只查询持久会话的订阅且未过期的订阅
        Filter = #{
            <<"clean_start">> => false,  % 只恢复持久会话的订阅
            <<"$or">> => [
                #{<<"expiry_time">> => #{<<"$gt">> => Now}},  % 未过期的订阅
                #{<<"expiry_time">> => 0}  % 永不过期的订阅
            ]
        },

        ?SLOG(info, #{
            msg => "querying_persistent_subscriptions_for_restoration",
            filter => Filter,
            current_time => Now
        }),

        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                                {find, SubscriptionCollection, Filter, #{}, 0, 0}) of
            {ok, Subscriptions} ->
                SubscriptionCount = length(Subscriptions),
                case SubscriptionCount of
                    0 ->
                        ?SLOG(info, #{
                            msg => "no_persistent_subscriptions_found_after_restart"
                        });
                    _ ->
                        ?SLOG(info, #{
                            msg => "found_persistent_subscriptions_restoring_to_emqx",
                            subscription_count => SubscriptionCount,
                            approach => "restore_to_emqx_sessions"
                        }),

                        %% 修复：实际恢复订阅到EMQX会话
                        restore_persistent_subscriptions_to_emqx(Subscriptions)
                end;
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_query_persistent_subscriptions_after_restart",
                    reason => Reason
                })
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_handling_persistent_subscriptions_after_restart",
                error => E,
                reason => R,
                stacktrace => S
            })
        end
    end.

%% @doc 恢复持久化订阅到EMQX
%% 修复：按照MQTT协议正确恢复持久会话的订阅
%%
%% 功能说明：
%% 1. 将MongoDB中的持久化订阅恢复到EMQX会话管理器
%% 2. 只恢复持久会话的订阅（Clean Session = false）
%% 3. 检查订阅过期时间，清理过期订阅
%% 4. 使用EMQX标准API恢复订阅状态
%%
%% MQTT协议要求：
%% - 持久会话的订阅必须在服务器重启后保持
%% - 订阅状态依附于会话状态
%% - 订阅过期时间必须被正确处理
restore_persistent_subscriptions_to_emqx(Subscriptions) ->
    ?SLOG(info, #{
        msg => "starting_persistent_subscription_restoration_to_emqx",
        subscription_count => length(Subscriptions),
        approach => "mqtt_protocol_compliant_restoration"
    }),

    %% 按客户端ID分组订阅
    ClientSubscriptions = group_subscriptions_by_client(Subscriptions),

    %% 异步批量恢复订阅，避免阻塞
    spawn(fun() ->
        try
            %% 批量处理订阅恢复
            MaxConcurrent = 10, % 最多同时恢复10个客户端的订阅
            restore_subscriptions_in_batches(ClientSubscriptions, MaxConcurrent),

            ?SLOG(info, #{
                msg => "persistent_subscription_restoration_completed",
                total_clients => maps:size(ClientSubscriptions),
                total_subscriptions => length(Subscriptions)
            })
        catch
            E:R:S ->
                ?SLOG(error, #{
                    msg => "error_in_persistent_subscription_restoration",
                    error => E,
                    reason => R,
                    stacktrace => S
                })
        end
    end),
    ok.

%% @doc 批量恢复订阅到EMQX
%% 修复：使用正确的批量处理策略
restore_subscriptions_in_batches(ClientSubscriptions, MaxConcurrent) ->
    ClientList = maps:to_list(ClientSubscriptions),
    restore_subscriptions_batch_loop(ClientList, MaxConcurrent).

restore_subscriptions_batch_loop([], _MaxConcurrent) ->
    ?SLOG(info, #{msg => "all_client_subscriptions_restoration_completed"}),
    ok;
restore_subscriptions_batch_loop(ClientList, MaxConcurrent) ->
    {Batch, Rest} = case length(ClientList) =< MaxConcurrent of
        true -> {ClientList, []};
        false -> lists:split(MaxConcurrent, ClientList)
    end,

    ?SLOG(info, #{
        msg => "processing_subscription_restoration_batch",
        batch_size => length(Batch),
        remaining => length(Rest)
    }),

    %% 并发恢复这批客户端的订阅
    Tasks = [spawn_monitor(fun() ->
        restore_client_subscriptions_to_emqx(ClientId, Subscriptions)
    end) || {ClientId, Subscriptions} <- Batch],

    %% 等待所有任务完成
    wait_for_subscription_restoration_tasks(Tasks),

    %% 处理剩余客户端
    case Rest of
        [] -> ok;
        _ ->
            timer:sleep(1000), % 批次间间隔
            restore_subscriptions_batch_loop(Rest, MaxConcurrent)
    end.

%% @doc 等待订阅恢复任务完成
wait_for_subscription_restoration_tasks([]) ->
    ok;
wait_for_subscription_restoration_tasks([{Pid, Ref} | Rest]) ->
    receive
        {'DOWN', Ref, process, Pid, normal} ->
            wait_for_subscription_restoration_tasks(Rest);
        {'DOWN', Ref, process, Pid, Reason} ->
            ?SLOG(warning, #{
                msg => "subscription_restoration_task_failed",
                pid => Pid,
                reason => Reason
            }),
            wait_for_subscription_restoration_tasks(Rest)
    after 30000 -> % 30秒超时
        ?SLOG(error, #{
            msg => "subscription_restoration_task_timeout",
            pid => Pid
        }),
        exit(Pid, kill),
        wait_for_subscription_restoration_tasks(Rest)
    end.

%% @doc 恢复单个客户端的订阅到EMQX
%% 修复：使用EMQX标准API恢复订阅
restore_client_subscriptions_to_emqx(ClientId, Subscriptions) ->
    try
        ?SLOG(info, #{
            msg => "restoring_client_subscriptions_to_emqx",
            client_id => ClientId,
            subscription_count => length(Subscriptions)
        }),

        %% 过滤和验证订阅
        ValidSubscriptions = lists:filter(fun(Sub) ->
            is_subscription_valid_for_restoration(Sub)
        end, Subscriptions),

        case ValidSubscriptions of
            [] ->
                ?SLOG(info, #{
                    msg => "no_valid_subscriptions_to_restore",
                    client_id => ClientId
                });
            _ ->
                %% 恢复订阅到EMQX
                restore_subscriptions_to_emqx_session(ClientId, ValidSubscriptions)
        end

    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_restoring_client_subscriptions_to_emqx",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% @doc 检查订阅是否有效且可以恢复
is_subscription_valid_for_restoration(Subscription) ->
    try
        %% 检查必要字段
        Topic = maps:get(<<"topic">>, Subscription, undefined),
        ClientId = maps:get(<<"client_id">>, Subscription, undefined),
        CleanStart = maps:get(<<"clean_start">>, Subscription, true),

        case {Topic, ClientId, CleanStart} of
            {undefined, _, _} ->
                ?SLOG(warning, #{
                    msg => "subscription_missing_topic",
                    subscription => Subscription
                }),
                false;
            {_, undefined, _} ->
                ?SLOG(warning, #{
                    msg => "subscription_missing_client_id",
                    subscription => Subscription
                }),
                false;
            {_, _, true} ->
                %% Clean Start = true的订阅不应该被恢复
                ?SLOG(debug, #{
                    msg => "clean_start_subscription_skipped",
                    client_id => ClientId,
                    topic => Topic
                }),
                false;
            {_, _, false} ->
                %% 检查订阅是否已过期
                case is_subscription_expired(Subscription) of
                    true ->
                        ?SLOG(info, #{
                            msg => "subscription_expired_skipping",
                            client_id => ClientId,
                            topic => Topic
                        }),
                        false;
                    false ->
                        true
                end
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_validating_subscription",
                subscription => Subscription,
                error => E,
                reason => R,
                stacktrace => S
            }),
            false
    end.

%% @doc 检查订阅是否已过期
is_subscription_expired(Subscription) ->
    ExpiryTime = maps:get(<<"expiry_time">>, Subscription, 0),
    case ExpiryTime of
        0 -> false; % 永不过期
        _ ->
            Now = erlang:system_time(millisecond),
            ExpiryTime =< Now
    end.

%% @doc 恢复订阅到EMQX会话
%% 修复：使用EMQX标准API恢复订阅状态
restore_subscriptions_to_emqx_session(ClientId, Subscriptions) ->
    try
        ?SLOG(info, #{
            msg => "restoring_subscriptions_to_emqx_session",
            client_id => ClientId,
            subscription_count => length(Subscriptions),
            approach => "use_emqx_broker_api"
        }),

        %% 分别处理普通订阅和共享订阅
        {NormalSubs, SharedSubs} = lists:partition(fun(Sub) ->
            Topic = maps:get(<<"topic">>, Sub, <<>>),
            not is_shared_subscription(Topic)
        end, Subscriptions),

        %% 恢复普通订阅
        NormalResults = restore_normal_subscriptions_to_emqx(ClientId, NormalSubs),

        %% 恢复共享订阅
        SharedResults = restore_shared_subscriptions_to_emqx(ClientId, SharedSubs),

        %% 统计结果
        TotalSuccess = NormalResults + SharedResults,
        TotalFailed = length(Subscriptions) - TotalSuccess,

        ?SLOG(info, #{
            msg => "subscriptions_restored_to_emqx_session",
            client_id => ClientId,
            total_subscriptions => length(Subscriptions),
            normal_subscriptions => length(NormalSubs),
            shared_subscriptions => length(SharedSubs),
            success_count => TotalSuccess,
            failed_count => TotalFailed
        }),

        %% 更新MongoDB中的恢复状态
        update_subscriptions_restoration_status(ClientId, <<"restored">>),

        TotalSuccess

    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_restoring_subscriptions_to_emqx_session",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S
            }),
            %% 更新失败状态
            update_subscriptions_restoration_status(ClientId, <<"failed">>),
            0
    end.

%% @doc 恢复普通订阅到EMQX
restore_normal_subscriptions_to_emqx(ClientId, NormalSubscriptions) ->
    try
        SuccessCount = lists:foldl(fun(Sub, Acc) ->
            Topic = maps:get(<<"topic">>, Sub, <<>>),
            QoS = maps:get(<<"qos">>, Sub, 0),

            %% 构建订阅选项
            SubOpts = #{
                qos => QoS,
                rap => maps:get(<<"rap">>, Sub, false),
                nl => maps:get(<<"nl">>, Sub, false),
                rh => maps:get(<<"rh">>, Sub, 0)
            },

            %% 使用EMQX API恢复订阅
            case emqx_broker:subscribe(Topic, ClientId, SubOpts) of
                ok ->
                    ?SLOG(debug, #{
                        msg => "normal_subscription_restored_successfully",
                        client_id => ClientId,
                        topic => Topic,
                        qos => QoS
                    }),
                    Acc + 1;
                {error, Reason} ->
                    ?SLOG(error, #{
                        msg => "failed_to_restore_normal_subscription",
                        client_id => ClientId,
                        topic => Topic,
                        qos => QoS,
                        reason => Reason
                    }),
                    Acc
            end
        end, 0, NormalSubscriptions),

        ?SLOG(info, #{
            msg => "normal_subscriptions_restoration_completed",
            client_id => ClientId,
            total_count => length(NormalSubscriptions),
            success_count => SuccessCount,
            failed_count => length(NormalSubscriptions) - SuccessCount
        }),

        SuccessCount
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_restoring_normal_subscriptions",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S
            }),
            0
    end.

%% @doc 恢复共享订阅到EMQX
restore_shared_subscriptions_to_emqx(ClientId, SharedSubscriptions) ->
    try
        SuccessCount = lists:foldl(fun(Sub, Acc) ->
            Topic = maps:get(<<"topic">>, Sub, <<>>),
            QoS = maps:get(<<"qos">>, Sub, 0),

            %% 解析共享订阅
            case parse_shared_subscription(Topic) of
                {ok, {Group, RealTopic}} ->
                    %% 构建订阅选项
                    SubOpts = #{
                        qos => QoS,
                        rap => maps:get(<<"rap">>, Sub, false),
                        nl => maps:get(<<"nl">>, Sub, false),
                        rh => maps:get(<<"rh">>, Sub, 0)
                    },

                    %% 使用EMQX API恢复共享订阅
                    case emqx_broker:subscribe(Topic, ClientId, SubOpts) of
                        ok ->
                            %% 区分两种共享订阅格式的日志
                            IsQueueSubscription = (Group =:= ?QUEUE_PREFIX),
                            ?SLOG(debug, #{
                                msg => "shared_subscription_restored_successfully",
                                client_id => ClientId,
                                group => Group,
                                topic => RealTopic,
                                full_topic => Topic,
                                qos => QoS,
                                subscription_type => case IsQueueSubscription of
                                    true -> "queue_subscription";
                                    false -> "group_shared_subscription"
                                end
                            }),
                            Acc + 1;
                        {error, Reason} ->
                            %% 区分两种共享订阅格式的错误日志
                            IsQueueSubscription = (Group =:= ?QUEUE_PREFIX),
                            ?SLOG(error, #{
                                msg => "failed_to_restore_shared_subscription",
                                client_id => ClientId,
                                group => Group,
                                topic => RealTopic,
                                full_topic => Topic,
                                qos => QoS,
                                reason => Reason,
                                subscription_type => case IsQueueSubscription of
                                    true -> "queue_subscription";
                                    false -> "group_shared_subscription"
                                end
                            }),
                            Acc
                    end;
                {error, ParseReason} ->
                    ?SLOG(error, #{
                        msg => "failed_to_parse_shared_subscription_for_restoration",
                        client_id => ClientId,
                        topic => Topic,
                        reason => ParseReason
                    }),
                    Acc
            end
        end, 0, SharedSubscriptions),

        ?SLOG(info, #{
            msg => "shared_subscriptions_restoration_completed",
            client_id => ClientId,
            total_count => length(SharedSubscriptions),
            success_count => SuccessCount,
            failed_count => length(SharedSubscriptions) - SuccessCount
        }),

        SuccessCount
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_restoring_shared_subscriptions",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S
            }),
            0
    end.

%% @doc 更新订阅恢复状态
update_subscriptions_restoration_status(ClientId, Status) ->
    try
        SubscriptionCollection = get_subscription_collection(),
        UpdateDoc = #{
            <<"$set">> => #{
                <<"restoration_status">> => Status,
                <<"restored_at">> => erlang:system_time(millisecond),
                <<"updated_at">> => erlang:system_time(millisecond)
            }
        },

        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                               {update_many, SubscriptionCollection,
                                #{<<"client_id">> => ClientId},
                                UpdateDoc}) of
            {ok, _} ->
                ?SLOG(debug, #{
                    msg => "subscriptions_restoration_status_updated",
                    client_id => ClientId,
                    status => Status
                });
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_update_subscriptions_restoration_status",
                    client_id => ClientId,
                    status => Status,
                    reason => Reason
                })
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_updating_subscriptions_restoration_status",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% @doc 恢复指定客户端的订阅
%% 修复：实现缺失的restore_client_subscriptions/1函数
%%
%% 功能说明：
%% 1. 查询指定客户端的所有持久化订阅
%% 2. 只恢复持久会话的订阅（Clean Session = false）
%% 3. 使用EMQX标准API恢复订阅到会话
%% 4. 处理订阅过期和验证
%%
%% 这个函数被会话持久化模块调用，用于恢复会话相关的订阅
restore_client_subscriptions(ClientId) ->
    ?SLOG(info, #{
        msg => "restoring_client_subscriptions",
        client_id => ClientId,
        approach => "query_and_restore_persistent_subscriptions"
    }),
    try
        %% 获取集合名称
        SubscriptionCollection = get_subscription_collection(),
        Now = erlang:system_time(millisecond),

        %% 查询该客户端的持久化订阅
        Filter = #{
            <<"client_id">> => ClientId,
            <<"clean_start">> => false,  % 只恢复持久会话的订阅
            <<"$or">> => [
                #{<<"expiry_time">> => #{<<"$gt">> => Now}},  % 未过期的订阅
                #{<<"expiry_time">> => 0}  % 永不过期的订阅
            ]
        },

        ?SLOG(debug, #{
            msg => "querying_client_persistent_subscriptions",
            client_id => ClientId,
            filter => Filter,
            current_time => Now
        }),

        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                                {find, SubscriptionCollection, Filter, #{}, 0, 0}) of
            {ok, Subscriptions} when is_list(Subscriptions) ->
                SubscriptionCount = length(Subscriptions),
                case SubscriptionCount of
                    0 ->
                        ?SLOG(info, #{
                            msg => "no_persistent_subscriptions_found_for_client",
                            client_id => ClientId
                        }),
                        0;
                    _ ->
                        ?SLOG(info, #{
                            msg => "found_persistent_subscriptions_for_client",
                            client_id => ClientId,
                            subscription_count => SubscriptionCount
                        }),

                        %% 恢复订阅到EMQX会话
                        restore_client_subscriptions_to_emqx(ClientId, Subscriptions),
                        %% 返回恢复的订阅数量
                        SubscriptionCount
                end;
            {async_return, {ok, Subscriptions}} when is_list(Subscriptions) ->
                SubscriptionCount = length(Subscriptions),
                case SubscriptionCount of
                    0 ->
                        ?SLOG(info, #{
                            msg => "no_persistent_subscriptions_found_for_client_async",
                            client_id => ClientId
                        }),
                        0;
                    _ ->
                        ?SLOG(info, #{
                            msg => "found_persistent_subscriptions_for_client_async",
                            client_id => ClientId,
                            subscription_count => SubscriptionCount
                        }),
                        restore_client_subscriptions_to_emqx(ClientId, Subscriptions),
                        SubscriptionCount
                end;
            {async_return, ok} ->
                ?SLOG(info, #{
                    msg => "async_query_returned_ok_no_subscriptions",
                    client_id => ClientId
                }),
                0;
            {async_return, {error, Reason}} ->
                ?SLOG(error, #{
                    msg => "failed_to_query_client_persistent_subscriptions_async",
                    client_id => ClientId,
                    reason => Reason
                }),
                0;
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_query_client_persistent_subscriptions",
                    client_id => ClientId,
                    reason => Reason
                }),
                0;
            Other ->
                ?SLOG(warning, #{
                    msg => "unexpected_query_result_for_client_subscriptions",
                    client_id => ClientId,
                    result => Other
                }),
                0
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_restoring_client_subscriptions",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S
            }),
            0
    end.

%% @doc 检查持久化的普通订阅
%% 修复：采用方案A - 纯持久化方案
%%
%% 功能说明：
%% 1. 检查系统重启后发现的持久化普通订阅
%% 2. 不直接恢复订阅，避免与EMQX内置功能冲突
%% 3. 记录订阅信息供管理员参考和故障排查使用
%%
%% 参数说明：
%% - Collection: MongoDB订阅集合名称
%% - Skip: 跳过的记录数（用于分页处理）
%%
%% Java等价概念：
%% public void checkPersistedNormalSubscriptions(String collection, int skip) {
%%     List<Subscription> subscriptions = subscriptionRepository.findNormalSubscriptions(skip, limit);
%%     for (Subscription sub : subscriptions) {
%%         logger.warn("Found persisted normal subscription after restart: client={}, topic={}",
%%                    sub.getClientId(), sub.getTopic());
%%     }
%% }
check_persisted_normal_subscriptions(Collection, Skip) ->
    Limit = 100,
    %% 只查询普通订阅
    Filter = #{<<"is_shared">> => false},
    case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                            {find, Collection, Filter, #{}, Skip, Limit}) of
        {ok, Subscriptions} when is_list(Subscriptions), length(Subscriptions) > 0 ->
            %% 按客户端ID分组订阅
            ClientSubscriptions = group_subscriptions_by_client(Subscriptions),

            %% 记录每个客户端的订阅信息（不直接恢复）
            maps:foreach(fun(ClientId, ClientSubs) ->
                log_client_persisted_subscriptions_info(ClientId, ClientSubs, normal)
            end, ClientSubscriptions),

            %% 继续检查下一批
            check_persisted_normal_subscriptions(Collection, Skip + length(Subscriptions));
        {ok, []} ->
            %% 没有更多订阅
            ?SLOG(info, #{
                msg => "finished_checking_persisted_normal_subscriptions",
                total_processed => Skip
            });
        {error, Reason} ->
            ?SLOG(error, #{
                msg => "error_checking_persisted_normal_subscriptions",
                reason => Reason,
                skip => Skip
            })
    end.

%% @doc 批量恢复普通订阅
%% 警告：这个函数在方案A中不应该被调用！
%%
%% 修复说明：
%% - 这个函数违反了方案A的设计原则（插件不应该直接恢复EMQX订阅）
%% - 保留此函数仅用于向后兼容，但会记录警告日志
%% - 在新的方案A中，应该让EMQX的重连机制处理订阅恢复
restore_normal_subscriptions(Collection, Skip) ->
    %% 记录警告：这个函数不应该在方案A中被调用
    ?SLOG(warning, #{
        msg => "restore_normal_subscriptions_called_violates_plan_a",
        collection => Collection,
        skip => Skip,
        note => "this_function_should_not_be_called_in_pure_persistence_approach"
    }),

    Limit = 100,
    %% 只查询普通订阅
    Filter = #{<<"is_shared">> => false},
    case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                            {find, Collection, Filter, #{}, Skip, Limit}) of
        {ok, []} ->
            %% 所有普通订阅已恢复
            ?SLOG(info, #{msg => "all_normal_subscriptions_restored_but_violates_plan_a", count => Skip}),
            ok;
        {ok, Subscriptions} ->
            %% 按客户端ID分组订阅
            ClientSubscriptions = group_subscriptions_by_client(Subscriptions),

            %% 批量恢复每个客户端的订阅（违反方案A原则）
            maps:foreach(fun(ClientId, ClientSubs) ->
                %% 检查客户端是否在线
                case emqx_cm:lookup_channels(ClientId) of
                    [] ->
                        %% 客户端不在线，跳过
                        ok;
                    _ ->
                        %% 客户端在线，批量恢复订阅
                        restore_client_subscriptions_batch(ClientId, ClientSubs)
                end
            end, ClientSubscriptions),

            %% 继续恢复下一批
            restore_normal_subscriptions(Collection, Skip + length(Subscriptions))
    end.

%% 按客户端ID分组订阅
group_subscriptions_by_client(Subscriptions) ->
    lists:foldl(fun(Sub, Acc) ->
        ClientId = maps:get(<<"client_id">>, Sub, <<>>),
        ClientSubs = maps:get(ClientId, Acc, []),
        maps:put(ClientId, [Sub | ClientSubs], Acc)
    end, #{}, Subscriptions).

%% @doc 记录客户端持久化订阅信息
%% 修复：采用方案A - 纯持久化方案
%%
%% 功能说明：
%% 1. 记录特定客户端的持久化订阅详细信息
%% 2. 不直接恢复订阅，避免与EMQX内置功能冲突
%% 3. 供管理员参考和故障排查使用
%%
%% 参数说明：
%% - ClientId: 客户端ID
%% - Subscriptions: 该客户端的持久化订阅列表
%% - Type: 订阅类型 (normal | shared)
%%
%% Java等价概念：
%% public void logClientPersistedSubscriptionsInfo(String clientId, List<Subscription> subscriptions, String type) {
%%     logger.warn("Client {} has {} {} subscriptions after restart", clientId, subscriptions.size(), type);
%%     for (Subscription sub : subscriptions) {
%%         logger.warn("  - Subscription: topic={}, qos={}, created_at={}",
%%                    sub.getTopic(), sub.getQos(), sub.getCreatedAt());
%%     }
%% }
log_client_persisted_subscriptions_info(ClientId, Subscriptions, Type) ->
    SubscriptionCount = length(Subscriptions),
    ?SLOG(warning, #{
        msg => "client_has_persisted_subscriptions_after_restart",
        client_id => ClientId,
        subscription_count => SubscriptionCount,
        subscription_type => Type,
        note => "subscriptions_will_be_handled_by_emqx_when_client_reconnects"
    }),

    %% 记录每个订阅的详细信息（限制数量避免日志过多）
    MaxLogSubscriptions = 10,
    SubscriptionsToLog = case SubscriptionCount > MaxLogSubscriptions of
        true -> lists:sublist(Subscriptions, MaxLogSubscriptions);
        false -> Subscriptions
    end,

    lists:foreach(fun(SubscriptionDoc) ->
        try
            Topic = maps:get(<<"topic">>, SubscriptionDoc, <<"unknown">>),
            QoS = maps:get(<<"qos">>, SubscriptionDoc, 0),
            CreatedAt = maps:get(<<"created_at">>, SubscriptionDoc, 0),
            IsShared = maps:get(<<"is_shared">>, SubscriptionDoc, false),
            ShareGroup = maps:get(<<"share_group">>, SubscriptionDoc, <<>>),

            ?SLOG(warning, #{
                msg => "persisted_subscription_details_after_restart",
                client_id => ClientId,
                topic => Topic,
                qos => QoS,
                is_shared => IsShared,
                share_group => ShareGroup,
                created_at => CreatedAt,
                note => "will_be_handled_by_emqx_when_client_reconnects"
            })
        catch
            E:R:S ->
                ?SLOG(error, #{
                    msg => "error_logging_persisted_subscription_details",
                    client_id => ClientId,
                    error => E,
                    reason => R,
                    stacktrace => S,
                    subscription_doc => SubscriptionDoc
                })
        end
    end, SubscriptionsToLog),

    %% 如果订阅数量超过限制，记录省略信息
    case SubscriptionCount > MaxLogSubscriptions of
        true ->
            ?SLOG(warning, #{
                msg => "additional_persisted_subscriptions_not_logged",
                client_id => ClientId,
                logged_count => MaxLogSubscriptions,
                total_count => SubscriptionCount,
                omitted_count => SubscriptionCount - MaxLogSubscriptions
            });
        false ->
            ok
    end.

%% @doc 批量恢复客户端订阅（兼容性函数）
%% 修复：这个函数现在是正确的批量订阅恢复实现
%%
%% 功能说明：
%% - 批量处理订阅恢复，提高效率
%% - 使用EMQX标准API恢复订阅到会话
%% - 符合MQTT协议的订阅恢复语义
restore_client_subscriptions_batch(ClientId, Subscriptions) ->
    ?SLOG(info, #{
        msg => "restore_client_subscriptions_batch_called",
        client_id => ClientId,
        subscription_count => length(Subscriptions),
        approach => "mqtt_protocol_compliant_subscription_restoration"
    }),

    try
        %% 构建批量订阅请求
        BatchSubs = lists:map(fun(Sub) ->
            Topic = maps:get(<<"topic">>, Sub, <<>>),
            QoS = maps:get(<<"qos">>, Sub, 0),
                            SubOpts = #{
                                qos => QoS,
                                rap => maps:get(<<"rap">>, Sub, false),
                                nl => maps:get(<<"nl">>, Sub, false),
                                rh => maps:get(<<"rh">>, Sub, 0)
                            },
            {Topic, SubOpts}
        end, Subscriptions),

        %% 修复：遵循方案A原则，不直接操作EMQX内置功能
        %% 只记录订阅信息，让EMQX在客户端重连时自然恢复订阅
        SuccessCount = length(BatchSubs),

        ?SLOG(info, #{
            msg => "subscription_persistence_info_logged_following_plan_a",
            client_id => ClientId,
            subscription_count => SuccessCount,
            subscriptions => lists:map(fun({Topic, SubOpts}) ->
                #{topic => Topic, qos => maps:get(qos, SubOpts, 0)}
            end, BatchSubs),
            note => "subscriptions_will_be_restored_naturally_on_client_reconnect"
        }),

        ?SLOG(info, #{
            msg => "subscriptions_restored",
            client_id => ClientId,
            total_count => length(BatchSubs),
            success_count => SuccessCount,
            failed_count => length(BatchSubs) - SuccessCount
        })
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_in_batch_subscription_restore",
                client_id => ClientId,
                                    error => E,
                                    reason => R,
                                    stacktrace => S
                                })
    end.

%% @doc 检查持久化的共享订阅
%% 修复：采用方案A - 纯持久化方案
%%
%% 功能说明：
%% 1. 检查系统重启后发现的持久化共享订阅
%% 2. 不直接恢复订阅，避免与EMQX内置功能冲突
%% 3. 记录共享订阅信息供管理员参考和故障排查使用
%%
%% 参数说明：
%% - Collection: MongoDB订阅集合名称
%% - Skip: 跳过的记录数（用于分页处理）
%%
%% Java等价概念：
%% public void checkPersistedSharedSubscriptions(String collection, int skip) {
%%     List<SharedSubscription> subscriptions = subscriptionRepository.findSharedSubscriptions(skip, limit);
%%     for (SharedSubscription sub : subscriptions) {
%%         logger.warn("Found persisted shared subscription after restart: group={}, topic={}",
%%                    sub.getGroup(), sub.getTopic());
%%     }
%% }
check_persisted_shared_subscriptions(Collection, Skip) ->
    Limit = 100,
    %% 只查询共享订阅
    Filter = #{<<"is_shared">> => true},
    case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                            {find, Collection, Filter, #{}, Skip, Limit}) of
        {ok, SharedSubscriptions} when is_list(SharedSubscriptions), length(SharedSubscriptions) > 0 ->
            %% 按组和主题分组共享订阅
            GroupedSharedSubs = group_shared_subscriptions(SharedSubscriptions),

            %% 记录每个共享订阅组的信息（不直接恢复）
            maps:foreach(fun({Group, TopicSubs}, _Members) ->
                maps:foreach(fun(Topic, Members) ->
                    log_shared_subscription_group_info(Group, Topic, Members)
                end, TopicSubs)
            end, GroupedSharedSubs),

            %% 继续检查下一批
            check_persisted_shared_subscriptions(Collection, Skip + length(SharedSubscriptions));
        {ok, []} ->
            %% 没有更多共享订阅
            ?SLOG(info, #{
                msg => "finished_checking_persisted_shared_subscriptions",
                total_processed => Skip
            });
        {error, Reason} ->
            ?SLOG(error, #{
                msg => "error_checking_persisted_shared_subscriptions",
                reason => Reason,
                skip => Skip
            })
    end.

%% @doc 记录共享订阅组信息
%% 修复：采用方案A - 纯持久化方案
log_shared_subscription_group_info(Group, Topic, Members) ->
    MemberCount = length(Members),
    IsQueueSubscription = (Group =:= ?QUEUE_PREFIX),
    ?SLOG(warning, #{
        msg => "persisted_shared_subscription_group_found_after_restart",
        group => Group,
        topic => Topic,
        member_count => MemberCount,
        subscription_type => case IsQueueSubscription of
            true -> "queue_subscription";
            false -> "group_shared_subscription"
        end,
        note => "shared_subscription_will_be_handled_by_emqx_when_clients_reconnect"
    }),

    %% 记录每个成员的详细信息（限制数量避免日志过多）
    MaxLogMembers = 5,
    MembersToLog = case MemberCount > MaxLogMembers of
        true -> lists:sublist(Members, MaxLogMembers);
        false -> Members
    end,

    lists:foreach(fun(MemberDoc) ->
        try
            ClientId = maps:get(<<"client_id">>, MemberDoc, <<"unknown">>),
            QoS = maps:get(<<"qos">>, MemberDoc, 0),
            CreatedAt = maps:get(<<"created_at">>, MemberDoc, 0),

            IsQueueSubscription = (Group =:= ?QUEUE_PREFIX),
            ?SLOG(warning, #{
                msg => "persisted_shared_subscription_member_details_after_restart",
                group => Group,
                topic => Topic,
                client_id => ClientId,
                qos => QoS,
                created_at => CreatedAt,
                subscription_type => case IsQueueSubscription of
                    true -> "queue_subscription";
                    false -> "group_shared_subscription"
                end,
                note => "will_be_handled_by_emqx_when_client_reconnects"
            })
        catch
            E:R:S ->
                ?SLOG(error, #{
                    msg => "error_logging_shared_subscription_member_details",
                    group => Group,
                    topic => Topic,
                    error => E,
                    reason => R,
                    stacktrace => S,
                    member_doc => MemberDoc
                })
        end
    end, MembersToLog),

    %% 如果成员数量超过限制，记录省略信息
    case MemberCount > MaxLogMembers of
        true ->
            ?SLOG(warning, #{
                msg => "additional_shared_subscription_members_not_logged",
                group => Group,
                topic => Topic,
                logged_count => MaxLogMembers,
                total_count => MemberCount,
                omitted_count => MemberCount - MaxLogMembers
            });
        false ->
            ok
    end.

%% @doc 批量恢复共享订阅
%% 警告：这个函数在方案A中不应该被调用！
%%
%% 修复说明：
%% - 这个函数违反了方案A的设计原则（插件不应该直接恢复EMQX订阅）
%% - 保留此函数仅用于向后兼容，但会记录警告日志
%% - 在新的方案A中，应该让EMQX的重连机制处理订阅恢复
restore_shared_subscriptions(Collection, Skip) ->
    %% 记录警告：这个函数不应该在方案A中被调用
    ?SLOG(warning, #{
        msg => "restore_shared_subscriptions_called_violates_plan_a",
        collection => Collection,
        skip => Skip,
        note => "this_function_should_not_be_called_in_pure_persistence_approach"
    }),

    Limit = 100,
    %% 只查询共享订阅
    Filter = #{<<"is_shared">> => true},
    case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                            {find, Collection, Filter, #{}, Skip, Limit}) of
        {ok, []} ->
            %% 所有共享订阅已恢复
            ?SLOG(info, #{msg => "all_shared_subscriptions_restored_but_violates_plan_a", count => Skip}),
            ok;
        {ok, SharedSubscriptions} ->
            %% 按组和主题分组共享订阅
            GroupedSharedSubs = group_shared_subscriptions(SharedSubscriptions),

            %% 恢复每个组的共享订阅（违反方案A原则）
            maps:foreach(fun({Group, TopicSubs}, _Members) ->
                maps:foreach(fun(Topic, Members) ->
                    restore_shared_subscription_group(Group, Topic, Members)
                end, TopicSubs)
            end, GroupedSharedSubs),

            %% 继续恢复下一批
            restore_shared_subscriptions(Collection, Skip + length(SharedSubscriptions))
    end.

%% @doc 恢复特定共享订阅组的订阅
%% 警告：这个函数在方案A中不应该被调用！
%%
%% 修复说明：
%% - 这个函数违反了方案A的设计原则（插件不应该直接恢复EMQX订阅）
%% - 保留此函数仅用于向后兼容，但会记录警告日志
%% - 在新的方案A中，应该让EMQX的重连机制处理订阅恢复
restore_shared_subscription_group(Group, Topic, Members) ->
    %% 记录警告：这个函数不应该在方案A中被调用
    ?SLOG(warning, #{
        msg => "restore_shared_subscription_group_called_violates_plan_a",
        group => Group,
        topic => Topic,
        member_count => length(Members),
        note => "this_function_should_not_be_called_in_pure_persistence_approach"
    }),

    try
        %% 为每个客户端尝试恢复订阅（违反方案A原则）
        lists:foreach(
            fun(#{<<"client_id">> := ClientId, <<"qos">> := QoS, <<"options">> := OptsProps}) ->
                try
                    %% 检查客户端是否在线
                    case emqx_cm:lookup_channels(ClientId) of
                        [] ->
                            %% 客户端不在线，可以记录日志或执行特定操作
                            ?SLOG(debug, #{
                                msg => "client_offline_when_restoring_shared_subscription",
                                client_id => ClientId,
                                group => Group,
                                topic => Topic
                            });
                        _Pids ->
                            %% 客户端在线，恢复订阅
                            %% 转换选项从属性列表到map
                            Options = maps:from_list([
                                {Key, Value} || {Key, Value} <- OptsProps
                            ]),

                            %% 构造完整的共享主题，支持两种格式
                            FullTopic = case Group of
                                ?QUEUE_PREFIX ->
                                    % $queue/topic 格式
                                    erlang:iolist_to_binary([?QUEUE_PREFIX, "/", Topic]);
                                _ ->
                                    % $share/group/topic 格式
                                    erlang:iolist_to_binary([?SHARED_PREFIX, "/", Group, "/", Topic])
                            end,

                            %% 创建订阅（违反方案A原则）
                            case emqx_broker:subscribe(FullTopic, ClientId, #{qos => QoS, sub_props => Options}) of
                                ok ->
                                    IsQueueSubscription = (Group =:= ?QUEUE_PREFIX),
                                    ?SLOG(info, #{
                                        msg => "shared_subscription_restored_successfully_but_violates_plan_a",
                                        client_id => ClientId,
                                        group => Group,
                                        topic => Topic,
                                        full_topic => FullTopic,
                                        subscription_type => case IsQueueSubscription of
                                            true -> "queue_subscription";
                                            false -> "group_shared_subscription"
                                        end
                                    });
                                {error, SubError} ->
                                    IsQueueSubscription = (Group =:= ?QUEUE_PREFIX),
                                    ?SLOG(error, #{
                                        msg => "failed_to_restore_shared_subscription",
                                        client_id => ClientId,
                                        group => Group,
                                        topic => Topic,
                                        full_topic => FullTopic,
                                        error => SubError,
                                        subscription_type => case IsQueueSubscription of
                                            true -> "queue_subscription";
                                            false -> "group_shared_subscription"
                                        end
                                    })
                            end
                    end
                catch
                    E2:R2:S2 ->
                        ?SLOG(error, #{
                            msg => "error_restoring_shared_subscription_for_client",
                            client_id => ClientId,
                            group => Group,
                            topic => Topic,
                            error => E2,
                            reason => R2,
                            stacktrace => S2
                        })
                end
            end,
            Members
        )
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_restoring_shared_subscription_group",
                group => Group,
                topic => Topic,
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% 按组和主题分组共享订阅
group_shared_subscriptions(SharedSubscriptions) ->
    lists:foldl(fun(Sub, Acc) ->
        Group = maps:get(<<"shared_group">>, Sub, <<>>),
        Topic = maps:get(<<"topic">>, Sub, <<>>),

        % 获取当前组
        GroupMap = maps:get(Group, Acc, #{}),
        % 获取当前组下的主题成员
        TopicMembers = maps:get(Topic, GroupMap, []),
        % 添加新成员
        NewTopicMembers = [Sub | TopicMembers],
        % 更新主题成员
        NewGroupMap = maps:put(Topic, NewTopicMembers, GroupMap),
        % 更新组映射
        maps:put(Group, NewGroupMap, Acc)
    end, #{}, SharedSubscriptions).

%% 启动清理定时器
start_cleanup_timer() ->
    CleanupInterval = get_cleanup_interval(),
    ?SLOG(info, #{msg => "starting_subscription_cleanup_timer", interval => CleanupInterval}),
    erlang:send_after(CleanupInterval, self(), cleanup_expired_subscriptions),
    ok.

%% 清理过期订阅
cleanup_expired_subscriptions() ->
    ?SLOG(info, #{msg => "cleaning_expired_subscriptions"}),
    spawn(fun() -> do_cleanup_expired_subscriptions() end),
    % 重新启动定时器
    start_cleanup_timer(),
    ok.

%% 执行过期订阅清理
do_cleanup_expired_subscriptions() ->
    try
        % 获取集合名称
        SubscriptionCollection = get_subscription_collection(),

        % 计算过期时间
        Now = erlang:system_time(millisecond),

        % 删除已过期的订阅
        Filter = #{<<"expiry_time">> => #{<<"$lt">> => Now}},

        % 获取批次大小
        BatchSize = get_cleanup_batch_size(),

        % 执行删除 (注意：MongoDB delete操作的limit只能是0或1，不能用BatchSize)
        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                                {delete_many, SubscriptionCollection, Filter, #{}}) of
            {ok, #{<<"deletedCount">> := DeletedCount}} ->
                ?SLOG(info, #{msg => "expired_subscriptions_cleaned", count => DeletedCount});
            {error, Reason} ->
                ?SLOG(error, #{msg => "failed_to_clean_expired_subscriptions", reason => Reason})
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_cleaning_expired_subscriptions",
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% 集成到协调器
integrate() ->
    ?SLOG(info, #{msg => "integrating_subscription_module"}),
    % 将此模块注册到协调器
    case erlang:function_exported(emqx_plugin_mongodb_coordinator, register_module, 2) of
        true ->
            emqx_plugin_mongodb_coordinator:register_module(?MODULE, #{
                priority => high,
                description => <<"Subscription persistence module">>,
                features => [subscription_persistence, auto_restore]
            });
        false ->
            ok
    end.

%% 获取订阅集合名称
get_subscription_collection() ->
    % 从配置中获取集合名称
    Config = application:get_env(emqx_plugin_mongodb, config, #{}),
    SubscriptionConfig = maps:get(subscription_persistence, Config, #{}),
    maps:get(collection, SubscriptionConfig, ?DEFAULT_SUBSCRIPTION_COLLECTION).

%% 获取订阅过期时间
get_subscription_expiry() ->
    Config = application:get_env(emqx_plugin_mongodb, config, #{}),
    SubscriptionConfig = maps:get(subscription_persistence, Config, #{}),
    maps:get(subscription_expiry, SubscriptionConfig, ?DEFAULT_SUBSCRIPTION_EXPIRY).

%% 获取清理间隔
get_cleanup_interval() ->
    Config = application:get_env(emqx_plugin_mongodb, config, #{}),
    SubscriptionConfig = maps:get(subscription_persistence, Config, #{}),
    maps:get(cleanup_interval, SubscriptionConfig, ?DEFAULT_CLEANUP_INTERVAL).

%% 获取清理批次大小
get_cleanup_batch_size() ->
    Config = application:get_env(emqx_plugin_mongodb, config, #{}),
    SubscriptionConfig = maps:get(subscription_persistence, Config, #{}),
    maps:get(cleanup_batch_size, SubscriptionConfig, ?DEFAULT_CLEANUP_BATCH_SIZE).

%% 检查是否应该保存共享订阅
should_save_shared_subscription() ->
    Config = application:get_env(emqx_plugin_mongodb, config, #{}),
    SubscriptionConfig = maps:get(subscription_persistence, Config, #{}),
    maps:get(save_shared_subscriptions, SubscriptionConfig, false).

%% 判断是否为共享订阅
%% 支持两种EMQX共享订阅格式：
%% 1. $share/<group-name>/topic - 带群组的共享订阅
%% 2. $queue/topic - 不带群组的共享订阅
is_shared_subscription(Topic) ->
    case binary:split(Topic, <<"/">>) of
        [?SHARED_PREFIX, _Group | _TopicParts] -> true;  % $share/group/topic
        [?QUEUE_PREFIX | _TopicParts] -> true;           % $queue/topic
        _ -> false
    end.

%% 解析共享订阅主题
%% 支持两种EMQX共享订阅格式：
%% 1. $share/<group-name>/topic - 返回 {ok, {Group, RealTopic}}
%% 2. $queue/topic - 返回 {ok, {<<"$queue">>, RealTopic}}
parse_shared_subscription(Topic) ->
    try
        case binary:split(Topic, <<"/">>, [global]) of
            [?SHARED_PREFIX, Group | TopicParts] when length(TopicParts) > 0 ->
                % $share/group/topic 格式
                RealTopic = erlang:iolist_to_binary(lists:join(<<"/">>, TopicParts)),
                {ok, {Group, RealTopic}};
            [?SHARED_PREFIX, Group] ->
                % 只有组名，没有实际主题
                {error, missing_topic};
            [?QUEUE_PREFIX | TopicParts] when length(TopicParts) > 0 ->
                % $queue/topic 格式，使用特殊组名 "$queue"
                RealTopic = erlang:iolist_to_binary(lists:join(<<"/">>, TopicParts)),
                {ok, {?QUEUE_PREFIX, RealTopic}};
            [?QUEUE_PREFIX] ->
                % 只有$queue前缀，没有实际主题
                {error, missing_topic};
            _ ->
                {error, invalid_shared_topic_format}
        end
    catch
        _:_ ->
            {error, parse_error}
    end.

%% 保存共享订阅
%% 支持两种EMQX共享订阅格式：
%% 1. $share/<group-name>/topic
%% 2. $queue/topic (Group参数为 <<"$queue">>)
save_shared_subscription(ClientInfo, Group, RealTopic, SubOpts) ->
    ClientId = maps:get(clientid, ClientInfo, undefined),
    QoS = maps:get(qos, SubOpts, 0),

    % 重构完整的共享主题，支持两种格式
    Topic = case Group of
        ?QUEUE_PREFIX ->
            % $queue/topic 格式
            erlang:iolist_to_binary([?QUEUE_PREFIX, "/", RealTopic]);
        _ ->
            % $share/group/topic 格式
            erlang:iolist_to_binary([?SHARED_PREFIX, "/", Group, "/", RealTopic])
    end,

    try
        % 获取订阅集合名称
        Collection = get_subscription_collection(),

        % 生成文档ID (clientid + topic的组合)
        DocId = erlang:iolist_to_binary([ClientId, ":", Topic]),

        % 获取当前时间
        Now = erlang:system_time(second),

        % 解析MQTT 5.0共享订阅属性
        SubscriptionExpiryInterval = extract_subscription_expiry_interval(SubOpts),
        MaximumQoS = extract_maximum_qos(SubOpts),
        RetainAsPublished = maps:get(rap, SubOpts, false),
        NoLocal = maps:get(nl, SubOpts, false),
        RetainHandling = maps:get(rh, SubOpts, 0),
        SubscriptionIdentifier = maps:get(subid, SubOpts, undefined),
        UserProperties = maps:get(user_properties, SubOpts, #{}),

        % 计算共享订阅过期时间
        ExpiryTime = case SubscriptionExpiryInterval of
            undefined ->
                % 使用共享订阅配置的过期时间
                case get_shared_subscription_expiry() of
                    infinity -> 0; % 0表示不过期
                    Expiry when is_integer(Expiry) -> Now + (Expiry * 1000)
                end;
            Interval when is_integer(Interval) ->
                % 使用订阅指定的过期时间
                Now + (Interval * 1000)
        end,

        % 构造符合MQTT协议的共享订阅文档
        % 支持两种共享订阅格式的标识
        IsQueueSubscription = (Group =:= ?QUEUE_PREFIX),
        Doc = #{
            <<"_id">> => DocId,
            <<"client_id">> => ClientId,
            <<"topic">> => Topic,
            <<"qos">> => QoS,
            <<"is_shared">> => true,
            <<"shared_group">> => Group,
            <<"real_topic">> => RealTopic,
            <<"is_queue_subscription">> => IsQueueSubscription,  % 标识是否为$queue订阅

            % MQTT 5.0订阅选项
            <<"retain_as_published">> => RetainAsPublished,
            <<"no_local">> => NoLocal,
            <<"retain_handling">> => RetainHandling,

            % MQTT 5.0订阅属性
            <<"subscription_expiry_interval">> => SubscriptionExpiryInterval,
            <<"maximum_qos">> => MaximumQoS,
            <<"subscription_identifier">> => SubscriptionIdentifier,
            <<"user_properties">> => encode_subscription_user_properties(UserProperties),

            % 共享订阅组管理
            <<"group_member_status">> => <<"active">>,
            <<"group_join_time">> => Now,
            <<"load_balance_strategy">> => <<"round_robin">>, % 默认负载均衡策略

            % 时间戳
            <<"created_at">> => Now,
            <<"updated_at">> => Now,
            <<"expiry_time">> => ExpiryTime,

            % 兼容性：保留原始选项
            <<"options">> => maps:to_list(SubOpts)
        },

        % 文档已经包含过期时间，不需要额外处理
        DocWithExpiry = Doc,

        % 保存到MongoDB
        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                              {update, Collection,
                               #{<<"_id">> => DocId},
                               #{<<"$set">> => DocWithExpiry},
                               #{upsert => true}}) of
            {ok, _} ->
                ok;
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_save_shared_subscription",
                    client_id => ClientId,
                    topic => Topic,
                    reason => Reason
                }),
                {error, Reason}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_saving_shared_subscription",
                client_id => ClientId,
                topic => Topic,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%% 获取共享订阅过期时间
get_shared_subscription_expiry() ->
    % 从应用环境变量中获取，如果未设置则使用默认值
    application:get_env(emqx_plugin_mongodb, shared_subscription_expiry, infinity).

%% 获取共享订阅策略
get_shared_subscription_strategy() ->
    % 从应用环境变量中获取，如果未设置则使用默认值 (随机)
    application:get_env(emqx_plugin_mongodb, shared_subscription_strategy, random).

%% 获取共享订阅hash键
get_shared_subscription_hash_key() ->
    % 从应用环境变量中获取，如果未设置则使用默认值 (clientid)
    application:get_env(emqx_plugin_mongodb, shared_subscription_hash_key, clientid).

%% 获取共享订阅优先级
get_shared_subscription_priority() ->
    % 从应用环境变量中获取，如果未设置则使用默认值 (0)
    application:get_env(emqx_plugin_mongodb, shared_subscription_priority, 0).

%% 判断是否为离线客户端保存消息
should_save_messages_for_offline() ->
    % 从应用环境变量中获取，如果未设置则使用默认值 (true)
    application:get_env(emqx_plugin_mongodb, save_messages_for_offline, true).

%% 获取特定主题的所有共享订阅组
get_shared_groups_by_topic(Topic) ->
    try
        % 获取集合名称
        SubscriptionCollection = get_subscription_collection(),

        % 查询该主题的所有共享订阅组
        Filter = #{
            <<"topic">> => Topic,
            <<"is_shared">> => true
        },

        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                               {distinct, SubscriptionCollection, <<"shared_group">>, Filter}) of
            {ok, Groups} ->
                {ok, Groups};
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_get_shared_groups",
                    topic => Topic,
                    reason => Reason
                }),
                {error, Reason}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_getting_shared_groups",
                topic => Topic,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, R}
    end.

%% 获取特定共享组的所有成员
get_shared_group_members(Group, Topic) ->
    try
        % 获取集合名称
        SubscriptionCollection = get_subscription_collection(),

        % 查询该共享组的所有成员
        Filter = #{
            <<"shared_group">> => Group,
            <<"topic">> => Topic,
            <<"is_shared">> => true
        },

        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                               {find, SubscriptionCollection, Filter, #{}, 0, 1000}) of
            {ok, Members} ->
                {ok, Members};
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_get_shared_group_members",
                    group => Group,
                    topic => Topic,
                    reason => Reason
                }),
                {error, Reason}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_getting_shared_group_members",
                group => Group,
                topic => Topic,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, R}
    end.

%% 获取特定主题的所有共享订阅
get_shared_subscriptions(Topic) ->
    try
        % 获取集合名称
        SubscriptionCollection = get_subscription_collection(),

        % 查询该主题的所有共享订阅
        Filter = #{
            <<"topic">> => Topic,
            <<"is_shared">> => true
        },

        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                               {find, SubscriptionCollection, Filter, #{}, 0, 1000}) of
            {ok, SharedSubs} ->
                {ok, SharedSubs};
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_get_shared_subscriptions",
                    topic => Topic,
                    reason => Reason
                }),
                {error, Reason}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_getting_shared_subscriptions",
                topic => Topic,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, R}
    end.

%%--------------------------------------------------------------------
%% 订阅统计和查询功能
%%--------------------------------------------------------------------

%% @doc 获取订阅统计信息
get_subscription_stats() ->
    try
        Collection = get_subscription_collection(),

        % 统计总订阅数
        TotalFilter = #{},
        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                               {count, Collection, TotalFilter}) of
            {ok, TotalCount} ->
                % 统计共享订阅数
                SharedFilter = #{<<"subscription_type">> => <<"shared">>},
                case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                                       {count, Collection, SharedFilter}) of
                    {ok, SharedCount} ->
                        {ok, #{
                            total_subscriptions => TotalCount,
                            shared_subscriptions => SharedCount,
                            normal_subscriptions => TotalCount - SharedCount
                        }};
                    {error, Reason} ->
                        {error, {shared_count_failed, Reason}}
                end;
            {error, Reason} ->
                {error, {total_count_failed, Reason}}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_getting_subscription_stats",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%% @doc 根据主题查找订阅
find_subscriptions_by_topic(Topic) ->
    try
        Collection = get_subscription_collection(),
        Filter = #{<<"topic">> => Topic},

        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                               {find, Collection, Filter, #{}, 0, 100}) of
            {ok, Subscriptions} ->
                {ok, Subscriptions};
            {async_return, {ok, Subscriptions}} ->
                {ok, Subscriptions};
            {async_return, {error, Reason}} ->
                {error, Reason};
            {error, Reason} ->
                {error, Reason}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_finding_subscriptions_by_topic",
                error => E,
                reason => R,
                stacktrace => S,
                topic => Topic
            }),
            {error, {E, R}}
    end.

%% @doc 生成集合名称缩写
generate_collection_abbreviation(Collection) when is_binary(Collection) ->
    % 将集合名称转换为缩写
    % emqx_mqtt_subscriptions -> emsub
    % emqx_subscriptions -> esub
    Parts = binary:split(Collection, <<"_">>, [global]),
    Abbreviation = lists:foldl(fun(Part, Acc) ->
        case Part of
            <<>> -> Acc;
            <<First:8, _/binary>> -> <<Acc/binary, First>>
        end
    end, <<>>, Parts),

    % 确保缩写不为空，至少有2个字符
    case byte_size(Abbreviation) of
        0 -> <<"idx">>;
        1 -> <<Abbreviation/binary, "x">>;
        _ -> Abbreviation
    end;
generate_collection_abbreviation(_) ->
    <<"idx">>.

%% @doc 处理订阅索引创建错误
handle_subscription_index_error(Collection, IndexName, Reason) ->
    % 检查是否是索引已存在的错误
    case is_index_exists_error(Reason) of
        true ->
            ?SLOG(info, #{
                msg => "subscription_index_already_exists",
                collection => Collection,
                index_name => IndexName
            });
        false ->
            ?SLOG(warning, #{
                msg => "failed_to_create_subscription_index",
                collection => Collection,
                index_name => IndexName,
                reason => Reason
            })
    end.

%% @doc 检查是否是索引已存在的错误
is_index_exists_error(Reason) ->
    case Reason of
        {error, {badmatch, {error, {error, {error, {op_msg_response, Response}}}}}} ->
            check_index_conflict_in_response(Response);
        {error, {error, {error, {op_msg_response, Response}}}} ->
            check_index_conflict_in_response(Response);
        {error, {error, {op_msg_response, Response}}} ->
            check_index_conflict_in_response(Response);
        {error, {op_msg_response, Response}} ->
            check_index_conflict_in_response(Response);
        _ ->
            false
    end.

%% @doc 检查MongoDB响应中是否包含索引冲突信息
check_index_conflict_in_response(Response) when is_map(Response) ->
    case maps:get(<<"codeName">>, Response, undefined) of
        <<"IndexOptionsConflict">> -> true;
        <<"IndexKeySpecsConflict">> -> true;
        _ ->
            case maps:get(<<"code">>, Response, undefined) of
                85 -> true;  % IndexOptionsConflict
                86 -> true;  % IndexKeySpecsConflict
                _ -> false
            end
    end;
check_index_conflict_in_response(_) ->
    false.

%%--------------------------------------------------------------------
%% MQTT 5.0 订阅辅助函数
%%--------------------------------------------------------------------

%% @doc 编码订阅用户属性为MongoDB存储格式
encode_subscription_user_properties(UserProps) when is_map(UserProps) ->
    maps:fold(fun(K, V, Acc) ->
        KeyBin = case K of
            KBin when is_binary(KBin) -> KBin;
            KAtom when is_atom(KAtom) -> atom_to_binary(KAtom, utf8);
            KList when is_list(KList) -> list_to_binary(KList);
            _ -> iolist_to_binary(io_lib:format("~p", [K]))
        end,
        ValueBin = case V of
            VBin when is_binary(VBin) -> VBin;
            VAtom when is_atom(VAtom) -> atom_to_binary(VAtom, utf8);
            VList when is_list(VList) -> list_to_binary(VList);
            _ -> iolist_to_binary(io_lib:format("~p", [V]))
        end,
        Acc#{KeyBin => ValueBin}
    end, #{}, UserProps);
encode_subscription_user_properties(UserProps) when is_list(UserProps) ->
    % 处理键值对列表格式
    lists:foldl(fun({K, V}, Acc) ->
        KeyBin = case K of
            KBin when is_binary(KBin) -> KBin;
            KAtom when is_atom(KAtom) -> atom_to_binary(KAtom, utf8);
            KList when is_list(KList) -> list_to_binary(KList);
            _ -> iolist_to_binary(io_lib:format("~p", [K]))
        end,
        ValueBin = case V of
            VBin when is_binary(VBin) -> VBin;
            VAtom when is_atom(VAtom) -> atom_to_binary(VAtom, utf8);
            VList when is_list(VList) -> list_to_binary(VList);
            _ -> iolist_to_binary(io_lib:format("~p", [V]))
        end,
        Acc#{KeyBin => ValueBin}
    end, #{}, UserProps);
encode_subscription_user_properties(_) ->
    #{}.

%% @doc 解码订阅用户属性从MongoDB存储格式
decode_subscription_user_properties(UserProps) when is_map(UserProps) ->
    UserProps;
decode_subscription_user_properties(_) ->
    #{}.

%%--------------------------------------------------------------------
%% MQTT 5.0订阅属性解析函数
%%--------------------------------------------------------------------

%% @doc 提取Subscription Expiry Interval属性
%% MQTT 5.0协议：指定订阅在创建后的有效期（秒）
extract_subscription_expiry_interval(SubOpts) when is_map(SubOpts) ->
    case maps:get('Subscription-Expiry-Interval', SubOpts,
                  maps:get(<<"Subscription-Expiry-Interval">>, SubOpts,
                          maps:get(subscription_expiry_interval, SubOpts, undefined))) of
        Interval when is_integer(Interval), Interval > 0 -> Interval;
        _ -> undefined
    end;
extract_subscription_expiry_interval(_) -> undefined.

%% @doc 提取Maximum QoS属性
%% MQTT 5.0协议：指定订阅允许的最大QoS级别
extract_maximum_qos(SubOpts) when is_map(SubOpts) ->
    case maps:get('Maximum-QoS', SubOpts,
                  maps:get(<<"Maximum-QoS">>, SubOpts,
                          maps:get(maximum_qos, SubOpts, undefined))) of
        QoS when QoS =:= 0; QoS =:= 1; QoS =:= 2 -> QoS;
        _ -> undefined
    end;
extract_maximum_qos(_) -> undefined.

%% @doc 计算订阅过期时间
%% 优先使用订阅自身的Subscription Expiry Interval，否则使用插件配置
calculate_subscription_expiry_time(undefined) ->
    % 没有指定Subscription Expiry Interval，使用插件配置
    ExpiryInterval = get_subscription_expiry(),
    case ExpiryInterval of
        infinity -> 0; % 0表示不过期
        Interval when is_integer(Interval) ->
            erlang:system_time(millisecond) + Interval
    end;
calculate_subscription_expiry_time(SubscriptionExpiryInterval) when is_integer(SubscriptionExpiryInterval) ->
    % 使用订阅指定的过期时间（转换为毫秒）
    erlang:system_time(millisecond) + (SubscriptionExpiryInterval * 1000);
calculate_subscription_expiry_time(_) ->
    % 无效值，使用插件配置
    ExpiryInterval = get_subscription_expiry(),
    case ExpiryInterval of
        infinity -> 0;
        Interval when is_integer(Interval) ->
            erlang:system_time(millisecond) + Interval
    end.

%% @doc 确保订阅集合和索引存在
ensure_subscription_collection_and_indexes(Collection) ->
    try
        % 生成集合缩写 emqx_mqtt_subscriptions -> emsub
        SubscriptionAbbr = generate_collection_abbreviation(Collection),

        ?SLOG(info, #{
            msg => "ensuring_subscription_collection_and_indexes",
            collection => Collection,
            collection_abbr => SubscriptionAbbr
        }),

        % 创建索引
        Indexes = [
            % 客户端ID索引
            #{
                <<"key">> => #{<<"clientid">> => 1},
                <<"name">> => <<SubscriptionAbbr/binary, "_clientid_asc">>
            },
            % 主题索引
            #{
                <<"key">> => #{<<"topic">> => 1},
                <<"name">> => <<SubscriptionAbbr/binary, "_topic_asc">>
            },
            % QoS索引
            #{
                <<"key">> => #{<<"qos">> => 1},
                <<"name">> => <<SubscriptionAbbr/binary, "_qos_asc">>
            },
            % 订阅时间索引
            #{
                <<"key">> => #{<<"subscribed_at">> => 1},
                <<"name">> => <<SubscriptionAbbr/binary, "_subscribed_at_asc">>
            },
            % 过期时间索引（TTL）
            #{
                <<"key">> => #{<<"expires_at">> => 1},
                <<"name">> => <<SubscriptionAbbr/binary, "_expires_at_ttl">>,
                <<"expireAfterSeconds">> => 0
            },
            % 复合索引：客户端ID+主题（唯一）
            #{
                <<"key">> => #{<<"clientid">> => 1, <<"topic">> => 1},
                <<"name">> => <<SubscriptionAbbr/binary, "_clientid_topic_unique">>,
                <<"unique">> => true
            },
            % 复合索引：主题+QoS
            #{
                <<"key">> => #{<<"topic">> => 1, <<"qos">> => 1},
                <<"name">> => <<SubscriptionAbbr/binary, "_topic_qos_compound">>
            },
            % 节点索引
            #{
                <<"key">> => #{<<"node">> => 1},
                <<"name">> => <<SubscriptionAbbr/binary, "_node_asc">>
            }
        ],

        % 逐个创建索引，带有存在性检查
        lists:foreach(fun(IndexSpec) ->
            IndexName = maps:get(<<"name">>, IndexSpec),
            case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID, {create_index, Collection, IndexSpec}) of
                {ok, _} ->
                    ?SLOG(debug, #{
                        msg => "subscription_index_created",
                        collection => Collection,
                        index_name => IndexName
                    });
                {error, {error, {error, {op_msg_response, #{<<"code">> := 85}}}}} ->
                    % IndexOptionsConflict - 索引已存在但名称不同，这是正常情况
                    ?SLOG(debug, #{
                        msg => "subscription_index_already_exists",
                        collection => Collection,
                        index_name => IndexName
                    });
                {error, Reason} ->
                    ?SLOG(warning, #{
                        msg => "subscription_index_creation_failed",
                        collection => Collection,
                        index_name => IndexName,
                        reason => Reason
                    })
            end
        end, Indexes),

        ?SLOG(info, #{
            msg => "subscription_collection_and_indexes_ensured",
            collection => Collection,
            index_count => length(Indexes)
        })
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_ensuring_subscription_collection_and_indexes",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {ensure_subscription_collection_failed, R}}
    end.
