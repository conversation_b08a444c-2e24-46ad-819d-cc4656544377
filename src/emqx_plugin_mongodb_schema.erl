%% @doc EMQX MongoDB Plugin Configuration Schema Module - 配置模式定义模块
%% 这个模块是MongoDB插件的配置模式定义中心，提供完整的配置验证和类型定义
%%
%% 功能概述：
%% 1. 配置模式定义 - 定义所有配置项的类型、约束和默认值
%% 2. 配置验证 - 提供配置项的格式验证和类型检查
%% 3. 配置文档 - 为每个配置项提供详细的描述和说明
%% 4. 嵌套配置支持 - 支持复杂的嵌套配置结构
%% 5. 枚举类型定义 - 定义各种枚举类型的有效值
%% 6. 默认值管理 - 为所有配置项提供合理的默认值
%%
%% 配置结构层次：
%% plugin_mongodb (根配置)
%% ├── connection (MongoDB连接配置)
%% ├── session_persistence (会话持久化配置)
%% ├── subscription_persistence (订阅持久化配置)
%% ├── message_persistence (消息持久化配置)
%% ├── will_persistence (遗嘱消息持久化配置)
%% ├── retained_persistence (保留消息持久化配置)
%% └── topics (主题过滤配置)
%%
%% Java等价概念：
%% 类似于Spring Boot的配置类和验证注解：
%% @ConfigurationProperties(prefix = "plugin.mongodb")
%% @Validated
%% public class MongoDBPluginConfig {
%%     @Valid
%%     @NotNull
%%     private ConnectionConfig connection;
%%
%%     @Valid
%%     private SessionPersistenceConfig sessionPersistence = new SessionPersistenceConfig();
%%
%%     @Valid
%%     private MessagePersistenceConfig messagePersistence = new MessagePersistenceConfig();
%%
%%     // 其他配置项...
%% }
%%
%% @ConfigurationProperties(prefix = "plugin.mongodb.connection")
%% @Validated
%% public class ConnectionConfig {
%%     @NotNull
%%     @Pattern(regexp = "single|sharded|rs")
%%     private String mongoType = "single";
%%
%%     @NotBlank
%%     private String database;
%%
%%     @Valid
%%     private List<String> bootstrapHosts;
%%
%%     // 其他连接配置...
%% }
%%
%% 设计模式：
%% - 建造者模式：通过fields/1函数构建配置结构
%% - 策略模式：不同配置项使用不同的验证策略
%% - 组合模式：嵌套配置结构的组合
%% - 工厂模式：配置对象的创建和验证
%% @end

-module(emqx_plugin_mongodb_schema).

%% 引入HOCON配置库的头文件
%% HOCON是一种人性化的配置格式，类似于JSON但更易读
-include_lib("hocon/include/hoconsc.hrl").
-include("emqx_plugin_mongodb.hrl").

%% ============================================================================
%% 配置模式API函数 - 配置结构定义和验证接口
%% 这些函数定义了配置的结构、字段和描述信息
%% 类似于Java中的配置类和验证注解
%% ============================================================================
-export([
    roots/0,            % 获取配置根节点
                       % 功能：定义配置的顶级节点名称
                       % Java等价：@ConfigurationProperties的prefix属性

    fields/1,          % 获取配置字段定义
                       % 功能：定义每个配置节点包含的字段和类型
                       % Java等价：配置类中的字段定义和验证注解

    desc/1             % 获取配置描述信息
                       % 功能：为配置项提供人性化的描述文档
                       % Java等价：@ConfigurationMetadata的description属性
]).

%% 导入HOCON库的枚举类型定义函数
%% 用于定义配置项的枚举值，类似于Java的@Pattern或自定义验证器
-import(hoconsc, [enum/1]).

%% @doc 获取配置根节点列表
%% 这个函数定义了配置的顶级节点名称
%%
%% 功能说明：
%% 返回配置的根节点名称列表，用于HOCON配置解析
%%
%% 返回值：
%% - [plugin_mongodb]: 表示配置的根节点是plugin_mongodb
%%
%% Java等价概念：
%% 类似于@ConfigurationProperties注解的prefix属性：
%% @ConfigurationProperties(prefix = "plugin.mongodb")
%% public class MongoDBPluginConfig { ... }
%%
%% 配置文件中的使用：
%% plugin_mongodb {
%%   connection { ... }
%%   session_persistence { ... }
%%   ...
%% }
roots() -> [plugin_mongodb].

%% @doc 获取指定配置节点的字段定义
%% 这个函数定义了每个配置节点包含的字段、类型、约束和默认值
%%
%% 参数说明：
%% - plugin_mongodb: 根配置节点，包含所有子配置模块
%%
%% 返回值：
%% - 字段定义列表，每个字段包含名称、类型、约束和描述
%%
%% Java等价概念：
%% 类似于配置类中的字段定义和验证注解：
%% public class MongoDBPluginConfig {
%%     @Valid @NotNull private ConnectionConfig connection;
%%     @Valid private SessionPersistenceConfig sessionPersistence = new SessionPersistenceConfig();
%%     // ...
%% }
fields(plugin_mongodb) ->
    [
        %% MongoDB连接配置
        %% 定义数据库连接的所有参数，包括主机、端口、认证等
        %% Java等价：@Valid @NotNull private ConnectionConfig connection;
        {connection, ?HOCON(?R_REF(connection), #{desc => ?DESC("connect_timeout")})},

        %% 会话持久化配置
        %% 控制MQTT会话的持久化行为和参数
        %% Java等价：@Valid private SessionPersistenceConfig sessionPersistence = new SessionPersistenceConfig();
        {session_persistence, ?HOCON(?R_REF(session_persistence),
            #{
                desc => ?DESC("session_persistence"),
                default => #{}                    % 默认为空映射，表示使用默认配置
            })},

        %% 订阅持久化配置
        %% 控制MQTT订阅关系的持久化行为和参数
        %% Java等价：@Valid private SubscriptionPersistenceConfig subscriptionPersistence = new SubscriptionPersistenceConfig();
        {subscription_persistence, ?HOCON(?R_REF(subscription_persistence),
            #{
                desc => ?DESC("subscription_persistence"),
                default => #{}                    % 默认为空映射，表示使用默认配置
            })},

        %% 消息持久化配置
        %% 控制MQTT消息的持久化行为和参数
        %% Java等价：@Valid private MessagePersistenceConfig messagePersistence = new MessagePersistenceConfig();
        {message_persistence, ?HOCON(?R_REF(message_persistence),
            #{
                desc => ?DESC("message_persistence"),
                default => #{}                    % 默认为空映射，表示使用默认配置
            })},

        %% 遗嘱消息持久化配置
        %% 控制MQTT遗嘱消息的持久化行为和参数
        %% Java等价：@Valid private WillPersistenceConfig willPersistence = new WillPersistenceConfig();
        {will_persistence, ?HOCON(?R_REF(will_persistence),
            #{
                desc => ?DESC("will_persistence"),
                default => #{}                    % 默认为空映射，表示使用默认配置
            })},

        %% 保留消息持久化配置
        %% 控制MQTT保留消息的持久化行为和参数
        %% Java等价：@Valid private RetainedPersistenceConfig retainedPersistence = new RetainedPersistenceConfig();
        {retained_persistence, ?HOCON(?R_REF(retained_persistence),
            #{
                desc => ?DESC("retained_persistence"),
                default => #{}                    % 默认为空映射，表示使用默认配置
            })},

        %% 主题过滤配置
        %% 定义需要处理的MQTT主题列表和过滤规则
        %% Java等价：@Valid @NotEmpty private List<TopicConfig> topics = new ArrayList<>();
        {topics, ?HOCON(?ARRAY(?R_REF(topic)),
            #{
                required => true,                 % 必需字段，必须配置主题
                default => [],                    % 默认为空列表
                desc => ?DESC("topics")
            })}
    ];

fields(connection) ->
    [
        {mongo_type, ?HOCON(enum([single, sharded, rs]),
            #{
                desc => ?DESC("mongo_type"),
                default => "single"
            })},
        {replica_set_name, ?HOCON(binary(),
            #{
                desc => ?DESC("replica_set_name")
            })},
        {bootstrap_hosts, bootstrap_hosts()},
        {w_mode, ?HOCON(enum([unsafe, safe]),
            #{
                desc => ?DESC("w_mode"),
                default => unsafe
            })},
        {database, ?HOCON(binary(),
            #{
                required => true,
                desc => ?DESC("database")
            })},
        {username, ?HOCON(binary(),
            #{
                desc => ?DESC("username")
            })},
        {password, emqx_connector_schema_lib:password_field()},
        {auth_source, ?HOCON(binary(),
            #{
                desc => ?DESC("auth_source"),
                default => <<"admin">>
            })},
        {read_preference, ?HOCON(enum([primary, primaryPreferred, secondary, secondaryPreferred, nearest]),
            #{
                desc => ?DESC("read_preference"),
                default => secondaryPreferred
            })},
        {read_concern_level, ?HOCON(enum([local, available, majority, linearizable, snapshot]),
            #{
                desc => ?DESC("read_concern_level"),
                default => majority
            })},
        {write_concern, ?HOCON(union([integer(), enum([majority]), binary()]),
            #{
                desc => ?DESC("write_concern"),
                default => majority
            })},
        {write_concern_timeout, ?HOCON(pos_integer(),
            #{
                desc => ?DESC("write_concern_timeout"),
                default => 10000
            })},
        {ssl, ?HOCON(?R_REF(ssl),
            #{
                desc => ?DESC("ssl")
            }
        )},
        {topology, ?HOCON(?R_REF(topology),
            #{
                desc => ?DESC("topology")
            }
        )},
        {health_check, ?HOCON(?R_REF(health_check),
            #{
                desc => ?DESC("health_check"),
                default => #{}
            }
        )},
        {circuit_breaker, ?HOCON(?R_REF(circuit_breaker),
            #{
                desc => ?DESC("circuit_breaker"),
                default => #{}
            }
        )},
        {batch, ?HOCON(?R_REF(batch),
            #{
                desc => ?DESC("batch"),
                default => #{}
            }
        )},
        {pipeline, ?HOCON(?R_REF(pipeline),
            #{
                desc => ?DESC("pipeline"),
                default => #{}
            }
        )},
        {connection_pool, ?HOCON(?R_REF(connection_pool),
            #{
                desc => ?DESC("connection_pool"),
                default => #{}
            }
        )},
        {cache, ?HOCON(?R_REF(cache),
            #{
                desc => ?DESC("cache"),
                default => #{}
            }
        )},
        {metrics, ?HOCON(?R_REF(metrics),
            #{
                desc => ?DESC("metrics"),
                default => #{}
            }
        )},
        {error_handler, ?HOCON(?R_REF(error_handler),
            #{
                desc => ?DESC("error_handler"),
                default => #{}
            }
        )},
        {resource_manager, ?HOCON(?R_REF(resource_manager),
            #{
                desc => ?DESC("resource_manager"),
                default => #{}
            }
        )},
        {backpressure, ?HOCON(?R_REF(backpressure),
            #{
                desc => ?DESC("backpressure"),
                default => #{}
            }
        )}
    ];

fields(ssl) ->
    [
        {enable, ?HOCON(boolean(),
            #{
                desc => ?DESC("ssl_enable"),
                default => false
            }
        )},
        {certfile, ?HOCON(binary(),
            #{
                desc => ?DESC("ssl_certfile"),
                default => <<"">>
            }
        )},
        {keyfile, ?HOCON(binary(),
            #{
                desc => ?DESC("ssl_keyfile"),
                default => <<"">>
            }
        )},
        {cacertfile, ?HOCON(binary(),
            #{
                desc => ?DESC("ssl_cacertfile"),
                default => <<"">>
            }
        )},
        {verify, ?HOCON(enum([verify_peer, verify_none]),
            #{
                desc => ?DESC("ssl_verify"),
                default => verify_peer
            }
        )},
        {server_name_indication, ?HOCON(binary(),
            #{
                desc => ?DESC("ssl_server_name_indication"),
                default => <<"">>
            }
        )},
        {versions, ?HOCON(?ARRAY(binary()),
            #{
                desc => ?DESC("ssl_versions"),
                default => [<<"tlsv1.2">>, <<"tlsv1.3">>]
            }
        )},
        {ciphers, ?HOCON(?ARRAY(binary()),
            #{
                desc => ?DESC("ssl_ciphers"),
                default => []
            }
        )},
        {depth, ?HOCON(pos_integer(),
            #{
                desc => ?DESC("ssl_depth"),
                default => 10
            }
        )},
        {verify_hostname, ?HOCON(boolean(),
            #{
                desc => ?DESC("ssl_verify_hostname"),
                default => true
            }
        )},
        {customize_hostname_check, ?HOCON(?ARRAY(map()),
            #{
                desc => ?DESC("ssl_customize_hostname_check"),
                default => []
            }
        )},
        {reuse_sessions, ?HOCON(boolean(),
            #{
                desc => ?DESC("ssl_reuse_sessions"),
                default => true
            }
        )},
        {handshake_timeout, ?HOCON(emqx_schema:timeout_duration_ms(),
            #{
                desc => ?DESC("ssl_handshake_timeout"),
                default => <<"15s">>
            }
        )},
        {partial_chain, ?HOCON(boolean(),
            #{
                desc => ?DESC("ssl_partial_chain"),
                default => false
            }
        )},
        {crl_check, ?HOCON(boolean(),
            #{
                desc => ?DESC("ssl_crl_check"),
                default => false
            }
        )},
        {secure_renegotiate, ?HOCON(boolean(),
            #{
                desc => ?DESC("ssl_secure_renegotiate"),
                default => true
            }
        )},
        {password, ?HOCON(binary(),
            #{
                desc => ?DESC("ssl_password"),
                default => <<"">>
            }
        )}
    ];

fields(topology) ->
    [
        {pool_size, ?HOCON(pos_integer(),
            #{
                desc => ?DESC("pool_size"),
                default => 16
            })},
        {max_overflow, ?HOCON(non_neg_integer(),
            #{
                desc => ?DESC("max_overflow"),
                default => 32
            })},
        {local_threshold_ms, ?HOCON(emqx_schema:timeout_duration_ms(),
            #{
                desc => ?DESC("local_threshold_ms"),
                default => <<"1s">>
            })},
        {connect_timeout_ms, ?HOCON(emqx_schema:timeout_duration_ms(),
            #{
                desc => ?DESC("connect_timeout_ms"),
                default => <<"20s">>
            })},
        {socket_timeout_ms, ?HOCON(emqx_schema:timeout_duration_ms(),
            #{
                desc => ?DESC("socket_timeout_ms"),
                default => <<"5s">>
            })},
        {server_selection_timeout_ms, ?HOCON(emqx_schema:timeout_duration_ms(),
            #{
                desc => ?DESC("server_selection_timeout_ms"),
                default => <<"30s">>
            })},
        {wait_queue_timeout_ms, ?HOCON(emqx_schema:timeout_duration_ms(),
            #{
                desc => ?DESC("wait_queue_timeout_ms"),
                default => <<"1s">>
            })},
        {heartbeat_frequency_ms, ?HOCON(emqx_schema:timeout_duration_ms(),
            #{
                desc => ?DESC("heartbeat_frequency_ms"),
                default => <<"10s">>
            })},
        {min_heartbeat_frequency_ms, ?HOCON(emqx_schema:timeout_duration_ms(),
            #{
                desc => ?DESC("min_heartbeat_frequency_ms"),
                default => <<"1s">>
            })}
    ];

fields(health_check) ->
    [
        {interval, ?HOCON(emqx_schema:timeout_duration_ms(),
            #{
                desc => ?DESC("health_check_interval"),
                default => <<"15s">>
            })},
        {timeout, ?HOCON(emqx_schema:timeout_duration_ms(),
            #{
                desc => ?DESC("health_check_timeout"),
                default => <<"5s">>
            })},
        {retry_count, ?HOCON(pos_integer(),
            #{
                desc => ?DESC("health_check_retry_count"),
                default => 3
            })},
        {cache_ttl, ?HOCON(emqx_schema:timeout_duration_ms(),
            #{
                desc => ?DESC("health_check_cache_ttl"),
                default => <<"10s">>
            })}
    ];

fields(circuit_breaker) ->
    [
        {enable, ?HOCON(boolean(),
            #{
                desc => ?DESC("circuit_breaker_enable"),
                default => true
            })},
        {failure_threshold, ?HOCON(pos_integer(),
            #{
                desc => ?DESC("failure_threshold"),
                default => 5
            })},
        {reset_timeout, ?HOCON(emqx_schema:timeout_duration_ms(),
            #{
                desc => ?DESC("reset_timeout"),
                default => <<"30s">>
            })},
        {half_open_ratio, ?HOCON(float(),
            #{
                desc => ?DESC("half_open_ratio"),
                default => 0.1
            })},
        {recovery_step, ?HOCON(float(),
            #{
                desc => ?DESC("recovery_step"),
                default => 0.1
            })},
        {recovery_interval, ?HOCON(emqx_schema:timeout_duration_ms(),
            #{
                desc => ?DESC("recovery_interval"),
                default => <<"5s">>
            })},
        {dimension_breakers, ?HOCON(?ARRAY(?R_REF(dimension_breaker)),
            #{
                desc => ?DESC("dimension_breakers"),
                default => []
            })},
        {error_thresholds, ?HOCON(?R_REF(error_thresholds),
            #{
                desc => ?DESC("error_thresholds"),
                default => #{}
            })}
    ];

fields(dimension_breaker) ->
    [
        {type, ?HOCON(string(),
            #{
                desc => ?DESC("dimension_type"),
                default => "topic"
            })},
        {pattern, ?HOCON(string(),
            #{
                desc => ?DESC("dimension_pattern"),
                default => ""
            })},
        {failure_threshold, ?HOCON(pos_integer(),
            #{
                desc => ?DESC("dimension_failure_threshold"),
                default => 3
            })},
        {reset_timeout, ?HOCON(emqx_schema:timeout_duration_ms(),
            #{
                desc => ?DESC("dimension_reset_timeout"),
                default => <<"15s">>
            })}
    ];

fields(error_thresholds) ->
    [
        {timeout, ?HOCON(float(),
            #{
                desc => ?DESC("timeout_threshold"),
                default => 0.6
            })},
        {network, ?HOCON(float(),
            #{
                desc => ?DESC("network_threshold"),
                default => 0.7
            })},
        {auth, ?HOCON(float(),
            #{
                desc => ?DESC("auth_threshold"),
                default => 0.9
            })},
        {query, ?HOCON(float(),
            #{
                desc => ?DESC("query_threshold"),
                default => 0.8
            })},
        {default, ?HOCON(float(),
            #{
                desc => ?DESC("default_threshold"),
                default => 0.8
            })}
    ];

fields(batch) ->
    [
        {enable, ?HOCON(boolean(),
            #{
                desc => ?DESC("batch_enable"),
                default => true
            })},
        {min_size, ?HOCON(pos_integer(),
            #{
                desc => ?DESC("batch_min_size"),
                default => 100
            })},
        {max_size, ?HOCON(pos_integer(),
            #{
                desc => ?DESC("batch_max_size"),
                default => 5000
            })},
        {timeout, ?HOCON(emqx_schema:timeout_duration_ms(),
            #{
                desc => ?DESC("batch_timeout"),
                default => <<"100ms">>
            })},
        {max_queue_size, ?HOCON(pos_integer(),
            #{
                desc => ?DESC("batch_max_queue_size"),
                default => 10000
            })},
        {adaptive, ?HOCON(boolean(),
            #{
                desc => ?DESC("batch_adaptive"),
                default => true
            })},
        {adjust_interval, ?HOCON(emqx_schema:timeout_duration_ms(),
            #{
                desc => ?DESC("batch_adjust_interval"),
                default => <<"30s">>
            })},
        {prediction_window, ?HOCON(emqx_schema:timeout_duration_ms(),
            #{
                desc => ?DESC("batch_prediction_window"),
                default => <<"60s">>
            })}
    ];

fields(pipeline) ->
    [
        {enable, ?HOCON(boolean(),
            #{
                desc => ?DESC("pipeline_enable"),
                default => true
            })},
        {parse_workers, ?HOCON(pos_integer(),
            #{
                desc => ?DESC("parse_workers"),
                default => 4
            })},
        {transform_workers, ?HOCON(pos_integer(),
            #{
                desc => ?DESC("transform_workers"),
                default => 4
            })},
        {batch_workers, ?HOCON(pos_integer(),
            #{
                desc => ?DESC("batch_workers"),
                default => 2
            })},
        {send_workers, ?HOCON(pos_integer(),
            #{
                desc => ?DESC("send_workers"),
                default => 4
            })},
        {queue_size, ?HOCON(pos_integer(),
            #{
                desc => ?DESC("pipeline_queue_size"),
                default => 10000
            })},
        {stage_timeout, ?HOCON(emqx_schema:timeout_duration_ms(),
            #{
                desc => ?DESC("stage_timeout"),
                default => <<"5s">>
            })}
    ];

fields(connection_pool) ->
    [
        {enable, ?HOCON(boolean(),
            #{
                desc => ?DESC("connection_pool_enable"),
                default => true
            })},
        {shard_count, ?HOCON(pos_integer(),
            #{
                desc => ?DESC("shard_count"),
                default => 8
            })},
        {preload, ?HOCON(boolean(),
            #{
                desc => ?DESC("preload_enable"),
                default => true
            })},
        {preload_ratio, ?HOCON(float(),
            #{
                desc => ?DESC("preload_ratio"),
                default => 0.5
            })},
        {health_aware_routing, ?HOCON(boolean(),
            #{
                desc => ?DESC("health_aware_routing"),
                default => true
            })}
    ];

fields(cache) ->
    [
        {enable, ?HOCON(boolean(),
            #{
                desc => ?DESC("cache_enable"),
                default => true
            })},
        {topic_match_size, ?HOCON(pos_integer(),
            #{
                desc => ?DESC("topic_match_cache_size"),
                default => 1000
            })},
        {topic_match_ttl, ?HOCON(emqx_schema:timeout_duration_ms(),
            #{
                desc => ?DESC("topic_match_cache_ttl"),
                default => <<"60s">>
            })},
        {health_check_ttl, ?HOCON(emqx_schema:timeout_duration_ms(),
            #{
                desc => ?DESC("health_check_cache_ttl"),
                default => <<"10s">>
            })}
    ];

fields(metrics) ->
    [
        {enable, ?HOCON(boolean(),
            #{
                desc => ?DESC("metrics_enable"),
                default => true
            })},
        {prometheus_export, ?HOCON(boolean(),
            #{
                desc => ?DESC("prometheus_export"),
                default => false
            })},
        {collection_interval, ?HOCON(emqx_schema:timeout_duration_ms(),
            #{
                desc => ?DESC("metrics_collection_interval"),
                default => <<"15s">>
            })}
    ];

fields(error_handler) ->
    [
        {enable, ?HOCON(boolean(),
            #{
                desc => ?DESC("error_handler_enable"),
                default => true
            })},
        {error_classification, ?HOCON(?R_REF(error_classification),
            #{
                desc => ?DESC("error_classification"),
                default => #{}
            })},
        {fault_injection, ?HOCON(?R_REF(fault_injection),
            #{
                desc => ?DESC("fault_injection_config"),
                default => #{}
            })},
        {auto_recovery, ?HOCON(?R_REF(auto_recovery),
            #{
                desc => ?DESC("auto_recovery"),
                default => #{}
            })}
    ];

fields(error_classification) ->
    [
        {enable, ?HOCON(boolean(),
            #{
                desc => ?DESC("error_classification_enable"),
                default => true
            })},
        {isolation_level, ?HOCON(string(),
            #{
                desc => ?DESC("isolation_level"),
                default => "partial"
            })}
    ];

fields(fault_injection) ->
    [
        {enable, ?HOCON(boolean(),
            #{
                desc => ?DESC("fault_injection_enable"),
                default => false
            })},
        {probability, ?HOCON(float(),
            #{
                desc => ?DESC("fault_injection_probability"),
                default => 0.05
            })},
        {interval, ?HOCON(emqx_schema:timeout_duration_ms(),
            #{
                desc => ?DESC("fault_injection_interval"),
                default => <<"60s">>
            })},
        {error_types, ?HOCON(?ARRAY(string()),
            #{
                desc => ?DESC("fault_injection_error_types"),
                default => ["timeout", "network", "query"]
            })}
    ];

fields(auto_recovery) ->
    [
        {enable, ?HOCON(boolean(),
            #{
                desc => ?DESC("auto_recovery_enable"),
                default => true
            })},
        {check_interval, ?HOCON(emqx_schema:timeout_duration_ms(),
            #{
                desc => ?DESC("check_interval"),
                default => <<"5s">>
            })},
        {max_attempts, ?HOCON(pos_integer(),
            #{
                desc => ?DESC("max_attempts"),
                default => 3
            })},
        {strategy, ?HOCON(string(),
            #{
                desc => ?DESC("recovery_strategy"),
                default => "exponential_backoff"
            })}
    ];

fields(resource_manager) ->
    [
        {enable, ?HOCON(boolean(),
            #{
                desc => ?DESC("resource_manager_enable"),
                default => true
            })},
        {monitoring, ?HOCON(?R_REF(resource_monitoring),
            #{
                desc => ?DESC("resource_monitoring"),
                default => #{}
            })},
        {graceful_degradation, ?HOCON(?R_REF(graceful_degradation),
            #{
                desc => ?DESC("graceful_degradation"),
                default => #{}
            })},
        {dynamic_allocation, ?HOCON(?R_REF(dynamic_allocation),
            #{
                desc => ?DESC("dynamic_allocation"),
                default => #{}
            })},
        {limits, ?HOCON(?R_REF(resource_limits),
            #{
                desc => ?DESC("resource_limits"),
                default => #{}
            })}
    ];

fields(resource_monitoring) ->
    [
        {enable, ?HOCON(boolean(),
            #{
                desc => ?DESC("monitoring_enable"),
                default => true
            })},
        {interval, ?HOCON(emqx_schema:timeout_duration_ms(),
            #{
                desc => ?DESC("monitoring_interval"),
                default => <<"5s">>
            })},
        {metrics, ?HOCON(?ARRAY(string()),
            #{
                desc => ?DESC("monitoring_metrics"),
                default => ["memory", "cpu", "connections", "queue"]
            })}
    ];

fields(graceful_degradation) ->
    [
        {enable, ?HOCON(boolean(),
            #{
                desc => ?DESC("graceful_degradation_enable"),
                default => true
            })},
        {thresholds, ?HOCON(?R_REF(degradation_thresholds),
            #{
                desc => ?DESC("degradation_thresholds"),
                default => #{}
            })},
        {actions, ?HOCON(?R_REF(degradation_actions),
            #{
                desc => ?DESC("degradation_actions"),
                default => #{}
            })}
    ];

fields(degradation_thresholds) ->
    [
        {light, ?HOCON(?R_REF(threshold_level),
            #{
                desc => ?DESC("light_threshold"),
                default => #{}
            })},
        {moderate, ?HOCON(?R_REF(threshold_level),
            #{
                desc => ?DESC("moderate_threshold"),
                default => #{}
            })},
        {severe, ?HOCON(?R_REF(threshold_level),
            #{
                desc => ?DESC("severe_threshold"),
                default => #{}
            })},
        {critical, ?HOCON(?R_REF(threshold_level),
            #{
                desc => ?DESC("critical_threshold"),
                default => #{}
            })}
    ];

fields(threshold_level) ->
    [
        {memory, ?HOCON(float(),
            #{
                desc => ?DESC("memory_threshold"),
                default => 0.7
            })},
        {cpu, ?HOCON(float(),
            #{
                desc => ?DESC("cpu_threshold"),
                default => 0.7
            })},
        {connections, ?HOCON(float(),
            #{
                desc => ?DESC("connections_threshold"),
                default => 0.7
            })},
        {queue, ?HOCON(float(),
            #{
                desc => ?DESC("queue_threshold"),
                default => 0.7
            })}
    ];

fields(degradation_actions) ->
    [
        {light, ?HOCON(?ARRAY(?R_REF(action_item)),
            #{
                desc => ?DESC("light_actions"),
                default => []
            })},
        {moderate, ?HOCON(?ARRAY(?R_REF(action_item)),
            #{
                desc => ?DESC("moderate_actions"),
                default => []
            })},
        {severe, ?HOCON(?ARRAY(?R_REF(action_item)),
            #{
                desc => ?DESC("severe_actions"),
                default => []
            })},
        {critical, ?HOCON(?ARRAY(?R_REF(action_item)),
            #{
                desc => ?DESC("critical_actions"),
                default => []
            })}
    ];

fields(action_item) ->
    [
        {action, ?HOCON(string(),
            #{
                desc => ?DESC("action_type"),
                default => ""
            })},
        {value, ?HOCON(hoconsc:union([boolean(), float()]),
            #{
                desc => ?DESC("action_value"),
                default => 0.0
            })}
    ];

fields(dynamic_allocation) ->
    [
        {enable, ?HOCON(boolean(),
            #{
                desc => ?DESC("dynamic_allocation_enable"),
                default => true
            })},
        {adjust_interval, ?HOCON(emqx_schema:timeout_duration_ms(),
            #{
                desc => ?DESC("adjust_interval"),
                default => <<"30s">>
            })},
        {connection_pool, ?HOCON(?R_REF(connection_pool_config),
            #{
                desc => ?DESC("connection_pool_config"),
                default => #{}
            })}
    ];

fields(connection_pool_config) ->
    [
        {min_size, ?HOCON(pos_integer(),
            #{
                desc => ?DESC("min_pool_size"),
                default => 8
            })},
        {max_size, ?HOCON(pos_integer(),
            #{
                desc => ?DESC("max_pool_size"),
                default => 64
            })},
        {step_size, ?HOCON(pos_integer(),
            #{
                desc => ?DESC("step_size"),
                default => 4
            })},
        {usage_threshold, ?HOCON(float(),
            #{
                desc => ?DESC("usage_threshold"),
                default => 0.7
            })}
    ];

fields(resource_limits) ->
    [
        {memory_limit, ?HOCON(float(),
            #{
                desc => ?DESC("memory_limit"),
                default => 0.8
            })},
        {cpu_limit, ?HOCON(float(),
            #{
                desc => ?DESC("cpu_limit"),
                default => 0.9
            })},
        {connection_limit, ?HOCON(pos_integer(),
            #{
                desc => ?DESC("connection_limit"),
                default => 10000
            })},
        {queue_limit, ?HOCON(pos_integer(),
            #{
                desc => ?DESC("queue_limit"),
                default => 100000
            })}
    ];

fields(topic) ->
    [
        {filter, ?HOCON(binary(),
            #{
                desc => ?DESC("topic_filter"),
                default => <<"test/#">>
            })},
        {name, ?HOCON(string(),
            #{
                desc => ?DESC("topic_name"),
                default => "emqx_test"
            })},
        {collection, ?HOCON(binary(),
            #{
                desc => ?DESC("topic_collection"),
                default => <<"mqtt">>
            }
        )}
    ];

fields(backpressure) ->
    [
        {enable, ?HOCON(boolean(),
            #{
                desc => ?DESC("backpressure_enable"),
                default => true
            }
        )},
        {queue_thresholds, ?HOCON(?R_REF(backpressure_queue_thresholds),
            #{
                desc => ?DESC("backpressure_queue_thresholds"),
                default => #{}
            }
        )},
        {time_thresholds, ?HOCON(?R_REF(backpressure_time_thresholds),
            #{
                desc => ?DESC("backpressure_time_thresholds"),
                default => #{}
            }
        )},
        {load_thresholds, ?HOCON(?R_REF(backpressure_load_thresholds),
            #{
                desc => ?DESC("backpressure_load_thresholds"),
                default => #{}
            }
        )},
        {actions, ?HOCON(?R_REF(backpressure_actions),
            #{
                desc => ?DESC("backpressure_actions"),
                default => #{}
            }
        )}
    ];

fields(backpressure_queue_thresholds) ->
    [
        {mild, ?HOCON(non_neg_integer(),
            #{
                desc => ?DESC("backpressure_queue_threshold_mild"),
                default => 1000
            }
        )},
        {moderate, ?HOCON(non_neg_integer(),
            #{
                desc => ?DESC("backpressure_queue_threshold_moderate"),
                default => 3000
            }
        )},
        {high, ?HOCON(non_neg_integer(),
            #{
                desc => ?DESC("backpressure_queue_threshold_high"),
                default => 5000
            }
        )},
        {critical, ?HOCON(non_neg_integer(),
            #{
                desc => ?DESC("backpressure_queue_threshold_critical"),
                default => 8000
            }
        )}
    ];

fields(backpressure_time_thresholds) ->
    [
        {mild, ?HOCON(non_neg_integer(),
            #{
                desc => ?DESC("backpressure_time_threshold_mild"),
                default => 100
            }
        )},
        {moderate, ?HOCON(non_neg_integer(),
            #{
                desc => ?DESC("backpressure_time_threshold_moderate"),
                default => 300
            }
        )},
        {high, ?HOCON(non_neg_integer(),
            #{
                desc => ?DESC("backpressure_time_threshold_high"),
                default => 500
            }
        )},
        {critical, ?HOCON(non_neg_integer(),
            #{
                desc => ?DESC("backpressure_time_threshold_critical"),
                default => 1000
            }
        )}
    ];

fields(backpressure_load_thresholds) ->
    [
        {mild, ?HOCON(float(),
            #{
                desc => ?DESC("backpressure_load_threshold_mild"),
                default => 0.6
            }
        )},
        {moderate, ?HOCON(float(),
            #{
                desc => ?DESC("backpressure_load_threshold_moderate"),
                default => 0.7
            }
        )},
        {high, ?HOCON(float(),
            #{
                desc => ?DESC("backpressure_load_threshold_high"),
                default => 0.8
            }
        )},
        {critical, ?HOCON(float(),
            #{
                desc => ?DESC("backpressure_load_threshold_critical"),
                default => 0.9
            }
        )}
    ];

fields(backpressure_actions) ->
    [
        {mild, ?HOCON(hoconsc:array(map()),
            #{
                desc => ?DESC("backpressure_action_mild"),
                default => [
                    #{throttle_rate => 0.9, batch_size => 0.8}
                ]
            }
        )},
        {moderate, ?HOCON(hoconsc:array(map()),
            #{
                desc => ?DESC("backpressure_action_moderate"),
                default => [
                    #{throttle_rate => 0.7, batch_size => 0.6, reject_low_priority => true}
                ]
            }
        )},
        {high, ?HOCON(hoconsc:array(map()),
            #{
                desc => ?DESC("backpressure_action_high"),
                default => [
                    #{throttle_rate => 0.4, batch_size => 0.3, reject_low_priority => true, reject_medium_priority => true}
                ]
            }
        )},
        {critical, ?HOCON(hoconsc:array(map()),
            #{
                desc => ?DESC("backpressure_action_critical"),
                default => [
                    #{throttle_rate => 0.1, batch_size => 0.1, reject_low_priority => true, reject_medium_priority => true, persist_only => true}
                ]
            }
        )}
    ];

fields(session_persistence) ->
    [
        {enabled, ?HOCON(boolean(),
            #{
                desc => ?DESC("session_persistence_enabled"),
                default => false
            }
        )},
        {auto_restore, ?HOCON(boolean(),
            #{
                desc => ?DESC("session_auto_restore"),
                default => true
            }
        )},
        {restore_on_startup, ?HOCON(boolean(),
            #{
                desc => ?DESC("session_restore_on_startup"),
                default => true
            }
        )},
        {restore_delay, ?HOCON(union([string(), emqx_schema:timeout_duration_ms()]),
            #{
                desc => ?DESC("session_restore_delay"),
                default => <<"5s">>
            }
        )},
        {max_concurrent_restores, ?HOCON(pos_integer(),
            #{
                desc => ?DESC("session_max_concurrent_restores"),
                default => 10
            }
        )},
        {session_expiry, ?HOCON(union([string(), emqx_schema:timeout_duration_ms()]),
            #{
                desc => ?DESC("session_expiry_interval"),
                default => <<"2h">>
            }
        )},
        {cleanup_interval, ?HOCON(emqx_schema:timeout_duration_ms(),
            #{
                desc => ?DESC("session_cleanup_interval"),
                default => <<"5m">>
            }
        )},
        {cleanup_batch_size, ?HOCON(pos_integer(),
            #{
                desc => ?DESC("session_cleanup_batch_size"),
                default => 100
            }
        )},
        {restore_batch_size, ?HOCON(pos_integer(),
            #{
                desc => ?DESC("session_restore_batch_size"),
                default => 500
            }
        )},
        {crash_recovery, ?HOCON(boolean(),
            #{
                desc => ?DESC("session_crash_recovery"),
                default => true
            }
        )},
        {crash_recovery_dir, ?HOCON(string(),
            #{
                desc => ?DESC("session_crash_recovery_dir"),
                default => "data"
            }
        )},
        {force_restore_on_crash, ?HOCON(boolean(),
            #{
                desc => ?DESC("session_force_restore_on_crash"),
                default => true
            }
        )},
        {collection, ?HOCON(binary(),
            #{
                desc => ?DESC("session_collection"),
                default => ?DEFAULT_SESSION_COLLECTION
            }
        )}
    ];

fields(subscription_persistence) ->
    [
        {enabled, ?HOCON(boolean(),
            #{
                desc => ?DESC("subscription_persistence_enabled"),
                default => false
            }
        )},
        {restore_on_startup, ?HOCON(boolean(),
            #{
                desc => ?DESC("subscription_restore_on_startup"),
                default => true
            }
        )},
        {auto_restore, ?HOCON(boolean(),
            #{
                desc => ?DESC("subscription_auto_restore"),
                default => true
            }
        )},
        {subscription_expiry, ?HOCON(union([string(), emqx_schema:timeout_duration_ms()]),
            #{
                desc => ?DESC("subscription_expiry_interval"),
                default => <<"2d">>
            }
        )},
        {cleanup_interval, ?HOCON(emqx_schema:timeout_duration_ms(),
            #{
                desc => ?DESC("subscription_cleanup_interval"),
                default => <<"30m">>
            }
        )},
        {cleanup_batch_size, ?HOCON(pos_integer(),
            #{
                desc => ?DESC("subscription_cleanup_batch_size"),
                default => 200
            }
        )},
        {save_shared_subscriptions, ?HOCON(boolean(),
            #{
                desc => ?DESC("subscription_save_shared"),
                default => false
            }
        )},
        {max_subscriptions_per_client, ?HOCON(pos_integer(),
            #{
                desc => ?DESC("subscription_max_per_client"),
                default => 100
            }
        )},
        {collection, ?HOCON(binary(),
            #{
                desc => ?DESC("subscription_collection"),
                default => ?DEFAULT_SUBSCRIPTION_COLLECTION
            }
        )},
        {shared_subscription, ?HOCON(?R_REF(shared_subscription),
            #{
                desc => ?DESC("shared_subscription"),
                default => #{}
            }
        )}
    ];

fields(shared_subscription) ->
    [
        {strategy, ?HOCON(enum([random, round_robin, hash, sticky]),
            #{
                desc => ?DESC("shared_subscription_strategy"),
                default => random
            }
        )},
        {hash_key, ?HOCON(enum([clientid, username, topic]),
            #{
                desc => ?DESC("shared_subscription_hash_key"),
                default => clientid
            }
        )},
        {priority, ?HOCON(pos_integer(),
            #{
                desc => ?DESC("shared_subscription_priority"),
                default => 10
            }
        )},
        {persistent, ?HOCON(boolean(),
            #{
                desc => ?DESC("shared_subscription_persistent"),
                default => true
            }
        )},
        {save_messages_for_offline, ?HOCON(boolean(),
            #{
                desc => ?DESC("shared_subscription_save_messages_for_offline"),
                default => true
            }
        )},
        {expiry, ?HOCON(union([string(), emqx_schema:timeout_duration_ms()]),
            #{
                desc => ?DESC("shared_subscription_expiry"),
                default => <<"24h">>
            }
        )}
    ];

fields(message_persistence) ->
    [
        {enabled, ?HOCON(boolean(),
            #{
                desc => ?DESC("message_persistence_enabled"),
                default => false
            }
        )},
        {retain_policy, ?HOCON(?ARRAY(string()),
            #{
                desc => ?DESC("message_retain_policy"),
                default => ["qos1", "qos2"]
            }
        )},
        {message_expiry, ?HOCON(union([string(), emqx_schema:timeout_duration_ms()]),
            #{
                desc => ?DESC("message_expiry_interval"),
                default => <<"4h">>
            }
        )},
        {cleanup_interval, ?HOCON(emqx_schema:timeout_duration_ms(),
            #{
                desc => ?DESC("message_cleanup_interval"),
                default => <<"15m">>
            }
        )},
        {cleanup_batch_size, ?HOCON(pos_integer(),
            #{
                desc => ?DESC("message_cleanup_batch_size"),
                default => 500
            }
        )},
        {restore_batch_size, ?HOCON(pos_integer(),
            #{
                desc => ?DESC("message_restore_batch_size"),
                default => 500
            }
        )},
        {restore_max_batches, ?HOCON(pos_integer(),
            #{
                desc => ?DESC("message_restore_max_batches"),
                default => 100
            }
        )},
        {max_messages_per_client, ?HOCON(pos_integer(),
            #{
                desc => ?DESC("message_max_per_client"),
                default => 1000
            }
        )},
        {store_message_properties, ?HOCON(boolean(),
            #{
                desc => ?DESC("store_message_properties"),
                default => true
            }
        )},
        {compress_payload, ?HOCON(boolean(),
            #{
                desc => ?DESC("compress_payload"),
                default => false
            }
        )},
        {large_message_threshold, ?HOCON(emqx_schema:bytesize(),
            #{
                desc => ?DESC("large_message_threshold"),
                default => <<"64KB">>
            }
        )},
        {store_retained_messages, ?HOCON(boolean(),
            #{
                desc => ?DESC("store_retained_messages"),
                default => false
            }
        )},
        {collection, ?HOCON(binary(),
            #{
                desc => ?DESC("message_collection"),
                default => ?DEFAULT_MESSAGE_COLLECTION
            }
        )}
    ];

fields(will_persistence) ->
    [
        {enabled, ?HOCON(boolean(),
            #{
                desc => ?DESC("will_persistence_enabled"),
                default => false
            }
        )},
        {will_expiry, ?HOCON(union([string(), emqx_schema:timeout_duration_ms()]),
            #{
                desc => ?DESC("will_expiry_interval"),
                default => <<"7d">>
            }
        )},
        {cleanup_interval, ?HOCON(emqx_schema:timeout_duration_ms(),
            #{
                desc => ?DESC("will_cleanup_interval"),
                default => <<"1d">>
            }
        )},
        {cleanup_batch_size, ?HOCON(pos_integer(),
            #{
                desc => ?DESC("will_cleanup_batch_size"),
                default => 100
            }
        )},
        {store_message_properties, ?HOCON(boolean(),
            #{
                desc => ?DESC("store_will_properties"),
                default => true
            }
        )},
        {compress_payload, ?HOCON(boolean(),
            #{
                desc => ?DESC("will_compress_payload"),
                default => false
            }
        )},
        {large_message_threshold, ?HOCON(emqx_schema:bytesize(),
            #{
                desc => ?DESC("large_will_threshold"),
                default => <<"64KB">>
            }
        )},
        {collection, ?HOCON(binary(),
            #{
                desc => ?DESC("will_collection"),
                default => ?DEFAULT_WILL_MESSAGE_COLLECTION
            }
        )}
    ];

fields(retained_persistence) ->
    [
        {enabled, ?HOCON(boolean(),
            #{
                desc => ?DESC("retained_persistence_enabled"),
                default => false
            }
        )},
        {retained_expiry, ?HOCON(union([string(), emqx_schema:timeout_duration_ms()]),
            #{
                desc => ?DESC("retained_expiry_interval"),
                default => <<"7d">>
            }
        )},
        {cleanup_interval, ?HOCON(emqx_schema:timeout_duration_ms(),
            #{
                desc => ?DESC("retained_cleanup_interval"),
                default => <<"1h">>
            }
        )},
        {cleanup_batch_size, ?HOCON(pos_integer(),
            #{
                desc => ?DESC("retained_cleanup_batch_size"),
                default => 1000
            }
        )},
        {max_retained_messages, ?HOCON(pos_integer(),
            #{
                desc => ?DESC("max_retained_messages"),
                default => 100000
            }
        )},
        {store_message_properties, ?HOCON(boolean(),
            #{
                desc => ?DESC("store_retained_properties"),
                default => true
            }
        )},
        {compress_payload, ?HOCON(boolean(),
            #{
                desc => ?DESC("retained_compress_payload"),
                default => false
            }
        )},
        {large_message_threshold, ?HOCON(emqx_schema:bytesize(),
            #{
                desc => ?DESC("large_retained_threshold"),
                default => <<"64KB">>
            }
        )},
        {topic_filters, ?HOCON(?ARRAY(binary()),
            #{
                desc => ?DESC("retained_topic_filters"),
                default => []
            }
        )},
        {enable_topic_filters, ?HOCON(boolean(),
            #{
                desc => ?DESC("enable_retained_topic_filters"),
                default => false
            }
        )},
        {delivery, ?HOCON(?R_REF(retained_delivery),
            #{
                desc => ?DESC("retained_delivery"),
                default => #{}
            }
        )},
        {collection, ?HOCON(binary(),
            #{
                desc => ?DESC("retained_collection"),
                default => ?DEFAULT_RETAINED_MESSAGE_COLLECTION
            }
        )},
        {indexes, ?HOCON(?R_REF(retained_indexes),
            #{
                desc => ?DESC("retained_indexes"),
                default => #{}
            }
        )}
    ];

fields(retained_delivery) ->
    [
        {send_on_subscribe, ?HOCON(boolean(),
            #{
                desc => ?DESC("retained_send_on_subscribe"),
                default => true
            }
        )},
        {max_concurrent_sends, ?HOCON(pos_integer(),
            #{
                desc => ?DESC("retained_max_concurrent_sends"),
                default => 10
            }
        )},
        {send_timeout, ?HOCON(emqx_schema:timeout_duration_ms(),
            #{
                desc => ?DESC("retained_send_timeout"),
                default => <<"5s">>
            }
        )},
        {respect_qos, ?HOCON(boolean(),
            #{
                desc => ?DESC("retained_respect_qos"),
                default => true
            }
        )}
    ];

fields(retained_indexes) ->
    [
        {auto_create, ?HOCON(boolean(),
            #{
                desc => ?DESC("retained_indexes_auto_create"),
                default => true
            }
        )},
        {ttl_index, ?HOCON(?R_REF(retained_ttl_index),
            #{
                desc => ?DESC("retained_ttl_index"),
                default => #{}
            }
        )},
        {topic_index, ?HOCON(?R_REF(retained_topic_index),
            #{
                desc => ?DESC("retained_topic_index"),
                default => #{}
            }
        )},
        {compound_indexes, ?HOCON(?ARRAY(?R_REF(compound_index)),
            #{
                desc => ?DESC("retained_compound_indexes"),
                default => []
            }
        )}
    ];

fields(retained_ttl_index) ->
    [
        {enabled, ?HOCON(boolean(),
            #{
                desc => ?DESC("retained_ttl_index_enabled"),
                default => true
            }
        )},
        {field, ?HOCON(binary(),
            #{
                desc => ?DESC("retained_ttl_index_field"),
                default => <<"expires_at">>
            }
        )}
    ];

fields(retained_topic_index) ->
    [
        {enabled, ?HOCON(boolean(),
            #{
                desc => ?DESC("retained_topic_index_enabled"),
                default => true
            }
        )},
        {unique, ?HOCON(boolean(),
            #{
                desc => ?DESC("retained_topic_index_unique"),
                default => true
            }
        )}
    ];

fields(compound_index) ->
    [
        {fields, ?HOCON(?ARRAY(binary()),
            #{
                desc => ?DESC("compound_index_fields"),
                required => true
            }
        )},
        {name, ?HOCON(binary(),
            #{
                desc => ?DESC("compound_index_name"),
                required => true
            }
        )},
        {unique, ?HOCON(boolean(),
            #{
                desc => ?DESC("compound_index_unique"),
                default => false
            }
        )},
        {sparse, ?HOCON(boolean(),
            #{
                desc => ?DESC("compound_index_sparse"),
                default => false
            }
        )},
        {background, ?HOCON(boolean(),
            #{
                desc => ?DESC("compound_index_background"),
                default => true
            }
        )}
    ].

bootstrap_hosts() ->
    Meta = #{desc => ?DESC("bootstrap_hosts")},
    emqx_schema:servers_sc(Meta, #{default_port => 27017}).

desc("backpressure") -> "背压机制配置";
desc("backpressure_enable") -> "是否启用背压机制";
desc("backpressure_queue_thresholds") -> "队列长度阈值配置";
desc("backpressure_queue_threshold_mild") -> "轻度背压队列长度阈值";
desc("backpressure_queue_threshold_moderate") -> "中度背压队列长度阈值";
desc("backpressure_queue_threshold_high") -> "高度背压队列长度阈值";
desc("backpressure_queue_threshold_critical") -> "临界背压队列长度阈值";
desc("backpressure_time_thresholds") -> "处理时间阈值配置";
desc("backpressure_time_threshold_mild") -> "轻度背压处理时间阈值(毫秒)";
desc("backpressure_time_threshold_moderate") -> "中度背压处理时间阈值(毫秒)";
desc("backpressure_time_threshold_high") -> "高度背压处理时间阈值(毫秒)";
desc("backpressure_time_threshold_critical") -> "临界背压处理时间阈值(毫秒)";
desc("backpressure_load_thresholds") -> "系统负载阈值配置";
desc("backpressure_load_threshold_mild") -> "轻度背压系统负载阈值(0.0-1.0)";
desc("backpressure_load_threshold_moderate") -> "中度背压系统负载阈值(0.0-1.0)";
desc("backpressure_load_threshold_high") -> "高度背压系统负载阈值(0.0-1.0)";
desc("backpressure_load_threshold_critical") -> "临界背压系统负载阈值(0.0-1.0)";
desc("backpressure_actions") -> "背压动作配置";
desc("backpressure_action_mild") -> "轻度背压动作";
desc("backpressure_action_moderate") -> "中度背压动作";
desc("backpressure_action_high") -> "高度背压动作";
desc("backpressure_action_critical") -> "临界背压动作";
desc("session_persistence") -> "会话持久化配置";
desc("session_persistence_enabled") -> "是否启用会话持久化";
desc("session_auto_restore") -> "断开连接时是否自动恢复会话";
desc("session_restore_on_startup") -> "启动时是否恢复所有会话";
desc("session_restore_delay") -> "启动恢复延迟时间";
desc("session_max_concurrent_restores") -> "最大并发恢复会话数";
desc("session_expiry_interval") -> "会话过期时间";
desc("session_cleanup_interval") -> "定期清理过期数据间隔";
desc("session_cleanup_batch_size") -> "清理批次大小";
desc("session_restore_batch_size") -> "EMQX重启后从MongoDB恢复会话时的批次大小";
desc("session_crash_recovery") -> "断电恢复功能";
desc("session_crash_recovery_dir") -> "断电恢复数据目录";
desc("session_force_restore_on_crash") -> "崩溃后强制恢复";
desc("session_collection") -> "MongoDB集合配置";
desc("subscription_persistence") -> "订阅持久化配置";
desc("subscription_persistence_enabled") -> "是否启用订阅持久化";
desc("subscription_restore_on_startup") -> "启动时是否恢复所有订阅";
desc("subscription_auto_restore") -> "断开连接时是否自动恢复订阅";
desc("subscription_expiry_interval") -> "订阅过期时间";
desc("subscription_cleanup_interval") -> "定期清理过期数据间隔";
desc("subscription_cleanup_batch_size") -> "清理批次大小";
desc("subscription_save_shared") -> "是否保存共享订阅";
desc("subscription_max_per_client") -> "每个客户端的最大订阅数";
desc("subscription_collection") -> "MongoDB集合配置";
desc("message_persistence") -> "消息持久化配置";
desc("message_persistence_enabled") -> "是否启用消息持久化";
desc("message_retain_policy") -> "消息保留策略";
desc("message_expiry_interval") -> "消息过期时间";
desc("message_cleanup_interval") -> "定期清理过期消息间隔";
desc("message_cleanup_batch_size") -> "清理批次大小";
desc("message_restore_batch_size") -> "EMQX重启后从MongoDB恢复消息时的批次大小";
desc("message_restore_max_batches") -> "最大恢复批次数，防止内存溢出";
desc("message_max_per_client") -> "每个客户端的最大消息数";
desc("store_message_properties") -> "是否存储消息属性";
desc("compress_payload") -> "是否压缩消息内容";
desc("large_message_threshold") -> "大消息阈值";
desc("store_retained_messages") -> "是否存储保留消息";
desc("message_collection") -> "MongoDB集合配置";
desc("shared_subscription") -> "共享订阅配置";
desc("shared_subscription_strategy") -> "共享订阅分发策略";
desc("shared_subscription_hash_key") -> "共享订阅哈希键（当策略为hash时使用）";
desc("shared_subscription_priority") -> "共享订阅会话恢复优先级";
desc("shared_subscription_persistent") -> "是否持久化共享订阅";
desc("shared_subscription_save_messages_for_offline") -> "所有成员不在线时是否保存为离线消息";
desc("shared_subscription_expiry") -> "共享订阅过期时间";
desc("will_persistence") -> "遗嘱消息持久化配置";
desc("will_persistence_enabled") -> "是否启用遗嘱消息持久化";
desc("will_expiry_interval") -> "遗嘱消息过期时间";
desc("will_cleanup_interval") -> "定期清理过期遗嘱消息间隔";
desc("will_cleanup_batch_size") -> "清理批次大小";
desc("store_will_properties") -> "是否存储遗嘱消息属性";
desc("will_compress_payload") -> "是否压缩遗嘱消息内容";
desc("large_will_threshold") -> "大遗嘱消息阈值";
desc("will_collection") -> "遗嘱消息MongoDB集合";

%% 连接配置描述
desc("auth_source") -> "认证数据库名称，默认为admin";
desc("read_preference") -> "读偏好设置：primary(主节点), primaryPreferred(优先主节点), secondary(从节点), secondaryPreferred(优先从节点), nearest(最近节点)";
desc("read_concern_level") -> "读关注级别：local(本地), available(可用), majority(多数), linearizable(线性化), snapshot(快照)";
desc("write_concern") -> "写关注级别：0(无确认), 1(单节点确认), majority(多数确认), 或自定义标签";
desc("write_concern_timeout") -> "写关注超时时间(毫秒)";

%% SSL配置描述
desc("ssl_enable") -> "是否启用SSL连接";
desc("ssl_certfile") -> "SSL客户端证书文件路径(PEM格式)";
desc("ssl_keyfile") -> "SSL客户端私钥文件路径(PEM格式)";
desc("ssl_cacertfile") -> "SSL CA证书文件路径(PEM格式)";
desc("ssl_verify") -> "SSL证书验证模式：verify_peer(验证对等证书), verify_none(不验证证书)";
desc("ssl_server_name_indication") -> "SSL服务器名称指示(SNI)，用于虚拟主机";
desc("ssl_versions") -> "支持的SSL/TLS协议版本列表";
desc("ssl_ciphers") -> "SSL加密套件列表，空列表表示使用系统默认";
desc("ssl_depth") -> "SSL证书链验证深度";
desc("ssl_verify_hostname") -> "是否验证服务器证书中的主机名";
desc("ssl_customize_hostname_check") -> "自定义主机名验证选项";
desc("ssl_reuse_sessions") -> "是否重用SSL会话以提高性能";
desc("ssl_handshake_timeout") -> "SSL握手超时时间";
desc("ssl_partial_chain") -> "是否允许部分证书链验证";
desc("ssl_crl_check") -> "是否检查证书撤销列表(CRL)";
desc("ssl_secure_renegotiate") -> "是否启用安全重新协商";
desc("ssl_password") -> "SSL私钥文件密码";

%% 保留消息持久化配置描述
desc("retained_persistence") -> "保留消息持久化配置";
desc("retained_persistence_enabled") -> "是否启用保留消息持久化";
desc("retained_expiry_interval") -> "保留消息过期时间";
desc("retained_cleanup_interval") -> "保留消息清理间隔";
desc("retained_cleanup_batch_size") -> "保留消息清理批次大小";
desc("max_retained_messages") -> "最大保留消息数量";
desc("store_retained_properties") -> "是否存储保留消息属性";
desc("retained_compress_payload") -> "是否压缩保留消息载荷";
desc("large_retained_threshold") -> "大保留消息阈值";
desc("retained_topic_filters") -> "保留消息主题过滤器列表";
desc("enable_retained_topic_filters") -> "是否启用保留消息主题过滤器";
desc("retained_delivery") -> "保留消息发送配置";
desc("retained_send_on_subscribe") -> "订阅时是否立即发送保留消息";
desc("retained_max_concurrent_sends") -> "保留消息发送最大并发数";
desc("retained_send_timeout") -> "保留消息发送超时时间";
desc("retained_respect_qos") -> "是否按QoS级别发送保留消息";
desc("retained_collection") -> "保留消息MongoDB集合";
desc("retained_indexes") -> "保留消息索引配置";
desc("retained_indexes_auto_create") -> "是否自动创建保留消息索引";
desc("retained_ttl_index") -> "保留消息TTL索引配置";
desc("retained_ttl_index_enabled") -> "是否启用保留消息TTL索引";
desc("retained_ttl_index_field") -> "保留消息TTL索引字段";
desc("retained_topic_index") -> "保留消息主题索引配置";
desc("retained_topic_index_enabled") -> "是否启用保留消息主题索引";
desc("retained_topic_index_unique") -> "保留消息主题索引是否唯一";
desc("retained_compound_indexes") -> "保留消息复合索引配置列表";
desc("compound_index_fields") -> "复合索引字段列表";
desc("compound_index_name") -> "复合索引名称";
desc("compound_index_unique") -> "复合索引是否唯一";
desc("compound_index_sparse") -> "复合索引是否稀疏";
desc("compound_index_background") -> "复合索引是否后台创建";

desc(_) ->
    undefined.
