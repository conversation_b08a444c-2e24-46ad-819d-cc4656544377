%%%-------------------------------------------------------------------
%%% @doc
%%% MongoDB插件的自适应批处理模块
%%% 实现多维度自适应批处理算法
%%%
%%% 功能说明：
%%% 1. 根据消息频率、大小、网络延迟等因素动态调整批处理参数
%%% 2. 支持主题级别的批处理配置和优先级管理
%%% 3. 提供实时性能监控和历史数据分析
%%% 4. 使用机器学习算法预测最优批处理大小和超时时间
%%%
%%% 架构设计：
%%% - 使用gen_server行为模式实现状态管理
%%% - 使用ETS表存储配置、指标和历史数据
%%% - 定时器驱动的预测和指标收集机制
%%% @end
%%%-------------------------------------------------------------------
-module(emqx_plugin_mongodb_adaptive_batch).

-behaviour(gen_server).

%% ============================================================================
%% API 导出函数 - 对外提供的接口函数
%% ============================================================================
-export([
    start_link/0,           % 启动自适应批处理服务器
    add_message/3,          % 添加消息到批处理系统
    get_batch_params/1,     % 获取特定主题的批处理参数
    adjust_batch_size/1,    % 调整批处理大小
    adjust_batch_timeout/1, % 调整批处理超时
    stop/0,                 % 停止服务器
    set_batch_size/1,       % 设置批处理大小
    get_queue_length/0,     % 获取当前队列长度
    integrate/0             % 集成到协调器
]).

%% ============================================================================
%% gen_server 回调函数导出 - OTP行为模式必需的回调函数
%% ============================================================================
-export([
    init/1,          % 初始化回调
    handle_call/3,   % 处理同步调用
    handle_cast/2,   % 处理异步调用
    handle_info/2,   % 处理消息
    terminate/2,     % 终止回调
    code_change/3    % 代码更新回调
]).

%% ============================================================================
%% 内部导出函数 - 供其他模块调用的内部函数
%% ============================================================================
-export([
    predict_batch_size/1,   % 预测批处理大小
    predict_batch_timeout/1,% 预测批处理超时
    update_metrics/3        % 更新指标
]).

-include("emqx_plugin_mongodb.hrl").

%% ============================================================================
%% 记录定义 - 定义模块内部使用的数据结构
%% ============================================================================
%% @doc 批处理状态记录
%% 用于存储自适应批处理模块的运行时状态信息
-record(batch_state, {
    % 性能指标映射表，包含各种统计数据
    metrics = #{
        message_count => 0,     % 消息总数
        message_size => 0,      % 消息总大小（字节）
        batch_count => 0,       % 批处理总数
        batch_success => 0,     % 成功的批处理数
        batch_error => 0,       % 失败的批处理数
        avg_latency => 0,       % 平均延迟（毫秒）
        cpu_usage => 0,         % CPU使用率（百分比）
        network_latency => 0    % 网络延迟（毫秒）
    },
    history = [],               % 历史数据列表（用于趋势分析）
    topic_patterns = [],        % 主题模式列表（用于主题匹配）
    prediction_timer,           % 预测定时器引用
    metrics_timer              % 指标收集定时器引用
}).

%%%===================================================================
%%% API - 应用程序编程接口
%%%===================================================================

%% @doc 启动自适应批处理服务器
%% @spec start_link() -> {ok, Pid} | {error, Reason}
%%
%% 功能说明：
%% - 启动一个本地注册的gen_server进程
%% - 进程名称为当前模块名(?MODULE)
%% - 使用空参数列表初始化
%%
%% 返回值：
%% - {ok, Pid}: 成功启动，返回进程ID
%% - {error, Reason}: 启动失败，返回错误原因
start_link() ->
    gen_server:start_link({local, ?MODULE}, ?MODULE, [], []).

%% @doc 添加消息到批处理系统
%% @spec add_message(Topic, Size, Priority) -> ok
%%
%% 参数说明：
%% - Topic: 消息主题（binary类型）
%% - Size: 消息大小（integer类型，单位：字节）
%% - Priority: 消息优先级（high | normal | low）
%%
%% 功能说明：
%% - 异步发送消息到批处理服务器
%% - 用于统计消息频率和大小，优化批处理参数
%% - 不阻塞调用进程
add_message(Topic, Size, Priority) ->
    gen_server:cast(?MODULE, {add_message, Topic, Size, Priority}).

%% @doc 获取特定主题的批处理参数
%% @spec get_batch_params(Topic) -> {BatchSize, BatchTimeout}
%%
%% 参数说明：
%% - Topic: 消息主题（binary类型）
%%
%% 返回值：
%% - {BatchSize, BatchTimeout}: 批处理大小和超时时间
%%
%% 功能说明：
%% - 首先查找主题的精确匹配配置
%% - 如果没有找到，尝试匹配主题模式（支持通配符#和+）
%% - 最后使用默认配置
get_batch_params(Topic) ->
    % 步骤1：尝试从ETS表获取特定主题的批处理配置
    case ets:lookup(?BATCH_CONFIG_TAB, Topic) of
        [{_, BatchSize, BatchTimeout, _Priority}] ->
            % 找到精确匹配的配置
            {BatchSize, BatchTimeout};
        [] ->
            % 步骤2：尝试匹配主题模式（如sensor/+/temperature, device/#等）
            case find_matching_topic_pattern(Topic) of
                {ok, Pattern} ->
                    % 找到匹配的模式，获取其配置
                    case ets:lookup(?BATCH_CONFIG_TAB, Pattern) of
                        [{_, BatchSize, BatchTimeout, _Priority}] ->
                            {BatchSize, BatchTimeout};
                        [] ->
                            % 模式存在但配置缺失，使用默认值
                            {?DEFAULT_BATCH_SIZE, ?DEFAULT_BATCH_TIMEOUT}
                    end;
                not_found ->
                    % 步骤3：没有匹配的模式，使用默认值
                    {?DEFAULT_BATCH_SIZE, ?DEFAULT_BATCH_TIMEOUT}
            end
    end.

%% @doc 调整批处理大小
%% @spec adjust_batch_size(Ratio) -> ok
%%
%% 参数说明：
%% - Ratio: 调整比例（float类型，0.1-2.0之间）
%%
%% 功能说明：
%% - 按比例调整所有主题的批处理大小
%% - 自动限制在最小和最大值范围内
%% - 用于系统负载变化时的动态调整
adjust_batch_size(Ratio) ->
    % 步骤1：验证比例在有效范围内（0.1到2.0倍）
    ValidRatio = max(0.1, min(2.0, Ratio)),

    % 步骤2：获取所有主题配置
    AllConfigs = ets:tab2list(?BATCH_CONFIG_TAB),

    % 步骤3：更新每个主题的批处理大小
    lists:foreach(
        fun({Topic, BatchSize, BatchTimeout, Priority}) ->
            % 计算新的批处理大小，确保在允许范围内
            NewBatchSize = max(?MIN_BATCH_SIZE, min(?MAX_BATCH_SIZE, round(BatchSize * ValidRatio))),
            % 更新ETS表中的配置
            ets:insert(?BATCH_CONFIG_TAB, {Topic, NewBatchSize, BatchTimeout, Priority})
        end,
        AllConfigs
    ),

    ok.

%% @doc 调整批处理超时
%% @spec adjust_batch_timeout(Ratio) -> ok
%%
%% 参数说明：
%% - Ratio: 调整比例（float类型，0.5-5.0之间）
%%
%% 功能说明：
%% - 按比例调整所有主题的批处理超时时间
%% - 自动限制在最小和最大值范围内
%% - 用于网络延迟变化时的动态调整
adjust_batch_timeout(Ratio) ->
    % 步骤1：验证比例在有效范围内（0.5到5.0倍）
    ValidRatio = max(0.5, min(5.0, Ratio)),

    % 步骤2：获取所有主题配置
    AllConfigs = ets:tab2list(?BATCH_CONFIG_TAB),

    % 步骤3：更新每个主题的批处理超时
    lists:foreach(
        fun({Topic, BatchSize, BatchTimeout, Priority}) ->
            % 计算新的批处理超时，确保在允许范围内
            NewBatchTimeout = max(?MIN_BATCH_TIMEOUT, min(?MAX_BATCH_TIMEOUT, round(BatchTimeout * ValidRatio))),
            % 更新ETS表中的配置
            ets:insert(?BATCH_CONFIG_TAB, {Topic, BatchSize, NewBatchTimeout, Priority})
        end,
        AllConfigs
    ),

    ok.

%% @doc 停止自适应批处理服务器
%% @spec stop() -> ok
%%
%% 功能说明：
%% - 同步调用停止服务器
%% - 会触发terminate/2回调函数
%% - 清理所有ETS表和定时器
stop() ->
    gen_server:call(?MODULE, stop).

%% @doc 集成到协调器
%% @spec integrate() -> ok
%%
%% 功能说明：
%% - 将当前模块注册到MongoDB插件协调器
%% - 设置模块优先级和功能描述
%% - 支持动态模块发现和管理
integrate() ->
    ?SLOG(info, #{msg => "integrating_adaptive_batch_module"}),
    % 检查协调器模块是否存在并导出了register_module/2函数
    case erlang:function_exported(emqx_plugin_mongodb_coordinator, register_module, 2) of
        true ->
            % 注册当前模块到协调器，提供模块元信息
            emqx_plugin_mongodb_coordinator:register_module(?MODULE, #{
                priority => high,  % 高优先级模块
                description => <<"Adaptive batch processing module">>,  % 模块描述
                features => [adaptive_batching, batch_size_prediction, topic_priority]  % 支持的功能
            });
        false ->
            % 协调器不可用，静默忽略
            ok
    end.

%% @doc 获取当前队列长度
%% @spec get_queue_length() -> integer()
%%
%% 返回值：
%% - integer(): 消息队列中的消息数量
%%
%% 功能说明：
%% - 获取gen_server进程的消息队列长度
%% - 用于监控系统负载和性能
get_queue_length() ->
    % 查找进程是否存在
    case erlang:whereis(?MODULE) of
        Pid when is_pid(Pid) ->
            % 获取进程信息中的消息队列长度
            {_, Len} = erlang:process_info(Pid, message_queue_len),
            Len;
        _ ->
            % 进程不存在，返回0
            0
    end.

%% @doc 设置批处理大小
%% @spec set_batch_size(Size) -> ok | {error, not_running}
%%
%% 参数说明：
%% - Size: 新的批处理大小（integer类型）
%%
%% 功能说明：
%% - 动态设置批处理大小
%% - 仅在服务器运行时有效
set_batch_size(Size) when is_integer(Size) ->
    % 检查服务器进程是否运行
    case erlang:whereis(?MODULE) of
        Pid when is_pid(Pid) ->
            % 异步发送设置消息
            gen_server:cast(Pid, {set_batch_size, Size});
        _ ->
            % 服务器未运行
            {error, not_running}
    end.

%%%===================================================================
%%% gen_server callbacks - OTP行为模式回调函数
%%%===================================================================

%% @doc 初始化回调函数
%% @spec init([]) -> {ok, State}
%%
%% 功能说明：
%% - 创建所需的ETS表用于存储配置和指标
%% - 初始化默认配置
%% - 启动定时器进行预测和指标收集
%% - 返回初始状态
init([]) ->
    % 步骤1：创建ETS表用于数据存储
    % 批处理指标表：存储每个主题的统计信息
    ets:new(?BATCH_METRICS_TAB, [named_table, public, {write_concurrency, true}]),
    % 批处理配置表：存储每个主题的批处理参数
    ets:new(?BATCH_CONFIG_TAB, [named_table, public, {read_concurrency, true}]),
    % 批处理历史表：存储历史指标数据，用于趋势分析
    ets:new(?BATCH_HISTORY_TAB, [named_table, public, ordered_set, {write_concurrency, true}]),
    % 主题优先级表：存储主题的优先级信息
    ets:new(?TOPIC_PRIORITY_TAB, [named_table, public, {read_concurrency, true}]),

    % 步骤2：初始化默认配置（为不同优先级设置默认参数）
    init_default_config(),

    % 步骤3：启动预测计时器（定期预测最优批处理参数）
    PredictionTimer = erlang:send_after(?PREDICTION_INTERVAL, self(), predict_batch_params),

    % 步骤4：启动指标收集计时器（每秒收集系统指标）
    MetricsTimer = erlang:send_after(1000, self(), collect_metrics),

    % 步骤5：初始化状态记录
    State = #batch_state{
        prediction_timer = PredictionTimer,  % 保存预测定时器引用
        metrics_timer = MetricsTimer         % 保存指标收集定时器引用
    },

    {ok, State}.

%% @doc 处理同步调用
%% @spec handle_call(Request, From, State) -> {reply, Reply, NewState} | {stop, Reason, Reply, NewState}
%%
%% 功能说明：
%% - 处理gen_server:call/2发送的同步消息
%% - 支持停止服务器和获取批处理参数的请求
handle_call(stop, _From, State) ->
    % 停止服务器请求，正常终止
    {stop, normal, ok, State};

handle_call({get_batch_params, Topic}, _From, State) ->
    % 获取特定主题的批处理参数
    Result = get_batch_params(Topic),
    {reply, Result, State};

handle_call(_Request, _From, State) ->
    % 未知请求，返回错误
    {reply, {error, unknown_call}, State}.

%% @doc 处理异步调用
%% @spec handle_cast(Msg, State) -> {noreply, NewState}
%%
%% 功能说明：
%% - 处理gen_server:cast/2发送的异步消息
%% - 主要处理消息添加和批处理结果更新
handle_cast({add_message, Topic, Size, Priority}, State) ->
    % 步骤1：更新ETS表中的消息指标
    update_message_metrics(Topic, Size),

    % 步骤2：更新主题优先级信息
    update_topic_priority(Topic, Priority),

    % 步骤3：更新状态中的全局指标
    NewState = State#batch_state{
        metrics = maps:update_with(
            message_size,  % 更新总消息大小
            fun(TotalSize) -> TotalSize + Size end,
            Size,  % 如果键不存在，使用Size作为初始值
            maps:update_with(
                message_count,  % 更新总消息数量
                fun(Count) -> Count + 1 end,
                1,  % 如果键不存在，使用1作为初始值
                State#batch_state.metrics
            )
        )
    },

    {noreply, NewState};

handle_cast({update_batch_result, Topic, Success, Latency}, State) ->
    % 处理批处理结果更新消息
    % 步骤1：更新ETS表中的批处理结果指标
    update_batch_result_metrics(Topic, Success, Latency),

    % 步骤2：更新状态中的成功/失败计数
    NewMetrics = case Success of
        true ->
            % 批处理成功，增加成功计数
            maps:update_with(
                batch_success,
                fun(Count) -> Count + 1 end,
                1,  % 初始值
                State#batch_state.metrics
            );
        false ->
            % 批处理失败，增加错误计数
            maps:update_with(
                batch_error,
                fun(Count) -> Count + 1 end,
                1,  % 初始值
                State#batch_state.metrics
            )
    end,

    % 步骤3：更新总批处理计数
    FinalMetrics = maps:update_with(
        batch_count,
        fun(Count) -> Count + 1 end,
        1,  % 初始值
        NewMetrics
    ),

    % 步骤4：计算新的平均延迟
    AvgLatency = calculate_avg_latency(
        maps:get(avg_latency, State#batch_state.metrics, 0),  % 当前平均延迟
        Latency,  % 新的延迟值
        maps:get(batch_count, State#batch_state.metrics, 0)  % 当前批处理总数
    ),

    % 步骤5：更新最终状态
    FinalState = State#batch_state{
        metrics = FinalMetrics#{avg_latency => AvgLatency}
    },

    {noreply, FinalState};

handle_cast(_Msg, State) ->
    % 处理未知的异步消息，忽略并保持状态不变
    {noreply, State}.

%% @doc 处理消息
%% @spec handle_info(Info, State) -> {noreply, NewState}
%%
%% 功能说明：
%% - 处理定时器消息和其他系统消息
%% - 主要处理预测和指标收集的定时任务
handle_info(predict_batch_params, State) ->
    % 处理预测批处理参数的定时器消息
    % 步骤1：执行批处理参数预测
    predict_all_batch_params(),

    % 步骤2：重新设置预测定时器，实现周期性预测
    NewPredictionTimer = erlang:send_after(?PREDICTION_INTERVAL, self(), predict_batch_params),

    % 步骤3：更新状态中的定时器引用
    {noreply, State#batch_state{prediction_timer = NewPredictionTimer}};

handle_info(collect_metrics, State) ->
    % 处理指标收集的定时器消息
    try
        % 步骤1：收集系统指标
        CpuUsage = get_cpu_usage(),           % 获取CPU使用率
        NetworkLatency = get_network_latency(), % 获取网络延迟

        % 步骤2：更新状态中的指标数据
        NewState = State#batch_state{
            metrics = (State#batch_state.metrics)#{
                cpu_usage => CpuUsage,        % 更新CPU使用率
                network_latency => NetworkLatency  % 更新网络延迟
            }
        },

        % 步骤3：存储历史指标用于趋势分析
        store_metrics_history(NewState#batch_state.metrics),

        % 步骤4：重新设置指标收集定时器
        NewMetricsTimer = erlang:send_after(1000, self(), collect_metrics),

        % 步骤5：返回更新后的状态
        {noreply, NewState#batch_state{metrics_timer = NewMetricsTimer}}
    catch
        E:R:S ->
            % 指标收集失败，记录错误日志
            ?SLOG(error, #{
                msg => "metrics_collection_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            % 即使失败也要重置定时器，确保下次能继续尝试
            FallbackTimer = erlang:send_after(1000, self(), collect_metrics),
            {noreply, State#batch_state{metrics_timer = FallbackTimer}}
    end;

handle_info(_Info, State) ->
    % 处理未知消息，忽略并保持状态不变
    {noreply, State}.

%% @doc 终止回调函数
%% @spec terminate(Reason, State) -> ok
%%
%% 功能说明：
%% - 在gen_server进程终止时调用
%% - 清理所有创建的ETS表
%% - 释放系统资源
terminate(_Reason, _State) ->
    % 安全删除所有ETS表，使用catch防止表不存在时的错误
    catch ets:delete(?BATCH_METRICS_TAB),    % 删除批处理指标表
    catch ets:delete(?BATCH_CONFIG_TAB),     % 删除批处理配置表
    catch ets:delete(?BATCH_HISTORY_TAB),    % 删除批处理历史表
    catch ets:delete(?TOPIC_PRIORITY_TAB),   % 删除主题优先级表
    ok.

%% @doc 代码更新回调函数
%% @spec code_change(OldVsn, State, Extra) -> {ok, NewState}
%%
%% 功能说明：
%% - 在热代码更新时调用
%% - 当前实现不需要状态转换，直接返回原状态
code_change(_OldVsn, State, _Extra) ->
    {ok, State}.

%%%===================================================================
%%% 内部函数 - 模块内部使用的辅助函数
%%%===================================================================

%% @doc 初始化默认配置
%% @spec init_default_config() -> ok
%%
%% 功能说明：
%% - 为不同优先级的主题设置默认的批处理参数
%% - 高优先级：小批次、短超时，保证低延迟
%% - 普通优先级：中等批次、中等超时，平衡性能和延迟
%% - 低优先级：大批次、长超时，优化吞吐量
init_default_config() ->
    % 默认批处理配置（普通优先级）
    ets:insert(?BATCH_CONFIG_TAB, {<<"default">>, ?DEFAULT_BATCH_SIZE, ?DEFAULT_BATCH_TIMEOUT, normal}),

    % 高优先级主题配置 - 更小的批次，更短的超时，确保低延迟
    ets:insert(?BATCH_CONFIG_TAB, {<<"high_priority">>, ?MIN_BATCH_SIZE * 2, ?MIN_BATCH_TIMEOUT * 2, high}),

    % 低优先级主题配置 - 更大的批次，更长的超时，优化吞吐量
    ets:insert(?BATCH_CONFIG_TAB, {<<"low_priority">>, ?MAX_BATCH_SIZE / 2, ?MAX_BATCH_TIMEOUT / 2, low}).

%% @doc 更新消息指标
%% @spec update_message_metrics(Topic, Size) -> true
%%
%% 参数说明：
%% - Topic: 消息主题（binary类型）
%% - Size: 消息大小（integer类型，单位：字节）
%%
%% 功能说明：
%% - 更新ETS表中特定主题的消息统计信息
%% - 包括消息数量、总大小和最后更新时间
%% - 使用原子操作确保并发安全
update_message_metrics(Topic, Size) ->
    % 获取当前时间戳（秒级精度）
    Now = erlang:system_time(second),

    % 尝试更新主题指标，使用try-catch处理并发情况
    try
        % 尝试插入新记录：{主题, 消息数量, 总大小, 最后更新时间}
        ets:insert_new(?BATCH_METRICS_TAB, {Topic, 1, Size, Now})
    catch
        error:badarg ->
            % 记录已存在，使用原子操作更新计数器
            % [{2, 1}] 表示将第2个字段（消息数量）增加1
            % [{3, Size}] 表示将第3个字段（总大小）增加Size
            ets:update_counter(?BATCH_METRICS_TAB, Topic, [{2, 1}, {3, Size}]),
            % 更新最后更新时间（第4个字段）
            ets:update_element(?BATCH_METRICS_TAB, Topic, {4, Now})
    end.

%% @doc 更新批处理结果指标
%% @spec update_batch_result_metrics(Topic, Success, Latency) -> true
%%
%% 参数说明：
%% - Topic: 消息主题（binary类型）
%% - Success: 批处理是否成功（boolean类型）
%% - Latency: 批处理延迟（float类型，单位：毫秒）
%%
%% 功能说明：
%% - 更新特定主题的批处理成功/失败统计
%% - 计算和更新平均延迟
%% - 如果主题记录不存在则创建新记录
update_batch_result_metrics(Topic, Success, Latency) ->
    % 获取当前时间戳
    Now = erlang:system_time(second),

    % 查找主题的现有指标记录
    case ets:lookup(?BATCH_METRICS_TAB, Topic) of
        [{Topic, Count, Size, LastUpdate, SuccessCount, ErrorCount, AvgLatency}] ->
            % 记录存在，更新成功/失败计数和平均延迟
            NewSuccessCount = case Success of
                true -> SuccessCount + 1;  % 成功则增加成功计数
                false -> SuccessCount      % 失败则保持不变
            end,

            NewErrorCount = case Success of
                true -> ErrorCount;        % 成功则保持错误计数不变
                false -> ErrorCount + 1    % 失败则增加错误计数
            end,

            % 计算新的平均延迟
            TotalBatches = SuccessCount + ErrorCount,
            NewAvgLatency = calculate_avg_latency(AvgLatency, Latency, TotalBatches),

            % 更新完整记录：{主题, 消息数, 总大小, 更新时间, 成功数, 错误数, 平均延迟}
            ets:insert(?BATCH_METRICS_TAB, {Topic, Count, Size, Now, NewSuccessCount, NewErrorCount, NewAvgLatency});
        [] ->
            % 记录不存在，创建新记录
            SuccessCount = case Success of
                true -> 1;   % 如果当前操作成功，初始成功计数为1
                false -> 0   % 如果当前操作失败，初始成功计数为0
            end,

            ErrorCount = case Success of
                true -> 0;   % 如果当前操作成功，初始错误计数为0
                false -> 1   % 如果当前操作失败，初始错误计数为1
            end,

            % 插入新记录：{主题, 消息数, 总大小, 更新时间, 成功数, 错误数, 平均延迟}
            ets:insert(?BATCH_METRICS_TAB, {Topic, 0, 0, Now, SuccessCount, ErrorCount, Latency})
    end.

%% @doc 更新主题优先级
%% @spec update_topic_priority(Topic, Priority) -> true
%%
%% 参数说明：
%% - Topic: 消息主题（binary类型）
%% - Priority: 主题优先级（high | normal | low）
%%
%% 功能说明：
%% - 在ETS表中存储或更新主题的优先级信息
%% - 优先级用于调整批处理参数的预测算法
update_topic_priority(Topic, Priority) ->
    % 直接插入或更新主题优先级记录
    ets:insert(?TOPIC_PRIORITY_TAB, {Topic, Priority}).

%% @doc 计算平均延迟
%% @spec calculate_avg_latency(CurrentAvg, NewLatency, Count) -> float()
%%
%% 参数说明：
%% - CurrentAvg: 当前平均延迟（float类型）
%% - NewLatency: 新的延迟值（float类型）
%% - Count: 当前样本数量（integer类型）
%%
%% 功能说明：
%% - 使用增量算法计算新的平均延迟
%% - 避免存储所有历史数据，节省内存
calculate_avg_latency(CurrentAvg, NewLatency, Count) ->
    case Count of
        0 -> NewLatency;  % 第一个样本，直接返回
        _ -> (CurrentAvg * Count + NewLatency) / (Count + 1)  % 增量计算新平均值
    end.

%% @doc 获取CPU使用率
%% @spec get_cpu_usage() -> float()
%%
%% 返回值：
%% - float(): CPU使用率百分比（0.0-100.0）
%%
%% 功能说明：
%% - 通过recon监控模块获取系统CPU使用率
%% - 用于自适应批处理参数调整
%% - 失败时返回默认值以保证系统稳定性
get_cpu_usage() ->
    % 暂时禁用监控模块调用，直接返回默认值
    try
        % emqx_plugin_mongodb_monitor:get_cpu_usage()
        0.5  % 直接返回默认值，避免启动监控模块
    catch
        E:R:S ->
            % CPU使用率获取失败，记录警告日志
            ?SLOG(warning, #{
                msg => "cpu_usage_detection_failed",
                error => E,
                reason => R,
                stacktrace => S,
                action => "using_default_cpu_usage"
            }),
            0.5  % 返回默认值50%，表示中等负载
    end.

%% @doc 获取网络延迟
%% @spec get_network_latency() -> float()
%%
%% 返回值：
%% - float(): 网络延迟（单位：毫秒）
%%
%% 功能说明：
%% - 通过向MongoDB发送ping命令测量网络延迟
%% - 使用spawn_monitor实现超时保护，避免阻塞
%% - 失败时返回合理的默认值
%% - 用于自适应批处理参数调整
get_network_latency() ->
    try
        % 步骤1：获取MongoDB连接进程ID
        Pid = case persistent_term:get(?PLUGIN_MONGODB_RESOURCE_ID) of
            undefined -> get(mongodb_connection_pid);  % 从进程字典获取
            P when is_pid(P) -> P;                     % 从persistent_term获取
            _ -> undefined                             % 无效值
        end,

        % 步骤2：检查连接是否有效并测量ping延迟
        case is_pid(Pid) andalso is_process_alive(Pid) of
            true ->
                % 连接有效，使用spawn_monitor设置超时保护
                {Caller, Ref} = spawn_monitor(
                    fun() ->
                        Result = try
                            % 记录开始时间（微秒精度）
                            StartTime = erlang:system_time(microsecond),
                            % 执行MongoDB ping命令
                            case mc_worker_api:command(Pid, #{<<"ping">> => 1}) of
                                {ok, #{<<"ok">> := 1.0}} ->
                                    % ping成功，计算延迟
                                    EndTime = erlang:system_time(microsecond),
                                    (EndTime - StartTime) / 1000; % 转换为毫秒
                                _ ->
                                    % ping失败，返回默认值
                                    50.0
                            end
                        catch
                            _:_ -> 50.0 % 异常时返回默认值
                        end,
                        % 通过exit返回结果
                        exit({ping_result, Result})
                    end
                ),

                % 步骤3：等待结果，带1秒超时
                receive
                    {'DOWN', Ref, process, Caller, {ping_result, Result}} ->
                        % 正常获取到ping结果
                        Result;
                    {'DOWN', Ref, process, Caller, _Reason} ->
                        % 进程异常退出，返回默认值
                        100.0
                after 1000 ->
                    % 超时处理：清理监控并终止调用进程
                    erlang:demonitor(Ref, [flush]),
                    exit(Caller, kill),
                    200.0 % 超时默认值，表示网络延迟较高
                end;
            false ->
                % 连接无效，返回默认值
                50.0
        end
    catch
        _:_ -> 50.0 % 整个过程异常，返回默认值
    end.

%% @doc 存储指标历史
%% @spec store_metrics_history(Metrics) -> true
%%
%% 参数说明：
%% - Metrics: 指标映射表（map类型）
%%
%% 功能说明：
%% - 将当前指标数据存储到历史表中
%% - 自动清理超出时间窗口的旧数据
%% - 用于趋势分析和预测算法
store_metrics_history(Metrics) ->
    % 获取当前时间戳（秒级精度）
    Now = erlang:system_time(second),

    % 存储当前指标到历史表：{时间戳, 指标数据}
    ets:insert(?BATCH_HISTORY_TAB, {Now, Metrics}),

    % 清理超出时间窗口的旧数据
    OldTime = Now - ?METRICS_WINDOW,
    % 使用select_delete删除时间戳小于OldTime的记录
    ets:select_delete(?BATCH_HISTORY_TAB, [{{'$1', '_'}, [{'<', '$1', OldTime}], [true]}]).

%% @doc 预测所有主题的批处理参数
%% @spec predict_all_batch_params() -> [ok]
%%
%% 功能说明：
%% - 遍历所有有统计数据的主题
%% - 为每个主题预测最优的批处理参数
%% - 基于消息频率、大小、延迟等多维度因素
%% - 更新ETS配置表中的参数
predict_all_batch_params() ->
    % 获取所有主题的统计数据
    Topics = ets:tab2list(?BATCH_METRICS_TAB),

    % 为每个主题预测批处理参数
    % 使用列表推导式并行处理所有主题
    [predict_topic_batch_params(Topic, Data) || {Topic, _Count, _Size, _LastUpdate, _Success, _Error, _Latency} = Data <- Topics].

%% @doc 预测特定主题的批处理参数
%% @spec predict_topic_batch_params(Topic, MetricsData) -> true
%%
%% 参数说明：
%% - Topic: 消息主题（binary类型）
%% - MetricsData: 主题的统计数据元组
%%
%% 功能说明：
%% - 基于历史数据和系统状态预测最优批处理参数
%% - 考虑消息频率、大小、延迟、错误率等多个维度
%% - 结合主题优先级和系统负载进行调整
%% - 更新ETS配置表中的批处理参数
predict_topic_batch_params(Topic, {_, Count, Size, LastUpdate, Success, Error, Latency}) ->
    % 步骤1：获取主题优先级
    Priority = case ets:lookup(?TOPIC_PRIORITY_TAB, Topic) of
        [{_, P}] -> P;      % 找到优先级记录
        [] -> normal        % 默认为普通优先级
    end,

    % 步骤2：获取最新的系统指标
    CpuUsage = get_latest_cpu_usage(),          % CPU使用率
    NetworkLatency = get_latest_network_latency(), % 网络延迟

    % 步骤3：计算消息频率（每秒消息数）- 添加异常处理
    Now = erlang:system_time(second),
    TimeDiff = max(1, Now - LastUpdate),  % 确保时间差至少为1秒，避免除零
    MessageRate = try
        Count / TimeDiff  % 消息数除以时间差
    catch
        _:_ -> 0.0  % 处理除零或其他数学错误
    end,

    % 步骤4：计算平均消息大小 - 添加异常处理
    AvgMessageSize = try
        case Count of
            0 -> 0;           % 没有消息，平均大小为0
            _ -> Size / Count % 总大小除以消息数
        end
    catch
        _:_ -> 0.0  % 处理除零或其他数学错误
    end,

    % 步骤5：计算错误率 - 添加异常处理
    TotalBatches = Success + Error,
    ErrorRate = try
        case TotalBatches of
            0 -> 0;                      % 没有批处理记录，错误率为0
            _ -> Error / TotalBatches    % 错误数除以总批处理数
        end
    catch
        _:_ -> 0.0  % 处理除零或其他数学错误
    end,

    % 步骤6：预测最优批处理大小
    PredictedBatchSize = predict_batch_size({
        MessageRate,     % 消息频率
        AvgMessageSize,  % 平均消息大小
        Latency,         % 平均延迟
        ErrorRate,       % 错误率
        CpuUsage,        % CPU使用率
        NetworkLatency,  % 网络延迟
        Priority         % 主题优先级
    }),

    % 步骤7：预测最优批处理超时
    PredictedBatchTimeout = predict_batch_timeout({
        MessageRate,     % 消息频率
        AvgMessageSize,  % 平均消息大小
        Latency,         % 平均延迟
        ErrorRate,       % 错误率
        CpuUsage,        % CPU使用率
        NetworkLatency,  % 网络延迟
        Priority         % 主题优先级
    }),

    % 步骤8：更新ETS配置表中的批处理参数
    ets:insert(?BATCH_CONFIG_TAB, {Topic, PredictedBatchSize, PredictedBatchTimeout, Priority}).

%% @doc 预测批处理大小
%% @spec predict_batch_size(Params) -> integer()
%%
%% 参数说明：
%% - Params: 包含多维度参数的元组 {MessageRate, AvgMessageSize, Latency, ErrorRate, CpuUsage, NetworkLatency, Priority}
%%
%% 返回值：
%% - integer(): 预测的最优批处理大小
%%
%% 功能说明：
%% - 基于多维度因素预测最优批处理大小
%% - 使用加权算法综合考虑各种系统指标
%% - 根据主题优先级设置不同的基础值
%% - 自动限制在合理范围内并处理异常情况
predict_batch_size({MessageRate, AvgMessageSize, Latency, ErrorRate, CpuUsage, NetworkLatency, Priority}) ->
    % 步骤1：根据主题优先级确定基础批处理大小
    BaseBatchSize = case Priority of
        high -> ?MIN_BATCH_SIZE * 2;    % 高优先级：较小批次，保证低延迟
        normal -> ?DEFAULT_BATCH_SIZE;  % 普通优先级：默认大小
        low -> ?MAX_BATCH_SIZE / 2      % 低优先级：较大批次，优化吞吐量
    end,

    % 步骤2：根据消息频率调整（消息越多，批次可以越大）
    RateAdjustment = if
        MessageRate < 10 -> 0.5;    % 低频率：减小批次
        MessageRate < 100 -> 1.0;   % 中等频率：保持不变
        MessageRate < 1000 -> 1.5;  % 高频率：增大批次
        true -> 2.0                 % 极高频率：显著增大批次
    end,

    % 步骤3：根据平均消息大小调整（消息越大，批次应该越小）
    SizeAdjustment = if
        AvgMessageSize < 100 -> 1.5;    % 小消息：可以增大批次
        AvgMessageSize < 1000 -> 1.0;   % 中等消息：保持不变
        AvgMessageSize < 10000 -> 0.7;  % 大消息：减小批次
        true -> 0.5                     % 超大消息：显著减小批次
    end,

    % 步骤4：根据网络延迟调整（延迟越低，批次可以越大）
    LatencyAdjustment = if
        Latency < 10 -> 1.5;    % 低延迟：可以增大批次
        Latency < 50 -> 1.0;    % 中等延迟：保持不变
        Latency < 100 -> 0.7;   % 高延迟：减小批次
        true -> 0.5             % 极高延迟：显著减小批次
    end,

    % 步骤5：根据错误率调整（错误率越低，批次可以越大）
    ErrorAdjustment = if
        ErrorRate < 0.01 -> 1.2;    % 极低错误率：略微增大批次
        ErrorRate < 0.05 -> 1.0;    % 低错误率：保持不变
        ErrorRate < 0.1 -> 0.7;     % 中等错误率：减小批次
        true -> 0.5                 % 高错误率：显著减小批次
    end,

    % 步骤6：根据CPU使用率调整（CPU负载越低，批次可以越大）
    CpuAdjustment = if
        CpuUsage < 30 -> 1.2;   % 低CPU使用率：略微增大批次
        CpuUsage < 60 -> 1.0;   % 中等CPU使用率：保持不变
        CpuUsage < 80 -> 0.7;   % 高CPU使用率：减小批次
        true -> 0.5             % 极高CPU使用率：显著减小批次
    end,

    % 步骤7：根据网络延迟调整（网络越快，批次可以越大）
    NetworkAdjustment = if
        NetworkLatency < 5 -> 1.2;      % 低网络延迟：略微增大批次
        NetworkLatency < 20 -> 1.0;     % 中等网络延迟：保持不变
        NetworkLatency < 50 -> 0.7;     % 高网络延迟：减小批次
        true -> 0.5                     % 极高网络延迟：显著减小批次
    end,

    % 步骤8：计算最终批处理大小（所有调整因子的乘积）
    AdjustedBatchSize = try
        BaseBatchSize * RateAdjustment * SizeAdjustment * LatencyAdjustment *
        ErrorAdjustment * CpuAdjustment * NetworkAdjustment
    catch
        _:_ ->
            % 计算出错，记录警告日志并使用基础大小
            ?SLOG(warning, #{
                msg => "batch_size_calculation_error",
                base_size => BaseBatchSize,
                rate_adj => RateAdjustment,
                size_adj => SizeAdjustment,
                latency_adj => LatencyAdjustment,
                error_adj => ErrorAdjustment,
                cpu_adj => CpuAdjustment,
                network_adj => NetworkAdjustment
            }),
            BaseBatchSize  % 出错时使用基础大小
    end,

    % 步骤9：验证计算结果并限制在合理范围内
    FinalSize = case is_number(AdjustedBatchSize) andalso is_float(AdjustedBatchSize) of
        true ->
            % 结果有效，限制在允许范围内并转换为整数
            round(max(?MIN_BATCH_SIZE, min(?MAX_BATCH_SIZE, AdjustedBatchSize)));
        false ->
            % 结果无效，使用默认值
            ?DEFAULT_BATCH_SIZE
    end,

    FinalSize.

%% @doc 预测批处理超时
%% @spec predict_batch_timeout(Params) -> integer()
%%
%% 参数说明：
%% - Params: 包含多维度参数的元组 {MessageRate, AvgMessageSize, Latency, ErrorRate, CpuUsage, NetworkLatency, Priority}
%%
%% 返回值：
%% - integer(): 预测的最优批处理超时时间（毫秒）
%%
%% 功能说明：
%% - 基于多维度因素预测最优批处理超时时间
%% - 与批处理大小预测相反的调整逻辑（超时与大小成反比）
%% - 根据主题优先级和系统负载动态调整
%% - 自动限制在合理范围内并处理异常情况
predict_batch_timeout({MessageRate, AvgMessageSize, Latency, ErrorRate, CpuUsage, NetworkLatency, Priority}) ->
    % 步骤1：根据主题优先级确定基础批处理超时
    BaseBatchTimeout = case Priority of
        high -> ?MIN_BATCH_TIMEOUT * 2;     % 高优先级：较短超时，保证低延迟
        normal -> ?DEFAULT_BATCH_TIMEOUT;   % 普通优先级：默认超时
        low -> ?MAX_BATCH_TIMEOUT / 2       % 低优先级：较长超时，优化吞吐量
    end,

    % 步骤2：根据消息频率调整（消息越多，超时可以越短）
    RateAdjustment = if
        MessageRate < 10 -> 1.5;    % 低频率：增加超时等待更多消息
        MessageRate < 100 -> 1.0;   % 中等频率：保持不变
        MessageRate < 1000 -> 0.7;  % 高频率：减少超时快速处理
        true -> 0.5                 % 极高频率：显著减少超时
    end,

    % 步骤3：根据平均消息大小调整（消息越大，超时应该越长）
    SizeAdjustment = if
        AvgMessageSize < 100 -> 0.7;    % 小消息：减少超时
        AvgMessageSize < 1000 -> 1.0;   % 中等消息：保持不变
        AvgMessageSize < 10000 -> 1.2;  % 大消息：增加超时
        true -> 1.5                     % 超大消息：显著增加超时
    end,

    % 步骤4：根据网络延迟调整（延迟越高，超时应该越长）
    LatencyAdjustment = if
        Latency < 10 -> 0.7;    % 低延迟：减少超时
        Latency < 50 -> 1.0;    % 中等延迟：保持不变
        Latency < 100 -> 1.2;   % 高延迟：增加超时
        true -> 1.5             % 极高延迟：显著增加超时
    end,

    % 步骤5：根据错误率调整（错误率越高，超时应该越长）
    ErrorAdjustment = if
        ErrorRate < 0.01 -> 0.8;    % 极低错误率：略微减少超时
        ErrorRate < 0.05 -> 1.0;    % 低错误率：保持不变
        ErrorRate < 0.1 -> 1.2;     % 中等错误率：增加超时
        true -> 1.5                 % 高错误率：显著增加超时
    end,

    % 步骤6：根据CPU使用率调整（CPU负载越高，超时应该越长）
    CpuAdjustment = if
        CpuUsage < 30 -> 0.8;   % 低CPU使用率：略微减少超时
        CpuUsage < 60 -> 1.0;   % 中等CPU使用率：保持不变
        CpuUsage < 80 -> 1.2;   % 高CPU使用率：增加超时
        true -> 1.5             % 极高CPU使用率：显著增加超时
    end,

    % 步骤7：根据网络延迟调整（网络越慢，超时应该越长）
    NetworkAdjustment = if
        NetworkLatency < 5 -> 0.8;      % 低网络延迟：略微减少超时
        NetworkLatency < 20 -> 1.0;     % 中等网络延迟：保持不变
        NetworkLatency < 50 -> 1.2;     % 高网络延迟：增加超时
        true -> 1.5                     % 极高网络延迟：显著增加超时
    end,

    % 步骤8：计算最终批处理超时（所有调整因子的乘积）
    AdjustedBatchTimeout = try
        BaseBatchTimeout * RateAdjustment * SizeAdjustment * LatencyAdjustment *
        ErrorAdjustment * CpuAdjustment * NetworkAdjustment
    catch
        _:_ ->
            % 计算出错，记录警告日志并使用基础超时
            ?SLOG(warning, #{
                msg => "batch_timeout_calculation_error",
                base_timeout => BaseBatchTimeout,
                rate_adj => RateAdjustment,
                size_adj => SizeAdjustment,
                latency_adj => LatencyAdjustment,
                error_adj => ErrorAdjustment,
                cpu_adj => CpuAdjustment,
                network_adj => NetworkAdjustment
            }),
            BaseBatchTimeout  % 出错时使用基础超时
    end,

    % 步骤9：验证计算结果并限制在合理范围内
    FinalTimeout = case is_number(AdjustedBatchTimeout) andalso is_float(AdjustedBatchTimeout) of
        true ->
            % 结果有效，限制在允许范围内并转换为整数
            round(max(?MIN_BATCH_TIMEOUT, min(?MAX_BATCH_TIMEOUT, AdjustedBatchTimeout)));
        false ->
            % 结果无效，使用默认值
            ?DEFAULT_BATCH_TIMEOUT
    end,

    FinalTimeout.

%% @doc 获取最近的CPU使用率
%% @spec get_latest_cpu_usage() -> float()
%%
%% 返回值：
%% - float(): 最近10秒的平均CPU使用率
%%
%% 功能说明：
%% - 从历史指标表中查询最近10秒的CPU使用率数据
%% - 计算平均值用于批处理参数预测
%% - 如果没有历史数据则返回0
get_latest_cpu_usage() ->
    Now = erlang:system_time(second),
    RecentTime = Now - 10, % 查询最近10秒的数据

    % 使用match specification查找最近的CPU使用率指标
    MS = ets:fun2ms(fun({Time, Metrics}) when Time >= RecentTime ->
        maps:get(cpu_usage, Metrics, 0)  % 提取CPU使用率，默认为0
    end),

    % 计算平均CPU使用率
    case ets:select(?BATCH_HISTORY_TAB, MS) of
        [] -> 0;  % 没有历史数据，返回0
        Values -> lists:sum(Values) / length(Values)  % 计算平均值
    end.

%% @doc 获取最近的网络延迟
%% @spec get_latest_network_latency() -> float()
%%
%% 返回值：
%% - float(): 最近10秒的平均网络延迟（毫秒）
%%
%% 功能说明：
%% - 从历史指标表中查询最近10秒的网络延迟数据
%% - 计算平均值用于批处理参数预测
%% - 如果没有历史数据则返回0
get_latest_network_latency() ->
    Now = erlang:system_time(second),
    RecentTime = Now - 10, % 查询最近10秒的数据

    % 使用match specification查找最近的网络延迟指标
    MS = ets:fun2ms(fun({Time, Metrics}) when Time >= RecentTime ->
        maps:get(network_latency, Metrics, 0)  % 提取网络延迟，默认为0
    end),

    % 计算平均网络延迟
    case ets:select(?BATCH_HISTORY_TAB, MS) of
        [] -> 0;  % 没有历史数据，返回0
        Values -> lists:sum(Values) / length(Values)  % 计算平均值
    end.

%% @doc 查找匹配的主题模式
%% @spec find_matching_topic_pattern(Topic) -> {ok, Pattern} | not_found
%%
%% 参数说明：
%% - Topic: 要匹配的主题（binary类型）
%%
%% 返回值：
%% - {ok, Pattern}: 找到匹配的模式
%% - not_found: 没有找到匹配的模式
%%
%% 功能说明：
%% - 从配置表中查找所有主题模式
%% - 过滤出包含通配符的模式（# 或 +）
%% - 逐一匹配直到找到合适的模式
find_matching_topic_pattern(Topic) ->
    % 从配置表中获取所有主题名称
    Patterns = ets:select(?BATCH_CONFIG_TAB, [{{'$1', '_', '_', '_'}, [], ['$1']}]),

    % 过滤出真正的主题模式（包含通配符的）
    TopicPatterns = [Pattern || Pattern <- Patterns, is_topic_pattern(Pattern)],

    % 查找匹配的模式
    find_matching_pattern(Topic, TopicPatterns).

%% @doc 检查是否为主题模式
%% @spec is_topic_pattern(Pattern) -> boolean()
%%
%% 参数说明：
%% - Pattern: 要检查的模式（binary类型）
%%
%% 返回值：
%% - boolean(): true表示是模式，false表示不是模式
%%
%% 功能说明：
%% - 检查二进制字符串是否包含MQTT通配符
%% - # 表示多级通配符，+ 表示单级通配符
is_topic_pattern(Pattern) ->
    binary:match(Pattern, <<"#">>) =/= nomatch orelse
    binary:match(Pattern, <<"+">>) =/= nomatch.

%% @doc 查找匹配的模式
%% @spec find_matching_pattern(Topic, Patterns) -> {ok, Pattern} | not_found
%%
%% 参数说明：
%% - Topic: 要匹配的主题（binary类型）
%% - Patterns: 模式列表（list类型）
%%
%% 功能说明：
%% - 递归遍历模式列表
%% - 使用EMQX的主题匹配函数进行匹配
%% - 返回第一个匹配的模式
find_matching_pattern(_Topic, []) ->
    not_found;  % 模式列表为空，没有找到匹配
find_matching_pattern(Topic, [Pattern | Rest]) ->
    case emqx_topic:match(Topic, Pattern) of
        true -> {ok, Pattern};  % 找到匹配的模式
        false -> find_matching_pattern(Topic, Rest)  % 继续查找下一个模式
    end.

%% @doc 更新指标
%% @spec update_metrics(Topic, Success, Latency) -> ok
%%
%% 参数说明：
%% - Topic: 消息主题（binary类型）
%% - Success: 批处理是否成功（boolean类型）
%% - Latency: 批处理延迟（float类型，单位：毫秒）
%%
%% 功能说明：
%% - 异步发送批处理结果更新消息到服务器
%% - 用于更新主题的成功/失败统计和延迟信息
%% - 供外部模块调用以报告批处理结果
update_metrics(Topic, Success, Latency) ->
    gen_server:cast(?MODULE, {update_batch_result, Topic, Success, Latency}).
