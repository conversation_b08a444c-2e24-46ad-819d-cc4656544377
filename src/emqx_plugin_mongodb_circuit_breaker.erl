%%%-------------------------------------------------------------------
%%% @doc
%%% MongoDB插件的高级熔断器模块
%%%
%%% 这个模块实现了高级熔断器（Circuit Breaker）模式，用于防止级联故障
%%% 熔断器是一种保护机制，当检测到下游服务故障时，自动切断请求流量，
%%% 避免无效请求堆积和系统资源浪费，并提供自动恢复机制
%%%
%%% 功能特性：
%%% 1. 多级熔断策略：closed（关闭）、half_open（半开）、open（开启）三种状态
%%% 2. 渐进式恢复：从故障状态逐步恢复到正常状态
%%% 3. 上下文感知：根据系统负载、网络延迟等上下文信息调整熔断策略
%%% 4. 错误类型分析：区分不同类型的错误并应用不同的阈值
%%% 5. 自适应阈值：根据历史数据动态调整熔断阈值
%%%
%%% Java等价概念：
%%% - 类似于Netflix Hystrix的熔断器
%%% - 类似于Resilience4j的CircuitBreaker
%%% - 类似于Spring Cloud Circuit Breaker
%%% - 类似于Envoy的异常检测和熔断
%%%
%%% 熔断器状态转换：
%%% Closed -> Open: 当错误率超过阈值时
%%% Open -> Half-Open: 经过重置超时时间后
%%% Half-Open -> Closed: 当测试请求成功时
%%% Half-Open -> Open: 当测试请求失败时
%%%
%%% @end
%%%-------------------------------------------------------------------
-module(emqx_plugin_mongodb_circuit_breaker).

%% 实现gen_server行为，提供状态管理和并发控制
%% 类似于Java中实现Runnable接口或继承Thread类
-behaviour(gen_server).

%% API - 公共接口函数
%% 这些函数提供熔断器的主要功能，类似于Java中的public方法
-export([
    start_link/1,           % 启动熔断器服务，类似于Java的服务启动方法
    check_breaker/2,        % 检查熔断器状态，类似于Hystrix的allowRequest()
    record_success/2,       % 记录成功操作，类似于Hystrix的markSuccess()
    record_failure/3,       % 记录失败操作，类似于Hystrix的markFailure()
    reset_breaker/1,        % 重置熔断器，类似于手动重置熔断器状态
    get_breaker_status/1,   % 获取熔断器状态，类似于健康检查接口
    update_config/2,        % 更新配置，类似于动态配置更新
    record_success/0,       % 记录全局成功，简化版本
    record_failure/1,       % 记录全局失败，简化版本
    is_open/0,              % 检查是否熔断，类似于isOpen()方法
    integrate/0             % 集成到系统中，类似于模块注册方法
]).

%% gen_server callbacks
-export([
    init/1,
    handle_call/3,
    handle_cast/2,
    handle_info/2,
    terminate/2,
    code_change/3
]).

-include("emqx_plugin_mongodb.hrl").

%% @doc 熔断器状态记录
%% 这个记录定义了熔断器的完整状态信息和配置参数
%% 类似于Java中的CircuitBreakerState类或配置类
%%
%% 在Java中相当于：
%% public class CircuitBreakerState {
%%     private String id;
%%     private CircuitBreakerState state;
%%     private int failureCount;
%%     private long lastFailureTime;
%%     // ... 其他字段
%% }
-record(circuit_breaker, {
    %% 熔断器唯一标识符，用于区分不同的熔断器实例
    %% 可以是主题名、客户端ID、服务名等
    %% 类似于Java中的熔断器名称或键
    id,

    %% 熔断器当前状态，类似于Java枚举
    %% - closed: 关闭状态，正常处理请求
    %% - half_open: 半开状态，允许少量请求通过进行测试
    %% - open: 开启状态，拒绝所有请求
    %% 类似于Hystrix的CircuitBreakerState枚举
    state = closed,

    %% 连续失败次数计数器
    %% 用于判断是否达到熔断阈值
    %% 类似于Java中的AtomicInteger计数器
    failure_count = 0,

    %% 最后一次失败的时间戳（毫秒）
    %% 用于计算重置超时和恢复策略
    %% 类似于Java中的System.currentTimeMillis()
    last_failure_time = 0,

    %% 重置超时时间（毫秒）
    %% 从open状态转换到half_open状态的等待时间
    %% 类似于Hystrix的sleepWindowInMilliseconds
    reset_timeout = ?DEFAULT_RESET_TIMEOUT,

    %% 失败阈值，触发熔断的连续失败次数
    %% 类似于Hystrix的requestVolumeThreshold
    failure_threshold = ?DEFAULT_FAILURE_THRESHOLD,

    %% 半开状态下允许通过的请求比例（0.0-1.0）
    %% 用于渐进式恢复，避免突然的流量冲击
    %% 类似于渐进式流量恢复策略
    half_open_ratio = ?DEFAULT_HALF_OPEN_RATIO,

    %% 恢复步长，每次成功后增加的健康比率
    %% 用于实现渐进式恢复
    recovery_step = ?DEFAULT_RECOVERY_STEP,

    %% 恢复间隔时间（毫秒）
    %% 控制恢复过程的速度
    recovery_interval = ?DEFAULT_RECOVERY_INTERVAL,

    %% 当前健康比率（0.0-1.0）
    %% 表示系统的健康程度，影响请求通过率
    %% 类似于健康度评分
    health_ratio = 1.0,
    %% 错误类型统计映射
    %% 记录不同类型错误的发生次数
    %% 类似于Java中的Map<ErrorType, Integer>
    %% 用于分析错误模式和调整熔断策略
    error_types = #{},

    %% 错误历史记录列表
    %% 格式：[{timestamp, error_type}, ...]
    %% 类似于Java中的List<ErrorEvent>
    %% 用于趋势分析和智能熔断决策
    error_history = [],

    %% 不同错误类型的熔断阈值配置
    %% 不同类型的错误有不同的容忍度
    %% 类似于Java中的Map<ErrorType, Double>
    error_thresholds = #{
        %% 默认错误类型阈值（错误率）
        default => ?DEFAULT_ERROR_THRESHOLD_RATIO,
        %% 超时错误阈值：60%错误率触发熔断
        %% 超时通常表示下游服务响应慢，需要较快熔断
        timeout => 0.6,
        %% 网络错误阈值：70%错误率触发熔断
        %% 网络问题可能是暂时的，稍微宽松一些
        network => 0.7,
        %% 认证错误阈值：90%错误率触发熔断
        %% 认证错误通常不是系统问题，阈值较高
        auth => 0.9,
        %% 查询错误阈值：80%错误率触发熔断
        %% 查询错误可能是数据问题，中等阈值
        query => 0.8
    },

    %% 上下文数据，用于上下文感知的熔断决策
    %% 类似于Java中的ContextData类
    %% 根据系统状态动态调整熔断策略
    context = #{
        %% 系统负载（0.0-1.0）
        %% 类似于Java中的CPU使用率监控
        system_load => 0.0,
        %% 网络延迟（毫秒）
        %% 类似于Java中的网络延迟监控
        network_latency => 0,
        %% 内存使用率（0.0-1.0）
        %% 类似于Java中的内存使用率监控
        memory_usage => 0.0,
        %% 连接池状态
        %% 类似于Java中的连接池健康状态
        pool_status => normal
    }
}).

%%%===================================================================
%%% API
%%%===================================================================

%% @doc 启动熔断器服务
%% 这个函数启动一个gen_server进程来管理熔断器状态和执行熔断逻辑
%%
%% 功能说明：
%% 1. 创建一个本地注册的gen_server进程
%% 2. 使用提供的配置初始化熔断器状态
%% 3. 开始监控系统状态和执行熔断决策
%% 4. 提供并发安全的熔断器操作
%%
%% 参数说明：
%% - Options: 熔断器配置映射，包含阈值、超时等配置信息
%%   格式示例：#{
%%     failure_threshold => 5,
%%     reset_timeout => 60000,
%%     half_open_ratio => 0.5
%%   }
%%
%% 返回值：
%% - {ok, Pid}: 启动成功，返回进程ID
%% - {error, Reason}: 启动失败，返回错误原因
%%
%% Java等价概念：
%% 类似于Spring的@Service注解的Bean启动
%% 或者Hystrix的HystrixCommand初始化
%%
%% 示例：
%% Options = #{failure_threshold => 5, reset_timeout => 60000},
%% {ok, Pid} = emqx_plugin_mongodb_circuit_breaker:start_link(Options)
start_link(Options) ->
    %% 启动gen_server进程
    %% {local, ?MODULE} 表示本地注册，进程名为模块名
    %% ?MODULE 是当前模块名的宏
    %% [Options] 是传递给init/1回调的参数列表
    %% [] 是gen_server的选项列表（这里使用默认选项）
    %%
    %% 在Java中相当于：
    %% @Service
    %% public class CircuitBreakerService {
    %%     public CircuitBreakerService(CircuitBreakerConfig config) { ... }
    %% }
    gen_server:start_link({local, ?MODULE}, ?MODULE, [Options], []).

%% @doc 检查熔断器状态
%% 这是熔断器的核心函数，决定是否允许请求通过
%%
%% 功能说明：
%% 1. 根据熔断器当前状态决定是否允许请求
%% 2. 在half_open状态下实现渐进式恢复
%% 3. 考虑上下文信息（系统负载、网络延迟等）进行智能决策
%% 4. 更新熔断器状态和统计信息
%%
%% 参数说明：
%% - BreakerId: 熔断器标识符，用于区分不同的熔断器实例
%% - Context: 上下文信息映射，包含系统状态、请求信息等
%%   格式示例：#{
%%     system_load => 0.7,
%%     network_latency => 100,
%%     request_type => query
%%   }
%%
%% 返回值：
%% - {allow, UpdatedContext}: 允许请求通过，返回更新的上下文
%% - {reject, Reason}: 拒绝请求，返回拒绝原因
%%
%% Java等价概念：
%% 类似于Hystrix的allowRequest()方法
%% 或者Resilience4j的acquirePermission()方法
%%
%% 示例：
%% Context = #{system_load => 0.5, request_type => insert},
%% case check_breaker(<<"mongodb_connection">>, Context) of
%%     {allow, _} -> execute_request();
%%     {reject, Reason} -> handle_rejection(Reason)
%% end
check_breaker(BreakerId, Context) ->
    %% 发送同步调用到gen_server进程
    %% gen_server:call/2会等待服务器处理并返回结果
    %% 类似于Java中的同步方法调用或Future.get()
    %%
    %% 在Java中相当于：
    %% public CircuitBreakerDecision checkBreaker(String breakerId, Context context) {
    %%     return circuitBreakerService.checkBreaker(breakerId, context);
    %% }
    gen_server:call(?MODULE, {check_breaker, BreakerId, Context}).

%% @doc 记录成功操作
%% 这个函数记录一次成功的操作，用于更新熔断器的健康状态
%%
%% 功能说明：
%% 1. 增加成功计数器
%% 2. 更新健康比率和响应时间
%% 3. 在half_open状态下可能触发状态转换到closed
%% 4. 重置连续失败计数器
%%
%% 参数说明：
%% - BreakerId: 熔断器标识符
%% - Context: 操作上下文，包含响应时间等信息
%%   格式示例：#{response_time => 150, operation_type => query}
%%
%% 返回值：
%% - ok: 异步调用，立即返回
%%
%% Java等价概念：
%% 类似于Hystrix的markSuccess()方法
%% 或者Resilience4j的onSuccess()回调
%%
%% 示例：
%% Context = #{response_time => 100, operation_type => insert},
%% record_success(<<"mongodb_connection">>, Context)
record_success(BreakerId, Context) ->
    %% 发送异步消息到gen_server进程
    %% gen_server:cast/2是异步调用，不等待返回结果
    %% 类似于Java中的异步方法调用或CompletableFuture.runAsync()
    %%
    %% 在Java中相当于：
    %% @Async
    %% public void recordSuccess(String breakerId, Context context) {
    %%     circuitBreakerService.recordSuccess(breakerId, context);
    %% }
    gen_server:cast(?MODULE, {record_success, BreakerId, Context}).

%% @doc 记录失败操作
%% 这个函数记录一次失败的操作，用于更新熔断器的错误状态
%%
%% 功能说明：
%% 1. 增加失败计数器和连续失败计数
%% 2. 更新错误率和错误类型统计
%% 3. 检查是否达到熔断阈值，可能触发状态转换到open
%% 4. 记录错误历史用于趋势分析
%%
%% 参数说明：
%% - BreakerId: 熔断器标识符
%% - ErrorType: 错误类型（timeout/network/auth/query等）
%% - Context: 操作上下文，包含错误详情和响应时间
%%   格式示例：#{error_reason => timeout, response_time => 5000}
%%
%% 返回值：
%% - ok: 异步调用，立即返回
%%
%% Java等价概念：
%% 类似于Hystrix的markFailure()方法
%% 或者Resilience4j的onError()回调
%%
%% 示例：
%% Context = #{error_reason => connection_timeout, response_time => 5000},
%% record_failure(<<"mongodb_connection">>, timeout, Context)
record_failure(BreakerId, ErrorType, Context) ->
    %% 发送异步消息到gen_server进程
    %% 包含错误类型信息，用于智能熔断决策
    %% 不同类型的错误有不同的处理策略和阈值
    %%
    %% 在Java中相当于：
    %% @Async
    %% public void recordFailure(String breakerId, ErrorType errorType, Context context) {
    %%     circuitBreakerService.recordFailure(breakerId, errorType, context);
    %% }
    gen_server:cast(?MODULE, {record_failure, BreakerId, ErrorType, Context}).

%% @doc 重置熔断器
%% 这个函数手动重置指定的熔断器到初始状态
%%
%% 功能说明：
%% 1. 将熔断器状态重置为closed（关闭）
%% 2. 清零所有计数器（失败次数、成功次数等）
%% 3. 重置健康比率为1.0
%% 4. 清空错误历史记录
%%
%% 参数说明：
%% - BreakerId: 要重置的熔断器标识符
%%
%% 返回值：
%% - ok: 重置成功
%% - {error, not_found}: 熔断器不存在
%% - {error, Reason}: 其他错误原因
%%
%% Java等价概念：
%% 类似于Hystrix的reset()方法
%% 或者手动重置熔断器状态的管理接口
%%
%% 使用场景：
%% - 故障恢复后手动重置
%% - 维护操作后的状态清理
%% - 测试环境的状态重置
reset_breaker(BreakerId) ->
    %% 发送同步调用到gen_server进程
    %% 使用call而不是cast，因为需要确认重置操作的结果
    %%
    %% 在Java中相当于：
    %% public void resetCircuitBreaker(String breakerId) throws CircuitBreakerException {
    %%     circuitBreakerService.reset(breakerId);
    %% }
    gen_server:call(?MODULE, {reset_breaker, BreakerId}).

%% @doc 获取熔断器状态
%% 这个函数获取指定熔断器的当前状态和统计信息
%%
%% 功能说明：
%% 1. 返回熔断器的当前状态（closed/half_open/open）
%% 2. 提供详细的统计信息（成功率、错误率、响应时间等）
%% 3. 包含健康度评分和错误历史
%% 4. 用于监控和诊断
%%
%% 参数说明：
%% - BreakerId: 要查询的熔断器标识符
%%
%% 返回值：
%% - {ok, Status}: 成功获取状态信息
%%   Status格式：#{
%%     state => closed,
%%     health_score => 0.85,
%%     failure_count => 3,
%%     error_rate => 0.15,
%%     last_failure_time => 1640995200000
%%   }
%% - {error, not_found}: 熔断器不存在
%%
%% Java等价概念：
%% 类似于Hystrix的getMetrics()方法
%% 或者监控系统的状态查询接口
%%
%% 示例：
%% case get_breaker_status(<<"mongodb_connection">>) of
%%     {ok, Status} -> analyze_status(Status);
%%     {error, not_found} -> create_new_breaker()
%% end
get_breaker_status(BreakerId) ->
    %% 发送同步调用获取状态信息
    %% 返回详细的熔断器状态和统计数据
    %%
    %% 在Java中相当于：
    %% public CircuitBreakerStatus getStatus(String breakerId) {
    %%     return circuitBreakerService.getStatus(breakerId);
    %% }
    gen_server:call(?MODULE, {get_status, BreakerId}).

%% @doc 更新熔断器配置
update_config(BreakerId, Config) ->
    gen_server:call(?MODULE, {update_config, BreakerId, Config}).

%% @doc 集成到协调器
%% 这个函数将熔断器模块注册到系统协调器中
%%
%% 功能说明：
%% 1. 检查协调器模块是否可用
%% 2. 注册熔断器模块到协调器
%% 3. 声明模块的优先级和功能特性
%% 4. 建立与其他模块的协作关系
%%
%% 返回值：
%% - ok: 集成成功或协调器不可用
%%
%% Java等价概念：
%% 类似于Spring的@Component注册
%% 或者微服务的服务注册与发现
%%
%% 模块特性：
%% - failure_detection: 故障检测能力
%% - auto_recovery: 自动恢复能力
%% - error_tracking: 错误跟踪能力
integrate() ->
    %% 记录集成操作日志
    %% ?SLOG是EMQX的结构化日志宏，类似于Java的Logger
    ?SLOG(info, #{msg => "integrating_circuit_breaker_module"}),

    %% 检查协调器模块是否存在并可用
    %% erlang:function_exported/3检查函数是否导出
    %% 类似于Java中的反射检查方法是否存在
    %%
    %% 在Java中相当于：
    %% try {
    %%     Class<?> coordinatorClass = Class.forName("CoordinatorService");
    %%     Method registerMethod = coordinatorClass.getMethod("registerModule", String.class, Map.class);
    %%     // 调用注册方法
    %% } catch (ClassNotFoundException | NoSuchMethodException e) {
    %%     // 协调器不可用，跳过注册
    %% }
    case erlang:function_exported(emqx_plugin_mongodb_coordinator, register_module, 2) of
        true ->
            %% 协调器可用，注册熔断器模块
            %% 提供模块的元数据信息，包括优先级和功能特性
            emqx_plugin_mongodb_coordinator:register_module(?MODULE, #{
                priority => critical,           % 关键优先级，优先启动和处理
                description => <<"Circuit breaker module">>,  % 模块描述
                features => [failure_detection, auto_recovery, error_tracking]  % 功能特性列表
            });
        false ->
            %% 协调器不可用，跳过注册
            %% 模块仍可独立工作，但无法与其他模块协作
            ok
    end.

%% @doc 记录成功操作（简化版本）
%% 这是record_success/2的简化版本，使用默认参数
%%
%% 功能说明：
%% 1. 检查熔断器服务是否运行
%% 2. 记录一次成功操作到默认熔断器
%% 3. 使用默认的上下文信息
%%
%% 返回值：
%% - ok: 记录成功
%% - {error, not_running}: 熔断器服务未运行
%%
%% Java等价概念：
%% 类似于重载方法或默认参数的方法调用
%%
%% 使用场景：
%% - 快速记录成功操作，无需详细上下文
%% - 简单的成功/失败统计
record_success() ->
    %% 检查熔断器服务进程是否存在
    %% erlang:whereis/1查找注册进程的PID
    %% 类似于Java中的服务发现或进程查找
    %%
    %% 在Java中相当于：
    %% Optional<CircuitBreakerService> service = serviceRegistry.findService("CircuitBreakerService");
    %% if (service.isPresent()) {
    %%     service.get().recordSuccess();
    %% } else {
    %%     throw new ServiceNotAvailableException();
    %% }
    case erlang:whereis(?MODULE) of
        Pid when is_pid(Pid) ->
            %% 服务运行中，发送成功记录消息
            gen_server:cast(Pid, record_success),
            ok;
        _ ->
            %% 服务未运行，返回错误
            {error, not_running}
    end.

%% @doc 记录失败操作（简化版本）
%% 这是record_failure/3的简化版本，使用默认参数
%%
%% 功能说明：
%% 1. 检查熔断器服务是否运行
%% 2. 记录一次失败操作到默认熔断器
%% 3. 使用提供的错误信息
%%
%% 参数说明：
%% - Error: 错误信息，可以是原子、字符串或错误元组
%%
%% 返回值：
%% - ok: 记录成功
%% - {error, not_running}: 熔断器服务未运行
%%
%% Java等价概念：
%% 类似于重载方法或默认参数的方法调用
%%
%% 使用场景：
%% - 快速记录失败操作
%% - 简单的错误统计
record_failure(Error) ->
    %% 检查熔断器服务进程是否存在
    %% 与record_success/0使用相同的检查逻辑
    case erlang:whereis(?MODULE) of
        Pid when is_pid(Pid) ->
            %% 服务运行中，发送失败记录消息
            %% 包含错误信息用于错误分析
            gen_server:cast(Pid, {record_failure, Error}),
            ok;
        _ ->
            %% 服务未运行，返回错误
            {error, not_running}
    end.

%% @doc 检查断路器是否打开
%% 这个函数检查默认熔断器是否处于打开（熔断）状态
%%
%% 功能说明：
%% 1. 检查熔断器服务是否运行
%% 2. 查询默认熔断器的状态
%% 3. 返回是否处于熔断状态
%%
%% 返回值：
%% - true: 熔断器已打开，请求将被拒绝
%% - false: 熔断器关闭或半开，请求可以通过
%%
%% Java等价概念：
%% 类似于Hystrix的isOpen()方法
%% 或者Resilience4j的getState() == OPEN
%%
%% 使用场景：
%% - 在执行操作前检查熔断状态
%% - 实现快速失败逻辑
%% - 监控和告警系统
%%
%% 示例：
%% if (is_open()) {
%%     return {error, circuit_breaker_open};
%% } else {
%%     execute_operation();
%% }
is_open() ->
    %% 检查熔断器服务进程是否存在
    %% 如果服务不存在，默认返回false（不熔断）
    case erlang:whereis(?MODULE) of
        Pid when is_pid(Pid) ->
            %% 服务运行中，查询熔断器状态
            %% 使用同步调用确保获取最新状态
            gen_server:call(Pid, is_open);
        _ ->
            %% 服务未运行，默认不熔断
            %% 这样可以避免因熔断器服务故障导致的级联失败
            {error, not_running}
    end.

%%%===================================================================
%%% gen_server callbacks
%%%===================================================================

%% @doc 初始化回调
%% 这是gen_server的初始化回调函数，在进程启动时被调用
%%
%% 功能说明：
%% 1. 创建ETS表用于高效的熔断器状态存储和并发访问
%% 2. 初始化默认熔断器配置
%% 3. 启动定期清理和监控任务
%% 4. 设置系统监控和上下文收集
%%
%% 参数说明：
%% - [Options]: 启动选项列表，包含熔断器配置
%%
%% 返回值：
%% - {ok, State}: 初始化成功，返回初始状态
%% - {stop, Reason}: 初始化失败，停止进程
%%
%% Java等价概念：
%% 类似于Spring的@PostConstruct方法或构造函数
%% 用于初始化服务的状态和依赖
init([Options]) ->
    %% 创建ETS表用于存储熔断器状态
    %% ETS（Erlang Term Storage）是Erlang的内存数据库
    %% 类似于Java中的ConcurrentHashMap或Redis的内存存储
    %%
    %% 选项说明：
    %% - named_table: 使用宏名作为表名，便于访问
    %% - public: 允许其他进程读写，类似于public字段
    %% - {read_concurrency, true}: 优化并发读取性能
    %%
    %% 在Java中相当于：
    %% private final ConcurrentHashMap<String, CircuitBreaker> breakerRegistry = new ConcurrentHashMap<>();
    ets:new(?BREAKER_REGISTRY, [named_table, public, {read_concurrency, true}]),

    %% 初始化默认熔断器配置
    %% 从启动选项中提取配置，如果没有提供则使用默认值
    %% 类似于Java中的配置对象初始化
    %%
    %% 在Java中相当于：
    %% CircuitBreakerConfig defaultConfig = CircuitBreakerConfig.builder()
    %%     .failureThreshold(options.getOrDefault("failure_threshold", DEFAULT_FAILURE_THRESHOLD))
    %%     .resetTimeout(options.getOrDefault("reset_timeout", DEFAULT_RESET_TIMEOUT))
    %%     .build();
    DefaultConfig = #{
        failure_threshold => maps:get(failure_threshold, Options, ?DEFAULT_FAILURE_THRESHOLD),
        reset_timeout => maps:get(reset_timeout, Options, ?DEFAULT_RESET_TIMEOUT),
        half_open_ratio => maps:get(half_open_ratio, Options, ?DEFAULT_HALF_OPEN_RATIO),
        recovery_step => maps:get(recovery_step, Options, ?DEFAULT_RECOVERY_STEP),
        recovery_interval => maps:get(recovery_interval, Options, ?DEFAULT_RECOVERY_INTERVAL)
    },

    %% 存储默认配置到ETS表
    %% 使用ETS存储配置可以提供高并发访问性能
    %% 类似于将配置存储到共享缓存中
    ets:insert(?BREAKER_REGISTRY, {default_config, DefaultConfig}),

    % 启动健康检查定时器
    erlang:send_after(?CIRCUIT_BREAKER_CHECK_INTERVAL, self(), check_system_health),

    {ok, #{default_config => DefaultConfig}}.

%% @doc 处理同步调用 - 检查熔断器状态
%% 这是熔断器检查请求的核心处理逻辑
%%
%% 处理流程：
%% 1. 获取或创建指定的熔断器实例
%% 2. 更新熔断器的上下文信息（系统负载、网络延迟等）
%% 3. 根据当前状态和上下文决定是否允许请求通过
%% 4. 保存更新后的熔断器状态
%% 5. 返回决策结果
handle_call({check_breaker, BreakerId, Context}, _From, State) ->
    %% 获取或创建熔断器实例
    %% 如果熔断器不存在，会使用默认配置创建新的熔断器
    %% 类似于Java中的懒加载或单例模式
    %%
    %% 在Java中相当于：
    %% CircuitBreaker breaker = circuitBreakerRegistry.computeIfAbsent(breakerId,
    %%     id -> CircuitBreaker.ofDefaults(id));
    CB = get_or_create_breaker(BreakerId, State),

    %% 更新熔断器的上下文信息
    %% 将当前的系统状态（负载、延迟等）更新到熔断器中
    %% 用于上下文感知的熔断决策
    %%
    %% 在Java中相当于：
    %% breaker.updateContext(systemLoad, networkLatency, memoryUsage);
    UpdatedCB = update_breaker_context(CB, Context),

    %% 检查熔断器状态并做出决策
    %% 根据当前状态（closed/half_open/open）和上下文信息
    %% 决定是否允许请求通过
    %%
    %% 在Java中相当于：
    %% CircuitBreakerDecision decision = breaker.checkState();
    %% if (decision.isAllowed()) {
    %%     return allowRequest();
    %% } else {
    %%     return rejectRequest(decision.getReason());
    %% }
    Result = check_breaker_state(UpdatedCB),

    %% 保存更新后的熔断器状态到ETS表
    %% 确保状态变化被持久化，供后续请求使用
    %% 类似于Java中的状态持久化或缓存更新
    save_breaker(UpdatedCB),

    %% 返回决策结果给调用者
    %% Result格式：{allow, Actions} 或 {reject, Reason}
    {reply, Result, State};

handle_call({reset_breaker, BreakerId}, _From, State) ->
    % 重置熔断器
    case ets:lookup(?BREAKER_REGISTRY, BreakerId) of
        [{_, CB}] ->
            NewCB = CB#circuit_breaker{
                state = closed,
                failure_count = 0,
                health_ratio = 1.0,
                error_types = #{}
            },
            ets:insert(?BREAKER_REGISTRY, {BreakerId, NewCB}),
            {reply, ok, State};
        [] ->
            {reply, {error, not_found}, State}
    end;

handle_call({get_status, BreakerId}, _From, State) ->
    % 获取熔断器状态
    case ets:lookup(?BREAKER_REGISTRY, BreakerId) of
        [{_, CB}] ->
            Status = #{
                id => CB#circuit_breaker.id,
                state => CB#circuit_breaker.state,
                failure_count => CB#circuit_breaker.failure_count,
                health_ratio => CB#circuit_breaker.health_ratio,
                error_types => CB#circuit_breaker.error_types,
                context => CB#circuit_breaker.context
            },
            {reply, {ok, Status}, State};
        [] ->
            {reply, {error, not_found}, State}
    end;

handle_call({update_config, BreakerId, Config}, _From, State) ->
    % 更新熔断器配置
    case ets:lookup(?BREAKER_REGISTRY, BreakerId) of
        [{_, CB}] ->
            NewCB = CB#circuit_breaker{
                failure_threshold = maps:get(failure_threshold, Config, CB#circuit_breaker.failure_threshold),
                reset_timeout = maps:get(reset_timeout, Config, CB#circuit_breaker.reset_timeout),
                half_open_ratio = maps:get(half_open_ratio, Config, CB#circuit_breaker.half_open_ratio),
                recovery_step = maps:get(recovery_step, Config, CB#circuit_breaker.recovery_step),
                recovery_interval = maps:get(recovery_interval, Config, CB#circuit_breaker.recovery_interval),
                error_thresholds = maps:get(error_thresholds, Config, CB#circuit_breaker.error_thresholds)
            },
            ets:insert(?BREAKER_REGISTRY, {BreakerId, NewCB}),
            {reply, ok, State};
        [] ->
            % 创建新的熔断器
            DefaultConfig = maps:get(default_config, State),
            CB = #circuit_breaker{
                id = BreakerId,
                failure_threshold = maps:get(failure_threshold, Config, maps:get(failure_threshold, DefaultConfig)),
                reset_timeout = maps:get(reset_timeout, Config, maps:get(reset_timeout, DefaultConfig)),
                half_open_ratio = maps:get(half_open_ratio, Config, maps:get(half_open_ratio, DefaultConfig)),
                recovery_step = maps:get(recovery_step, Config, maps:get(recovery_step, DefaultConfig)),
                recovery_interval = maps:get(recovery_interval, Config, maps:get(recovery_interval, DefaultConfig)),
                error_thresholds = maps:get(error_thresholds, Config, #{})
            },
            ets:insert(?BREAKER_REGISTRY, {BreakerId, CB}),
            {reply, ok, State}
    end;

handle_call(is_open, _From, State) ->
    % 检查是否有任何熔断器处于开启状态
    IsOpen = ets:foldl(
        fun({_Id, CB}, Acc) ->
            case CB#circuit_breaker.state of
                open -> true;
                _ -> Acc
            end
        end,
        false,
        ?BREAKER_REGISTRY
    ),
    {reply, IsOpen, State};

handle_call(get_system_health, _From, State) ->
    % 获取系统健康状态
    Health = get_system_health(),
    {reply, Health, State};

handle_call(stop, _From, State) ->
    {stop, normal, ok, State};

handle_call(_Request, _From, State) ->
    {reply, {error, unknown_call}, State}.

%% @doc 处理异步调用
handle_cast({record_success, BreakerId, Context}, State) ->
    % 获取熔断器
    case ets:lookup(?BREAKER_REGISTRY, BreakerId) of
        [{_, CB}] ->
            % 更新上下文
            UpdatedCB = update_breaker_context(CB, Context),

            % 处理成功记录
            NewCB = handle_success(UpdatedCB),

            % 保存更新后的熔断器
            save_breaker(NewCB);
        [] ->
            % 熔断器不存在，不做任何操作
            ok
    end,
    {noreply, State};

handle_cast({record_failure, BreakerId, ErrorType, Context}, State) ->
    % 获取或创建熔断器
    CB = get_or_create_breaker(BreakerId, State),

    % 更新上下文
    UpdatedCB = update_breaker_context(CB, Context),

    % 处理失败记录
    NewCB = handle_failure(UpdatedCB, ErrorType),

    % 保存更新后的熔断器
    save_breaker(NewCB),

    {noreply, State};

handle_cast(_Msg, State) ->
    {noreply, State}.

%% @doc 处理消息
handle_info(check_system_health, State) ->
    try
        % 获取系统健康状态
        SystemHealth = get_system_health(),

        % 更新所有熔断器的系统上下文
        update_all_breakers_context(SystemHealth),

        % 检查半开状态的熔断器，进行渐进式恢复
        progress_recovery_for_half_open_breakers()
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "circuit_breaker_health_check_failed",
                error => E,
                reason => R,
                stacktrace => S
            })
    end,

    % 重置定时器
    erlang:send_after(?CIRCUIT_BREAKER_CHECK_INTERVAL, self(), check_system_health),

    {noreply, State};

handle_info(_Info, State) ->
    {noreply, State}.

%% @doc 终止回调
terminate(_Reason, _State) ->
    % 删除ETS表
    catch ets:delete(?BREAKER_REGISTRY),
    ok.

%% @doc 代码更新回调
code_change(_OldVsn, State, _Extra) ->
    {ok, State}.

%%%===================================================================
%%% 内部函数
%%%===================================================================

%% @doc 获取或创建熔断器
get_or_create_breaker(BreakerId, State) ->
    case ets:lookup(?BREAKER_REGISTRY, BreakerId) of
        [{_, CB}] ->
            CB;
        [] ->
            % 获取默认配置
            DefaultConfig = maps:get(default_config, State),

            % 创建新的熔断器
            CB = #circuit_breaker{
                id = BreakerId,
                failure_threshold = maps:get(failure_threshold, DefaultConfig),
                reset_timeout = maps:get(reset_timeout, DefaultConfig),
                half_open_ratio = maps:get(half_open_ratio, DefaultConfig),
                recovery_step = maps:get(recovery_step, DefaultConfig),
                recovery_interval = maps:get(recovery_interval, DefaultConfig)
            },

            % 保存熔断器
            ets:insert(?BREAKER_REGISTRY, {BreakerId, CB}),

            CB
    end.

%% @doc 检查熔断器状态
check_breaker_state(CB) ->
    Now = erlang:system_time(millisecond),

    case CB#circuit_breaker.state of
        closed ->
            % 关闭状态，允许请求通过
            {ok, CB};
        open ->
            % 开启状态，检查是否可以转为半开状态
            TimeSinceFailure = Now - CB#circuit_breaker.last_failure_time,
            if
                TimeSinceFailure >= CB#circuit_breaker.reset_timeout ->
                    % 转为半开状态
                    {ok, CB#circuit_breaker{
                        state = half_open,
                        health_ratio = CB#circuit_breaker.half_open_ratio
                    }};
                true ->
                    % 维持开启状态，拒绝请求
                    {error, circuit_open}
            end;
        half_open ->
            % 半开状态，根据健康比率决定是否允许请求通过
            case rand:uniform() < CB#circuit_breaker.health_ratio of
                true -> {ok, CB};
                false -> {error, circuit_half_open}
            end
    end.

%% @doc 处理成功记录
handle_success(CB = #circuit_breaker{state = closed}) ->
    % 已经关闭，保持状态
    CB;
handle_success(CB = #circuit_breaker{state = half_open}) ->
    % 半开状态下成功，增加健康比率
    NewRatio = min(1.0, CB#circuit_breaker.health_ratio + CB#circuit_breaker.recovery_step),
    if
        NewRatio >= 0.9 ->
            % 健康比率足够高，完全关闭断路器
            CB#circuit_breaker{
                state = closed,
                failure_count = 0,
                health_ratio = 1.0
            };
        true ->
            % 继续半开状态，但提高健康比率
            CB#circuit_breaker{
                health_ratio = NewRatio
            }
    end;
handle_success(CB = #circuit_breaker{state = open}) ->
    % 开启状态下成功（异常情况），转为半开状态
    CB#circuit_breaker{
        state = half_open,
        health_ratio = CB#circuit_breaker.half_open_ratio
    }.

%% @doc 处理失败记录
handle_failure(CB, ErrorType) ->
    Now = erlang:system_time(millisecond),

    % 更新错误类型统计
    NewErrorTypes = maps:update_with(
        ErrorType,
        fun(Count) -> Count + 1 end,
        1,
        CB#circuit_breaker.error_types
    ),

    % 更新错误历史
    NewErrorHistory = [{Now, ErrorType} | CB#circuit_breaker.error_history],
    % 限制历史记录大小
    TrimmedErrorHistory = lists:sublist(NewErrorHistory, ?MAX_ERROR_HISTORY),

    % 增加失败计数
    NewFailureCount = CB#circuit_breaker.failure_count + 1,

    % 检查是否达到失败阈值
    NewState = case CB#circuit_breaker.state of
        closed when NewFailureCount >= CB#circuit_breaker.failure_threshold ->
            % 检查错误类型是否达到特定阈值
            case check_error_type_threshold(ErrorType, NewErrorTypes, CB) of
                true ->
                    % 达到阈值，开启断路器
                    open;
                false ->
                    % 未达到特定错误类型阈值，保持关闭状态
                    closed
            end;
        half_open ->
            % 半开状态下失败，重新开启断路器
            open;
        _ ->
            % 其他情况保持当前状态
            CB#circuit_breaker.state
    end,

    % 更新熔断器状态
    CB#circuit_breaker{
        state = NewState,
        failure_count = NewFailureCount,
        last_failure_time = Now,
        error_types = NewErrorTypes,
        error_history = TrimmedErrorHistory,
        health_ratio = case NewState of
            open -> 0.0;
            half_open -> CB#circuit_breaker.half_open_ratio;
            closed -> 1.0
        end
    }.

%% @doc 检查错误类型是否达到阈值
check_error_type_threshold(ErrorType, ErrorTypes, CB) ->
    % 获取总错误数
    TotalErrors = maps:fold(fun(_, Count, Acc) -> Acc + Count end, 0, ErrorTypes),

    % 空检查，防止除零错误
    case TotalErrors of
        0 ->
            % 没有错误，返回false
            false;
        _ ->
    % 获取当前错误类型数量
    TypeCount = maps:get(ErrorType, ErrorTypes, 0),

    % 计算错误类型比率
    TypeRatio = TypeCount / TotalErrors,

    % 获取错误类型阈值
            DefaultThreshold = ?DEFAULT_ERROR_THRESHOLD_RATIO,
            Threshold = try
                % 先从错误类型特定阈值中查找
                maps:get(ErrorType, CB#circuit_breaker.error_thresholds,
                    % 如果没有，使用默认阈值
                    maps:get(default, CB#circuit_breaker.error_thresholds, DefaultThreshold))
            catch
                _:_ -> DefaultThreshold
            end,

    % 检查是否达到阈值
            TypeRatio >= Threshold
    end.

%% @doc 更新熔断器上下文
update_breaker_context(CB, Context) ->
    % 合并上下文
    NewContext = maps:merge(CB#circuit_breaker.context, Context),
    CB#circuit_breaker{context = NewContext}.

%% @doc 保存熔断器
save_breaker(CB) ->
    ets:insert(?BREAKER_REGISTRY, {CB#circuit_breaker.id, CB}).

%% @doc 获取系统健康状态 - 优化版
%% 修复监控模块集成问题，提供更可靠的系统健康状态获取
get_system_health() ->
    % 获取CPU使用率 - 增强错误处理
    CpuUsage = get_safe_cpu_usage(),

    % 获取内存使用率 - 增强错误处理
    MemUsage = get_safe_memory_usage(),

    % 获取网络延迟 - 优化实现
    NetworkLatency = get_safe_network_latency(),

    % 获取连接池状态 - 增强检查
    PoolStatus = get_safe_pool_status(),

    % 获取响应时间统计 - 新增
    AvgResponseTime = get_safe_avg_response_time(),

    % 获取并发请求数 - 新增
    ConcurrentRequests = get_safe_concurrent_requests(),

    % 计算错误率 - 新增
    ErrorRate = get_safe_error_rate(),

    #{
        system_load => CpuUsage,
        memory_usage => MemUsage,
        network_latency => NetworkLatency,
        pool_status => PoolStatus,
        avg_response_time => AvgResponseTime,
        concurrent_requests => ConcurrentRequests,
        error_rate => ErrorRate,
        timestamp => erlang:system_time(millisecond),
        node => node()
    }.

%% @doc 安全获取CPU使用率 - 暂时禁用监控模块
get_safe_cpu_usage() ->
    try
        % 暂时禁用监控模块启动，专注于解决消息持久化问题
        % case catch emqx_plugin_mongodb_monitor:start() of
        %     ok -> ok;
        %     _ -> ok  % 可能已经启动
        % end,

        % 暂时禁用监控模块调用，直接使用回退方法
        % case catch emqx_plugin_mongodb_monitor:get_cpu_usage() of
        %     CpuUsage when is_number(CpuUsage) ->
        %         % 确保值在合理范围内
        %         max(0.0, min(1.0, CpuUsage));
        %     _ ->
        %         % 回退到系统调用
        %         get_fallback_cpu_usage()
        % end

        % 直接使用回退方法，避免启动监控模块
        get_fallback_cpu_usage()
    catch
        E:R:S ->
            ?SLOG(warning, #{
                msg => "cpu_usage_detection_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            0.5  % 保守默认值
    end.

%% @doc 安全获取内存使用率 - 暂时禁用监控模块
get_safe_memory_usage() ->
    try
        % 暂时禁用监控模块调用，直接使用回退方法
        % case catch emqx_plugin_mongodb_monitor:get_memory_usage() of
        %     MemUsage when is_number(MemUsage) ->
        %         max(0.0, min(1.0, MemUsage));
        %     _ ->
        %         % 回退到VM内存比例
        %         get_fallback_memory_usage()
        % end

        % 直接使用回退方法，避免调用监控模块
        get_fallback_memory_usage()
    catch
        E:R:S ->
            ?SLOG(warning, #{
                msg => "memory_usage_detection_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            0.5  % 保守默认值
    end.

%% @doc 安全获取网络延迟
get_safe_network_latency() ->
    try
        % 获取MongoDB连接配置
        {Host, Port} = get_mongodb_connection_info(),

        % 测试连接延迟
        StartTime = erlang:system_time(microsecond),
        case gen_tcp:connect(Host, Port, [binary, {packet, 0}], 2000) of
            {ok, Socket} ->
                ConnectTime = erlang:system_time(microsecond) - StartTime,
                gen_tcp:close(Socket),
                max(1, ConnectTime div 1000);  % 微秒转毫秒，最小1ms
            {error, _} ->
                5000  % 连接失败，返回高延迟
        end
    catch
        _:_ -> 1000  % 默认1000ms
    end.

%% @doc 安全获取连接池状态
get_safe_pool_status() ->
    try
        % 检查MongoDB资源状态
        case emqx_resource:get_instance(?PLUGIN_MONGODB_RESOURCE_ID) of
            {ok, _} ->
                % 资源存在，检查连接状态
                case emqx_resource:health_check(?PLUGIN_MONGODB_RESOURCE_ID) of
                    {ok, _} -> normal;
                    _ -> degraded
                end;
            _ ->
                disconnected
        end
    catch
        _:_ ->
            % 回退检查
            case whereis(emqx_plugin_mongodb_connection) of
                undefined -> disconnected;
                _ -> unknown
            end
    end.

%% @doc 更新所有熔断器的系统上下文
update_all_breakers_context(SystemHealth) ->
    % 获取所有熔断器
    ets:foldl(
        fun({Key, CB}, _) when is_record(CB, circuit_breaker) ->
            % 更新上下文
            NewCB = update_breaker_context(CB, SystemHealth),

            % 动态调整熔断器参数
            AdjustedCB = adjust_breaker_parameters(NewCB),

            % 保存更新后的熔断器
            save_breaker(AdjustedCB),

            ok;
        (_, Acc) ->
            Acc
        end,
        ok,
        ?BREAKER_REGISTRY
    ).

%% @doc 动态调整熔断器参数
adjust_breaker_parameters(CB) ->
    % 获取系统负载
    SystemLoad = maps:get(system_load, CB#circuit_breaker.context, 0.0),

    % 获取网络延迟
    NetworkLatency = maps:get(network_latency, CB#circuit_breaker.context, 0),

    % 根据系统状态调整参数
    NewFailureThreshold = if
        SystemLoad > 0.9 -> max(2, CB#circuit_breaker.failure_threshold - 2);
        SystemLoad > 0.7 -> max(3, CB#circuit_breaker.failure_threshold - 1);
        SystemLoad < 0.3 -> min(10, CB#circuit_breaker.failure_threshold + 1);
        true -> CB#circuit_breaker.failure_threshold
    end,

    NewResetTimeout = if
        NetworkLatency > 500 -> min(60000, CB#circuit_breaker.reset_timeout * 2);
        NetworkLatency < 50 -> max(5000, CB#circuit_breaker.reset_timeout div 2);
        true -> CB#circuit_breaker.reset_timeout
    end,

    % 更新熔断器参数
    CB#circuit_breaker{
        failure_threshold = NewFailureThreshold,
        reset_timeout = NewResetTimeout
    }.

%% @doc 对半开状态的熔断器进行渐进式恢复
progress_recovery_for_half_open_breakers() ->
    Now = erlang:system_time(millisecond),

    % 获取所有半开状态的熔断器
    ets:foldl(
        fun({Key, CB}, _) when is_record(CB, circuit_breaker), CB#circuit_breaker.state =:= half_open ->
            % 检查上次失败时间
            TimeSinceFailure = Now - CB#circuit_breaker.last_failure_time,

            % 如果超过恢复间隔，增加健康比率
            if
                TimeSinceFailure >= CB#circuit_breaker.recovery_interval ->
                    NewRatio = min(1.0, CB#circuit_breaker.health_ratio + CB#circuit_breaker.recovery_step),

                    NewState = if
                        NewRatio >= 0.9 -> closed;
                        true -> half_open
                    end,

                    NewCB = CB#circuit_breaker{
                        health_ratio = NewRatio,
                        state = NewState,
                        failure_count = case NewState of
                            closed -> 0;
                            _ -> CB#circuit_breaker.failure_count
                        end
                    },

                    % 保存更新后的熔断器
                    save_breaker(NewCB);
                true ->
                    % 未达到恢复间隔，不做任何操作
                    ok
            end;
        (_, Acc) ->
            Acc
        end,
        ok,
        ?BREAKER_REGISTRY
    ).

%% @doc 安全获取平均响应时间
get_safe_avg_response_time() ->
    try
        % 从ETS表中获取响应时间统计
        case ets:info(?RESPONSE_TIME_TAB) of
            undefined ->
                % 表不存在，创建并返回默认值
                create_response_time_table(),
                100.0;  % 默认100ms
            _ ->
                calculate_avg_response_time()
        end
    catch
        _:_ -> 100.0  % 默认100ms
    end.

%% @doc 安全获取并发请求数
get_safe_concurrent_requests() ->
    try
        case ets:info(?CONCURRENT_TAB) of
            undefined ->
                create_concurrent_table(),
                0;
            _ ->
                case ets:lookup(?CONCURRENT_TAB, concurrent_requests) of
                    [{_, Count}] when is_integer(Count) -> Count;
                    _ -> 0
                end
        end
    catch
        _:_ -> 0
    end.

%% @doc 安全获取错误率
get_safe_error_rate() ->
    try
        case ets:info(?ERROR_RATE_TAB) of
            undefined ->
                create_error_rate_table(),
                0.0;
            _ ->
                calculate_error_rate()
        end
    catch
        _:_ -> 0.0
    end.

%% @doc 获取回退CPU使用率
get_fallback_cpu_usage() ->
    try
        % 使用调度器使用率作为CPU使用率的近似值
        case erlang:statistics(scheduler_wall_time) of
            undefined ->
                % 启用调度器墙时间统计
                erlang:system_flag(scheduler_wall_time, true),
                timer:sleep(100),  % 等待一小段时间
                get_fallback_cpu_usage();
            SchedulerTimes ->
                calculate_scheduler_usage(SchedulerTimes)
        end
    catch
        _:_ -> 0.5
    end.

%% @doc 获取回退内存使用率
get_fallback_memory_usage() ->
    try
        MemInfo = erlang:memory(),
        Total = proplists:get_value(total, MemInfo, 1),
        Processes = proplists:get_value(processes, MemInfo, 0),
        case Total of
            0 -> 0.3;
            _ -> min(0.8, max(0.1, Processes / Total))
        end
    catch
        _:_ -> 0.3
    end.

%% @doc 获取MongoDB连接信息
get_mongodb_connection_info() ->
    try
        % 从配置中获取MongoDB连接信息
        Config = application:get_env(emqx_plugin_mongodb, config, #{}),
        Connection = maps:get(connection, Config, #{}),

        % 获取主机和端口
        Hosts = maps:get(bootstrap_hosts, Connection, ["localhost:27017"]),
        case Hosts of
            [FirstHost | _] ->
                parse_host_port(FirstHost);
            _ ->
                {"localhost", 27017}
        end
    catch
        _:_ -> {"localhost", 27017}
    end.

%% @doc 解析主机端口字符串
parse_host_port(HostPort) when is_list(HostPort) ->
    case string:split(HostPort, ":") of
        [Host, PortStr] ->
            Port = try
                list_to_integer(PortStr)
            catch
                _:_ -> 27017
            end,
            {Host, Port};
        [Host] ->
            {Host, 27017};
        _ ->
            {"localhost", 27017}
    end;
parse_host_port(HostPort) when is_binary(HostPort) ->
    parse_host_port(binary_to_list(HostPort));
parse_host_port(_) ->
    {"localhost", 27017}.

%% @doc 创建响应时间统计表
create_response_time_table() ->
    try
        ets:new(?RESPONSE_TIME_TAB, [named_table, public, {write_concurrency, true}])
    catch
        error:badarg ->
            % 表可能已存在
            ?RESPONSE_TIME_TAB
    end.

%% @doc 创建并发请求统计表
create_concurrent_table() ->
    try
        ets:new(?CONCURRENT_TAB, [named_table, public, {write_concurrency, true}]),
        ets:insert(?CONCURRENT_TAB, {concurrent_requests, 0})
    catch
        error:badarg ->
            % 表可能已存在
            ?CONCURRENT_TAB
    end.

%% @doc 创建错误率统计表
create_error_rate_table() ->
    try
        ets:new(?ERROR_RATE_TAB, [named_table, public, {write_concurrency, true}])
    catch
        error:badarg ->
            % 表可能已存在
            ?ERROR_RATE_TAB
    end.

%% @doc 计算平均响应时间
calculate_avg_response_time() ->
    try
        Now = erlang:system_time(millisecond),
        TimeWindow = 60000,  % 1分钟窗口
        CutoffTime = Now - TimeWindow,

        % 获取时间窗口内的响应时间
        ResponseTimes = ets:select(?RESPONSE_TIME_TAB, [
            {{'$1', '$2', '$3'}, [{'>', '$3', CutoffTime}], ['$2']}
        ]),

        case ResponseTimes of
            [] -> 100.0;  % 默认100ms
            _ ->
                Sum = lists:sum(ResponseTimes),
                Count = length(ResponseTimes),
                Sum / Count
        end
    catch
        _:_ -> 100.0
    end.

%% @doc 计算错误率
calculate_error_rate() ->
    try
        Now = erlang:system_time(millisecond),
        TimeWindow = 60000,  % 1分钟窗口
        CutoffTime = Now - TimeWindow,

        % 获取时间窗口内的请求统计
        AllRequests = ets:select(?ERROR_RATE_TAB, [
            {{'$1', '$2', '$3'}, [{'>', '$3', CutoffTime}], ['$2']}
        ]),

        case AllRequests of
            [] -> 0.0;
            _ ->
                ErrorCount = length([R || R <- AllRequests, R =:= error]),
                TotalCount = length(AllRequests),
                case TotalCount of
                    0 -> 0.0;
                    _ -> ErrorCount / TotalCount
                end
        end
    catch
        _:_ -> 0.0
    end.

%% @doc 计算调度器使用率
calculate_scheduler_usage(SchedulerTimes) ->
    try
        % 简化的调度器使用率计算
        TotalActive = lists:sum([Active || {_, Active, _} <- SchedulerTimes]),
        TotalTime = lists:sum([Total || {_, _, Total} <- SchedulerTimes]),

        case TotalTime of
            0 -> 0.5;
            _ -> min(1.0, max(0.0, TotalActive / TotalTime))
        end
    catch
        _:_ -> 0.5
    end.
