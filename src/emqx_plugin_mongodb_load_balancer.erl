%% @doc EMQX MongoDB插件智能负载均衡模块
%% 提供多种负载均衡算法和动态调整策略
%% 
%% 功能特性：
%% 1. 多种负载均衡算法（轮询、加权轮询、最少连接、响应时间）
%% 2. 动态权重调整
%% 3. 健康检查集成
%% 4. 故障自动切换
%% 5. 负载预测和调整
%% 6. 性能监控集成

-module(emqx_plugin_mongodb_load_balancer).

-behaviour(gen_server).

%% API exports
-export([
    start_link/1,
    stop/0,
    select_connection/1,
    select_connection_pool/1,
    update_connection_weight/2,
    set_load_balancing_algorithm/1,
    get_load_balancing_stats/0,
    enable_dynamic_adjustment/0,
    disable_dynamic_adjustment/0,
    force_rebalance/0
]).

%% gen_server callbacks
-export([
    init/1,
    handle_call/3,
    handle_cast/2,
    handle_info/2,
    terminate/2,
    code_change/3
]).

%% Internal exports
-export([
    round_robin_select/2,
    weighted_round_robin_select/2,
    least_connections_select/1,
    response_time_select/1,
    adaptive_select/3,
    update_connection_stats/2,
    calculate_dynamic_weights/2
]).

-include("emqx_plugin_mongodb.hrl").
-include_lib("emqx/include/logger.hrl").

%% 负载均衡算法类型
-type lb_algorithm() :: round_robin | weighted_round_robin | least_connections | 
                       response_time | adaptive | consistent_hash.

%% 状态记录定义
-record(lb_state, {
    algorithm = round_robin,         % 当前使用的负载均衡算法
    dynamic_adjustment = false,      % 是否启用动态调整
    connection_weights = #{},        % 连接权重映射
    connection_stats = #{},          % 连接统计信息
    round_robin_index = 0,          % 轮询索引
    adjustment_interval = 30000,     % 动态调整间隔
    last_adjustment_time = 0,        % 上次调整时间
    rebalance_threshold = 0.2        % 重平衡阈值
}).

%%====================================================================
%% API functions
%%====================================================================

%% @doc 启动负载均衡服务
-spec start_link(map()) -> {ok, pid()} | {error, term()}.
start_link(Options) ->
    gen_server:start_link({local, ?MODULE}, ?MODULE, [Options], []).

%% @doc 停止负载均衡服务
-spec stop() -> ok.
stop() ->
    gen_server:stop(?MODULE).

%% @doc 选择连接
-spec select_connection(term()) -> {ok, pid()} | {error, term()}.
select_connection(Request) ->
    gen_server:call(?MODULE, {select_connection, Request}).

%% @doc 选择连接池
-spec select_connection_pool(term()) -> {ok, atom()} | {error, term()}.
select_connection_pool(Request) ->
    gen_server:call(?MODULE, {select_connection_pool, Request}).

%% @doc 更新连接权重
-spec update_connection_weight(pid(), float()) -> ok.
update_connection_weight(Connection, Weight) ->
    gen_server:cast(?MODULE, {update_connection_weight, Connection, Weight}).

%% @doc 设置负载均衡算法
-spec set_load_balancing_algorithm(lb_algorithm()) -> ok.
set_load_balancing_algorithm(Algorithm) ->
    gen_server:cast(?MODULE, {set_algorithm, Algorithm}).

%% @doc 获取负载均衡统计
-spec get_load_balancing_stats() -> {ok, map()} | {error, term()}.
get_load_balancing_stats() ->
    gen_server:call(?MODULE, get_load_balancing_stats).

%% @doc 启用动态调整
-spec enable_dynamic_adjustment() -> ok.
enable_dynamic_adjustment() ->
    gen_server:cast(?MODULE, enable_dynamic_adjustment).

%% @doc 禁用动态调整
-spec disable_dynamic_adjustment() -> ok.
disable_dynamic_adjustment() ->
    gen_server:cast(?MODULE, disable_dynamic_adjustment).

%% @doc 强制重平衡
-spec force_rebalance() -> ok.
force_rebalance() ->
    gen_server:cast(?MODULE, force_rebalance).

%%====================================================================
%% gen_server callbacks
%%====================================================================

%% @doc 初始化负载均衡服务
init([Options]) ->
    try
        % 初始化ETS表
        init_load_balancer_tables(),
        
        % 解析配置选项
        State = parse_lb_options(Options, #lb_state{}),
        
        % 启动动态调整定时器（如果启用）
        case State#lb_state.dynamic_adjustment of
            true ->
                schedule_dynamic_adjustment(State#lb_state.adjustment_interval);
            false ->
                ok
        end,
        
        ?SLOG(info, #{
            msg => "load_balancer_started",
            algorithm => State#lb_state.algorithm,
            dynamic_adjustment => State#lb_state.dynamic_adjustment
        }),
        
        {ok, State}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "load_balancer_init_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {stop, {init_failed, R}}
    end.

%% @doc 处理同步调用
handle_call({select_connection, Request}, _From, State) ->
    try
        Result = do_select_connection(Request, State),
        {reply, Result, State}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "select_connection_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {reply, {error, R}, State}
    end;

handle_call({select_connection_pool, Request}, _From, State) ->
    try
        Result = do_select_connection_pool(Request, State),
        {reply, Result, State}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "select_connection_pool_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {reply, {error, R}, State}
    end;

handle_call(get_load_balancing_stats, _From, State) ->
    try
        Stats = generate_load_balancing_stats(State),
        {reply, {ok, Stats}, State}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "get_load_balancing_stats_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {reply, {error, R}, State}
    end;

handle_call(_Request, _From, State) ->
    {reply, {error, unknown_request}, State}.

%% @doc 处理异步消息
handle_cast({update_connection_weight, Connection, Weight}, State) ->
    NewWeights = maps:put(Connection, Weight, State#lb_state.connection_weights),
    NewState = State#lb_state{connection_weights = NewWeights},
    ?SLOG(debug, #{
        msg => "connection_weight_updated",
        connection => Connection,
        weight => Weight
    }),
    {noreply, NewState};

handle_cast({set_algorithm, Algorithm}, State) ->
    NewState = State#lb_state{algorithm = Algorithm},
    ?SLOG(info, #{
        msg => "load_balancing_algorithm_changed",
        old_algorithm => State#lb_state.algorithm,
        new_algorithm => Algorithm
    }),
    {noreply, NewState};

handle_cast(enable_dynamic_adjustment, State) ->
    NewState = State#lb_state{dynamic_adjustment = true},
    schedule_dynamic_adjustment(State#lb_state.adjustment_interval),
    ?SLOG(info, #{msg => "dynamic_adjustment_enabled"}),
    {noreply, NewState};

handle_cast(disable_dynamic_adjustment, State) ->
    NewState = State#lb_state{dynamic_adjustment = false},
    ?SLOG(info, #{msg => "dynamic_adjustment_disabled"}),
    {noreply, NewState};

handle_cast(force_rebalance, State) ->
    NewState = perform_rebalance(State),
    ?SLOG(info, #{msg => "forced_rebalance_completed"}),
    {noreply, NewState};

handle_cast(_Msg, State) ->
    {noreply, State}.

%% @doc 处理定时器和其他消息
handle_info(dynamic_adjustment_timer, State) ->
    try
        % 执行动态调整
        NewState = perform_dynamic_adjustment(State),
        
        % 调度下次动态调整
        case NewState#lb_state.dynamic_adjustment of
            true ->
                schedule_dynamic_adjustment(NewState#lb_state.adjustment_interval);
            false ->
                ok
        end,
        
        {noreply, NewState}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "dynamic_adjustment_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            % 即使出错也要调度下次调整
            case State#lb_state.dynamic_adjustment of
                true ->
                    schedule_dynamic_adjustment(State#lb_state.adjustment_interval);
                false ->
                    ok
            end,
            {noreply, State}
    end;

handle_info(_Info, State) ->
    {noreply, State}.

%% @doc 服务终止处理
terminate(_Reason, _State) ->
    ?SLOG(info, #{msg => "load_balancer_terminated"}),
    ok.

%% @doc 代码热更新
code_change(_OldVsn, State, _Extra) ->
    {ok, State}.

%%====================================================================
%% Internal functions
%%====================================================================

%% @doc 初始化负载均衡相关的ETS表
init_load_balancer_tables() ->
    % 连接统计表
    ets:new(?LOAD_BALANCER_STATS_TAB, [
        named_table, public, set,
        {write_concurrency, true},
        {read_concurrency, true}
    ]),

    % 连接权重表
    ets:new(?CONNECTION_WEIGHTS_TAB, [
        named_table, public, set,
        {write_concurrency, true},
        {read_concurrency, true}
    ]),

    % 负载均衡历史表
    ets:new(?LOAD_BALANCE_HISTORY_TAB, [
        named_table, public, ordered_set,
        {write_concurrency, true},
        {read_concurrency, true}
    ]).

%% @doc 解析负载均衡配置选项
parse_lb_options(Options, State) ->
    Algorithm = maps:get(algorithm, Options, round_robin),
    DynamicAdjustment = maps:get(dynamic_adjustment, Options, false),
    AdjustmentInterval = maps:get(adjustment_interval, Options, 30000),
    RebalanceThreshold = maps:get(rebalance_threshold, Options, 0.2),

    State#lb_state{
        algorithm = Algorithm,
        dynamic_adjustment = DynamicAdjustment,
        adjustment_interval = AdjustmentInterval,
        rebalance_threshold = RebalanceThreshold
    }.

%% @doc 调度动态调整任务
schedule_dynamic_adjustment(Interval) ->
    erlang:send_after(Interval, self(), dynamic_adjustment_timer).

%% @doc 执行连接选择
do_select_connection(Request, State) ->
    try
        % 获取可用连接列表
        AvailableConnections = get_available_connections(),

        case AvailableConnections of
            [] ->
                {error, no_available_connections};
            _ ->
                % 根据算法选择连接
                SelectedConnection = select_by_algorithm(
                    State#lb_state.algorithm,
                    AvailableConnections,
                    Request,
                    State
                ),

                % 更新连接统计
                update_connection_usage(SelectedConnection),

                {ok, SelectedConnection}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "do_select_connection_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, connection_selection_failed}
    end.

%% @doc 执行连接池选择
do_select_connection_pool(Request, State) ->
    try
        % 获取可用连接池列表
        AvailablePools = get_available_connection_pools(),

        case AvailablePools of
            [] ->
                {error, no_available_pools};
            _ ->
                % 根据算法选择连接池
                SelectedPool = select_pool_by_algorithm(
                    State#lb_state.algorithm,
                    AvailablePools,
                    Request,
                    State
                ),

                % 更新连接池统计
                update_pool_usage(SelectedPool),

                {ok, SelectedPool}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "do_select_connection_pool_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, pool_selection_failed}
    end.

%% @doc 根据算法选择连接
select_by_algorithm(round_robin, Connections, _Request, State) ->
    round_robin_select(Connections, State);
select_by_algorithm(weighted_round_robin, Connections, _Request, State) ->
    weighted_round_robin_select(Connections, State);
select_by_algorithm(least_connections, Connections, _Request, _State) ->
    least_connections_select(Connections);
select_by_algorithm(response_time, Connections, _Request, _State) ->
    response_time_select(Connections);
select_by_algorithm(adaptive, Connections, Request, State) ->
    adaptive_select(Connections, Request, State);
select_by_algorithm(consistent_hash, Connections, Request, _State) ->
    consistent_hash_select(Connections, Request);
select_by_algorithm(_, Connections, _Request, State) ->
    % 默认使用轮询
    round_robin_select(Connections, State).

%% @doc 轮询算法选择连接
round_robin_select(Connections, State) ->
    Index = State#lb_state.round_robin_index rem length(Connections),
    lists:nth(Index + 1, Connections).

%% @doc 加权轮询算法选择连接
weighted_round_robin_select(Connections, State) ->
    % 获取连接权重
    ConnectionWeights = get_connection_weights(Connections, State#lb_state.connection_weights),

    % 计算总权重
    TotalWeight = lists:sum([Weight || {_, Weight} <- ConnectionWeights]),

    case TotalWeight of
        0 ->
            % 如果没有权重，使用普通轮询
            round_robin_select(Connections, State);
        _ ->
            % 使用加权轮询
            Random = rand:uniform() * TotalWeight,
            select_by_weight_threshold(ConnectionWeights, Random, 0)
    end.

%% @doc 最少连接算法选择连接
least_connections_select(Connections) ->
    % 获取每个连接的当前连接数
    ConnectionCounts = get_connection_counts(Connections),

    % 选择连接数最少的连接
    {SelectedConnection, _} = lists:min(
        fun({_, Count1}, {_, Count2}) -> Count1 =< Count2 end,
        ConnectionCounts
    ),

    SelectedConnection.

%% @doc 响应时间算法选择连接
response_time_select(Connections) ->
    % 获取每个连接的平均响应时间
    ResponseTimes = get_connection_response_times(Connections),

    % 选择响应时间最短的连接
    {SelectedConnection, _} = lists:min(
        fun({_, Time1}, {_, Time2}) -> Time1 =< Time2 end,
        ResponseTimes
    ),

    SelectedConnection.

%% @doc 自适应算法选择连接
adaptive_select(Connections, Request, State) ->
    % 综合考虑多个因素
    ConnectionScores = calculate_adaptive_scores(Connections, Request, State),

    % 选择评分最高的连接
    {SelectedConnection, _} = lists:max(
        fun({_, Score1}, {_, Score2}) -> Score1 >= Score2 end,
        ConnectionScores
    ),

    SelectedConnection.

%% @doc 一致性哈希算法选择连接
consistent_hash_select(Connections, Request) ->
    % 计算请求的哈希值
    RequestHash = calculate_request_hash(Request),

    % 使用一致性哈希选择连接
    select_by_consistent_hash(Connections, RequestHash).

%% @doc 执行动态调整
perform_dynamic_adjustment(State) ->
    try
        % 收集当前性能指标
        PerformanceMetrics = collect_performance_metrics(),

        % 分析负载分布
        LoadDistribution = analyze_load_distribution(),

        % 计算新的权重
        NewWeights = calculate_dynamic_weights(PerformanceMetrics, LoadDistribution),

        % 检查是否需要重平衡
        NeedRebalance = check_rebalance_needed(LoadDistribution, State#lb_state.rebalance_threshold),

        % 更新状态
        NewState = State#lb_state{
            connection_weights = NewWeights,
            last_adjustment_time = erlang:system_time(millisecond)
        },

        % 如果需要重平衡，执行重平衡
        case NeedRebalance of
            true ->
                perform_rebalance(NewState);
            false ->
                NewState
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "dynamic_adjustment_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            State
    end.

%% ============================================================================
%% 支持函数实现
%% ============================================================================

%% @doc 获取可用连接列表
get_available_connections() ->
    try
        case ets:tab2list(?CONNECTION_HEALTH_TAB) of
            [] -> [];
            HealthRecords ->
                [Pid || {Pid, _} <- HealthRecords, is_process_alive(Pid)]
        end
    catch
        _:_ -> []
    end.

%% @doc 获取可用连接池列表
get_available_connection_pools() ->
    try
        case ets:tab2list(?CONNECTION_POOL_TAB) of
            [] -> [];
            Pools -> [PoolId || {PoolId, _} <- Pools]
        end
    catch
        _:_ -> []
    end.

%% @doc 更新连接使用统计
update_connection_usage(Connection) ->
    try
        Timestamp = erlang:system_time(millisecond),
        ets:insert(?LOAD_BALANCER_STATS_TAB, {Connection, #{
            last_used => Timestamp,
            usage_count => get_usage_count(Connection) + 1
        }})
    catch
        _:_ -> ok
    end.

%% @doc 更新连接池使用统计
update_pool_usage(PoolId) ->
    try
        Timestamp = erlang:system_time(millisecond),
        ets:insert(?LOAD_BALANCER_STATS_TAB, {PoolId, #{
            last_used => Timestamp,
            usage_count => get_pool_usage_count(PoolId) + 1
        }})
    catch
        _:_ -> ok
    end.

%% @doc 获取连接使用次数
get_usage_count(Connection) ->
    try
        case ets:lookup(?LOAD_BALANCER_STATS_TAB, Connection) of
            [{Connection, #{usage_count := Count}}] -> Count;
            [] -> 0
        end
    catch
        _:_ -> 0
    end.

%% @doc 获取连接池使用次数
get_pool_usage_count(PoolId) ->
    try
        case ets:lookup(?LOAD_BALANCER_STATS_TAB, PoolId) of
            [{PoolId, #{usage_count := Count}}] -> Count;
            [] -> 0
        end
    catch
        _:_ -> 0
    end.

%% @doc 根据算法选择连接池
select_pool_by_algorithm(Algorithm, Pools, Request, State) ->
    % 简化实现，使用轮询选择连接池
    Index = State#lb_state.round_robin_index rem length(Pools),
    lists:nth(Index + 1, Pools).

%% @doc 获取连接权重
get_connection_weights(Connections, WeightMap) ->
    lists:map(fun(Conn) ->
        Weight = maps:get(Conn, WeightMap, ?DEFAULT_CONNECTION_WEIGHT),
        {Conn, Weight}
    end, Connections).

%% @doc 根据权重阈值选择连接
select_by_weight_threshold([], _, _) ->
    error(no_connections);
select_by_weight_threshold([{Conn, Weight} | Rest], Threshold, Accumulator) ->
    NewAccumulator = Accumulator + Weight,
    case Threshold =< NewAccumulator of
        true -> Conn;
        false -> select_by_weight_threshold(Rest, Threshold, NewAccumulator)
    end.

%% @doc 获取连接数量统计
get_connection_counts(Connections) ->
    lists:map(fun(Conn) ->
        Count = get_connection_active_count(Conn),
        {Conn, Count}
    end, Connections).

%% @doc 获取连接活跃数量
get_connection_active_count(Connection) ->
    try
        % 简化实现，返回随机数量
        rand:uniform(10)
    catch
        _:_ -> 0
    end.

%% @doc 获取连接响应时间
get_connection_response_times(Connections) ->
    lists:map(fun(Conn) ->
        ResponseTime = get_connection_avg_response_time(Conn),
        {Conn, ResponseTime}
    end, Connections).

%% @doc 获取连接平均响应时间
get_connection_avg_response_time(Connection) ->
    try
        case ets:lookup(?CONNECTION_HEALTH_TAB, Connection) of
            [{Connection, #{avg_response_time := Time}}] -> Time;
            [] -> 100 % 默认100ms
        end
    catch
        _:_ -> 100
    end.

%% @doc 计算自适应评分
calculate_adaptive_scores(Connections, Request, State) ->
    lists:map(fun(Conn) ->
        % 获取各项指标
        ResponseTime = get_connection_avg_response_time(Conn),
        ConnectionCount = get_connection_active_count(Conn),
        ErrorRate = get_connection_error_rate(Conn),
        HealthScore = get_connection_health_score(Conn),

        % 计算综合评分
        Factors = ?ADAPTIVE_SCORE_FACTORS,
        Score =
            (1.0 / max(1, ResponseTime)) * maps:get(response_time, Factors, 0.4) +
            (1.0 / max(1, ConnectionCount)) * maps:get(connection_count, Factors, 0.3) +
            (1.0 - ErrorRate) * maps:get(error_rate, Factors, 0.2) +
            HealthScore * maps:get(health_score, Factors, 0.1),

        {Conn, Score}
    end, Connections).

%% @doc 获取连接错误率
get_connection_error_rate(Connection) ->
    try
        case ets:lookup(?CONNECTION_HEALTH_TAB, Connection) of
            [{Connection, #{error_rate := Rate}}] -> Rate;
            [] -> 0.0
        end
    catch
        _:_ -> 0.0
    end.

%% @doc 获取连接健康评分
get_connection_health_score(Connection) ->
    try
        case ets:lookup(?CONNECTION_HEALTH_TAB, Connection) of
            [{Connection, #{health_score := Score}}] -> Score;
            [] -> 1.0
        end
    catch
        _:_ -> 1.0
    end.

%% @doc 计算请求哈希值
calculate_request_hash(Request) ->
    try
        % 简化的哈希计算
        erlang:phash2(Request, 1000000)
    catch
        _:_ -> 0
    end.

%% @doc 一致性哈希选择连接
select_by_consistent_hash(Connections, RequestHash) ->
    try
        % 简化的一致性哈希实现
        Index = RequestHash rem length(Connections),
        lists:nth(Index + 1, Connections)
    catch
        _:_ -> hd(Connections)
    end.

%% @doc 收集性能指标
collect_performance_metrics() ->
    try
        #{
            timestamp => erlang:system_time(millisecond),
            cpu_usage => get_cpu_usage_simple(),
            memory_usage => get_memory_usage_simple(),
            connection_count => length(get_available_connections())
        }
    catch
        _:_ -> #{}
    end.

%% @doc 简化的CPU使用率获取
get_cpu_usage_simple() ->
    try
        case recon:node_stats(1, 1000) of
            [{_, Stats}] ->
                proplists:get_value(scheduler_usage, Stats, 0.0);
            _ ->
                0.0
        end
    catch
        _:_ -> 0.0
    end.

%% @doc 简化的内存使用率获取
get_memory_usage_simple() ->
    try
        MemoryInfo = erlang:memory(),
        Total = proplists:get_value(total, MemoryInfo, 1),
        Process = proplists:get_value(processes, MemoryInfo, 0),
        Process / Total
    catch
        _:_ -> 0.0
    end.

%% @doc 分析负载分布
analyze_load_distribution() ->
    try
        Connections = get_available_connections(),
        case Connections of
            [] -> #{distribution => "no_connections"};
            _ ->
                % 计算每个连接的负载
                LoadData = lists:map(fun(Conn) ->
                    UsageCount = get_usage_count(Conn),
                    ResponseTime = get_connection_avg_response_time(Conn),
                    {Conn, #{usage_count => UsageCount, response_time => ResponseTime}}
                end, Connections),

                % 计算负载分布统计
                UsageCounts = [Count || {_, #{usage_count := Count}} <- LoadData],
                AvgUsage = case UsageCounts of
                    [] -> 0;
                    _ -> lists:sum(UsageCounts) / length(UsageCounts)
                end,

                #{
                    distribution => "analyzed",
                    avg_usage => AvgUsage,
                    connection_count => length(Connections),
                    load_data => LoadData
                }
        end
    catch
        _:_ -> #{distribution => "analysis_failed"}
    end.

%% @doc 计算动态权重
calculate_dynamic_weights(PerformanceMetrics, LoadDistribution) ->
    try
        case maps:get(load_data, LoadDistribution, []) of
            [] -> #{};
            LoadData ->
                maps:from_list(lists:map(fun({Conn, ConnData}) ->
                    % 基于使用次数和响应时间计算权重
                    UsageCount = maps:get(usage_count, ConnData, 0),
                    ResponseTime = maps:get(response_time, ConnData, 100),

                    % 使用次数越少，权重越高
                    % 响应时间越短，权重越高
                    Weight = max(?MIN_CONNECTION_WEIGHT,
                                min(?MAX_CONNECTION_WEIGHT,
                                    ?DEFAULT_CONNECTION_WEIGHT * (100 / max(1, ResponseTime)) *
                                    (10 / max(1, UsageCount)))),

                    {Conn, Weight}
                end, LoadData))
        end
    catch
        _:_ -> #{}
    end.

%% @doc 检查是否需要重平衡
check_rebalance_needed(LoadDistribution, Threshold) ->
    try
        case maps:get(load_data, LoadDistribution, []) of
            [] -> false;
            LoadData when length(LoadData) < 2 -> false;
            LoadData ->
                % 计算负载差异
                UsageCounts = [Count || {_, #{usage_count := Count}} <- LoadData],
                MaxUsage = lists:max(UsageCounts),
                MinUsage = lists:min(UsageCounts),

                case MaxUsage of
                    0 -> false;
                    _ ->
                        LoadDifference = (MaxUsage - MinUsage) / MaxUsage,
                        LoadDifference > Threshold
                end
        end
    catch
        _:_ -> false
    end.

%% @doc 执行重平衡
perform_rebalance(State) ->
    try
        ?SLOG(info, #{msg => "performing_load_rebalance"}),

        % 重置轮询索引
        NewState = State#lb_state{round_robin_index = 0},

        % 清理旧的统计数据
        ets:delete_all_objects(?LOAD_BALANCER_STATS_TAB),

        NewState
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "perform_rebalance_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            State
    end.

%% @doc 生成负载均衡统计
generate_load_balancing_stats(State) ->
    try
        #{
            algorithm => State#lb_state.algorithm,
            dynamic_adjustment => State#lb_state.dynamic_adjustment,
            round_robin_index => State#lb_state.round_robin_index,
            connection_weights => maps:size(State#lb_state.connection_weights),
            last_adjustment_time => State#lb_state.last_adjustment_time,
            available_connections => length(get_available_connections()),
            available_pools => length(get_available_connection_pools())
        }
    catch
        _:_ -> #{error => "Failed to generate stats"}
    end.

%% @doc 更新连接统计
update_connection_stats(Connection, Stats) ->
    try
        ets:insert(?LOAD_BALANCER_STATS_TAB, {Connection, Stats})
    catch
        _:_ -> ok
    end.
