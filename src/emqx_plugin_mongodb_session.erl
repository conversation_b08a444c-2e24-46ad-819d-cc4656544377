%% @doc MQTT会话持久化模块 - 企业级会话管理和持久化系统
%% 这个模块是MQTT会话持久化的核心组件，提供完整的会话生命周期管理
%%
%% 功能概述：
%% 1. 会话生命周期管理 - 完整的MQTT会话创建、恢复、终止管理
%% 2. 持久化存储 - 将会话状态持久化到MongoDB，确保系统重启后不丢失
%% 3. 会话恢复 - 系统启动时自动恢复持久化的会话到EMQX内存
%% 4. 过期清理 - 定期清理过期的会话，防止存储膨胀
%% 5. 统计监控 - 提供详细的会话统计信息和监控指标
%% 6. 会话接管 - 处理客户端重连时的会话接管逻辑
%% 7. 遗嘱消息管理 - 集成遗嘱消息的持久化和处理
%%
%% MQTT会话机制：
%% - 持久会话：客户端断开后保持会话状态，重连时恢复
%% - 临时会话：客户端断开后立即清理会话状态
%% - 会话接管：同一客户端ID的新连接接管旧会话
%% - 会话恢复：系统重启后从持久化存储恢复会话
%%
%% 修复后的架构设计：
%% - 非侵入式设计：不干扰EMQX内置的会话管理逻辑，只负责持久化
%% - 事件驱动：通过EMQX钩子系统响应会话生命周期事件
%% - 异步处理：所有持久化操作都是异步的，不阻塞会话处理
%% - 缓存优化：添加会话缓存层，减少MongoDB查询频率
%% - 容错恢复：支持系统故障后的会话状态恢复，包含降级机制
%% - 性能优化：使用批量操作和索引优化，提高处理性能
%%
%% Java等价概念：
%% 类似于Spring Boot中的会话管理服务：
%% @Service
%% @Component
%% @Transactional
%% public class SessionPersistenceService {
%%     @Autowired private MongoTemplate mongoTemplate;
%%     @Autowired private SessionRepository sessionRepository;
%%     @Autowired private RedisTemplate redisTemplate; // 缓存层
%%
%%     @EventListener
%%     @Async
%%     public void onSessionCreated(SessionCreatedEvent event) {
%%         // 异步保存会话状态
%%         Session session = convertToSession(event);
%%         sessionRepository.save(session);
%%     }
%%
%%     @EventListener
%%     @Async
%%     public void onSessionTerminated(SessionTerminatedEvent event) {
%%         // 异步更新会话状态或删除临时会话
%%         if (event.getSession().isPersistent()) {
%%             sessionRepository.updateStatus(event.getClientId(), "disconnected");
%%         } else {
%%             sessionRepository.delete(event.getClientId());
%%         }
%%     }
%%
%%     @Scheduled(fixedDelay = 300000) // 5分钟清理过期会话
%%     public void cleanupExpiredSessions() {
%%         sessionRepository.deleteExpiredSessions();
%%     }
%%
%%     @PostConstruct
%%     public void restoreSessions() {
%%         // 系统启动时恢复持久化会话
%%         List<Session> sessions = sessionRepository.findActiveSessions();
%%         for (Session session : sessions) {
%%             emqxSessionManager.restoreSession(session);
%%         }
%%     }
%% }
%%
%% 设计模式：
%% - 观察者模式：监听EMQX的会话生命周期事件
%% - 策略模式：不同类型会话使用不同的处理策略
%% - 模板方法模式：统一的会话处理流程模板
%% - 工厂模式：会话对象的创建和转换
%% @end
-module(emqx_plugin_mongodb_session).

-include("emqx_plugin_mongodb.hrl").
% 包含EMQX持久会话的记录定义
-include_lib("emqx/src/emqx_persistent_session_ds.hrl").

%% ============================================================================
%% 生命周期管理API - 类似于Java的@PostConstruct和@PreDestroy
%% 在Java中相当于：
%% @PostConstruct public void init() { ... }
%% @PreDestroy public void cleanup() { ... }
%% ============================================================================
-export([
    init/0,     % 初始化模块 - 类似于@PostConstruct
    load/1,     % 加载配置 - 类似于配置加载
    unload/0    % 卸载模块 - 类似于@PreDestroy
]).

%% ============================================================================
%% MQTT会话事件监听器 - 类似于Java的@EventListener
%% 在Java中相当于：
%% @EventListener
%% public void onSessionCreated(SessionCreatedEvent event) { ... }
%% ============================================================================
-export([
    on_session_created/2,       % 会话创建事件 - onSessionCreated(ClientInfo, SessionInfo)
    on_session_resumed/2,       % 会话恢复事件 - onSessionResumed(ClientInfo, SessionInfo)
    on_session_discarded/2,     % 会话丢弃事件 - onSessionDiscarded(ClientInfo, SessionInfo)
    on_session_terminated/3,    % 会话终止事件 - onSessionTerminated(ClientInfo, Reason, SessionInfo)
    on_client_connected/2,      % 客户端连接事件 - onClientConnected(ClientInfo, ConnInfo)
    on_client_disconnected/3    % 客户端断开事件 - onClientDisconnected(ClientInfo, Reason, ConnInfo)
]).

%% ============================================================================
%% 会话管理业务API - 类似于Java的Service层方法
%% 在Java中相当于：
%% @Service
%% public class SessionService {
%%     public void saveSession(ClientInfo clientInfo, SessionInfo sessionInfo);
%%     public List<Session> restoreSessions();
%%     public void cleanupExpiredSessions();
%% }
%% ============================================================================
-export([
    save_session/2,             % 保存会话 - saveSession(ClientInfo, SessionInfo)
    handle_clean_start_session/3,   % 处理Clean Start会话
                                   % 功能：根据MQTT协议处理Clean Start = true的会话
                                   % Java等价：public void handleCleanStartSession(String clientId, ClientInfo info, SessionInfo sessInfo)

    handle_persistent_session/4,    % 处理持久会话
                                   % 功能：根据MQTT协议和Session Expiry Interval处理持久会话
                                   % Java等价：public void handlePersistentSession(String clientId, ClientInfo info, SessionInfo sessInfo, int expiryInterval)

    create_persistent_session/4,    % 创建持久会话
                                   % 功能：创建符合MQTT协议的持久会话
                                   % Java等价：public void createPersistentSession(String clientId, ClientInfo info, SessionInfo sessInfo, int expiryInterval)



    handle_persisted_sessions_after_restart/0, % 处理系统重启后的持久化会话
    cleanup_expired_sessions/0 % 清理过期会话
]).

%% 内部函数
-export([
    start_cleanup_timer/0,
    do_cleanup_expired_sessions/0,
    get_session_collection/0,
    get_will_message_collection/0,
    integrate/0,
    ensure_session_collection_and_indexes/1
]).

%% 注意：集合名称获取函数已在内部函数中导出，避免重复导出

%% ============================================================================
%% 配置常量定义 - 会话持久化的核心配置参数
%% 这些常量控制会话持久化的行为和性能特征
%% 类似于Java中的配置常量或@ConfigurationProperties
%% ============================================================================

%% 默认会话过期时间：2小时（7200000毫秒）
%% 功能：控制持久化会话在MongoDB中的保存时间
%% 超过此时间的会话将被自动清理，防止存储无限增长
%% Java等价：@Value("${session.expiry.time:7200000}")
%% 或者：spring.session.expiry-time=7200000
-define(DEFAULT_SESSION_EXPIRY, 7200000).

%% 默认清理间隔：5分钟（300000毫秒）
%% 功能：控制过期会话清理任务的执行频率
%% 定时任务会按此间隔清理过期的持久化会话
%% Java等价：@Scheduled(fixedDelay = 300000)
%% 或者：spring.task.scheduling.pool.size=1
-define(DEFAULT_CLEANUP_INTERVAL, 300000).

%% 默认批次大小：100条会话记录
%% 功能：控制批量操作的大小，平衡性能和内存使用
%% 清理任务每次处理的会话数量，避免一次性处理过多数据
%% Java等价：@Value("${session.cleanup.batch-size:100}")
%% 或者：spring.data.mongodb.batch-size=100
-define(DEFAULT_CLEANUP_BATCH_SIZE, 100).

%% ============================================================================
%% 模块初始化函数 - 会话持久化模块的启动入口
%% ============================================================================

%% @doc 初始化会话持久化模块
%% 这个函数是模块的启动入口，负责初始化所有必要的资源和服务
%%
%% 功能说明：
%% 1. 检查会话持久化功能是否启用
%% 2. 等待MongoDB资源就绪
%% 3. 创建必要的集合和索引
%% 4. 启动过期会话清理定时器
%% 5. 记录初始化状态日志
%%
%% 返回值：
%% - ok: 初始化成功
%%
%% Java等价概念：
%% @PostConstruct
%% @ConditionalOnProperty(name = "session.persistence.enabled", havingValue = "true")
%% public void initializeSessionPersistence() {
%%     if (sessionPersistenceEnabled) {
%%         waitForMongoDBReady();
%%         createCollectionsAndIndexes();
%%         startCleanupScheduler();
%%         logger.info("Session persistence initialized successfully");
%%     }
%% }
%%
%% 设计特点：
%% - 条件初始化：只有在启用会话持久化时才执行初始化
%% - 资源等待：确保MongoDB资源可用后再进行后续操作
%% - 完整设置：一次性完成所有必要的初始化工作
init() ->
    %% 记录模块初始化开始的日志
    %% 这有助于系统启动过程的跟踪和调试
    ?SLOG(info, #{msg => "initializing_mongodb_session_module"}),

    %% 注意：实际的初始化逻辑现在在load/1函数中执行
    %% 这是因为init/0在load/1之前被调用，但需要load/1设置的环境变量
    ?SLOG(info, #{msg => "session_module_init_completed_actual_initialization_in_load"}),
    ok.

%% 加载会话模块
load(Config) ->
    ?SLOG(info, #{msg => "loading_mongodb_session_module", config => Config}),
    % 检查会话持久化是否启用
    SessionConfig = maps:get(session_persistence, Config, #{}),
    SessionPersistenceEnabled = maps:get(enabled, SessionConfig, false),

    % 保存启用状态到应用环境变量中
    application:set_env(emqx_plugin_mongodb, session_persistence_enabled, SessionPersistenceEnabled),

    % 注册会话相关钩子
    register_hooks(),

    % 如果会话持久化启用，检查是否需要启动时恢复会话
    case SessionPersistenceEnabled of
        true ->
            ?SLOG(info, #{msg => "session_persistence_enabled_checking_connection"}),
            % 检查MongoDB连接是否已建立
            case check_mongodb_connection_ready() of
                true ->
                    ?SLOG(info, #{msg => "mongodb_connection_ready_initializing_session_persistence"}),
                    initialize_session_persistence();
                false ->
                    ?SLOG(info, #{msg => "mongodb_connection_not_ready_deferring_session_persistence_initialization"}),
                    % 启动后台任务等待连接建立
                    spawn_link(fun() -> wait_and_initialize_session_persistence() end),
                    ok
            end,
                % 如果配置了启动时恢复会话，则执行恢复
                % 会话恢复现在通过MongoDB连接事件触发，不再使用时间延迟
                ?SLOG(info, #{msg => "session_restoration_will_be_triggered_on_mongodb_connection"}),
                ok;
        false ->
            ?SLOG(info, #{msg => "session_persistence_disabled"})
    end,
    ok.

%% @doc 检查MongoDB连接是否已建立
-spec check_mongodb_connection_ready() -> boolean().
check_mongodb_connection_ready() ->
    try
        % 使用简单的资源健康检查而不是查询
        case emqx_resource:health_check(?PLUGIN_MONGODB_RESOURCE_ID) of
            ok ->
                true;
            {ok, _} ->
                true;
            _ ->
                false
        end
    catch
        _:_ ->
            false
    end.

%% @doc 等待MongoDB连接建立并初始化会话持久化
-spec wait_and_initialize_session_persistence() -> ok.
wait_and_initialize_session_persistence() ->
    wait_and_initialize_session_persistence(60). % 最多等待60次，每次2秒

%% @doc 等待循环
-spec wait_and_initialize_session_persistence(integer()) -> ok.
wait_and_initialize_session_persistence(0) ->
    ?SLOG(error, #{msg => "session_persistence_initialization_timeout"}),
    ok;
wait_and_initialize_session_persistence(Retries) ->
    timer:sleep(2000), % 每2秒检查一次
    case check_mongodb_connection_ready() of
        true ->
            ?SLOG(info, #{msg => "mongodb_connection_ready_initializing_session_persistence_delayed"}),
            initialize_session_persistence();
        false ->
            wait_and_initialize_session_persistence(Retries - 1)
    end.

%% @doc 初始化会话持久化
-spec initialize_session_persistence() -> ok.
initialize_session_persistence() ->
    try
        % 确保集合存在
        ensure_collections(),
        % 启动清理定时器
        start_cleanup_timer(),

        % 检查是否需要在启动时恢复会话
        Config = emqx_plugin_mongodb:read_config(),
        ?SLOG(info, #{msg => "checking_session_restoration_config", config_type => get_type_name(Config)}),

        case Config of
            {error, Reason} ->
                ?SLOG(warning, #{
                    msg => "failed_to_read_config_for_session_restoration",
                    reason => Reason,
                    note => "using_default_restore_on_startup_true"
                }),
                % 配置读取失败，使用默认值
                ?SLOG(info, #{msg => "starting_session_restoration_on_startup_default"}),
                spawn(fun() ->
                    timer:sleep(5000),
                    handle_persisted_sessions_after_restart()
                end);
            ConfigMap when is_map(ConfigMap) ->
                SessionConfig = maps:get(session_persistence, ConfigMap, #{}),
                RestoreOnStartup = maps:get(restore_on_startup, SessionConfig, true),

                ?SLOG(info, #{
                    msg => "session_restoration_config_read",
                    restore_on_startup => RestoreOnStartup,
                    session_config_keys => maps:keys(SessionConfig)
                }),

                case RestoreOnStartup of
                    true ->
                        ?SLOG(info, #{msg => "starting_session_restoration_on_startup"}),
                        % 异步执行会话恢复，确保EMQX核心组件完全初始化
                        spawn(fun() ->
                            wait_for_emqx_and_mongodb_ready(), % 等待EMQX和MongoDB完全就绪
                            handle_persisted_sessions_after_restart()
                        end);
                    false ->
                        ?SLOG(info, #{msg => "session_restoration_on_startup_disabled"})
                end;
            Other ->
                ?SLOG(warning, #{
                    msg => "unexpected_config_format",
                    config => Other,
                    note => "using_default_restore_on_startup_true"
                }),
                ?SLOG(info, #{msg => "starting_session_restoration_on_startup_fallback"}),
                spawn(fun() ->
                    timer:sleep(5000),
                    handle_persisted_sessions_after_restart()
                end)
        end,

        ?SLOG(info, #{msg => "session_persistence_initialized_successfully"}),
        ok
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "failed_to_initialize_session_persistence",
                error => E,
                reason => R,
                stacktrace => S
            }),
            ok
    end.

%% @doc 获取值的类型名称（用于调试）
get_type_name(Value) when is_map(Value) -> map;
get_type_name(Value) when is_list(Value) -> list;
get_type_name(Value) when is_binary(Value) -> binary;
get_type_name(Value) when is_atom(Value) -> atom;
get_type_name(Value) when is_integer(Value) -> integer;
get_type_name(Value) when is_tuple(Value) -> tuple;
get_type_name(_) -> unknown.

%% 卸载会话模块
unload() ->
    ?SLOG(info, #{msg => "unloading_mongodb_session_module"}),
    % 停止清理定时器
    stop_cleanup_timer(),
    % 注销会话相关钩子
    unregister_hooks(),
    ok.

%% 确保会话相关的集合存在
ensure_collections() ->
    try
        % 获取集合名称
        SessionCollection = get_session_collection(),
        WillMessageCollection = get_will_message_collection(),

        ?SLOG(info, #{
            msg => "ensuring_session_collections",
            session_collection => SessionCollection,
            will_message_collection => WillMessageCollection
        }),

        % 统一集合创建策略：主动创建集合和索引，与保留消息模块保持一致
        % 这样确保所有集合在插件启动时就存在，便于管理和调试
        ensure_session_collection_and_indexes(SessionCollection),

        ?SLOG(info, #{msg => "session_collections_ensured",
                     session_collection => SessionCollection,
                     will_message_collection => WillMessageCollection})
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_ensuring_collections",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {ensure_collections_failed, R}}
    end.

%% 创建必要的索引
create_indexes() ->
    % 获取集合名称
    SessionCollection = get_session_collection(),

    % 生成集合缩写 emqx_mqtt_sessions -> ems
    SessionAbbr = generate_collection_abbreviation(SessionCollection),

    ?SLOG(info, #{
        msg => "creating_session_indexes",
        collection => SessionCollection,
        collection_abbr => SessionAbbr
    }),

    % 定义会话集合索引
    SessionIndexes = [
        % 客户端ID唯一索引
        #{
            <<"key">> => #{<<"client_id">> => 1},
            <<"unique">> => true,
            <<"name">> => <<SessionAbbr/binary, "_client_id_unique">>
        },
        % 过期时间TTL索引
        #{
            <<"key">> => #{<<"expiry_time">> => 1},
            <<"expireAfterSeconds">> => 0,
            <<"name">> => <<SessionAbbr/binary, "_expiry_time_ttl">>
        },
        % 会话状态索引
        #{
            <<"key">> => #{<<"status">> => 1},
            <<"name">> => <<SessionAbbr/binary, "_status_asc">>
        },
        % 创建时间索引
        #{
            <<"key">> => #{<<"created_at">> => 1},
            <<"name">> => <<SessionAbbr/binary, "_created_at_asc">>
        }
    ],

    % 逐个创建索引
    lists:foreach(fun(IndexSpec) ->
        IndexName = maps:get(<<"name">>, IndexSpec),
        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                               {create_index, SessionCollection, IndexSpec}) of
            {ok, _} ->
                ?SLOG(info, #{
                    msg => "session_index_created",
                    collection => SessionCollection,
                    index_name => IndexName
                });
            {async_return, ok} ->
                ?SLOG(info, #{
                    msg => "session_index_created_async",
                    collection => SessionCollection,
                    index_name => IndexName
                });
            {async_return, {ok, _}} ->
                ?SLOG(info, #{
                    msg => "session_index_created_async",
                    collection => SessionCollection,
                    index_name => IndexName
                });
            {async_return, {error, Reason}} ->
                handle_session_index_error(SessionCollection, IndexName, Reason);
            {error, Reason} ->
                handle_session_index_error(SessionCollection, IndexName, Reason)
        end
    end, SessionIndexes).

%% 注册会话相关钩子 - 修复后的简化钩子策略
register_hooks() ->
    ?SLOG(info, #{msg => "registering_session_persistence_hooks"}),

    % 简化钩子策略：只注册必要的钩子，避免重复处理和竞争条件
    % 会话相关钩子 - 负责会话状态的持久化
    emqx_hooks:add('session.created', {?MODULE, on_session_created, []}, 100),
    emqx_hooks:add('session.resumed', {?MODULE, on_session_resumed, []}, 100),
    emqx_hooks:add('session.discarded', {?MODULE, on_session_discarded, []}, 100),
    emqx_hooks:add('session.terminated', {?MODULE, on_session_terminated, []}, 100),

    % 客户端连接/断开钩子 - 负责会话状态更新和统计
    emqx_hooks:add('client.connected', {?MODULE, on_client_connected, []}, 100),
    emqx_hooks:add('client.disconnected', {?MODULE, on_client_disconnected, []}, 100),

    ?SLOG(info, #{msg => "session_persistence_hooks_registered_successfully"}).

%% 注销会话相关钩子 - 修复后的简化注销逻辑
unregister_hooks() ->
    try
        % 会话相关钩子
        safe_unhook('session.created', {?MODULE, on_session_created}),
        safe_unhook('session.resumed', {?MODULE, on_session_resumed}),
        safe_unhook('session.discarded', {?MODULE, on_session_discarded}),
        safe_unhook('session.terminated', {?MODULE, on_session_terminated}),

        % 客户端连接/断开钩子
        safe_unhook('client.connected', {?MODULE, on_client_connected}),
        safe_unhook('client.disconnected', {?MODULE, on_client_disconnected}),

        ?SLOG(info, #{msg => "session_hooks_unregistered_successfully"})
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_unregistering_session_hooks",
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% 安全地注销钩子
safe_unhook(HookPoint, Callback) ->
    try
        emqx_hooks:del(HookPoint, Callback)
    catch
        E:R ->
            ?SLOG(warning, #{
                msg => "failed_to_unregister_hook",
                hook_point => HookPoint,
                callback => Callback,
                error => E,
                reason => R
            })
    end.

%% ============================================================================
%% 辅助函数 - 会话缓存和查询
%% ============================================================================

%% @doc 从MongoDB查找持久会话数据（带缓存优化）
find_persistent_session_in_mongodb(ClientId) ->
    try
        % 首先检查缓存
        case get_session_from_cache(ClientId) of
            {ok, SessionDoc} ->
                {ok, SessionDoc};
            {error, not_found} ->
                % 缓存未命中，查询MongoDB
                Query = #{<<"client_id">> => ClientId},
                case emqx_plugin_mongodb_api:get_mongodb_connection() of
                    {ok, Connection} ->
                        case emqx_plugin_mongodb_api:find_one(Connection, ?DEFAULT_SESSION_COLLECTION, Query) of
                            {ok, SessionDoc} when SessionDoc =/= null, SessionDoc =/= undefined ->
                                % 将结果缓存
                                cache_session(ClientId, SessionDoc),
                                {ok, SessionDoc};
                            {ok, _} ->
                                {error, not_found};
                            {error, Reason} ->
                                {error, Reason}
                        end;
                    {error, Reason} ->
                        {error, {connection_failed, Reason}}
                end
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "exception_querying_mongodb_for_session",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {exception, E, R}}
    end.

%% @doc 会话缓存管理 - 减少MongoDB查询频率
get_session_from_cache(ClientId) ->
    % 简单的进程字典缓存，生产环境可以使用ETS
    CacheKey = {session_cache, ClientId},
    case get(CacheKey) of
        undefined ->
            {error, not_found};
        {SessionDoc, Timestamp} ->
            % 检查缓存是否过期（5分钟）
            Now = erlang:system_time(millisecond),
            if
                Now - Timestamp > 300000 -> % 5分钟过期
                    erase(CacheKey),
                    {error, not_found};
                true ->
                    {ok, SessionDoc}
            end
    end.

cache_session(ClientId, SessionDoc) ->
    CacheKey = {session_cache, ClientId},
    Timestamp = erlang:system_time(millisecond),
    put(CacheKey, {SessionDoc, Timestamp}).

invalidate_session_cache(ClientId) ->
    CacheKey = {session_cache, ClientId},
    erase(CacheKey).

%% @doc 记录会话状态变化到MongoDB（非侵入式）
record_session_state_change(ClientId, State, Reason) ->
    try
        % 异步记录状态变化，不阻塞主流程
        spawn(fun() ->
            try
                Now = erlang:system_time(millisecond),
                StateDoc = #{
                    <<"client_id">> => ClientId,
                    <<"state">> => State,
                    <<"reason">> => format_reason(Reason),
                    <<"timestamp">> => Now,
                    <<"node">> => atom_to_binary(node(), utf8)
                },

                SessionCollection = get_session_collection(),
                UpdateDoc = #{
                    <<"$set">> => #{
                        <<"status">> => State,
                        <<"updated_at">> => Now
                    },
                    <<"$push">> => #{
                        <<"status_history">> => #{
                            <<"$each">> => [StateDoc],
                            <<"$slice">> => -10  % 只保留最近10条历史
                        }
                    }
                },

                % 使用upsert确保记录存在
                emqx_plugin_mongodb_api:update_one(
                    SessionCollection,
                    #{<<"client_id">> => ClientId},
                    UpdateDoc,
                    #{<<"upsert">> => true}
                ),

                % 清除缓存
                invalidate_session_cache(ClientId)
            catch
                _:_ -> ok  % 忽略记录错误，不影响主流程
            end
        end)
    catch
        _:_ -> ok
    end.

%% @doc 等待EMQX核心组件和MongoDB完全就绪
%% 这是解决会话恢复时机问题的关键函数
wait_for_emqx_and_mongodb_ready() ->
    ?SLOG(info, #{msg => "waiting_for_emqx_and_mongodb_ready"}),

    % 等待基本时间，确保EMQX核心组件启动
    timer:sleep(3000),

    % 检查EMQX持久会话是否启用
    wait_for_emqx_persistent_session_ready(),

    % 检查MongoDB连接是否就绪
    wait_for_mongodb_connection_ready(),

    ?SLOG(info, #{msg => "emqx_and_mongodb_ready_session_restoration_can_proceed"}).

%% @doc 等待EMQX持久会话功能就绪
%% 修复：简化检查逻辑，不依赖EMQX内置持久会话功能
wait_for_emqx_persistent_session_ready() ->
    wait_for_emqx_persistent_session_ready(30). % 最多等待30秒

wait_for_emqx_persistent_session_ready(0) ->
    ?SLOG(info, #{msg => "emqx_session_check_timeout_proceeding_anyway"}),
    ok;
wait_for_emqx_persistent_session_ready(Retries) ->
    try
        % 修复：检查EMQX会话管理器是否可用，而不是检查持久会话功能
        case whereis(emqx_session_manager) of
            undefined ->
                ?SLOG(debug, #{msg => "waiting_for_emqx_session_manager"}),
                timer:sleep(1000),
                wait_for_emqx_persistent_session_ready(Retries - 1);
            Pid when is_pid(Pid) ->
                % 检查会话管理器是否正常运行
                case is_process_alive(Pid) of
                    true ->
                        ?SLOG(info, #{msg => "emqx_session_manager_ready"}),
                        ok;
                    false ->
                        ?SLOG(debug, #{msg => "emqx_session_manager_not_alive"}),
                        timer:sleep(1000),
                        wait_for_emqx_persistent_session_ready(Retries - 1)
                end
        end
    catch
        E:R:S ->
            ?SLOG(debug, #{
                msg => "error_checking_emqx_session_manager",
                error => E,
                reason => R,
                stacktrace => S
            }),
            timer:sleep(1000),
            wait_for_emqx_persistent_session_ready(Retries - 1)
    end.

%% @doc 等待MongoDB连接就绪
wait_for_mongodb_connection_ready() ->
    wait_for_mongodb_connection_ready(30). % 最多等待30秒

wait_for_mongodb_connection_ready(0) ->
    ?SLOG(warning, #{msg => "timeout_waiting_for_mongodb_connection"}),
    ok;
wait_for_mongodb_connection_ready(Retries) ->
    try
        case emqx_plugin_mongodb_api:get_mongodb_connection() of
            {ok, _Connection} ->
                ?SLOG(info, #{msg => "mongodb_connection_ready"}),
                ok;
            {error, _Reason} ->
                ?SLOG(debug, #{msg => "waiting_for_mongodb_connection"}),
                timer:sleep(1000),
                wait_for_mongodb_connection_ready(Retries - 1)
        end
    catch
        _:_ ->
            timer:sleep(1000),
            wait_for_mongodb_connection_ready(Retries - 1)
    end.



%% ============================================================================
%% 钩子回调函数 - 修复后的非侵入式实现
%% ============================================================================



%% 检查并恢复持久化会话（如果存在）
restore_persistent_session_if_exists(ClientId) ->
    try
        SessionCollection = get_session_collection(),
        Filter = #{
            <<"client_id">> => ClientId,
            <<"clean_start">> => false,
            <<"expiry_time">> => #{<<"$gt">> => erlang:system_time(millisecond)}
        },

        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                               {find_one, SessionCollection, Filter}) of
            {ok, SessionDoc} when SessionDoc =/= null ->
                ?SLOG(info, #{
                    msg => "found_persistent_session_restoring_to_emqx",
                    client_id => ClientId
                }),

                % 记录会话信息供调试
                % 实际的会话恢复在EMQX启动时批量进行
                try
                    % 恢复订阅信息
                    Subscriptions = maps:get(<<"subscriptions">>, SessionDoc, []),

                    % 使用emqx_broker API恢复订阅
                    lists:foreach(fun(SubDoc) ->
                        Topic = maps:get(<<"topic">>, SubDoc, <<>>),
                        QoS = maps:get(<<"qos">>, SubDoc, 0),
                        SubOpts = #{
                            qos => QoS,
                            rh => maps:get(<<"rh">>, SubDoc, 0),
                            rap => maps:get(<<"rap">>, SubDoc, 0),
                            nl => maps:get(<<"nl">>, SubDoc, 0)
                        },

                        % 预先注册订阅到路由表
                        case emqx_broker:subscribe(Topic, ClientId, SubOpts) of
                            ok ->
                                ?SLOG(debug, #{
                                    msg => "subscription_restored_to_broker",
                                    client_id => ClientId,
                                    topic => Topic,
                                    qos => QoS
                                });
                            {error, Reason} ->
                                ?SLOG(warning, #{
                                    msg => "failed_to_restore_subscription",
                                    client_id => ClientId,
                                    topic => Topic,
                                    reason => Reason
                                })
                        end
                    end, Subscriptions),

                    ?SLOG(info, #{
                        msg => "persistent_session_subscriptions_restored",
                        client_id => ClientId,
                        subscriptions_count => length(Subscriptions)
                    })
                catch
                    E:R:S ->
                        ?SLOG(error, #{
                            msg => "failed_to_restore_persistent_session",
                            client_id => ClientId,
                            error => E,
                            reason => R,
                            stacktrace => S
                        })
                end;
            {ok, null} ->
                ?SLOG(debug, #{
                    msg => "no_persistent_session_found",
                    client_id => ClientId
                });
            ok ->
                % 处理某些MongoDB驱动返回ok而不是{ok, null}的情况
                ?SLOG(debug, #{
                    msg => "no_persistent_session_found_ok_response",
                    client_id => ClientId
                });
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "error_querying_persistent_session",
                    client_id => ClientId,
                    reason => Reason
                })
        end
    catch
        Error:ErrorReason:Stack ->
            ?SLOG(error, #{
                msg => "error_in_restore_persistent_session_if_exists",
                client_id => ClientId,
                error => Error,
                reason => ErrorReason,
                stacktrace => Stack
            })
    end.

%% 会话创建钩子回调 - 修复：使用is_persistent字段判断会话类型
on_session_created(ClientInfo, SessInfo) ->
    ClientId = maps:get(clientid, ClientInfo, <<>>),
    % 使用is_persistent字段而不是clean_start，因为EMQX内部将clean_start转换为is_persistent
    IsPersistent = maps:get(is_persistent, SessInfo, false),
    % 根据is_persistent推导clean_start：is_persistent=true表示clean_start=false
    CleanStart = not IsPersistent,

    ?SLOG(info, #{
        msg => "session_created",
        client_id => ClientId,
        is_persistent => IsPersistent,
        clean_start => CleanStart
    }),

    % 异步处理会话持久化，不阻塞会话创建
    spawn(fun() ->
        try
            case IsPersistent of
                false ->
                    % Clean Start会话（非持久）：删除旧的持久化数据
                    ?SLOG(debug, #{msg => "clean_start_session_deleting_old_data", client_id => ClientId}),
                    delete_session_data(ClientId),
                    % 记录状态变化
                    record_session_state_change(ClientId, <<"created_clean">>, normal);
                true ->
                    % 持久会话：保存会话信息
                    ?SLOG(debug, #{msg => "persistent_session_saving_data", client_id => ClientId}),
                    save_session(ClientInfo, SessInfo),
                    % 记录状态变化
                    record_session_state_change(ClientId, <<"created_persistent">>, normal)
            end
        catch
            E:R:S ->
                ?SLOG(error, #{
                    msg => "error_in_session_created_handler",
                    client_id => ClientId,
                    error => E,
                    reason => R,
                    stacktrace => S
                })
        end
    end),
    ok.

%% 会话恢复钩子回调 - 修复：从MongoDB恢复会话数据
on_session_resumed(ClientInfo, SessInfo) ->
    ClientId = maps:get(clientid, ClientInfo, <<>>),
    ?SLOG(info, #{msg => "session_resumed", client_id => ClientId}),

    % 异步恢复会话数据和记录恢复事件
    spawn(fun() ->
        try
            % 第一步：从MongoDB恢复会话数据
            case restore_session_data_from_mongodb(ClientId) of
                {ok, RestoredData} ->
                    ?SLOG(info, #{
                        msg => "session_data_restored_from_mongodb",
                        client_id => ClientId,
                        restored_data => RestoredData
                    });
                {error, Reason} ->
                    ?SLOG(warning, #{
                        msg => "failed_to_restore_session_data_from_mongodb",
                        client_id => ClientId,
                        reason => Reason
                    })
            end,

            % 第二步：记录会话恢复状态
            record_session_state_change(ClientId, <<"resumed">>, normal),

            % 第三步：更新会话统计信息
            update_session_stats(ClientId, connect),

            ?SLOG(debug, #{msg => "session_resume_completed", client_id => ClientId})
        catch
            E:R:S ->
                ?SLOG(error, #{
                    msg => "error_in_session_resume_handler",
                    client_id => ClientId,
                    error => E,
                    reason => R,
                    stacktrace => S
                })
        end
    end),
    ok.

%% 会话丢弃钩子回调 - 修复后的简化实现
on_session_discarded(ClientInfo, _SessInfo) ->
    ClientId = maps:get(clientid, ClientInfo, <<>>),
    ?SLOG(debug, #{msg => "session_discarded", client_id => ClientId}),

    % 异步记录会话丢弃事件
    spawn(fun() ->
        try
            record_session_state_change(ClientId, <<"discarded">>, normal),
            update_session_stats(ClientId, disconnect)
        catch
            E:R:S ->
                ?SLOG(error, #{
                    msg => "error_recording_session_discard",
                    client_id => ClientId,
                    error => E,
                    reason => R,
                    stacktrace => S
                })
        end
    end),
    ok.

%% 会话终止钩子回调 - 修复后的简化实现
on_session_terminated(ClientInfo, Reason, _SessInfo) ->
    ClientId = maps:get(clientid, ClientInfo, <<>>),
    ?SLOG(debug, #{msg => "session_terminated", client_id => ClientId, reason => Reason}),

    % 异步记录会话终止事件
    spawn(fun() ->
        try
            record_session_state_change(ClientId, <<"terminated">>, Reason),
            update_session_stats(ClientId, disconnect)
        catch
            E:R:S ->
                ?SLOG(error, #{
                    msg => "error_recording_session_termination",
                    client_id => ClientId,
                    error => E,
                    reason => R,
                    stacktrace => S
                })
        end
    end),
    ok.

%% 客户端连接钩子回调 - 修复后的简化实现
on_client_connected(ClientInfo, ConnInfo) ->
    ClientId = maps:get(clientid, ClientInfo, <<>>),
    CleanStart = maps:get(clean_start, ConnInfo, true),

    ?SLOG(debug, #{
        msg => "client_connected",
        client_id => ClientId,
        clean_start => CleanStart
    }),

    % 异步记录连接事件
    spawn(fun() ->
        try
            % 记录连接状态
            record_session_state_change(ClientId, <<"connected">>, normal),

            % 更新连接统计
            update_session_stats(ClientId, connect),

            % 处理会话持久化逻辑
            case CleanStart of
                true ->
                    % Clean Start会话：删除旧的持久化数据
                    ?SLOG(debug, #{msg => "clean_start_session_deleting_old_data", client_id => ClientId}),
                    delete_session_data(ClientId);
                false ->
                    % 持久会话：保存会话信息并更新连接信息
                    ?SLOG(debug, #{msg => "persistent_session_saving_data", client_id => ClientId}),
                    % 构造会话信息（从连接信息中提取）
                    SessInfo = #{
                        clean_start => CleanStart,
                        expiry_interval => maps:get(expiry_interval, ConnInfo, 0)
                    },
                    save_session(ClientInfo, SessInfo),
                    update_connection_info(ClientId, ConnInfo)
            end
        catch
            E:R:S ->
                ?SLOG(error, #{
                    msg => "error_recording_client_connection",
                    client_id => ClientId,
                    error => E,
                    reason => R,
                    stacktrace => S
                })
        end
    end),
    ok.

%% @doc 更新连接信息到MongoDB
update_connection_info(ClientId, ConnInfo) ->
    try
        Now = erlang:system_time(millisecond),
        SessionCollection = get_session_collection(),

        ConnDoc = #{
            <<"$set">> => #{
                <<"is_online">> => true,
                <<"last_connected_at">> => Now,
                <<"conn_props">> => #{
                    <<"proto_ver">> => maps:get(proto_ver, ConnInfo, 4),
                    <<"proto_name">> => maps:get(proto_name, ConnInfo, <<"MQTT">>),
                    <<"keepalive">> => maps:get(keepalive, ConnInfo, 60),
                    <<"clean_start">> => maps:get(clean_start, ConnInfo, true),
                    <<"ip_address">> => format_peername(maps:get(peername, ConnInfo, undefined))
                }
            }
        },

        emqx_plugin_mongodb_api:update_one(
            SessionCollection,
            #{<<"client_id">> => ClientId},
            ConnDoc,
            #{<<"upsert">> => true}
        )
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_updating_connection_info",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% @doc 格式化对等方名称（IP地址和端口）
format_peername(undefined) -> <<>>;
format_peername({{A, B, C, D}, Port}) ->
    % IPv4
    list_to_binary(io_lib:format("~p.~p.~p.~p:~p", [A, B, C, D, Port]));
format_peername({IP, Port}) when is_tuple(IP), tuple_size(IP) =:= 8 ->
    % IPv6
    list_to_binary(io_lib:format("~s:~p", [inet:ntoa(IP), Port]));
format_peername(Peername) ->
    % 其他格式
    list_to_binary(io_lib:format("~p", [Peername])).

%% 客户端断开连接钩子回调 - 修复后的简化实现
on_client_disconnected(ClientInfo, Reason, _Env) ->
    ClientId = maps:get(clientid, ClientInfo, <<>>),

    ?SLOG(debug, #{
        msg => "client_disconnected",
        client_id => ClientId,
        reason => Reason
    }),

    % 异步记录断开连接事件
    spawn(fun() ->
        try
            % 记录断开连接状态
            record_session_state_change(ClientId, <<"disconnected">>, Reason),

            % 更新断开连接统计
            update_session_stats(ClientId, disconnect),

            % 更新断开连接信息
            update_disconnection_info(ClientId, Reason),

            % 清除会话缓存
            invalidate_session_cache(ClientId)
        catch
            E:R:S ->
                ?SLOG(error, #{
                    msg => "error_recording_client_disconnection",
                    client_id => ClientId,
                    error => E,
                    reason => R,
                    stacktrace => S
                })
        end
    end),
    ok.



%% @doc 更新断开连接信息到MongoDB
update_disconnection_info(ClientId, Reason) ->
    try
        Now = erlang:system_time(millisecond),
        SessionCollection = get_session_collection(),

        DisconnectDoc = #{
            <<"$set">> => #{
                <<"is_online">> => false,
                <<"disconnected_at">> => Now,
                <<"disconnect_reason">> => format_reason(Reason),
                <<"updated_at">> => Now
            }
        },

        emqx_plugin_mongodb_api:update_one(
            SessionCollection,
            #{<<"client_id">> => ClientId},
            DisconnectDoc
        )
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_updating_disconnection_info",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S
            })
    end.



%% @doc 保存MQTT会话信息到MongoDB - 类似于Java的JPA save操作
%%
%% 在Java中的等价实现：
%% @Service
%% public class SessionPersistenceService {
%%     @Autowired private SessionRepository sessionRepository;
%%
%%     public void saveSession(ClientInfo clientInfo, SessionInfo sessionInfo) {
%%         try {
%%             String clientId = clientInfo.getClientId();
%%
%%             // 构建会话实体对象
%%             SessionEntity session = SessionEntity.builder()
%%                 .clientId(clientId)
%%                 .username(clientInfo.getUsername())
%%                 .cleanStart(sessionInfo.isCleanStart())
%%                 .connectedAt(sessionInfo.getCreatedAt())
%%                 .node(getCurrentNode())
%%                 .status("connected")
%%                 .updatedAt(System.currentTimeMillis())
%%                 .build();
%%
%%             // 计算过期时间
%%             long expiryInterval = sessionInfo.getExpiryInterval();
%%             long expiryTime = (expiryInterval == 0) ? 0 :
%%                 System.currentTimeMillis() + expiryInterval;
%%             session.setExpiryTime(expiryTime);
%%
%%             // 使用upsert操作保存到数据库
%%             sessionRepository.upsert(session);
%%             logger.debug("Session saved for client: {}", clientId);
%%
%%         } catch (Exception e) {
%%             logger.error("Error saving session", e);
%%         }
%%     }
%% }
%%
%% @param ClientInfo MQTT客户端信息 - 类似于Java的ClientInfo对象
%% @param SessInfo MQTT会话信息 - 类似于Java的SessionInfo对象
save_session(ClientInfo, SessInfo) ->
    ClientId = maps:get(clientid, ClientInfo, <<"unknown">>),
    try
        % 优先使用is_persistent字段，如果没有则使用clean_start字段
        IsPersistent = maps:get(is_persistent, SessInfo, undefined),
        CleanStart = case IsPersistent of
            undefined ->
                % 如果没有is_persistent字段，使用clean_start字段
                maps:get(clean_start, SessInfo, true);
            true ->
                false;  % is_persistent=true 表示 clean_start=false
            false ->
                true    % is_persistent=false 表示 clean_start=true
        end,

        % 获取Session Expiry Interval，支持MQTT 5.0
        ExpiryInterval = case maps:get(expiry_interval, SessInfo, undefined) of
            undefined ->
                % MQTT 3.1.1 或未设置，使用默认值
                maps:get(session_expiry_interval, SessInfo, 0);
            EI when is_integer(EI) ->
                % MQTT 5.0，转换为秒
                EI div 1000;
            _ ->
                0
        end,

        ?SLOG(debug, #{
            msg => "saving_session",
            client_id => ClientId,
            is_persistent => IsPersistent,
            clean_start => CleanStart,
            expiry_interval => ExpiryInterval
        }),

        % 根据MQTT协议处理会话
        case CleanStart of
            true ->
                % Clean Start会话：删除旧数据，不持久化新会话
                handle_clean_start_session(ClientId, ClientInfo, SessInfo);
            false when ExpiryInterval > 0 ->
                % 持久会话：保存到MongoDB
                handle_persistent_session(ClientId, ClientInfo, SessInfo, ExpiryInterval);
            false ->
                % Clean Start=false但ExpiryInterval=0：会话在连接关闭时结束
                ?SLOG(debug, #{
                    msg => "session_expires_on_disconnect",
                    client_id => ClientId
                })
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_saving_session",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% @doc 处理Clean Start会话 - 修复后的实现
%% 根据MQTT协议，Clean Start = true的会话不应该被持久化
handle_clean_start_session(ClientId, _ClientInfo, _SessInfo) ->
    ?SLOG(debug, #{
        msg => "handling_clean_start_session",
        client_id => ClientId,
        note => "clean_start_sessions_not_persisted_per_mqtt_protocol"
    }),

    % 删除任何现有的持久会话记录
    delete_session_data(ClientId).

%% @doc 处理持久会话 - 修复后的简化实现
handle_persistent_session(ClientId, ClientInfo, SessInfo, ExpiryInterval) ->
    ?SLOG(debug, #{
        msg => "handling_persistent_session",
        client_id => ClientId,
        expiry_interval => ExpiryInterval
    }),

    % 创建持久会话记录
    create_persistent_session(ClientId, ClientInfo, SessInfo, ExpiryInterval).

%% @doc 创建持久会话 - 修复后的简化实现
create_persistent_session(ClientId, ClientInfo, SessInfo, ExpiryInterval) ->
    try
        Now = erlang:system_time(millisecond),

        % 计算过期时间
        ExpiryTime = case ExpiryInterval of
            16#FFFFFFFF ->
                % 永不过期
                0;
            Seconds when Seconds > 0 ->
                % 转换为毫秒并计算过期时间
                Now + (Seconds * 1000);
            _ ->
                % 默认2小时过期
                Now + ?DEFAULT_SESSION_EXPIRY
        end,

        % 构建会话文档
        SessionDoc = #{
            <<"client_id">> => ClientId,
            <<"username">> => maps:get(username, ClientInfo, <<>>),
            <<"clean_start">> => false,
            <<"session_expiry_interval">> => ExpiryInterval,
            <<"expiry_time">> => ExpiryTime,
            <<"created_at">> => Now,
            <<"connected_at">> => Now,
            <<"node">> => atom_to_binary(node(), utf8),
            <<"status">> => <<"created">>,
            <<"is_online">> => true,
            <<"updated_at">> => Now,
            <<"connect_count">> => 1,
            <<"disconnect_count">> => 0,
            <<"total_online_time">> => 0,
            <<"status_history">> => []
        },

        % 保存到MongoDB
        SessionCollection = get_session_collection(),
        emqx_plugin_mongodb_api:upsert_one(
            SessionCollection,
            #{<<"client_id">> => ClientId},
            SessionDoc
        ),

        % 缓存会话数据
        cache_session(ClientId, SessionDoc),

        ?SLOG(debug, #{
            msg => "persistent_session_created",
            client_id => ClientId,
            expiry_interval => ExpiryInterval
        })
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_creating_persistent_session",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% 更新会话状态
update_session_status(ClientInfo, Status) ->
    update_session_status(ClientInfo, Status, undefined).

%% 更新会话状态（带原因）
update_session_status(ClientInfo, Status, Reason) ->
    try
        ClientId = maps:get(clientid, ClientInfo),

        % 构造更新文档
        UpdateDoc = #{
            <<"status">> => Status,
            <<"updated_at">> => erlang:system_time(millisecond)
        },

        % 如果提供了原因，添加到文档中
        UpdateDoc2 = case Reason of
            undefined -> UpdateDoc;
            _ -> UpdateDoc#{<<"reason">> => format_reason(Reason)}
        end,

        % 获取集合名称
        SessionCollection = get_session_collection(),

        % 更新会话状态
        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                                {update_session_status, SessionCollection, ClientId, UpdateDoc2}) of
            {ok, _} ->
                ?SLOG(debug, #{msg => "session_status_updated", client_id => ClientId, status => Status});
            {error, UpdateReason} ->
                ?SLOG(error, #{msg => "failed_to_update_session_status", client_id => ClientId, status => Status, reason => UpdateReason})
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_updating_session_status",
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% 格式化断开连接原因
format_reason(normal) -> <<"normal">>;
format_reason(shutdown) -> <<"shutdown">>;
format_reason({shutdown, Reason}) when is_atom(Reason) ->
                atom_to_binary(Reason, utf8);
format_reason({shutdown, Reason}) when is_binary(Reason) ->
    Reason;
format_reason({shutdown, Reason}) ->
    list_to_binary(io_lib:format("~p", [Reason]));
format_reason(Reason) when is_atom(Reason) ->
    atom_to_binary(Reason, utf8);
format_reason(Reason) when is_binary(Reason) ->
    Reason;
format_reason(Reason) ->
    list_to_binary(io_lib:format("~p", [Reason])).

%% @doc 处理系统重启后的持久化会话 - 修复后的正确实现
%%
%% 设计原理：
%% - 系统重启后，需要将MongoDB中的持久会话恢复到EMQX内存
%% - 这样客户端重连时，EMQX能正确设置session_present=true
%% - 使用EMQX标准API进行会话恢复，确保兼容性
%% - 提供降级机制，确保MongoDB故障时不影响EMQX启动
handle_persisted_sessions_after_restart() ->
    %% 检查是否为全新安装，如果是则跳过数据恢复
    case emqx_plugin_mongodb_api:is_fresh_installation() of
        true ->
            ?SLOG(info, #{
                msg => "fresh_installation_detected_skipping_session_restoration",
                reason => "no_existing_session_data_to_restore"
            }),
            ok;
        false ->
            ?SLOG(info, #{
                msg => "handling_persistent_sessions_after_restart",
                approach => "restore_sessions_to_emqx_memory"
            }),

    try
        % 检查MongoDB连接状态
        case check_mongodb_connection_ready() of
            false ->
                ?SLOG(warning, #{
                    msg => "mongodb_not_ready_skipping_session_restoration",
                    note => "sessions_will_be_handled_when_mongodb_becomes_available"
                }),
                ok;
            true ->
                % 执行会话恢复
                restore_persistent_sessions_to_emqx()
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_handling_persistent_sessions_after_restart",
                error => E,
                reason => R,
                stacktrace => S
            }),
            % 降级处理：即使出错也不影响EMQX启动
            ok
        end
    end.

%% @doc 恢复持久会话到EMQX内存
%% 这是EMQX异常重启后恢复会话的关键函数
restore_persistent_sessions_to_emqx() ->
    try
        SessionCollection = get_session_collection(),
        Now = erlang:system_time(millisecond),

        % 第一步：清理过期的会话数据
        ExpiredFilter = #{
            <<"expiry_time">> => #{
                <<"$lt">> => Now,
                <<"$ne">> => 0  % 不删除永不过期的会话
            }
        },

        ?SLOG(info, #{
            msg => "cleaning_expired_sessions_before_restoration",
            current_time => Now
        }),

        % 清理过期会话
        case emqx_plugin_mongodb_api:delete_many(SessionCollection, ExpiredFilter) of
            {ok, DeletedCount} ->
                ?SLOG(info, #{
                    msg => "expired_sessions_cleaned",
                    deleted_count => DeletedCount
                });
            {error, Reason} ->
                ?SLOG(warning, #{
                    msg => "failed_to_clean_expired_sessions",
                    reason => Reason
                })
        end,

        % 第二步：查询需要恢复的持久会话
        ActiveFilter = #{
            <<"clean_start">> => false,
            <<"$or">> => [
                #{<<"expiry_time">> => 0},  % 永不过期
                #{<<"expiry_time">> => #{<<"$gt">> => Now}}  % 未过期
            ]
        },

        ?SLOG(info, #{
            msg => "querying_persistent_sessions_for_restoration",
            filter => ActiveFilter
        }),

        case emqx_plugin_mongodb_api:find_many(SessionCollection, ActiveFilter, #{}) of
            {ok, SessionDocs} when is_list(SessionDocs), length(SessionDocs) > 0 ->
                ?SLOG(info, #{
                    msg => "found_persistent_sessions_starting_restoration",
                    session_count => length(SessionDocs)
                }),

                % 第三步：批量恢复会话到EMQX
                restore_sessions_to_emqx_batch(SessionDocs);
            {ok, []} ->
                ?SLOG(info, #{
                    msg => "no_persistent_sessions_found_for_restoration"
                });
            {error, Reason2} ->
                ?SLOG(error, #{
                    msg => "failed_to_query_persistent_sessions",
                    reason => Reason2
                })
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_in_session_restoration",
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% @doc 批量恢复会话到EMQX内存
%% 使用EMQX标准API恢复持久会话，确保session_present正确工作
restore_sessions_to_emqx_batch(SessionDocs) ->
    ?SLOG(info, #{
        msg => "starting_batch_session_restoration_to_emqx",
        session_count => length(SessionDocs)
    }),

    % 异步批量恢复，避免阻塞
    spawn(fun() ->
        try
            RestoredCount = lists:foldl(fun(SessionDoc, Acc) ->
                case restore_single_session_to_emqx(SessionDoc) of
                    ok -> Acc + 1;
                    {error, _} -> Acc
                end
            end, 0, SessionDocs),

            ?SLOG(info, #{
                msg => "batch_session_restoration_completed",
                total_sessions => length(SessionDocs),
                restored_sessions => RestoredCount,
                failed_sessions => length(SessionDocs) - RestoredCount
            })
        catch
            E:R:S ->
                ?SLOG(error, #{
                    msg => "error_in_batch_session_restoration",
                    error => E,
                    reason => R,
                    stacktrace => S
                })
        end
    end),
    ok.

%% @doc 恢复单个会话到EMQX内存 - 修复版本
%%
%% 重要说明：
%% EMQX的会话恢复机制设计为在客户端重连时自动处理，而不是在系统启动时预创建会话。
%% 正确的做法是：
%% 1. 保持MongoDB中的会话数据完整
%% 2. 当客户端重连时，通过session.resumed钩子恢复会话状态
%% 3. 避免在系统启动时创建"孤儿"会话
%%
%% 因此，这个函数现在只验证会话数据的完整性，不实际创建EMQX会话
restore_single_session_to_emqx(SessionDoc) ->
    try
        ClientId = maps:get(<<"client_id">>, SessionDoc),
        Username = maps:get(<<"username">>, SessionDoc, <<>>),
        SessionExpiryInterval = maps:get(<<"session_expiry_interval">>, SessionDoc, 7200),
        ExpiryTime = maps:get(<<"expiry_time">>, SessionDoc, 0),
        Now = erlang:system_time(millisecond),

        ?SLOG(debug, #{
            msg => "validating_session_for_restoration",
            client_id => ClientId,
            username => Username,
            session_expiry_interval => SessionExpiryInterval,
            expiry_time => ExpiryTime,
            current_time => Now
        }),

        % 验证会话是否仍然有效
        case ExpiryTime of
            0 ->
                % 永不过期的会话
                ?SLOG(debug, #{
                    msg => "session_validated_never_expires",
                    client_id => ClientId
                }),
                ok;
            _ when ExpiryTime > Now ->
                % 未过期的会话
                ?SLOG(debug, #{
                    msg => "session_validated_not_expired",
                    client_id => ClientId,
                    remaining_time => ExpiryTime - Now
                }),
                ok;
            _ ->
                % 已过期的会话，应该被清理
                ?SLOG(warning, #{
                    msg => "session_expired_should_be_cleaned",
                    client_id => ClientId,
                    expired_time => Now - ExpiryTime
                }),
                {error, session_expired}
        end
    catch
        E:R:S ->
            ClientIdForError = maps:get(<<"client_id">>, SessionDoc, <<"unknown">>),
            ?SLOG(error, #{
                msg => "exception_restoring_single_session",
                client_id => ClientIdForError,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {exception, E, R}}
    end.





%% @doc 从MongoDB恢复会话数据
%% 这是会话恢复的核心函数，负责从MongoDB中恢复订阅、未确认消息等会话状态
restore_session_data_from_mongodb(ClientId) ->
    try
        ?SLOG(debug, #{
            msg => "starting_session_data_restoration_from_mongodb",
            client_id => ClientId
        }),

        RestoredData = #{},

        % 第一步：恢复订阅数据
        SubscriptionResult = case emqx_plugin_mongodb_subscription:restore_client_subscriptions(ClientId) of
            {ok, SubscriptionCount} when is_integer(SubscriptionCount) ->
                ?SLOG(debug, #{
                    msg => "subscriptions_restored",
                    client_id => ClientId,
                    count => SubscriptionCount
                }),
                #{subscriptions => SubscriptionCount};
            SubscriptionCount when is_integer(SubscriptionCount) ->
                % 处理直接返回数字的情况
                ?SLOG(debug, #{
                    msg => "subscriptions_restored_direct_count",
                    client_id => ClientId,
                    count => SubscriptionCount
                }),
                #{subscriptions => SubscriptionCount};
            {error, SubReason} ->
                ?SLOG(warning, #{
                    msg => "failed_to_restore_subscriptions",
                    client_id => ClientId,
                    reason => SubReason
                }),
                #{subscriptions => 0};
            Other ->
                % 处理其他意外返回值
                ?SLOG(warning, #{
                    msg => "unexpected_subscription_restore_result",
                    client_id => ClientId,
                    result => Other
                }),
                #{subscriptions => 0}
        end,

        % 第二步：恢复未确认消息（如果消息持久化启用）
        Config = emqx_plugin_mongodb:read_config(),
        MessageConfig = maps:get(message_persistence, Config, #{}),
        MessageResult = case maps:get(enabled, MessageConfig, false) of
            true ->
                case emqx_plugin_mongodb_message:restore_client_messages(ClientId) of
                    {ok, MessageCount} ->
                        ?SLOG(debug, #{
                            msg => "messages_restored",
                            client_id => ClientId,
                            count => MessageCount
                        }),
                        #{messages => MessageCount};
                    {error, MsgReason} ->
                        ?SLOG(warning, #{
                            msg => "failed_to_restore_messages",
                            client_id => ClientId,
                            reason => MsgReason
                        }),
                        #{messages => 0}
                end;
            false ->
                #{messages => 0}
        end,

        % 合并恢复结果
        FinalResult = maps:merge(maps:merge(RestoredData, SubscriptionResult), MessageResult),

        ?SLOG(info, #{
            msg => "session_data_restoration_completed",
            client_id => ClientId,
            restored_data => FinalResult
        }),

        {ok, FinalResult}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "exception_in_session_data_restoration",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {exception, E, R}}
    end.

%% @doc 检查会话是否已过期
%% 简化：保留这个实用函数，用于会话过期检查
is_session_expired(Session) ->
    ExpiryTime = maps:get(<<"expiry_time">>, Session, 0),
    case ExpiryTime of
        0 -> false; % 永不过期
        _ ->
            Now = erlang:system_time(millisecond),
            ExpiryTime =< Now
    end.

%% @doc 清理过期会话数据
cleanup_expired_session(ClientId) ->
    try
        ?SLOG(info, #{msg => "cleaning_expired_session", client_id => ClientId}),

        % 删除过期的会话数据
        SessionCollection = get_session_collection(),
        SessionFilter = #{<<"client_id">> => ClientId},

        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                                {delete, SessionCollection, SessionFilter}) of
            {ok, _} ->
                ?SLOG(info, #{msg => "expired_session_cleaned", client_id => ClientId});
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_clean_expired_session",
                    client_id => ClientId,
                    reason => Reason
                })
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_cleaning_expired_session",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% @doc 删除会话数据（用于clean_start=true的情况）
delete_session_data(ClientId) ->
    try
        ?SLOG(debug, #{msg => "deleting_session_data_for_clean_start", client_id => ClientId}),

        % 删除会话数据
        SessionCollection = get_session_collection(),
        SessionFilter = #{<<"client_id">> => ClientId},
        emqx_plugin_mongodb_api:delete_many(SessionCollection, SessionFilter),

        % 清除缓存
        invalidate_session_cache(ClientId),

        ?SLOG(debug, #{msg => "session_data_deleted_successfully", client_id => ClientId})
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_deleting_session_data",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S
            })
    end.



%% 启动清理定时器
start_cleanup_timer() ->
    CleanupInterval = get_cleanup_interval(),
    ?SLOG(info, #{msg => "starting_session_cleanup_timer", interval => CleanupInterval}),
    % 使用独立进程处理定时清理，避免进程依赖问题
    TimerPid = spawn(fun() -> cleanup_timer_loop(CleanupInterval) end),
    % 将定时器进程ID存储到进程字典中，以便后续停止
    put(session_cleanup_timer_pid, TimerPid),
    ok.

%% 停止清理定时器
stop_cleanup_timer() ->
    case get(session_cleanup_timer_pid) of
        TimerPid when is_pid(TimerPid) ->
            ?SLOG(info, #{msg => "stopping_session_cleanup_timer", pid => TimerPid}),
            case is_process_alive(TimerPid) of
                true ->
                    exit(TimerPid, normal),
                    ?SLOG(info, #{msg => "session_cleanup_timer_stopped", pid => TimerPid});
                false ->
                    ?SLOG(debug, #{msg => "session_cleanup_timer_already_dead", pid => TimerPid})
            end,
            erase(session_cleanup_timer_pid);
        _ ->
            ?SLOG(warning, #{msg => "invalid_session_cleanup_timer_pid"}),
            erase(session_cleanup_timer_pid)
    end,
    ok.

%% 清理定时器循环
cleanup_timer_loop(Interval) ->
    receive
        stop ->
            ?SLOG(info, #{msg => "session_cleanup_timer_received_stop_signal"}),
            ok
    after Interval ->
        ?SLOG(info, #{msg => "cleaning_expired_sessions"}),
        % 执行清理
        spawn(fun() -> do_cleanup_expired_sessions() end),
        % 继续循环
        cleanup_timer_loop(Interval)
    end.

%% 清理过期会话（保留原函数用于手动调用）
cleanup_expired_sessions() ->
    ?SLOG(info, #{msg => "manual_cleaning_expired_sessions"}),
    spawn(fun() -> do_cleanup_expired_sessions() end),
    ok.

%% 执行过期会话清理
do_cleanup_expired_sessions() ->
    try
        % 获取集合名称
        SessionCollection = get_session_collection(),

        % 计算过期时间
        Now = erlang:system_time(millisecond),

        % 删除已过期的会话
        Filter = #{<<"expiry_time">> => #{<<"$lt">> => Now}},

        % 获取批次大小
        BatchSize = get_cleanup_batch_size(),

        % 执行删除 (注意：MongoDB delete操作的limit只能是0或1，不能用BatchSize)
        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                                {delete_many, SessionCollection, Filter, #{}}) of
            {ok, #{<<"deletedCount">> := DeletedCount}} ->
                ?SLOG(info, #{msg => "expired_sessions_cleaned", count => DeletedCount});
            {error, Reason} ->
                ?SLOG(error, #{msg => "failed_to_clean_expired_sessions", reason => Reason})
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_cleaning_expired_sessions",
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% 集成函数
integrate() ->
    ?SLOG(info, #{msg => "integrating_session_module"}),
    % 将此模块注册到协调器
    case erlang:function_exported(emqx_plugin_mongodb_coordinator, register_module, 2) of
        true ->
            emqx_plugin_mongodb_coordinator:register_module(?MODULE, #{
                priority => high,
                description => <<"Session persistence module">>,
                features => [session_persistence, auto_restore]
            });
        false ->
            ok
    end.

%% 获取集合名称
get_session_collection() ->
    % 从配置中获取集合名称
    Config = application:get_env(emqx_plugin_mongodb, config, #{}),
    SessionConfig = maps:get(session_persistence, Config, #{}),
    maps:get(collection, SessionConfig, ?DEFAULT_SESSION_COLLECTION).

%% 获取遗嘱消息集合名称
get_will_message_collection() ->
    ?DEFAULT_WILL_MESSAGE_COLLECTION.

%% 获取会话过期时间
get_session_expiry() ->
    Config = application:get_env(emqx_plugin_mongodb, config, #{}),
    SessionConfig = maps:get(session_persistence, Config, #{}),
    maps:get(session_expiry, SessionConfig, ?DEFAULT_SESSION_EXPIRY).

%% 获取清理间隔
get_cleanup_interval() ->
    Config = application:get_env(emqx_plugin_mongodb, config, #{}),
    SessionConfig = maps:get(session_persistence, Config, #{}),
    maps:get(cleanup_interval, SessionConfig, ?DEFAULT_CLEANUP_INTERVAL).

%% 获取清理批次大小
get_cleanup_batch_size() ->
    Config = application:get_env(emqx_plugin_mongodb, config, #{}),
    SessionConfig = maps:get(session_persistence, Config, #{}),
    maps:get(cleanup_batch_size, SessionConfig, ?DEFAULT_CLEANUP_BATCH_SIZE).

%% 等待MongoDB资源就绪
wait_for_mongodb_ready() ->
    wait_for_resource(30).

wait_for_resource(0) ->
    ?SLOG(error, #{msg => "mongodb_resource_not_ready_after_retries"}),
    {error, timeout};
wait_for_resource(Retries) ->
    case emqx_resource:health_check(?PLUGIN_MONGODB_RESOURCE_ID) of
        ok ->
            ?SLOG(info, #{msg => "mongodb_resource_ready"}),
            ok;
        {ok, _} ->
            ?SLOG(info, #{msg => "mongodb_resource_ready"}),
            ok;
        _ ->
            ?SLOG(warning, #{msg => "mongodb_resource_not_ready", retries_left => Retries}),
            timer:sleep(1000),
            wait_for_resource(Retries - 1)
    end.



%% @doc 更新会话统计信息 - 修复后的简化实现
update_session_stats(ClientId, Action) ->
    try
        Now = erlang:system_time(millisecond),
        SessionCollection = get_session_collection(),

        % 根据动作执行不同的更新
        case Action of
            connect ->
                % 连接时更新计数和状态
                UpdateDoc = #{
                    <<"$inc">> => #{
                        <<"connect_count">> => 1
                    },
                    <<"$set">> => #{
                        <<"last_connected_at">> => Now,
                        <<"is_online">> => true,
                        <<"updated_at">> => Now
                    }
                },

                emqx_plugin_mongodb_api:update_one(
                    SessionCollection,
                    #{<<"client_id">> => ClientId},
                    UpdateDoc
                ),

                ?SLOG(debug, #{
                    msg => "session_stats_updated_connect",
                    client_id => ClientId
                });

            disconnect ->
                % 断开连接时更新统计信息
                UpdateDoc = #{
                    <<"$inc">> => #{
                        <<"disconnect_count">> => 1
                    },
                    <<"$set">> => #{
                        <<"last_disconnected_at">> => Now,
                        <<"is_online">> => false,
                        <<"updated_at">> => Now
                    }
                },

                emqx_plugin_mongodb_api:update_one(
                    SessionCollection,
                    #{<<"client_id">> => ClientId},
                    UpdateDoc
                ),

                ?SLOG(debug, #{
                    msg => "session_stats_updated_disconnect",
                    client_id => ClientId
                });

            _ ->
                ?SLOG(warning, #{
                    msg => "unknown_session_stats_action",
                    client_id => ClientId,
                    action => Action
                })
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_updating_session_stats",
                client_id => ClientId,
                action => Action,
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% @doc 生成集合缩写
generate_collection_abbreviation(CollectionName) when is_binary(CollectionName) ->
    % 简单的缩写生成：取每个单词的首字母
    Parts = binary:split(CollectionName, <<"_">>, [global]),
    Abbr = lists:foldl(fun(Part, Acc) ->
        case Part of
            <<First:8, _/binary>> when First >= $a, First =< $z ->
                <<Acc/binary, First>>;
            <<First:8, _/binary>> when First >= $A, First =< $Z ->
                <<Acc/binary, First>>;
            _ ->
                Acc
        end
    end, <<>>, Parts),
    case Abbr of
        <<>> -> <<"col">>;
        _ -> Abbr
    end;
generate_collection_abbreviation(_) ->
    <<"col">>.

%% @doc 处理会话索引错误
handle_session_index_error(Collection, IndexName, Reason) ->
    ?SLOG(error, #{
        msg => "failed_to_create_session_index",
        collection => Collection,
        index_name => IndexName,
        reason => Reason
    }).

%% @doc 确保会话集合和索引存在
ensure_session_collection_and_indexes(Collection) ->
    try
        % 生成集合缩写 emqx_mqtt_sessions -> ems
        SessionAbbr = generate_collection_abbreviation(Collection),

        ?SLOG(info, #{
            msg => "ensuring_session_collection_and_indexes",
            collection => Collection,
            collection_abbr => SessionAbbr
        }),

        % 创建索引
        Indexes = [
            % 客户端ID唯一索引
            #{
                <<"key">> => #{<<"client_id">> => 1},
                <<"unique">> => true,
                <<"name">> => <<SessionAbbr/binary, "_client_id_unique">>
            },
            % 过期时间TTL索引
            #{
                <<"key">> => #{<<"expiry_time">> => 1},
                <<"expireAfterSeconds">> => 0,
                <<"name">> => <<SessionAbbr/binary, "_expiry_time_ttl">>
            },
            % 会话状态索引
            #{
                <<"key">> => #{<<"status">> => 1},
                <<"name">> => <<SessionAbbr/binary, "_status_asc">>
            },
            % 创建时间索引
            #{
                <<"key">> => #{<<"created_at">> => 1},
                <<"name">> => <<SessionAbbr/binary, "_created_at_asc">>
            },
            % 节点索引
            #{
                <<"key">> => #{<<"node">> => 1},
                <<"name">> => <<SessionAbbr/binary, "_node_asc">>
            },
            % 复合索引：状态+过期时间
            #{
                <<"key">> => #{<<"status">> => 1, <<"expiry_time">> => 1},
                <<"name">> => <<SessionAbbr/binary, "_status_expiry_compound">>
            }
        ],

        % 逐个创建索引，带有存在性检查
        lists:foreach(fun(IndexSpec) ->
            IndexName = maps:get(<<"name">>, IndexSpec),
            case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID, {create_index, Collection, IndexSpec}) of
                {ok, _} ->
                    ?SLOG(debug, #{
                        msg => "session_index_created",
                        collection => Collection,
                        index_name => IndexName
                    });
                {error, {error, {error, {op_msg_response, #{<<"code">> := 85}}}}} ->
                    % IndexOptionsConflict - 索引已存在但名称不同，这是正常情况
                    ?SLOG(debug, #{
                        msg => "session_index_already_exists",
                        collection => Collection,
                        index_name => IndexName
                    });
                {error, Reason} ->
                    ?SLOG(warning, #{
                        msg => "session_index_creation_failed",
                        collection => Collection,
                        index_name => IndexName,
                        reason => Reason
                    })
            end
        end, Indexes),

        ?SLOG(info, #{
            msg => "session_collection_and_indexes_ensured",
            collection => Collection,
            index_count => length(Indexes)
        })
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_ensuring_session_collection_and_indexes",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {ensure_session_collection_failed, R}}
    end.





