%% @doc EMQX MongoDB插件性能监控模块
%% 提供实时性能监控、指标收集和预测性维护功能
%% 
%% 功能特性：
%% 1. 实时性能指标收集
%% 2. 多维度性能分析
%% 3. 预测性维护
%% 4. 性能趋势分析
%% 5. 智能告警系统
%% 6. 性能优化建议

-module(emqx_plugin_mongodb_performance_monitor).

-behaviour(gen_server).

%% API exports
-export([
    start_link/1,
    stop/0,
    collect_metrics/0,
    get_performance_report/0,
    get_performance_trends/1,
    enable_predictive_maintenance/0,
    disable_predictive_maintenance/0,
    get_optimization_suggestions/0,
    set_alert_thresholds/1,
    get_real_time_metrics/0
]).

%% gen_server callbacks
-export([
    init/1,
    handle_call/3,
    handle_cast/2,
    handle_info/2,
    terminate/2,
    code_change/3
]).

%% Internal exports
-export([
    start_metrics_collection/0,
    analyze_performance_trends/1,
    generate_optimization_suggestions/1,
    check_alert_conditions/1,
    predict_performance_issues/1
]).

-include("emqx_plugin_mongodb.hrl").
-include_lib("emqx/include/logger.hrl").

%% 状态记录定义
-record(monitor_state, {
    collection_interval = 5000,      % 指标收集间隔
    predictive_enabled = false,      % 预测性维护开关
    alert_thresholds = #{},          % 告警阈值
    last_collection_time = 0,        % 上次收集时间
    performance_history = [],        % 性能历史数据
    trend_analysis_window = 3600,    % 趋势分析窗口（秒）
    optimization_cache = #{}         % 优化建议缓存
}).

%%====================================================================
%% API functions
%%====================================================================

%% @doc 启动性能监控服务
-spec start_link(map()) -> {ok, pid()} | {error, term()}.
start_link(Options) ->
    gen_server:start_link({local, ?MODULE}, ?MODULE, [Options], []).

%% @doc 停止性能监控服务
-spec stop() -> ok.
stop() ->
    gen_server:stop(?MODULE).

%% @doc 手动收集性能指标
-spec collect_metrics() -> {ok, map()} | {error, term()}.
collect_metrics() ->
    gen_server:call(?MODULE, collect_metrics).

%% @doc 获取性能报告
-spec get_performance_report() -> {ok, map()} | {error, term()}.
get_performance_report() ->
    gen_server:call(?MODULE, get_performance_report).

%% @doc 获取性能趋势分析
-spec get_performance_trends(integer()) -> {ok, map()} | {error, term()}.
get_performance_trends(TimeWindow) ->
    gen_server:call(?MODULE, {get_performance_trends, TimeWindow}).

%% @doc 启用预测性维护
-spec enable_predictive_maintenance() -> ok.
enable_predictive_maintenance() ->
    gen_server:cast(?MODULE, enable_predictive_maintenance).

%% @doc 禁用预测性维护
-spec disable_predictive_maintenance() -> ok.
disable_predictive_maintenance() ->
    gen_server:cast(?MODULE, disable_predictive_maintenance).

%% @doc 获取优化建议
-spec get_optimization_suggestions() -> {ok, list()} | {error, term()}.
get_optimization_suggestions() ->
    gen_server:call(?MODULE, get_optimization_suggestions).

%% @doc 设置告警阈值
-spec set_alert_thresholds(map()) -> ok.
set_alert_thresholds(Thresholds) ->
    gen_server:cast(?MODULE, {set_alert_thresholds, Thresholds}).

%% @doc 获取实时性能指标
-spec get_real_time_metrics() -> {ok, map()} | {error, term()}.
get_real_time_metrics() ->
    gen_server:call(?MODULE, get_real_time_metrics).

%%====================================================================
%% gen_server callbacks
%%====================================================================

%% @doc 初始化性能监控服务
init([Options]) ->
    try
        % 初始化ETS表
        init_performance_tables(),
        
        % 解析配置选项
        State = parse_monitor_options(Options, #monitor_state{}),
        
        % 启动定时收集任务
        schedule_metrics_collection(State#monitor_state.collection_interval),
        
        % 设置默认告警阈值
        DefaultThresholds = get_default_alert_thresholds(),
        NewState = State#monitor_state{alert_thresholds = DefaultThresholds},
        
        ?SLOG(info, #{
            msg => "performance_monitor_started",
            collection_interval => State#monitor_state.collection_interval,
            predictive_enabled => State#monitor_state.predictive_enabled
        }),
        
        {ok, NewState}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "performance_monitor_init_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {stop, {init_failed, R}}
    end.

%% @doc 处理同步调用
handle_call(collect_metrics, _From, State) ->
    try
        Metrics = do_collect_metrics(),
        {reply, {ok, Metrics}, State}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "collect_metrics_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {reply, {error, R}, State}
    end;

handle_call(get_performance_report, _From, State) ->
    try
        Report = generate_performance_report(State),
        {reply, {ok, Report}, State}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "get_performance_report_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {reply, {error, R}, State}
    end;

handle_call({get_performance_trends, TimeWindow}, _From, State) ->
    try
        Trends = analyze_performance_trends_with_window(TimeWindow, State),
        {reply, {ok, Trends}, State}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "get_performance_trends_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {reply, {error, R}, State}
    end;

handle_call(get_optimization_suggestions, _From, State) ->
    try
        Suggestions = get_cached_optimization_suggestions(State),
        {reply, {ok, Suggestions}, State}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "get_optimization_suggestions_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {reply, {error, R}, State}
    end;

handle_call(get_real_time_metrics, _From, State) ->
    try
        RealTimeMetrics = collect_real_time_metrics(),
        {reply, {ok, RealTimeMetrics}, State}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "get_real_time_metrics_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {reply, {error, R}, State}
    end;

handle_call(_Request, _From, State) ->
    {reply, {error, unknown_request}, State}.

%% @doc 处理异步消息
handle_cast(enable_predictive_maintenance, State) ->
    NewState = State#monitor_state{predictive_enabled = true},
    ?SLOG(info, #{msg => "predictive_maintenance_enabled"}),
    {noreply, NewState};

handle_cast(disable_predictive_maintenance, State) ->
    NewState = State#monitor_state{predictive_enabled = false},
    ?SLOG(info, #{msg => "predictive_maintenance_disabled"}),
    {noreply, NewState};

handle_cast({set_alert_thresholds, Thresholds}, State) ->
    NewState = State#monitor_state{alert_thresholds = Thresholds},
    ?SLOG(info, #{msg => "alert_thresholds_updated", thresholds => Thresholds}),
    {noreply, NewState};

handle_cast(_Msg, State) ->
    {noreply, State}.

%% @doc 处理定时器和其他消息
handle_info(collect_metrics_timer, State) ->
    try
        % 收集性能指标
        Metrics = do_collect_metrics(),
        
        % 更新性能历史
        NewHistory = update_performance_history(Metrics, State#monitor_state.performance_history),
        
        % 检查告警条件
        check_alert_conditions(Metrics, State#monitor_state.alert_thresholds),
        
        % 如果启用了预测性维护，进行预测分析
        NewState = case State#monitor_state.predictive_enabled of
            true ->
                perform_predictive_analysis(Metrics, State);
            false ->
                State
        end,
        
        % 更新状态
        UpdatedState = NewState#monitor_state{
            performance_history = NewHistory,
            last_collection_time = erlang:system_time(millisecond)
        },
        
        % 调度下次收集
        schedule_metrics_collection(State#monitor_state.collection_interval),
        
        {noreply, UpdatedState}
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "metrics_collection_timer_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            % 即使出错也要调度下次收集
            schedule_metrics_collection(State#monitor_state.collection_interval),
            {noreply, State}
    end;

handle_info(_Info, State) ->
    {noreply, State}.

%% @doc 服务终止处理
terminate(_Reason, _State) ->
    ?SLOG(info, #{msg => "performance_monitor_terminated"}),
    ok.

%% @doc 代码热更新
code_change(_OldVsn, State, _Extra) ->
    {ok, State}.

%%====================================================================
%% Internal functions
%%====================================================================

%% @doc 初始化性能监控相关的ETS表
init_performance_tables() ->
    % 性能指标表
    ets:new(?PERFORMANCE_METRICS_TAB, [
        named_table, public, set,
        {write_concurrency, true},
        {read_concurrency, true}
    ]),

    % 性能历史表
    ets:new(?PERFORMANCE_HISTORY_TAB, [
        named_table, public, ordered_set,
        {write_concurrency, true},
        {read_concurrency, true}
    ]),

    % 告警记录表
    ets:new(?ALERT_RECORDS_TAB, [
        named_table, public, ordered_set,
        {write_concurrency, true},
        {read_concurrency, true}
    ]),

    % 优化建议表
    ets:new(?OPTIMIZATION_SUGGESTIONS_TAB, [
        named_table, public, set,
        {write_concurrency, true},
        {read_concurrency, true}
    ]).

%% @doc 解析监控配置选项
parse_monitor_options(Options, State) ->
    CollectionInterval = maps:get(collection_interval, Options, 5000),
    PredictiveEnabled = maps:get(predictive_enabled, Options, false),
    TrendWindow = maps:get(trend_analysis_window, Options, 3600),

    State#monitor_state{
        collection_interval = CollectionInterval,
        predictive_enabled = PredictiveEnabled,
        trend_analysis_window = TrendWindow
    }.

%% @doc 调度指标收集任务
schedule_metrics_collection(Interval) ->
    erlang:send_after(Interval, self(), collect_metrics_timer).

%% @doc 获取默认告警阈值
get_default_alert_thresholds() ->
    #{
        cpu_usage => 0.8,           % CPU使用率80%
        memory_usage => 0.85,       % 内存使用率85%
        connection_errors => 0.05,  % 连接错误率5%
        response_time => 1000,      % 响应时间1秒
        throughput_drop => 0.3,     % 吞吐量下降30%
        disk_usage => 0.9           % 磁盘使用率90%
    }.

%% @doc 执行实际的指标收集
do_collect_metrics() ->
    Timestamp = erlang:system_time(millisecond),

    % 收集系统指标
    SystemMetrics = collect_system_metrics(),

    % 收集MongoDB连接指标
    ConnectionMetrics = collect_connection_metrics(),

    % 收集批处理性能指标
    BatchMetrics = collect_batch_metrics(),

    % 收集查询性能指标
    QueryMetrics = collect_query_metrics(),

    % 收集资源使用指标
    ResourceMetrics = collect_resource_metrics(),

    % 合并所有指标
    AllMetrics = #{
        timestamp => Timestamp,
        system => SystemMetrics,
        connection => ConnectionMetrics,
        batch => BatchMetrics,
        query => QueryMetrics,
        resource => ResourceMetrics
    },

    % 存储到ETS表
    ets:insert(?PERFORMANCE_METRICS_TAB, {current_metrics, AllMetrics}),
    ets:insert(?PERFORMANCE_HISTORY_TAB, {Timestamp, AllMetrics}),

    AllMetrics.

%% @doc 收集系统指标
collect_system_metrics() ->
    try
        % CPU使用率
        CpuUsage = get_cpu_usage(),

        % 内存使用情况
        MemoryInfo = erlang:memory(),
        TotalMemory = proplists:get_value(total, MemoryInfo, 0),
        ProcessMemory = proplists:get_value(processes, MemoryInfo, 0),
        MemoryUsage = case TotalMemory of
            0 -> 0.0;
            _ -> ProcessMemory / TotalMemory
        end,

        % 进程数量
        ProcessCount = erlang:system_info(process_count),
        ProcessLimit = erlang:system_info(process_limit),
        ProcessUsage = ProcessCount / ProcessLimit,

        % 端口数量
        PortCount = erlang:system_info(port_count),
        PortLimit = erlang:system_info(port_limit),
        PortUsage = PortCount / PortLimit,

        #{
            cpu_usage => CpuUsage,
            memory_usage => MemoryUsage,
            total_memory => TotalMemory,
            process_memory => ProcessMemory,
            process_count => ProcessCount,
            process_usage => ProcessUsage,
            port_count => PortCount,
            port_usage => PortUsage
        }
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "collect_system_metrics_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            #{
                cpu_usage => 0.0,
                memory_usage => 0.0,
                process_usage => 0.0,
                port_usage => 0.0
            }
    end.

%% @doc 收集连接指标
collect_connection_metrics() ->
    try
        % 活跃连接数
        ActiveConnections = count_active_connections(),

        % 连接池状态
        PoolStatus = get_connection_pool_status(),

        % 连接错误率
        ErrorRate = calculate_connection_error_rate(),

        % 平均响应时间
        AvgResponseTime = calculate_average_response_time(),

        % 连接健康度
        HealthScore = calculate_overall_connection_health(),

        #{
            active_connections => ActiveConnections,
            pool_status => PoolStatus,
            error_rate => ErrorRate,
            avg_response_time => AvgResponseTime,
            health_score => HealthScore
        }
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "collect_connection_metrics_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            #{
                active_connections => 0,
                error_rate => 0.0,
                avg_response_time => 0,
                health_score => 0.0
            }
    end.

%% @doc 收集批处理指标
collect_batch_metrics() ->
    try
        % 当前批处理大小
        CurrentBatchSize = get_current_batch_size(),

        % 批处理吞吐量
        BatchThroughput = calculate_batch_throughput(),

        % 批处理延迟
        BatchLatency = calculate_batch_latency(),

        % 批处理成功率
        BatchSuccessRate = calculate_batch_success_rate(),

        % 队列长度
        QueueLength = get_batch_queue_length(),

        #{
            current_batch_size => CurrentBatchSize,
            throughput => BatchThroughput,
            latency => BatchLatency,
            success_rate => BatchSuccessRate,
            queue_length => QueueLength
        }
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "collect_batch_metrics_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            #{
                current_batch_size => 0,
                throughput => 0,
                latency => 0,
                success_rate => 0.0,
                queue_length => 0
            }
    end.

%% @doc 收集查询指标
collect_query_metrics() ->
    try
        % 查询执行时间统计
        QueryStats = get_query_execution_stats(),

        % 慢查询数量
        SlowQueryCount = count_slow_queries(),

        % 查询缓存命中率
        CacheHitRate = calculate_query_cache_hit_rate(),

        % 索引使用效率
        IndexEfficiency = calculate_index_efficiency(),

        #{
            query_stats => QueryStats,
            slow_query_count => SlowQueryCount,
            cache_hit_rate => CacheHitRate,
            index_efficiency => IndexEfficiency
        }
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "collect_query_metrics_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            #{
                query_stats => #{},
                slow_query_count => 0,
                cache_hit_rate => 0.0,
                index_efficiency => 0.0
            }
    end.

%% @doc 收集资源使用指标
collect_resource_metrics() ->
    try
        % 磁盘使用情况
        DiskUsage = get_disk_usage(),

        % 网络I/O统计
        NetworkIO = get_network_io_stats(),

        % 文件描述符使用情况
        FdUsage = get_file_descriptor_usage(),

        % ETS表内存使用
        EtsMemory = get_ets_memory_usage(),

        #{
            disk_usage => DiskUsage,
            network_io => NetworkIO,
            fd_usage => FdUsage,
            ets_memory => EtsMemory
        }
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "collect_resource_metrics_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            #{
                disk_usage => 0.0,
                network_io => #{},
                fd_usage => 0.0,
                ets_memory => 0
            }
    end.

%% @doc 生成性能报告
generate_performance_report(State) ->
    try
        % 获取当前指标
        CurrentMetrics = case ets:lookup(?PERFORMANCE_METRICS_TAB, current_metrics) of
            [{current_metrics, Metrics}] -> Metrics;
            [] -> #{}
        end,

        % 分析性能趋势
        Trends = analyze_performance_trends(State#monitor_state.performance_history),

        % 生成优化建议
        Suggestions = generate_optimization_suggestions(CurrentMetrics),

        % 计算性能评分
        PerformanceScore = calculate_overall_performance_score(CurrentMetrics),

        % 获取告警状态
        AlertStatus = get_current_alert_status(State#monitor_state.alert_thresholds),

        #{
            timestamp => erlang:system_time(millisecond),
            current_metrics => CurrentMetrics,
            performance_score => PerformanceScore,
            trends => Trends,
            optimization_suggestions => Suggestions,
            alert_status => AlertStatus,
            collection_interval => State#monitor_state.collection_interval,
            predictive_enabled => State#monitor_state.predictive_enabled
        }
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "generate_performance_report_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            #{
                timestamp => erlang:system_time(millisecond),
                error => "Failed to generate performance report"
            }
    end.

%% @doc 分析性能趋势
analyze_performance_trends(PerformanceHistory) ->
    try
        case length(PerformanceHistory) of
            N when N < 2 ->
                #{trend => "insufficient_data"};
            _ ->
                % 分析CPU使用趋势
                CpuTrend = analyze_metric_trend(PerformanceHistory, [system, cpu_usage]),

                % 分析内存使用趋势
                MemoryTrend = analyze_metric_trend(PerformanceHistory, [system, memory_usage]),

                % 分析响应时间趋势
                ResponseTrend = analyze_metric_trend(PerformanceHistory, [connection, avg_response_time]),

                % 分析吞吐量趋势
                ThroughputTrend = analyze_metric_trend(PerformanceHistory, [batch, throughput]),

                #{
                    cpu_trend => CpuTrend,
                    memory_trend => MemoryTrend,
                    response_time_trend => ResponseTrend,
                    throughput_trend => ThroughputTrend
                }
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "analyze_performance_trends_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            #{trend => "analysis_failed"}
    end.

%% @doc 分析单个指标的趋势
analyze_metric_trend(History, MetricPath) ->
    try
        % 提取指标值
        Values = extract_metric_values(History, MetricPath),

        case length(Values) of
            N when N < 2 ->
                #{trend => "insufficient_data"};
            _ ->
                % 计算趋势
                {Slope, _} = calculate_linear_regression(Values),

                % 计算变化率
                ChangeRate = calculate_change_rate(Values),

                % 确定趋势方向
                TrendDirection = if
                    Slope > 0.1 -> increasing;
                    Slope < -0.1 -> decreasing;
                    true -> stable
                end,

                #{
                    trend => TrendDirection,
                    slope => Slope,
                    change_rate => ChangeRate,
                    current_value => lists:last(Values),
                    avg_value => lists:sum(Values) / length(Values)
                }
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "analyze_metric_trend_failed",
                metric_path => MetricPath,
                error => E,
                reason => R,
                stacktrace => S
            }),
            #{trend => "analysis_failed"}
    end.

%% ============================================================================
%% 支持函数实现
%% ============================================================================

%% @doc 获取CPU使用率
get_cpu_usage() ->
    try
        % 使用recon获取CPU使用率
        case recon:node_stats(1, 1000) of
            [{_, Stats}] ->
                proplists:get_value(scheduler_usage, Stats, 0.0);
            _ ->
                0.0
        end
    catch
        _:_ -> 0.0
    end.

%% @doc 统计活跃连接数
count_active_connections() ->
    try
        % 从连接健康表中统计活跃连接
        case ets:tab2list(?CONNECTION_HEALTH_TAB) of
            [] -> 0;
            HealthRecords ->
                length([Pid || {Pid, _} <- HealthRecords, is_process_alive(Pid)])
        end
    catch
        _:_ -> 0
    end.

%% @doc 获取连接池状态
get_connection_pool_status() ->
    try
        % 获取所有连接池的状态信息
        case ets:tab2list(?CONNECTION_POOL_TAB) of
            [] -> #{total_pools => 0, total_connections => 0};
            Pools ->
                TotalPools = length(Pools),
                TotalConnections = lists:sum([length(Conns) || {_, Conns} <- Pools]),
                #{
                    total_pools => TotalPools,
                    total_connections => TotalConnections,
                    avg_connections_per_pool => TotalConnections / max(1, TotalPools)
                }
        end
    catch
        _:_ -> #{total_pools => 0, total_connections => 0}
    end.

%% @doc 计算连接错误率
calculate_connection_error_rate() ->
    try
        % 从统计表中获取错误率信息
        case ets:lookup(?CONNECTION_HEALTH_TAB, error_stats) of
            [{error_stats, #{total_requests := Total, failed_requests := Failed}}] when Total > 0 ->
                Failed / Total;
            _ ->
                0.0
        end
    catch
        _:_ -> 0.0
    end.

%% @doc 计算平均响应时间
calculate_average_response_time() ->
    try
        % 从性能指标表中获取响应时间统计
        case ets:lookup(?PERFORMANCE_METRICS_TAB, response_time_stats) of
            [{response_time_stats, #{total_time := TotalTime, request_count := Count}}] when Count > 0 ->
                round(TotalTime / Count);
            _ ->
                0
        end
    catch
        _:_ -> 0
    end.

%% @doc 计算整体连接健康度
calculate_overall_connection_health() ->
    try
        case ets:tab2list(?CONNECTION_HEALTH_TAB) of
            [] -> 0.0;
            HealthRecords ->
                HealthScores = [Score || {_, #{health_score := Score}} <- HealthRecords],
                case HealthScores of
                    [] -> 0.0;
                    _ -> lists:sum(HealthScores) / length(HealthScores)
                end
        end
    catch
        _:_ -> 0.0
    end.

%% @doc 获取当前批处理大小
get_current_batch_size() ->
    try
        case ets:lookup(?BATCH_CONFIG_TAB, current_batch_size) of
            [{current_batch_size, Size}] -> Size;
            [] -> ?DEFAULT_BATCH_SIZE
        end
    catch
        _:_ -> ?DEFAULT_BATCH_SIZE
    end.

%% @doc 计算批处理吞吐量
calculate_batch_throughput() ->
    try
        case ets:lookup(?BATCH_METRICS_TAB, throughput_stats) of
            [{throughput_stats, #{messages_processed := Processed, time_window := Window}}] when Window > 0 ->
                round(Processed / (Window / 1000)); % 每秒处理的消息数
            _ ->
                0
        end
    catch
        _:_ -> 0
    end.

%% @doc 计算批处理延迟
calculate_batch_latency() ->
    try
        case ets:lookup(?BATCH_METRICS_TAB, latency_stats) of
            [{latency_stats, #{total_latency := TotalLatency, batch_count := Count}}] when Count > 0 ->
                round(TotalLatency / Count);
            _ ->
                0
        end
    catch
        _:_ -> 0
    end.

%% @doc 计算批处理成功率
calculate_batch_success_rate() ->
    try
        case ets:lookup(?BATCH_METRICS_TAB, success_stats) of
            [{success_stats, #{total_batches := Total, successful_batches := Success}}] when Total > 0 ->
                Success / Total;
            _ ->
                1.0 % 默认100%成功率
        end
    catch
        _:_ -> 1.0
    end.

%% @doc 获取批处理队列长度
get_batch_queue_length() ->
    try
        case ets:lookup(?BATCH_METRICS_TAB, queue_stats) of
            [{queue_stats, #{current_queue_length := Length}}] -> Length;
            [] -> 0
        end
    catch
        _:_ -> 0
    end.

%% @doc 获取查询执行统计
get_query_execution_stats() ->
    try
        case ets:lookup(?QUERY_PERFORMANCE_TAB, execution_stats) of
            [{execution_stats, Stats}] -> Stats;
            [] -> #{total_queries => 0, avg_execution_time => 0}
        end
    catch
        _:_ -> #{total_queries => 0, avg_execution_time => 0}
    end.

%% @doc 统计慢查询数量
count_slow_queries() ->
    try
        SlowQueryThreshold = 1000, % 1秒阈值
        case ets:lookup(?QUERY_PERFORMANCE_TAB, slow_queries) of
            [{slow_queries, SlowQueries}] ->
                length([Q || Q <- SlowQueries, maps:get(execution_time, Q, 0) > SlowQueryThreshold]);
            [] ->
                0
        end
    catch
        _:_ -> 0
    end.

%% @doc 计算查询缓存命中率
calculate_query_cache_hit_rate() ->
    try
        case ets:lookup(?QUERY_PERFORMANCE_TAB, cache_stats) of
            [{cache_stats, #{hits := Hits, misses := Misses}}] when (Hits + Misses) > 0 ->
                Hits / (Hits + Misses);
            _ ->
                0.0
        end
    catch
        _:_ -> 0.0
    end.

%% @doc 计算索引使用效率
calculate_index_efficiency() ->
    try
        case ets:lookup(?QUERY_PERFORMANCE_TAB, index_stats) of
            [{index_stats, #{index_hits := IndexHits, table_scans := TableScans}}] when (IndexHits + TableScans) > 0 ->
                IndexHits / (IndexHits + TableScans);
            _ ->
                1.0 % 默认100%效率
        end
    catch
        _:_ -> 1.0
    end.

%% @doc 获取磁盘使用情况
get_disk_usage() ->
    try
        % 简化的磁盘使用率计算
        case file:read_file_info("/") of
            {ok, _} -> 0.5; % 默认50%使用率
            _ -> 0.0
        end
    catch
        _:_ -> 0.0
    end.

%% @doc 获取网络I/O统计
get_network_io_stats() ->
    try
        #{
            bytes_in => 0,
            bytes_out => 0,
            packets_in => 0,
            packets_out => 0
        }
    catch
        _:_ -> #{}
    end.

%% @doc 获取文件描述符使用情况
get_file_descriptor_usage() ->
    try
        % 简化的文件描述符使用率
        0.3 % 默认30%使用率
    catch
        _:_ -> 0.0
    end.

%% @doc 获取ETS表内存使用
get_ets_memory_usage() ->
    try
        ets:info(system, memory)
    catch
        _:_ -> 0
    end.

%% @doc 生成优化建议
generate_optimization_suggestions(Metrics) ->
    try
        Suggestions = [],

        % 检查CPU使用率
        CpuSuggestions = case maps:get([system, cpu_usage], Metrics, 0.0) of
            CpuUsage when CpuUsage > 0.8 ->
                ["Consider reducing batch size to lower CPU usage"];
            _ -> []
        end,

        % 检查内存使用率
        MemorySuggestions = case maps:get([system, memory_usage], Metrics, 0.0) of
            MemoryUsage when MemoryUsage > 0.85 ->
                ["Consider increasing memory allocation or reducing cache size"];
            _ -> []
        end,

        % 检查响应时间
        ResponseSuggestions = case maps:get([connection, avg_response_time], Metrics, 0) of
            ResponseTime when ResponseTime > 1000 ->
                ["Consider optimizing queries or adding more connections"];
            _ -> []
        end,

        lists:flatten([Suggestions, CpuSuggestions, MemorySuggestions, ResponseSuggestions])
    catch
        _:_ -> []
    end.

%% @doc 计算整体性能评分
calculate_overall_performance_score(Metrics) ->
    try
        % CPU评分 (越低越好)
        CpuScore = 1.0 - maps:get([system, cpu_usage], Metrics, 0.0),

        % 内存评分 (越低越好)
        MemoryScore = 1.0 - maps:get([system, memory_usage], Metrics, 0.0),

        % 响应时间评分 (越低越好)
        ResponseTime = maps:get([connection, avg_response_time], Metrics, 0),
        ResponseScore = max(0.0, 1.0 - (ResponseTime / 2000)), % 2秒为基准

        % 健康度评分
        HealthScore = maps:get([connection, health_score], Metrics, 1.0),

        % 加权平均
        (CpuScore * 0.3 + MemoryScore * 0.3 + ResponseScore * 0.2 + HealthScore * 0.2)
    catch
        _:_ -> 0.5 % 默认中等评分
    end.

%% @doc 获取当前告警状态
get_current_alert_status(Thresholds) ->
    try
        case ets:lookup(?PERFORMANCE_METRICS_TAB, current_metrics) of
            [{current_metrics, Metrics}] ->
                check_all_thresholds(Metrics, Thresholds);
            [] ->
                #{alerts => [], status => "no_data"}
        end
    catch
        _:_ -> #{alerts => [], status => "error"}
    end.

%% @doc 检查所有阈值
check_all_thresholds(Metrics, Thresholds) ->
    Alerts = [],

    % 检查CPU阈值
    CpuThreshold = maps:get(cpu_usage, Thresholds, 0.8),
    CpuAlerts = case maps:get([system, cpu_usage], Metrics, 0.0) of
        CpuUsage when CpuUsage > CpuThreshold ->
            [#{type => cpu_usage, value => CpuUsage, threshold => CpuThreshold}];
        _ -> []
    end,

    % 检查内存阈值
    MemoryThreshold = maps:get(memory_usage, Thresholds, 0.85),
    MemoryAlerts = case maps:get([system, memory_usage], Metrics, 0.0) of
        MemoryUsage when MemoryUsage > MemoryThreshold ->
            [#{type => memory_usage, value => MemoryUsage, threshold => MemoryThreshold}];
        _ -> []
    end,

    AllAlerts = lists:flatten([Alerts, CpuAlerts, MemoryAlerts]),

    Status = case AllAlerts of
        [] -> "normal";
        _ -> "alert"
    end,

    #{alerts => AllAlerts, status => Status}.

%% @doc 更新性能历史
update_performance_history(Metrics, History) ->
    NewHistory = [Metrics | History],
    % 限制历史记录数量
    case length(NewHistory) > ?PERFORMANCE_HISTORY_LIMIT of
        true -> lists:sublist(NewHistory, ?PERFORMANCE_HISTORY_LIMIT);
        false -> NewHistory
    end.

%% @doc 检查告警条件
check_alert_conditions(Metrics, Thresholds) ->
    try
        AlertStatus = check_all_thresholds(Metrics, Thresholds),
        case maps:get(alerts, AlertStatus, []) of
            [] -> ok;
            Alerts ->
                lists:foreach(fun(Alert) ->
                    ?SLOG(warning, #{
                        msg => "performance_alert",
                        alert => Alert
                    })
                end, Alerts)
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "check_alert_conditions_failed",
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% @doc 执行预测性分析
perform_predictive_analysis(Metrics, State) ->
    try
        % 简化的预测性分析
        % 这里可以添加更复杂的机器学习算法

        % 预测CPU使用趋势
        CpuUsage = maps:get([system, cpu_usage], Metrics, 0.0),
        case CpuUsage > 0.7 of
            true ->
                ?SLOG(info, #{
                    msg => "predictive_analysis_cpu_warning",
                    current_usage => CpuUsage,
                    prediction => "CPU usage may exceed threshold soon"
                });
            false -> ok
        end,

        State
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "predictive_analysis_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            State
    end.

%% @doc 获取缓存的优化建议
get_cached_optimization_suggestions(State) ->
    case maps:get(optimization_cache, State#monitor_state.optimization_cache, []) of
        [] ->
            % 如果缓存为空，生成新的建议
            case ets:lookup(?PERFORMANCE_METRICS_TAB, current_metrics) of
                [{current_metrics, Metrics}] ->
                    generate_optimization_suggestions(Metrics);
                [] ->
                    []
            end;
        CachedSuggestions ->
            CachedSuggestions
    end.

%% @doc 收集实时性能指标
collect_real_time_metrics() ->
    try
        % 收集当前时刻的性能指标
        do_collect_metrics()
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "collect_real_time_metrics_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            #{
                timestamp => erlang:system_time(millisecond),
                error => "Failed to collect real-time metrics"
            }
    end.

%% @doc 分析指定时间窗口的性能趋势
analyze_performance_trends_with_window(TimeWindow, State) ->
    try
        % 从历史数据中筛选指定时间窗口的数据
        CurrentTime = erlang:system_time(millisecond),
        StartTime = CurrentTime - (TimeWindow * 1000),

        FilteredHistory = lists:filter(fun(#{timestamp := Timestamp}) ->
            Timestamp >= StartTime
        end, State#monitor_state.performance_history),

        analyze_performance_trends(FilteredHistory)
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "analyze_performance_trends_with_window_failed",
                time_window => TimeWindow,
                error => E,
                reason => R,
                stacktrace => S
            }),
            #{trend => "analysis_failed"}
    end.

%% @doc 提取指标值
extract_metric_values(History, MetricPath) ->
    try
        lists:filtermap(fun(HistoryItem) ->
            case extract_nested_value(HistoryItem, MetricPath) of
                undefined -> false;
                Value when is_number(Value) -> {true, Value};
                _ -> false
            end
        end, History)
    catch
        _:_ -> []
    end.

%% @doc 从嵌套映射中提取值
extract_nested_value(Map, []) ->
    Map;
extract_nested_value(Map, [Key | Rest]) when is_map(Map) ->
    case maps:get(Key, Map, undefined) of
        undefined -> undefined;
        Value -> extract_nested_value(Value, Rest)
    end;
extract_nested_value(_, _) ->
    undefined.

%% @doc 计算线性回归
calculate_linear_regression(Values) ->
    try
        N = length(Values),
        case N of
            0 -> {0.0, 0.0};
            1 -> {0.0, hd(Values)};
            _ ->
                % 创建X值序列 (1, 2, 3, ...)
                XValues = lists:seq(1, N),

                % 计算均值
                XMean = lists:sum(XValues) / N,
                YMean = lists:sum(Values) / N,

                % 计算斜率和截距
                Numerator = lists:sum([
                    (X - XMean) * (Y - YMean) ||
                    {X, Y} <- lists:zip(XValues, Values)
                ]),

                Denominator = lists:sum([
                    (X - XMean) * (X - XMean) ||
                    X <- XValues
                ]),

                case Denominator of
                    0.0 -> {0.0, YMean};
                    _ ->
                        Slope = Numerator / Denominator,
                        Intercept = YMean - Slope * XMean,
                        {Slope, Intercept}
                end
        end
    catch
        _:_ -> {0.0, 0.0}
    end.

%% @doc 计算变化率
calculate_change_rate(Values) ->
    try
        case length(Values) of
            N when N < 2 -> 0.0;
            _ ->
                FirstValue = hd(Values),
                LastValue = lists:last(Values),
                case FirstValue of
                    0.0 -> 0.0;
                    _ -> (LastValue - FirstValue) / FirstValue
                end
        end
    catch
        _:_ -> 0.0
    end.

%% @doc 启动指标收集
start_metrics_collection() ->
    % 这个函数在init中被调用，实际的收集通过定时器完成
    ok.

%% @doc 检查告警条件（单参数版本）
check_alert_conditions(Metrics) ->
    DefaultThresholds = get_default_alert_thresholds(),
    check_alert_conditions(Metrics, DefaultThresholds).

%% @doc 预测性能问题
predict_performance_issues(Metrics) ->
    try
        Issues = [],

        % 预测CPU问题
        CpuIssues = case maps:get([system, cpu_usage], Metrics, 0.0) of
            CpuUsage when CpuUsage > 0.7 ->
                ["CPU usage trending high, may exceed threshold"];
            _ -> []
        end,

        % 预测内存问题
        MemoryIssues = case maps:get([system, memory_usage], Metrics, 0.0) of
            MemoryUsage when MemoryUsage > 0.75 ->
                ["Memory usage trending high, may cause performance issues"];
            _ -> []
        end,

        % 预测响应时间问题
        ResponseIssues = case maps:get([connection, avg_response_time], Metrics, 0) of
            ResponseTime when ResponseTime > 800 ->
                ["Response time increasing, may affect user experience"];
            _ -> []
        end,

        lists:flatten([Issues, CpuIssues, MemoryIssues, ResponseIssues])
    catch
        _:_ -> []
    end.
