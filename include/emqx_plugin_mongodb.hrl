%%%-------------------------------------------------------------------
%%% @doc
%%% MongoDB插件的共享头文件
%%% 包含所有模块共享的宏定义和记录定义
%%% @end
%%%-------------------------------------------------------------------

%%% @doc MongoDB插件头文件
%%% 定义插件使用的宏和常量
-ifndef(EMQX_PLUGIN_MONGODB_HRL).
-define(EMQX_PLUGIN_MONGODB_HRL, true).

%% 引入EMQX消息记录定义
-include_lib("emqx_utils/include/emqx_message.hrl").
%% 引入EMQX日志头文件
-include_lib("emqx/include/logger.hrl").

%% 定义共享订阅前缀宏
-define(SHARED_PREFIX, <<"$share">>).
-define(QUEUE_PREFIX, <<"$queue">>).

%%% @doc 插件资源组名称（二进制字符串，用于MongoDB连接）
-define(PLUGIN_MONGODB_RESOURCE_GROUP, <<"emqx_plugin_mongodb">>).

%%% @doc 插件资源ID（原子，用于EMQX资源标识）
-define(PLUGIN_MONGODB_RESOURCE_ID, mongodb_resource).

%%% @doc 插件使用的ETS表名（原子，用于ETS表标识）
-define(PLUGIN_MONGODB_TAB, emqx_plugin_mongodb_tab).

%% 钩子优先级
-define(HP_HIGHEST, 1000).
-define(HP_HIGH, 500).
-define(HP_MEDIUM, 0).
-define(HP_LOW, -500).
-define(HP_LOWEST, -1000).

%% 批处理相关宏
-define(DEFAULT_BATCH_SIZE, 1000).
-define(MIN_BATCH_SIZE, 100).
-define(MAX_BATCH_SIZE, 5000).
-define(DEFAULT_BATCH_TIMEOUT, 100). % ms
-define(MIN_BATCH_TIMEOUT, 10). % ms
-define(MAX_BATCH_TIMEOUT, 1000). % ms

%% 缓存相关宏
-define(TOPIC_MATCH_CACHE_TAB, emqx_plugin_mongodb_topic_cache).
-define(TOPIC_MATCH_CACHE_RECENT_TAB, emqx_plugin_mongodb_topic_recent).
-define(TOPIC_MATCH_CACHE_SIZE, 1000).
-define(TOPIC_MATCH_CACHE_TTL, 60000). % 60秒

%% 连接相关宏
-define(CONNECTION_POOLS_TAB, emqx_plugin_mongodb_connection_pools).
-define(CONNECTION_HEALTH_TAB, emqx_plugin_mongodb_connection_health).
-define(CONNECTION_SHARDS, 8).  % 默认分片数
-define(PREHEAT_CONNECTIONS, 4).  % 预热连接数
-define(HEALTH_CHECK_INTERVAL, 10000).  % 10秒
-define(HEALTH_THRESHOLD_GOOD, 0.9).  % 90%健康率为良好
-define(HEALTH_THRESHOLD_FAIR, 0.7).  % 70%健康率为一般
-define(HEALTH_DECAY_FACTOR, 0.95).  % 健康度衰减因子
-define(PING_COLLECTION, <<"foo">>).

%% 自适应批处理相关宏
-define(BATCH_METRICS_TAB, emqx_plugin_mongodb_batch_metrics).
-define(BATCH_CONFIG_TAB, emqx_plugin_mongodb_batch_config).
-define(BATCH_HISTORY_TAB, emqx_plugin_mongodb_batch_history).
-define(TOPIC_PRIORITY_TAB, emqx_plugin_mongodb_topic_priority).
-define(METRICS_WINDOW, 60). % 60秒窗口
-define(PREDICTION_INTERVAL, 5000). % 5秒

%% 并行处理管道相关宏
-define(PIPELINE_WORKERS, 4).
-define(PARSE_WORKERS, 2).
-define(TRANSFORM_WORKERS, 2).
-define(BATCH_WORKERS, 2).
-define(SEND_WORKERS, 2).
-define(QUEUE_SIZE_LIMIT, 10000).
-define(STAGE_TIMEOUT, 5000).
-define(PIPELINE_SUPERVISOR, emqx_plugin_mongodb_pipeline_sup).
-define(PIPELINE_REGISTRY, emqx_plugin_mongodb_pipeline_registry).

%% 高级熔断器相关宏
-define(BREAKER_REGISTRY, emqx_plugin_mongodb_circuit_breakers).
-define(DEFAULT_FAILURE_THRESHOLD, 5).
-define(DEFAULT_RESET_TIMEOUT, 30000). % 30秒
-define(DEFAULT_HALF_OPEN_RATIO, 0.1). % 10%
-define(DEFAULT_RECOVERY_STEP, 0.1). % 10%
-define(DEFAULT_RECOVERY_INTERVAL, 5000). % 5秒

%% 监控数据存储相关宏 - 用于熔断器高级监控
-define(RESPONSE_TIME_TAB, emqx_plugin_mongodb_response_times).
-define(CONCURRENT_TAB, emqx_plugin_mongodb_concurrent_requests).
-define(ERROR_RATE_TAB, emqx_plugin_mongodb_error_rates).
-define(DEFAULT_ERROR_THRESHOLD_RATIO, 0.8). % 80%
-define(CIRCUIT_BREAKER_CHECK_INTERVAL, 5000). % 5秒
-define(MAX_ERROR_HISTORY, 100).

%% 错误严重程度
-define(SEVERITY_CRITICAL, critical).
-define(SEVERITY_MAJOR, major).
-define(SEVERITY_MINOR, minor).
-define(SEVERITY_WARNING, warning).
-define(SEVERITY_INFO, info).

%% 错误类型
-define(ERROR_TYPE_CONNECTION, connection).
-define(ERROR_TYPE_AUTHENTICATION, authentication).
-define(ERROR_TYPE_TIMEOUT, timeout).
-define(ERROR_TYPE_QUERY, query).
-define(ERROR_TYPE_NETWORK, network).
-define(ERROR_TYPE_SYSTEM, system).
-define(ERROR_TYPE_UNKNOWN, unknown).

%% 异常处理增强相关宏
-define(ERROR_REGISTRY, emqx_plugin_mongodb_error_registry).
-define(ERROR_HANDLER_REGISTRY, emqx_plugin_mongodb_error_handler_registry).
-define(ERROR_STATS, emqx_plugin_mongodb_error_stats).
-define(RECOVERY_INTERVAL, 5000). % 5秒
-define(FAULT_INJECTION_INTERVAL, 60000). % 1分钟

%% 资源管理优化相关宏
-define(RESOURCE_STATS, emqx_plugin_mongodb_resource_stats).
-define(RESOURCE_LIMITS, emqx_plugin_mongodb_resource_limits).
-define(MONITOR_INTERVAL, 5000). % 5秒
-define(DEGRADATION_CHECK_INTERVAL, 1000). % 1秒
-define(DEFAULT_MEMORY_LIMIT, 0.8). % 80%内存使用率
-define(DEFAULT_CPU_LIMIT, 0.9). % 90%CPU使用率
-define(DEFAULT_CONNECTION_LIMIT, 10000). % 最大连接数
-define(DEFAULT_QUEUE_LIMIT, 100000). % 最大队列长度
-define(DEGRADATION_LEVELS, [normal, light, moderate, severe, critical]).

%% 背压机制相关宏
-define(BACKPRESSURE_TAB, emqx_plugin_mongodb_backpressure).
-define(BACKPRESSURE_MONITOR_INTERVAL, 1000). % 1秒
-define(BACKPRESSURE_DEFAULT_QUEUE_THRESHOLD_MILD, 1000).
-define(BACKPRESSURE_DEFAULT_QUEUE_THRESHOLD_MODERATE, 3000).
-define(BACKPRESSURE_DEFAULT_QUEUE_THRESHOLD_HIGH, 5000).
-define(BACKPRESSURE_DEFAULT_QUEUE_THRESHOLD_CRITICAL, 8000).
-define(BACKPRESSURE_DEFAULT_TIME_THRESHOLD_MILD, 100). % 毫秒
-define(BACKPRESSURE_DEFAULT_TIME_THRESHOLD_MODERATE, 300). % 毫秒
-define(BACKPRESSURE_DEFAULT_TIME_THRESHOLD_HIGH, 500). % 毫秒
-define(BACKPRESSURE_DEFAULT_TIME_THRESHOLD_CRITICAL, 1000). % 毫秒
-define(BACKPRESSURE_PRIORITY_HIGH, high).
-define(BACKPRESSURE_PRIORITY_MEDIUM, medium).
-define(BACKPRESSURE_PRIORITY_LOW, low).
-define(BACKPRESSURE_LEVEL_NORMAL, normal).
-define(BACKPRESSURE_LEVEL_MILD, mild).
-define(BACKPRESSURE_LEVEL_MODERATE, moderate).
-define(BACKPRESSURE_LEVEL_HIGH, high).
-define(BACKPRESSURE_LEVEL_CRITICAL, critical).

%% 会话持久化相关宏定义
-define(SESSION_PERSISTENCE_ENABLED, session_persistence_enabled).
-define(SESSION_AUTO_RESTORE, session_auto_restore).
-define(SESSION_RESTORE_ON_STARTUP, session_restore_on_startup).
-define(SESSION_EXPIRY, session_expiry).
-define(MESSAGE_EXPIRY, message_expiry).
-define(CLEANUP_INTERVAL, cleanup_interval).
-define(CLEANUP_BATCH_SIZE, cleanup_batch_size).

%%--------------------------------------------------------------------
%% MongoDB集合名称统一定义
%%--------------------------------------------------------------------

%% 会话持久化默认集合名称
-define(DEFAULT_SESSION_COLLECTION, <<"emqx_mqtt_sessions">>).
-define(DEFAULT_WILL_MESSAGE_COLLECTION, <<"emqx_mqtt_will_messages">>).

%% 集合名称别名（用于向后兼容）
-define(SESSIONS_COLLECTION, ?DEFAULT_SESSION_COLLECTION).
-define(MESSAGES_COLLECTION, ?DEFAULT_MESSAGE_COLLECTION).
-define(RETAINED_COLLECTION, ?DEFAULT_RETAINED_MESSAGE_COLLECTION).

%% 消息持久化默认集合名称
-define(DEFAULT_MESSAGE_COLLECTION, <<"emqx_mqtt_messages">>).
-define(DEFAULT_DATA_COLLECTION, <<"emqx_mqtt_data">>).

%% 订阅持久化默认集合名称
-define(DEFAULT_SUBSCRIPTION_COLLECTION, <<"emqx_mqtt_subscriptions">>).

%% 保留消息持久化默认集合名称
-define(DEFAULT_RETAINED_MESSAGE_COLLECTION, <<"emqx_mqtt_retained_messages">>).

%%--------------------------------------------------------------------
%% 消息去重机制相关宏定义
%%--------------------------------------------------------------------

%% 消息去重ETS表名称
-define(MESSAGE_DEDUP_TABLE, emqx_mongodb_message_dedup).

%% 消息去重记录过期时间（毫秒）- 24小时
-define(MESSAGE_DEDUP_TTL, 86400000).

%% 消息去重清理间隔（毫秒）- 1小时
-define(MESSAGE_DEDUP_CLEANUP_INTERVAL, 3600000).

%% 消息去重表最大记录数
-define(MESSAGE_DEDUP_MAX_RECORDS, 1000000).

%%--------------------------------------------------------------------
%% 内存泄漏检测器相关宏定义
%%--------------------------------------------------------------------

%% 内存泄漏检测器ETS表名称
-define(MEMORY_STATS_TAB, emqx_mongodb_memory_stats).
-define(ETS_MONITOR_TAB, emqx_mongodb_ets_monitor).
-define(MEMORY_HISTORY_TAB, emqx_mongodb_memory_history).
-define(LEAK_ALERTS_TAB, emqx_mongodb_leak_alerts).

%% 内存泄漏检测间隔（毫秒）
-define(MEMORY_DETECTION_INTERVAL, 30000).        % 30秒检测一次
-define(MEMORY_CLEANUP_INTERVAL, 300000).         % 5分钟清理一次
-define(MEMORY_TREND_ANALYSIS_INTERVAL, 600000).  % 10分钟趋势分析一次

%% 内存使用阈值（百分比）
-define(MEMORY_WARNING_THRESHOLD, 0.7).    % 70%内存使用率警告
-define(MEMORY_CRITICAL_THRESHOLD, 0.85).  % 85%内存使用率严重
-define(MEMORY_EMERGENCY_THRESHOLD, 0.95). % 95%内存使用率紧急

%% ETS表大小阈值
-define(ETS_SIZE_WARNING, 100000).         % 10万条记录警告
-define(ETS_SIZE_CRITICAL, 500000).        % 50万条记录严重
-define(ETS_MEMORY_WARNING, 104857600).    % 100MB内存警告

%% 内存增长率阈值（百分比）
-define(MEMORY_GROWTH_RATE_WARNING, 0.1).  % 10%增长率警告
-define(MEMORY_GROWTH_RATE_CRITICAL, 0.2). % 20%增长率严重

%% 历史数据保留配置
-define(MEMORY_HISTORY_RETENTION_HOURS, 24).      % 保留24小时历史数据
-define(MEMORY_MAX_HISTORY_RECORDS, 2880).        % 最大历史记录数（24小时*60分钟/0.5分钟）

%%--------------------------------------------------------------------
%% 通用超时时间定义（毫秒）
%%--------------------------------------------------------------------

%% MongoDB操作超时
-define(DEFAULT_MONGODB_TIMEOUT, 15000).          % 15秒
-define(DEFAULT_MONGODB_CONNECT_TIMEOUT, 10000).  % 10秒
-define(DEFAULT_MONGODB_QUERY_TIMEOUT, 5000).     % 5秒

%% 进程间通信超时
-define(DEFAULT_GENSERVER_TIMEOUT, 5000).         % 5秒
-define(DEFAULT_CAST_TIMEOUT, 1000).               % 1秒

%% 健康检查超时
-define(DEFAULT_HEALTH_CHECK_TIMEOUT, 3000).      % 3秒

%%--------------------------------------------------------------------
%% 连接健康检查和故障转移相关宏定义
%%--------------------------------------------------------------------

%% 连接健康检查相关ETS表名称
-define(CONNECTION_HEALTH_DETAIL_TAB, emqx_mongodb_connection_health_detail).
-define(HEALTH_STATISTICS_TAB, emqx_mongodb_health_statistics).

%% 连接健康检查间隔和阈值
-define(DEFAULT_HEALTH_CHECK_INTERVAL, 30000).    % 默认健康检查间隔30秒
-define(HEALTH_CHECK_TIMEOUT, 10000).             % 健康检查超时时间10秒
-define(CONNECTION_RECOVERY_DELAY, 30000).        % 连接恢复延迟30秒
-define(FAILOVER_THRESHOLD, 0.3).                 % 故障转移阈值
-define(MAX_CONSECUTIVE_FAILURES, 5).             % 最大连续失败次数
-define(CONNECTION_SCORE_HISTORY_SIZE, 100).      % 连接评分历史记录大小

%% 连接池管理优化相关宏
-define(CONNECTION_POOL_TAB, emqx_plugin_mongodb_connection_pool).
-define(CONNECTION_POOL_STATS_TAB, emqx_plugin_mongodb_pool_stats).
-define(DEFAULT_POOL_SIZE, 10).                   % 默认连接池大小
-define(MIN_POOL_SIZE, 2).                        % 最小连接池大小
-define(MAX_POOL_SIZE, 50).                       % 最大连接池大小
-define(POOL_OPTIMIZATION_INTERVAL, 60000).       % 连接池优化间隔60秒
-define(CONNECTION_WARMUP_TIMEOUT, 10000).        % 连接预热超时10秒
-define(RECOVERY_BATCH_SIZE, 3).                  % 恢复批次大小
-define(RECOVERY_DELAY, 2000).                    % 恢复延迟2秒

%% 连接健康阈值定义（扩展）
-define(HEALTH_THRESHOLD_EXCELLENT, 0.9).         % 90%健康率为优秀
-define(HEALTH_THRESHOLD_POOR, 0.5).              % 50%健康率为较差
-define(HEALTH_RECOVERY_THRESHOLD, 0.6).          % 60%健康率开始恢复

%% 批处理优化相关ETS表名称
-define(BATCH_PERFORMANCE_TAB, emqx_mongodb_batch_performance).
-define(PERFORMANCE_METRICS_TAB, emqx_mongodb_performance_metrics).
-define(PREDICTION_CACHE_TAB, emqx_mongodb_prediction_cache).

%% 批处理优化配置参数
-define(PERFORMANCE_ANALYSIS_INTERVAL, 60000).    % 性能分析间隔60秒
-define(BATCH_OPTIMIZATION_THRESHOLD, 0.1).       % 批处理优化阈值
-define(PERFORMANCE_HISTORY_SIZE, 100).           % 性能历史记录大小

%% 批处理超时优化相关宏
-define(TIMEOUT_OPTIMIZATION_ENABLED, true).      % 启用超时优化
-define(TIMEOUT_ADJUSTMENT_FACTOR, 0.3).          % 超时调整因子
-define(LOAD_BASED_TIMEOUT_ENABLED, true).        % 启用基于负载的超时调整

%% 索引优化相关ETS表名称
-define(INDEX_USAGE_TAB, emqx_mongodb_index_usage).
-define(QUERY_PERFORMANCE_TAB, emqx_mongodb_query_performance).
-define(QUERY_STATS_TAB, emqx_mongodb_query_stats).
-define(INDEX_RECOMMENDATIONS_TAB, emqx_mongodb_index_recommendations).

%% 索引优化配置参数
-define(DEFAULT_SLOW_QUERY_THRESHOLD, 1000).      % 默认慢查询阈值1秒
-define(INDEX_USAGE_THRESHOLD, 0.1).              % 索引使用率阈值
-define(INDEX_ANALYSIS_INTERVAL, 300000).         % 索引分析间隔5分钟
-define(MAX_INDEX_RECOMMENDATIONS, 50).           % 最大索引推荐数量

%% 并发优化相关ETS表名称
-define(CONCURRENCY_STATS_TAB, emqx_mongodb_concurrency_stats).
-define(LOCK_CONTENTION_TAB, emqx_mongodb_lock_contention).
-define(DEADLOCK_DETECTION_TAB, emqx_mongodb_deadlock_detection).

%% 并发优化配置参数
-define(DEFAULT_CONCURRENCY_LEVEL, 10).           % 默认并发级别
-define(MAX_CONCURRENCY_LEVEL, 100).              % 最大并发级别
-define(MIN_CONCURRENCY_LEVEL, 1).                % 最小并发级别
-define(CONCURRENCY_ANALYSIS_INTERVAL, 60000).    % 并发分析间隔1分钟
-define(LOCK_CONTENTION_THRESHOLD, 0.3).          % 锁竞争阈值
-define(DEADLOCK_DETECTION_INTERVAL, 30000).      % 死锁检测间隔30秒

%% 容错和错误恢复相关ETS表名称
-define(FAULT_TOLERANCE_STATS_TAB, fault_tolerance_stats).
-define(RECOVERY_HISTORY_TAB, recovery_history).
-define(HEALTH_METRICS_TAB, health_metrics).

%% 容错配置参数
-define(DEFAULT_HEALTH_THRESHOLD, 70).            % 默认健康阈值
-define(DEFAULT_RECOVERY_TIMEOUT, 30000).         % 默认恢复超时30秒
-define(DEFAULT_MAX_RECOVERY_ATTEMPTS, 3).        % 默认最大恢复尝试次数
-define(SELF_HEALING_COOLDOWN, 60000).            % 自愈冷却时间60秒

%%--------------------------------------------------------------------
%% 通用大小限制定义
%%--------------------------------------------------------------------

%% 内存限制（字节）
-define(DEFAULT_PROCESS_MEMORY_LIMIT, 52428800).  % 50MB
-define(DEFAULT_ETS_MEMORY_LIMIT, 104857600).     % 100MB
-define(DEFAULT_TOTAL_MEMORY_LIMIT, **********).  % 1GB

%% 队列大小限制
-define(DEFAULT_MESSAGE_QUEUE_LIMIT, 10000).      % 消息队列限制
-define(DEFAULT_BATCH_QUEUE_LIMIT, 5000).         % 批处理队列限制

%% 性能监控相关ETS表名称
-define(PERFORMANCE_HISTORY_TAB, emqx_mongodb_performance_history).
-define(ALERT_RECORDS_TAB, emqx_mongodb_alert_records).
-define(OPTIMIZATION_SUGGESTIONS_TAB, emqx_mongodb_optimization_suggestions).

%% 性能监控配置参数
-define(DEFAULT_COLLECTION_INTERVAL, 5000).       % 默认指标收集间隔5秒
-define(DEFAULT_TREND_WINDOW, 3600).              % 默认趋势分析窗口1小时
-define(PERFORMANCE_HISTORY_LIMIT, 1000).         % 性能历史记录限制
-define(ALERT_HISTORY_LIMIT, 500).                % 告警历史记录限制

%% 性能阈值定义
-define(CPU_USAGE_THRESHOLD, 0.8).                % CPU使用率阈值80%
-define(MEMORY_USAGE_THRESHOLD, 0.85).            % 内存使用率阈值85%
-define(RESPONSE_TIME_THRESHOLD, 1000).           % 响应时间阈值1秒
-define(ERROR_RATE_THRESHOLD, 0.05).              % 错误率阈值5%
-define(THROUGHPUT_DROP_THRESHOLD, 0.3).          % 吞吐量下降阈值30%

%% 负载均衡相关ETS表名称
-define(LOAD_BALANCER_STATS_TAB, emqx_mongodb_load_balancer_stats).
-define(CONNECTION_WEIGHTS_TAB, emqx_mongodb_connection_weights).
-define(LOAD_BALANCE_HISTORY_TAB, emqx_mongodb_load_balance_history).

%% 负载均衡配置参数
-define(DEFAULT_LB_ALGORITHM, round_robin).       % 默认负载均衡算法
-define(DEFAULT_ADJUSTMENT_INTERVAL, 30000).      % 默认动态调整间隔30秒
-define(DEFAULT_REBALANCE_THRESHOLD, 0.2).        % 默认重平衡阈值20%
-define(MIN_CONNECTION_WEIGHT, 0.1).              % 最小连接权重
-define(MAX_CONNECTION_WEIGHT, 10.0).             % 最大连接权重
-define(DEFAULT_CONNECTION_WEIGHT, 1.0).          % 默认连接权重

%% 负载均衡算法相关参数
-define(CONSISTENT_HASH_REPLICAS, 100).           % 一致性哈希副本数
-define(ADAPTIVE_SCORE_FACTORS, #{                % 自适应评分因子
    response_time => 0.4,
    connection_count => 0.3,
    error_rate => 0.2,
    health_score => 0.1
}).

-endif.
